# About Substrate Logging Extension

*<div style="text-align: right; font-size:12px">Last Modified: @@LastModified</div>*

Welcome to the **Substrate Logging Extension**!

This extension（[Microsoft.M365.Core.Telemetry.R9.Logging.Substrate](https://o365exchange.visualstudio.com/O365%20Core/_artifacts/feed/M365Common/NuGet/Microsoft.M365.Core.Telemetry.R9.Logging.Substrate/overview/1.2.0-rc)） is designed specifically for Substrate users to facilitate the utilization of **R9 Logging**. It streamlines the setup and configuration process for various logging scenarios, providing you with the flexibility to easily configure data sampling and specify where your logs are stored by plugging in components.

## 🎉 Key Features

- **One Stop Logging Configure APIs:**
  - Provide streamline and user-friendly extension APIs for ILoggingBuilder and IServiceCollection respectively to configure logging.
    - **Dependency Injection (DI)**: Initialize R9 Logging using `IServiceCollection` for a smooth integration within your application's dependency injection setup. For advanced scenarios, customize the `ILoggerBuilder` when using `ServiceCollection` to fine-tune logging behavior.
    - **Non-DI**: Set up R9 Logging without dependency injection if your project doesn't utilize DI patterns.
  - Both .NET Core and .NET Framework services are supported.

- **Composite Exporter:**
  - Enable routing log records to differentiate destinations based on configuration.
  - Supports minute-level hot-reloading and refresh.

- **ECS Integration APIs:**
  - Provided APIs to integrate ECS [(What is ECS? | ECS User Manual)](https://eng.ms/docs/experiences-devices/m365-core/substrate-platform/substrate-change-management/m365-experimentation-and-configuration-ecs/ecs-user-manual) as the configuration source, besides the common configuration source [(Configuration - .NET Microsoft Learn)](https://learn.microsoft.com/en-us/dotnet/core/extensions/configuration#configuration-providers).
  - Both .NET Core and .NET Framework services are supported and APIs are designed user-friendly.
  - Provide two-layer fallback mechanism to ensure resilience.


## 📚 Resources

- **Source Code:** [R9.Logging.Substrate - Repos](https://o365exchange.visualstudio.com/O365%20Core/_git/TelemetryCore?path=/sources/dev/R9.Logging.Substrate)
- **Sample Apps:** [DI & Non-DI Sample Apps](https://o365exchange.visualstudio.com/O365%20Core/_git/TelemetryCore?path=/sources/sample/R9.Logging.Substrate)
- **Design Spec:** [Substrate R9 Logging Extension support globally control log configurations.docx](https://microsoftapc-my.sharepoint.com/:w:/g/personal/yangyzh_microsoft_com/ESH-WiNpU2pCpa1uyYxJfN0BjXuYZnnF08YETZUid0FS0A?e=BaAp7O&CID=816fab5b-4838-0dbb-f925-ec429e629e1f)

## 🌈 Design Philosophy
This  Logging extension is built around a simple yet powerful concept: most of its behavior is controlled via configuration. Besides, we have several individual [Components](Reference/ComponentRef.md) which can be plugged in to support some complex logging scenario.

**Navigate to pages for detail:**

- [**APIs**](Reference/APIRef.md): Explore extension methods and practical examples that demonstrate how to use our logging functionalities.

- [**Configuration**](Reference/ConfigRef.md): Discover the configuration options that allow you to effortlessly tailor logging behavior to your specific requirements.
Understand mandatory settings that are essential for a seamless logging experience.

- [**Components**](Reference/ComponentRef.md): Learn about the individual components that can be integrated to support complex logging scenarios, providing additional flexibility and functionality to your logging.