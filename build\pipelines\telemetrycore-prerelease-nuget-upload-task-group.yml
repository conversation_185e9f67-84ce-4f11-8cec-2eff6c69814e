# This Yaml Document has been converted by ESAI Yaml Pipeline Conversion Tool.
# Please make sure to check all the converted content, it is your team's responsibility to make sure that the pipeline is still valid and functions as expected.
# The 'ms-vscs-artifact.build-tasks.artifactDropDownloadTask-1.artifactDropDownloadTask@0' tasks have been converted to inputs within the `templateContext` section of each job.
parameters:
- name: Build.Repository.Id
  type: string
  default: $(Build.Repository.Id)
- name: FilePath
  type: string
  default: build\configuration\public\xml\PackageInfo.xml
- name: PathToVstsBuildHelper
  type: string
  default: $(System.ArtifactsDirectory)\VstsBuildHelper\bin\VstsBuildHelper.exe
- name: ReleasePath
  type: string
  default: $(System.DefaultWorkingDirectory)\drop\target\distrib
steps:
- task: oenginternal.office-ado-isolation-tasks-extension.6873BD18-F0BB-47D6-BE2C-DD9F3E0EC5F4.ADOAgentSetup@0
  displayName: ADO Agent Setup
- task: DownloadPackage@1
  displayName: 'Download VstsBuildHelper Package '
  inputs:
    feed: /f205f2e0-40d8-4f02-9b91-709b4ecaf6cb
    definition: c5e87f30-8c54-431d-9dc4-dde9e7911deb
    version: 1.3.36
    downloadPath: $(System.ArtifactsDirectory)\VstsBuildHelper
- task: PowerShell@2
  displayName: Download File - PackageInfo.xml
  inputs:
    targetType: inline
    script: |
      Write-Host "Download PackageInfo.xml"
      Write-Host "${{parameters.PathToVstsBuildHelper}} -b $(Build.SourceBranch) -u $(SYSTEM.TEAMFOUNDATIONCOLLECTIONURI) -n $(Build.Repository.Name) -p $(SYSTEM.TEAMPROJECTID) -i ${{parameters['Build.Repository.Id']}} -o FileDownload -bt Daily -d ${{parameters.FilePath}} -r false -z"
      ${{parameters.PathToVstsBuildHelper}} -b $(Build.SourceBranch) -u $(SYSTEM.TEAMFOUNDATIONCOLLECTIONURI) -n $(Build.Repository.Name) -p $(SYSTEM.TEAMPROJECTID) -i ${{parameters['Build.Repository.Id']}} -o FileDownload -bt Daily -d ${{parameters.FilePath}} -r false -z 
      Write-Host "File dowloaded at the following location:"
      dir $(AGENT.RELEASEDIRECTORY)\build\configuration\public\xml
  env:
    DEVOPS_KEY: $(CodeRead_PAT)
- task: PowerShell@2
  displayName: VstsBuildHelper(PKG) - Publish Packages from Micro Repos Using Package Publisher
  inputs:
    targetType: inline
    script: |
      #Setting BuildVersion Variable
      $BuildVersion = "$(Build.BuildNumber)".TrimStart("v")
      Write-Host "-----------------------------------"
      Write-Host "Running VstsBuildHelper to Publish Packages from MicroRepos"
      Write-Host "${{parameters.PathToVstsBuildHelper}} -b master -u $(SYSTEM.TEAMFOUNDATIONCOLLECTIONURI) --RepositoryName $(Build.Repository.Name) -p $(SYSTEM.TEAMPROJECTID) --RepositoryId ${{parameters['Build.Repository.Id']}} -o PublishPackage  --PublishPackagePath ${{parameters.ReleasePath}} --PackageBuildVersion $BuildVersion --PackageInfoPath ${{parameters.FilePath}} --BuildIsSigned --AuthWithToken"
      ${{parameters.PathToVstsBuildHelper}} -b master -u $(SYSTEM.TEAMFOUNDATIONCOLLECTIONURI) --RepositoryName $(Build.Repository.Name) -p $(SYSTEM.TEAMPROJECTID) --RepositoryId ${{parameters['Build.Repository.Id']}} -o PublishPackage  --PublishPackagePath ${{parameters.ReleasePath}} --PackageBuildVersion $BuildVersion --PackageInfoPath ${{parameters.FilePath}} --BuildIsSigned --AuthWithToken
  env:
    EFENVIRONMENT: $(ENVIRONMENT)
    DEVOPS_KEY: $(PACKAGE_PAT)