{"options": [{"enabled": false, "definition": {"id": "a9db38f9-9fdc-478c-b0f9-464221e58316"}, "inputs": {"workItemType": "399868", "assignToRequestor": "false", "additionalFields": "{}"}}, {"enabled": false, "definition": {"id": "5d58cc01-7c75-450c-be18-a388ddb129ec"}, "inputs": {"branchFilters": "[\"+refs/heads/*\"]", "additionalFields": "{}"}}], "variables": {"build.cloudbuildqueue": {"value": "OSS_{{RepositoryName}}_Git"}, "build.pipeline": {"value": "{{RepositoryName}}"}, "system.debug": {"value": "false", "allowOverride": true}}, "retentionRules": [{"branches": ["+refs/heads/*"], "artifacts": ["build.SourceLabel"], "artifactTypesToDelete": [], "daysToKeep": 10, "minimumToKeep": 1, "deleteBuildRecord": true, "deleteTestResults": true}], "properties": {}, "tags": [], "jobAuthorizationScope": 1, "jobTimeoutInMinutes": 60, "jobCancelTimeoutInMinutes": 5, "process": {"phases": [{"steps": [{"environment": {}, "enabled": true, "continueOnError": true, "alwaysRun": true, "displayName": "MSBR: Precheckin Debug $(build.cloudbuildqueue) Agentless", "timeoutInMinutes": 0, "condition": "succeededOrFailed()", "task": {"id": "1e0779be-f211-4972-829e-3d84a479e1e2", "versionSpec": "1.*", "definitionType": "metaTask"}, "inputs": {"AdditionalFlags": "", "TARGET_CLOUDBUILDQUEUE": "$(build.cloudbuildqueue)"}}], "name": "Debug", "refName": "Job_1", "condition": "succeeded()", "target": {"executionOptions": {"type": 0}, "type": 2}, "jobAuthorizationScope": 1}], "type": 1}, "repository": {"properties": {"labelSources": "0", "reportBuildStatus": "false", "fetchDepth": "0", "gitLfsSupport": "false", "skipSyncSource": "true", "cleanOptions": "0", "checkoutNestedSubmodules": "false", "labelSourcesFormat": "$(build.buildNumber)"}, "id": "{{RepositoryId}}", "type": "TfsGit", "name": "{{RepositoryName}}", "url": "{{RepositoryUrl}}", "defaultBranch": "refs/heads/master", "clean": "true", "checkoutSubmodules": false}, "processParameters": {}, "quality": 1, "drafts": [], "queue": {"id": 26, "name": "Official", "url": "https://o365exchange.visualstudio.com/_apis/build/Queues/26", "pool": {"id": 11, "name": "Official"}}, "id": 4642, "name": "{{RepositoryName}} Precheckin_Debug", "path": "\\{{RepositoryName}}"}