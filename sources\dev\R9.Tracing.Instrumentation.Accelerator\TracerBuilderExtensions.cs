// <copyright file="TracerBuilderExtensions.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

namespace Microsoft.M365.Core.Telemetry.R9.Tracing.Instrumentation.Accelerator
{
    using System;
    using System.Collections.Generic;
#if !NETFRAMEWORK
    using Microsoft.R9.Extensions.Tracing.Http;
#endif
    using Microsoft.Extensions.DependencyInjection;
    using Microsoft.Extensions.DependencyInjection.Extensions;
    using Microsoft.M365.Core.Telemetry.Exporters.ODLTCP.Trace;
    using Microsoft.R9.Extensions.Diagnostics;
    using Microsoft.R9.Extensions.HttpClient.Tracing;
    using Microsoft.R9.Extensions.Telemetry.Exporter.Filters;
    using Microsoft.R9.Extensions.Tracing.Exporters;
    using OpenTelemetry.Trace;

    /// <summary>
    /// TracerProviderBuilderExtensions for R9.Tracing.Instrumentation.Accelerator
    /// </summary>
    public static class TracerBuilderExtensions
    {
        /// <summary>
        /// AddHttpClientTracing
        /// </summary>
        /// <param name="builder"></param>
        /// <param name="options"></param>
        /// <returns></returns>
        internal static TracerProviderBuilder AddHttpClientTracing(this TracerProviderBuilder builder, HttpClientTracingOptionsInherited options)
        {
            Throws.IfNull(builder, nameof(builder));

            if (!options.IsEnabled)
            {
                return builder;
            }
            _ = builder.ConfigureServices((IServiceCollection services) =>
            {
                services.TryAddSingleton(options);
            });

            return builder
                .AddHttpClientTracing(
                    opt =>
                    {
                        opt.RequestPathParameterRedactionMode = options.RequestPathParameterRedactionMode;
                        opt.RouteParameterDataClasses = options.RouteParameterDataClasses;
                    });
        }

        /// <summary>
        /// AddHttpTracing
        /// </summary>
        /// <param name="builder"></param>
        /// <param name="options"></param>
        /// <returns></returns>
        internal static TracerProviderBuilder AddHttpTracing(this TracerProviderBuilder builder, HttpTracingOptionsInherited options)
        {
            Throws.IfNull(builder, nameof(builder));

            if (!options.IsEnabled)
            {
                return builder;
            }

            _ = builder.ConfigureServices((IServiceCollection services) =>
            {
                services.TryAddSingleton(options);
            });

            return builder
#if NETFRAMEWORK
                .AddAspNetInstrumentation();
#else
                .AddHttpTracing(
                    opt =>
                    {
                        opt.RequestPathParameterRedactionMode = options.RequestPathParameterRedactionMode;
                        opt.RouteParameterDataClasses = options.RouteParameterDataClasses;
                        opt.ExcludePathStartsWith.UnionWith(options.ExcludePathStartsWith);
                    });
#endif
        }

        /// <summary>
        /// AddConsoleExporter
        /// </summary>
        /// <param name="builder"></param>
        /// <param name="options"></param>
        /// <returns></returns>
        internal static TracerProviderBuilder AddConsoleExporter(this TracerProviderBuilder builder, ConsoleTracingExporterOptionsInherited options)
        {
            if (!options.IsEnabled)
            {
                return builder;
            }

            return builder.AddConsoleExporter(
                opt =>
                {
                    opt.Targets = options.Targets;
                });
        }

        /// <summary>
        /// AddGenevaExporter
        /// </summary>
        /// <param name="builder"></param>
        /// <param name="options"></param>
        /// <param name="sampler"></param>
        /// <returns></returns>
        internal static TracerProviderBuilder AddGenevaExporter(this TracerProviderBuilder builder, GenevaTraceExporterOptionsInherited options, Sampler sampler = null)
        {
            Action<GenevaTraceExporterOptions> action = opt =>
            {
                opt.ConnectionString = options.ConnectionString;
                opt.CustomFields = options.CustomFields;
                opt.MaxQueueSize = options.MaxQueueSize;
                opt.ExportInterval = options.ExportInterval;

                // This is required for OpenTelemetry.Exporter.Geneva 1.3.1 +
                opt.TableNameMappings = options.TableNameMappings ?? new Dictionary<string, string>()
                {
                    ["Span"] = Constants.DefaultTableName,
                };

                opt.PrepopulatedFields = options.PrepopulatedFields;
            };
            return sampler != null ? builder.AddGenevaExporter(action, sampler) : builder.AddGenevaExporter(action);
        }

        /// <summary>
        /// AddODLExporter
        /// </summary>
        /// <param name="builder"></param>
        /// <param name="options"></param>
        /// <param name="sampler"></param>
        /// <returns></returns>
        internal static TracerProviderBuilder AddODLExporter(this TracerProviderBuilder builder, ODLTraceExporterOptionsInherited options, Sampler sampler = null)
        {
            Action<ODLTraceExporterOptions> action = opt =>
            {
                opt.EnableFallBack = options.EnableFallBack;
            };

            return sampler != null ? builder.AddODLExporter(action, options.BatchExport, sampler) : builder.AddODLExporter(action, options.BatchExport);
        }

        /// <summary>
        /// Add ODL for TCP exporter
        /// </summary>
        /// <param name="builder"></param>
        /// <param name="options"></param>
        /// <param name="sampler"></param>
        /// <returns></returns>
        internal static TracerProviderBuilder AddODLTcpExporter(this TracerProviderBuilder builder, ODLTcpTraceExporterOptionsInherited options, Sampler sampler = null)
        {
            Action<ODLTcpTraceExporterOptions> action = opt =>
            {
                opt = options;
            };

            return sampler != null ? builder.AddODLTcpExporter(action, options.BatchExport, sampler) : builder.AddODLTcpExporter(action, options.BatchExport);
        }
    }
}
