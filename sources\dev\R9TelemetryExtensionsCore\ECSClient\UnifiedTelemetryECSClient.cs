﻿// <copyright file="UnifiedTelemetryECSClient.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.Configuration;
using System.Diagnostics.CodeAnalysis;
using System.Security.Cryptography.X509Certificates;
using System.Threading;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Identity.Client;
using Microsoft.M365.Core.Telemetry.Enrichment;
using Microsoft.M365.Core.Telemetry.SDKLogger;
using Microsoft.M365.Security.CredSMART;
using Microsoft.Skype.ECS.Client;

namespace Microsoft.M365.Core.Telemetry.ECSClient
{
    /// <summary>
    /// UnifiedTelemetryECSClient
    /// </summary>
    internal class UnifiedTelemetryECSClient
    {
        /// <summary>
        /// UnifiedTelemetryECSClient object that is being lazily initialized.
        /// </summary>
        private static Lazy<UnifiedTelemetryECSClient> ecsInstance;

        /// <summary>
        /// create single object
        /// </summary>
        /// <param name="configuration"></param>
        /// <returns>UnifiedTelemetryECSClient singleton.</returns>
        public static UnifiedTelemetryECSClient EcsClientInstance(IConfiguration configuration)
        {
            if (ecsInstance == null)
            {
                ecsInstance = new Lazy<UnifiedTelemetryECSClient>(() => new UnifiedTelemetryECSClient(configuration), LazyThreadSafetyMode.PublicationOnly);
            }
            else
            {
                InitializeInstanceProperties(configuration);
            }
            return ecsInstance.Value;
        }

        /// <summary>
        /// create single object
        /// </summary>
        /// <param name="identifiers"></param>
        /// <returns>UnifiedTelemetryECSClient singleton.</returns>
        public static UnifiedTelemetryECSClient EcsClientInstance(Dictionary<string, string> identifiers)
        {
            if (ecsInstance == null)
            {
                ecsInstance = new Lazy<UnifiedTelemetryECSClient>(() => new UnifiedTelemetryECSClient(identifiers), LazyThreadSafetyMode.PublicationOnly);
            }
            return ecsInstance.Value;
        }

        /// <summary>
        /// The ECS config requester.
        /// </summary>
        public IECSConfigurationRequester EcsRequester { get; set; }

        /// <summary>
        /// ECSCommon filter: service name.
        /// </summary>
        public string ServiceName { get; set; }

        /// <summary>
        /// ECSCommon filter: runtime model.
        /// </summary>
        public string RuntimeModel { get; set; }

        /// <summary>
        /// ECS EnvironmentType: Production or Integration
        /// </summary>
        private string ecsEnvironment;

        /// <summary>
        /// Event wrapper.
        /// </summary>
        internal EventWrapper RegularUpdateEventWrapper { get; set; }

        // TODO(chenzejun): This constructor will be deprecated after fully migrating to V2.

        /// <summary>
        /// Constructor of the <see cref="UnifiedTelemetryECSClient" /> class.
        /// </summary>
        /// <param name="configuration"> The application configuration properties. </param>
        public UnifiedTelemetryECSClient(IConfiguration configuration)
        {
            SDKLog.Info("Initializing UnifiedTelemetryECSClient");

            //TODO: refine parameter names when we have a conclusion
            ServiceName = configuration.GetValue<string>("Microsoft_m365_core_telemetry:ServiceMetadata:ServiceName");
            RuntimeModel = configuration.GetValue<string>("Microsoft_m365_core_telemetry:ServiceMetadata:RuntimeModel");
            ecsEnvironment = Environment.GetEnvironmentVariable("Microsoft_M365_Core_Telemetry_ECSClient_ECSEnvironment");
            if (string.IsNullOrEmpty(ServiceName) || string.IsNullOrEmpty(RuntimeModel))
            {
                SDKLog.Error($"The configuration property 'serviceName' or 'runtimeModel' is missing in appsettings.json!");
            }

            if (!string.IsNullOrEmpty(RuntimeModel) && !Enum.IsDefined(typeof(Constants.RuntimeModel), RuntimeModel))
            {
                SDKLog.Error($"The RuntimeModel {RuntimeModel} from appsettings.json is not supported. Supported values: ModelA, ModelB, ModelB2, ModelD, ModelD2, Cosmic");
            }

            try
            {
                this.InitializeECSRequester(new Dictionary<string, string>()
                {
                    { "ServiceName", ServiceName },
                    { "Model",  RuntimeModel }
                });
            }
            catch (Exception e)
            {
                // TODO(jiadeng): Crash for init error instead try catch.
                SDKLog.Error($"ECS client init exception: {e}");
            }
        }

        /// <summary>
        /// Constructor of the <see cref="UnifiedTelemetryECSClient" /> class.
        /// </summary>
        /// <param name="identifiers">The identifiers to fetch ECS config</param>
        public UnifiedTelemetryECSClient(Dictionary<string, string> identifiers)
        {
            SDKLog.Info("Initializing UnifiedTelemetryECSClient");

            if (!identifiers.ContainsKey("DeployRing"))
            {
                SDKLog.Error("Missing required identifiers when initializing UnifiedTelemetryECSClient!");
            }

            if (!identifiers.TryGetValue("ServiceName", out string serviceName))
            {
                SDKLog.Error("Missing required identifiers 'ServiceName'");
            }

            if (!identifiers.TryGetValue("RuntimeModel", out string runtimeModel))
            {
                SDKLog.Error("Missing required identifiers 'RuntimeModel'");
            }

            ServiceName = serviceName;
            RuntimeModel = runtimeModel;
            ecsEnvironment = Environment.GetEnvironmentVariable("Microsoft_M365_Core_Telemetry_ECSClient_ECSEnvironment");
            this.InitializeECSRequester(identifiers);
        }

        /// <summary>
        /// Add ServiceName and RuntimeModel values into the instance when both V1 and V2 exist.
        /// </summary>
        /// <param name="configuration">The configuration from appsettings.json</param>
        private static void InitializeInstanceProperties(IConfiguration configuration)
        {
            var instance = ecsInstance.Value;

            instance.ServiceName = configuration.GetValue<string>("Microsoft_m365_core_telemetry:ServiceMetadata:ServiceName");
            instance.RuntimeModel = configuration.GetValue<string>("Microsoft_m365_core_telemetry:ServiceMetadata:RuntimeModel");
        }

        /// <summary>
        /// Generate certificate from its name.
        /// </summary>
        /// <param name="certificateName">The certificate name.</param>
        /// <returns></returns>
        private static X509Certificate2 GenerateCertificate(string certificateName)
        {
            M365Secret secretProvider = new M365Secret();
            X509Certificate2 cert = null;
            if (!string.IsNullOrEmpty(certificateName))
            {
                try
                {
                    SDKLog.Info($"Getting cert {certificateName}");
                    cert = secretProvider.GetLatestM365CertificateV2(certificateName);
                }
                catch (Exception e)
                {
                    SDKLog.Error($"Failed to get certificate {certificateName} from M365Secret: {e}");
                }
            }
            return cert;
        }

        /// <summary>
        /// Generate confidential client application with certificate.
        /// </summary>
        /// <param name="certificate">The certificate.</param>
        /// <returns></returns>
        [ExcludeFromCodeCoverage]
        private static IConfidentialClientApplication GenerateConfidential(X509Certificate2 certificate)
        {
            return ConfidentialClientApplicationBuilder.Create(Constants.ApplicationId)
                .WithCertificate(certificate, sendX5C: true)
                .WithAuthority(new Uri($"{Constants.MicrosoftHost}/{Constants.TenantId}"))
                .Build();
        }

        /// <summary>
        /// Initialize the ECS requester settings.
        /// </summary>
        private void InitializeECSRequester(Dictionary<string, string> identifiers)
        {
            ECSClientConfiguration ecsClientConfiguration;

            bool isTDS = DimensionValues.IsTDS.Equals("True", StringComparison.OrdinalIgnoreCase);

            if ((!string.IsNullOrEmpty(ecsEnvironment) && "Integration".Equals(ecsEnvironment, StringComparison.OrdinalIgnoreCase)) || isTDS)
            {
                ecsClientConfiguration = new ECSClientConfiguration(EnvironmentType.Integration, Constants.ECSClientName, Constants.ECSTeamNameInt);
            }
            else
            {
                ecsClientConfiguration = new ECSClientConfiguration(EnvironmentType.Production, Constants.ECSClientName, Constants.ECSTeamNameProd);
            }

            // Enable AAD auth
            if (!identifiers.ContainsKey("DeployRing"))
            {
                identifiers.Add("DeployRing", DimensionValues.DeployRing);
            }

            if (!identifiers.ContainsKey("MachineName"))
            {
                identifiers.Add("MachineName", DimensionValues.Machine);
            }

            if (!identifiers.ContainsKey("Forest"))
            {
                identifiers.Add("Forest", DimensionValues.Forest);
            }

            if (!identifiers.ContainsKey("MachineBuildVersion"))
            {
                identifiers.Add("MachineBuildVersion", DimensionValues.BuildVersion);
            }

            if (!identifiers.ContainsKey("IsTDS"))
            {
                identifiers.Add("IsTDS", DimensionValues.IsTDS);
            }

            string certificateName = GetCertificateName();

            IConfidentialClientApplication confidentialClientApplication = null;
            X509Certificate2 certificate = null;
            try
            {
                certificate = GenerateCertificate(certificateName);
                string certificateInfo = certificate == null ? $"{certificateName}_NotFound" : $"{certificate.IssuerName.Name}_{certificateName}";
                if (!identifiers.ContainsKey("CertificateName"))
                {
                    identifiers.Add("CertificateName", certificateInfo);
                }

                confidentialClientApplication = GenerateConfidential(certificate);
                ecsClientConfiguration.ConfigureHttpMessageHandlers = (builder) => builder.AddHttpMessageHandler(() => new EcsAuthHttpHandler(confidentialClientApplication));
            }
            catch (Exception e)
            {
                SDKLog.Error($"Failed to configure handler for acquiring token: {e}");
            }

            // These are required value for ecs client configuration
            ecsClientConfiguration.Cache.DefaultConfigJson = @"[]";
            ecsClientConfiguration.Cache.DefaultGroupsJson = @"{""Groups"":{}}";
            ecsClientConfiguration.DefaultRequestIdentifiers = identifiers;
            ecsClientConfiguration.RefreshInterval = TimeSpan.FromMinutes(Constants.DefaultRefreshInterval);

            IECSConfigurationRequester requester = ECSClientFactory.CreateLocal();
            RegularUpdateEventWrapper = new EventWrapper();
            requester.ConfigurationChanged += OnConfigurationChanged;
            requester.ConfigurationError += OnConfigurationErrorHandler;
            requester.Initialize(ecsClientConfiguration).GetAwaiter().GetResult();
            EcsRequester = requester;
        }

        [ExcludeFromCodeCoverage]
        private string GetCertificateName()
        {
            string certificateName = Constants.EXOCertificateName;

            if (!string.IsNullOrEmpty(DimensionValues.DeployRing) &&
                     DimensionValues.DeployRing.IndexOf("gallatin", StringComparison.OrdinalIgnoreCase) >= 0)
            {
                certificateName = Constants.GallatinCertificateName;
            }
            else if (!string.IsNullOrEmpty(DimensionValues.DeployRing) &&
                     DimensionValues.DeployRing.IndexOf("itar", StringComparison.OrdinalIgnoreCase) >= 0)
            {
                certificateName = Constants.ItarCertificateName;
            }
            else if (ECSClientUtilities.IsModelD(RuntimeModel))
            {
                certificateName = Constants.ModelDCertificateName;
            }
            else if (!string.IsNullOrEmpty(DimensionValues.Forest) &&
                DimensionValues.Forest.IndexOf("eop", StringComparison.OrdinalIgnoreCase) >= 0)
            {
                certificateName = Constants.EOPCertificateName;
            }
            else if (ECSClientUtilities.IsCosmic(RuntimeModel))
            {
                certificateName = Constants.CosmicCertificateName;
            }

            return certificateName;
        }

        /// <summary>
        /// Event handler for ConfigurationError event
        /// </summary>
        /// <param name="sender">event sender</param>
        /// <param name="eventArgs">event arguments</param>
        private void OnConfigurationErrorHandler(object sender, ConfigErrorEventArgs eventArgs)
        {
            SDKLog.Error($"ECS client gets configuration failed. {eventArgs?.Exception}");
        }

        /// <summary>
        /// Called when configuration changed.
        /// </summary>
        /// <param name="sender">The sender</param>
        /// <param name="args">event arguments</param>
        private void OnConfigurationChanged(object sender, ConfigChangedEventArgs args)
        {
            if (args.ConfigChangedAgents == null)
            {
                return;
            }

            UpdateConfigEventArgs updateConfigEventArgs = new UpdateConfigEventArgs
            {
                ChangedAgents = new HashSet<string>(args.ConfigChangedAgents, StringComparer.InvariantCultureIgnoreCase),
                WaitHandleCounter = new WaitHandleCounter(0)
            };
            RegularUpdateEventWrapper.Notify(updateConfigEventArgs);
        }
    }
}
