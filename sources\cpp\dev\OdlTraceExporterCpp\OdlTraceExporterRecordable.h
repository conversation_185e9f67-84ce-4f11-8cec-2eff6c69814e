#pragma once

#include <nlohmann/json.hpp>
#include <opentelemetry/common/attribute_value.h>
#include <opentelemetry/common/key_value_iterable.h>
#include <opentelemetry/common/timestamp.h>
#include <opentelemetry/logs/logger.h>
#include <opentelemetry/nostd/string_view.h>
#include <opentelemetry/sdk/instrumentationscope/instrumentation_scope.h>
#include <opentelemetry/sdk/resource/resource.h>
#include <opentelemetry/sdk/trace/recordable.h>
#include <opentelemetry/trace/span_context.h>
#include <opentelemetry/trace/span_id.h>
#include <opentelemetry/trace/span_metadata.h>
#include <opentelemetry/trace/trace_flags.h>
#include <opentelemetry/version.h>

#include <chrono>
#include <map>
#include <string>
#include <unordered_set>

namespace Microsoft::M365::Exporters
{
using OdlSpan = nlohmann::json;

class OdlTraceExporterRecordable final : public opentelemetry::sdk::trace::Recordable
{
  public:
    // TODO(jiayiwang): Should use Resource once the crash is fixed.
    std::string Serialize(const std::unordered_map<std::string, std::string> &common_dimensions, std::shared_ptr<opentelemetry::logs::Logger> logger) const noexcept;

    void SetIdentity(const opentelemetry::trace::SpanContext &span_context,
                     opentelemetry::trace::SpanId parent_span_id) noexcept override;

    void SetAttribute(opentelemetry::nostd::string_view key,
                      const opentelemetry::common::AttributeValue &value) noexcept override;

    void AddEvent(opentelemetry::nostd::string_view name,
                  opentelemetry::common::SystemTimestamp timestamp,
                  const opentelemetry::common::KeyValueIterable &attributes) noexcept override;

    void AddLink(const opentelemetry::trace::SpanContext &span_context,
                 const opentelemetry::common::KeyValueIterable &attributes) noexcept override;

    void SetStatus(opentelemetry::trace::StatusCode code,
                   opentelemetry::nostd::string_view description) noexcept override;

    void SetName(opentelemetry::nostd::string_view name) noexcept override;

    void SetTraceFlags(opentelemetry::trace::TraceFlags flags) noexcept override;

    void SetStartTime(opentelemetry::common::SystemTimestamp start_time) noexcept override;

    void SetSpanKind(opentelemetry::trace::SpanKind span_kind) noexcept override;

    void SetResource(const opentelemetry::sdk::resource::Resource &resource) noexcept override;

    void SetDuration(std::chrono::nanoseconds duration) noexcept override;

    void SetInstrumentationScope(
        const opentelemetry::sdk::instrumentationscope::InstrumentationScope &instrumentation_scope) noexcept override;
    
    std::string DebugString() const noexcept;

  private:
    struct Event
    {
        std::string name;
        std::string timestamp;
        std::map<std::string, opentelemetry::common::AttributeValue> attributes;
    };

    std::string trace_id_;
    std::string span_id_;
    std::string kind_;
    std::string status_ = "Unset";
    std::string display_name_;
    std::string source_name_;
    std::string source_version_;
    std::string duration_;
    std::string start_time_utc_;
    std::map<std::string, opentelemetry::common::AttributeValue> attributes_;
    // Temp hack to store the geneva span attrs: env_dt_spanId as otel_spanId, env_dt_traceId as otel_traceId, parentId as otel_parentSpanId
    // TODO(jiayiwang): Remove when ODL exporter processor and geneva processor can start spans with the same ids.
    std::map<std::string, std::string> owned_attributes_;
    std::vector<Event> events_;
    std::string parent_id_;
};
} // namespace Microsoft::M365::Exporters