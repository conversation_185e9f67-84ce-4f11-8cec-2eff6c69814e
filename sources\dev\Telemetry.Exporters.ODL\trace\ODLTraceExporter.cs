﻿// <copyright file="ODLTraceExporter.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.Diagnostics;
using Microsoft.Extensions.Options;
using Microsoft.R9.Extensions.Diagnostics;
using Microsoft.R9.Extensions.Telemetry.Exporters.Base;
using OpenTelemetry;
using OpenTelemetry.Logs;

namespace Microsoft.R9.Extensions.Tracing.Exporters
{
    /// <summary>
    /// a exporter used to export odl logs.
    /// </summary>
    public class ODLTraceExporter : BaseExporter<Activity>
    {
        private readonly IReadOnlyDictionary<string, string> logTypeMappings;

        private readonly IReadOnlyDictionary<string, string> fields;

        private readonly ServiceDependentLogger odlLogger;

        private readonly bool matchAll;

        private readonly string matchAllLogType;

        /// <summary>
        /// Initializes a new instance of the <see cref="ODLTraceExporter"/> class.
        /// </summary>
        /// <param name="configurations">exporter options for odl nrt.</param>
        public ODLTraceExporter(IOptions<ODLTraceExporterOptions> configurations)
        {
            _ = Throws.IfNull(configurations);
            ODLTraceExporterOptions options = configurations.Value;
            _ = Throws.IfNull(options);

            // set fields with prepoluated fields
            fields = options.PrepopulatedFields;

            logTypeMappings = options.LogTypeMappings;

            //using default logtype while logtype mapping is empty or null. 
            if (logTypeMappings == null || logTypeMappings.Count == 0)
            {
                logTypeMappings = new Dictionary<string, string>
                {
                    ["*"] = "R9TraceExporter",
                };
            }
            foreach (var entry in logTypeMappings)
            {
                if (string.IsNullOrEmpty(entry.Key))
                {
                    Throws.ArgumentNullException("activity source name", "please provide valid activity source and logtype mapping");
                }
                if (string.IsNullOrEmpty(entry.Value))
                {
                    Throws.ArgumentNullException("ODL logtype", "please provide valid activity source and logtype mapping");
                }
            }

            // if only one logtype used, and all activity source related to the same logtype 
            if (logTypeMappings.TryGetValue("*", out matchAllLogType))
            {
                matchAll = true;
            }

            odlLogger = new ServiceDependentLogger(options.EnableFallBack);
        }

        /// <summary>
        /// export batch of LogRecords<see cref="LogRecord"/> using wrapped DBALogger.
        /// </summary>
        /// <param name="batch">batch records.</param>
        /// <returns>export result.</returns>
        public override ExportResult Export(in Batch<Activity> batch)
        {
            foreach (var activity in batch)
            {
                // prefer match user typed logtype first
                if (!logTypeMappings.TryGetValue(activity.Source.Name, out string mappedLogType))
                {
                    if (matchAll)
                    {
                        mappedLogType = matchAllLogType;
                    }
                    else
                    {
                        continue;
                    }
                }

                var atguid = Guid.NewGuid().ToString();

                odlLogger.Trace(mappedLogType, atguid, SerializeActivity(activity));
            }

            return ExportResult.Success;
        }

        /// <summary>
        /// Serialize activity to json string.
        /// </summary>
        /// <param name="activity"></param>
        /// <returns>json string</returns>
        internal string SerializeActivity(Activity activity)
        {
            return ActivitySerializer.Serialize(activity, this.fields);
        }
    }
}