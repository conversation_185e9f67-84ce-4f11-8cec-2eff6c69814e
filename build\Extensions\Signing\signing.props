<?xml version="1.0" encoding="utf-8"?>
<!--
  This file is imported for all projects in 'src' root directory
-->
<Project xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  
  <PropertyGroup Label="Assign signing type">
    <Signing_Type_At_Request>cloudSign</Signing_Type_At_Request>
    <Build_IsSigned Condition="'$(Build_IsSigned)'==''">true</Build_IsSigned>
    <ENABLE_CLOUDSIGN Condition="'$(ENABLE_CLOUDSIGN)'=='' AND '$(Signing_Type_At_Request)' == 'cloudSign'">1</ENABLE_CLOUDSIGN>
    <ENABLE_EPRS Condition="'$(ENABLE_EPRS)'=='' AND '$(Signing_Type_At_Request)' == 'esrp'">1</ENABLE_EPRS>
  </PropertyGroup>

</Project>

