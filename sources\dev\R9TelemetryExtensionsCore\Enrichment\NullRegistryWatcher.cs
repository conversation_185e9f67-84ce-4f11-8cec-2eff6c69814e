﻿// <copyright file="NullRegistryWatcher.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System;
using System.Diagnostics.CodeAnalysis;

namespace Microsoft.M365.Core.Telemetry.Enrichment
{
    /// <summary>
    /// NullRegistryWatcher
    /// </summary>
    [ExcludeFromCodeCoverage]
    internal class NullRegistryWatcher : IRegistryWatcher
    {
        /// <summary>
        /// singleton
        /// </summary>
        public static readonly NullRegistryWatcher Instance = new NullRegistryWatcher();

        /// <summary>
        /// private ctor
        /// </summary>
        private NullRegistryWatcher()
        {
        }

        /// <inheritdoc />
        public Action KeyChanged { get; set; }

        /// <inheritdoc />
        public void Start()
        {
            // do nothing
        }
    }
}
