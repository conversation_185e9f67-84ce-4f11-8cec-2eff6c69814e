## Check list for migrating packages from Root repo to Coral repo
<ol>
  <li>Search these packages in Root repo to see if they are referred to by other projects.</li>
  <li>For each package, check the git history of its whole directory to know the latest commitment from where we start code migration.</li>
  <li>Stop checking in new code of these packages in Root repo.</li>
  <li>Copy whole directories of both dev and test code to Coral repo, branch release/TelemetryCore_1.0.18. (First complete migration in R9 1.18 branch, then sync to other branch later)</li>
  <li>Open TelemetryCore.sln via VS, right click to add existing prjects that we copied to the solution directory in preceding step.</li>
  <li>Msbuild TelemetryCore.sln, may occur errors, typically 'package not found'.</li>
  <li>Add required denpendencies in Directory.Packages.props to fix preceding errors.</li>
  <li>Fix breaking change if exist, build and execute unit test, improve code coverage.</li>
  <li>Complete PR to check in target branch in Coral repo.</li>
  <li>Publish and apply new version of these packages to destinaion like Substrate for package validation.</li>
  <li>If there are projects reference in Root repo, Change to package reference.</li>
  <li>Delete legacy code in Root repo.</li>
</ol>


