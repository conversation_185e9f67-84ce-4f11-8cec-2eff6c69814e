﻿// <copyright file="LibraryManagedServiceCollection.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System;
using System.IO;
using System.Text;
using System.Threading;

using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.M365.Core.Telemetry.SDKLogger;
using Microsoft.R9.Extensions.Logging;
using Microsoft.R9.Extensions.Logging.Exporters;
using Microsoft.R9.Extensions.Metering;

namespace Microsoft.M365.Core.Telemetry.ECSClient
{
    /// <summary>
    /// LibraryManagedServiceCollection
    /// </summary>
    internal class LibraryManagedServiceCollection : IManagedServiceCollection
    {
        /// <summary>
        /// Singleton of the instance
        /// </summary>
        private static LibraryManagedServiceCollection instance;

        /// <summary>
        /// Default config of R9 host
        /// </summary>
        private static string defaultR9Config = "{\"Logging\": {\"GenevaLogging\": {\"ConnectionString\": \"EtwSession=o365PassiveMonitoringSessionR9\",\"TableNameMappings\": {\"PassiveSamples.CustomExceptionEvent\": \"CustomEventR9\"}}}, \"Metering\": {\"GenevaMetering\": {\"Protocol\": \"Etw\",\"MonitoringAccount\": \"O365_Monitoring_Pop\",\"MonitoringNamespace\": \"R9EventCounter\",\"MonitoringNamespaceOverrides\": {\"Microsoft.Office.Datacenter.PassiveMonitoring.Samples.CustomMetric\": \"FakeMetricR9\"}}}}";

        /// <summary>
        /// Configuration of R9 host
        /// </summary>
        private IConfigurationRoot r9Option;

        /// <summary>
        /// Singleton of the host
        /// </summary>
        private IServiceProvider serviceProvider;

        /// <summary>
        /// Lock for instance
        /// </summary>
        private ReaderWriterLockSlim instanceLock = new ReaderWriterLockSlim();

        /// <summary>
        /// Property to access singleton instance
        /// </summary>
        public static LibraryManagedServiceCollection Instance
        {
            get
            {
                if (instance == null)
                {
                    instance = new LibraryManagedServiceCollection();
                    var memoryStream = new MemoryStream(Encoding.ASCII.GetBytes(defaultR9Config));
                    try
                    {
                        IConfigurationRoot tmpOption = new ConfigurationBuilder().AddJsonStream(memoryStream).Build();
                        instance.UpdateInstance(tmpOption);
                    }
                    finally
                    {
                        memoryStream.Dispose();
                    }
                }

                return instance;
            }
        }

        /// <summary>
        /// AcquireR9Option
        /// </summary>
        public IConfigurationRoot AcquireR9Option
        {
            get
            {
                this.instanceLock.EnterReadLock();
                try
                {
                    return this.r9Option;
                }
                finally
                {
                    this.instanceLock.ExitReadLock();
                }
            }
        }

        /// <summary>
        /// GetMeter
        /// </summary>
        /// <typeparam name="T">T</typeparam>
        /// <returns></returns>
        public IMeter<T> GetMeter<T>()
        {
            this.instanceLock.EnterReadLock();
            try
            {
                return serviceProvider.GetRequiredService<IMeter<T>>();
            }
            finally
            {
                this.instanceLock.ExitReadLock();
            }
        }

        /// <summary>
        /// GetLogger
        /// </summary>
        /// <typeparam name="T">T</typeparam>
        /// <returns></returns>
        public ILogger<T> GetLogger<T>()
        {
            this.instanceLock.EnterReadLock();
            try
            {
                return serviceProvider.GetRequiredService<ILoggerFactory>().CreateLogger<T>();
            }
            finally
            {
                this.instanceLock.ExitReadLock();
            }
        }

        /// <summary>
        /// UpdateInstance
        /// </summary>
        /// <param name="option"></param>
        public void UpdateInstance(IConfigurationRoot option)
        {
            this.instanceLock.EnterWriteLock();
            try
            {
                this.serviceProvider = new HostBuilder()
                .ConfigureLogging(builder =>
                {
                    _ = builder
                        .AddOpenTelemetryLogging()
                        .AddGenevaExporter(option.GetSection("Logging:GenevaLogging"));
                })
                .ConfigureServices(services =>
                {
                    _ = services
                        .AddGenevaMetering(option.GetSection("Metering:GenevaMetering"));
                    Enrichment.CommonInit.InitSubstrateEnrichers(services);
                })
                .Build().Services;

                this.r9Option = option;

                SDKLog.Info("Updated the Internal Host Instance.");
            }
            catch (Exception ex)
            {
                SDKLog.Error($"Error when update the LibraryManagedServiceCollection: {ex.Message}");
            }
            finally
            {
                this.instanceLock.ExitWriteLock();
            }
        }
    }
}
