﻿using System;
using System.Collections.Generic;
using System.Diagnostics.CodeAnalysis;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Microsoft.M365.Core.Telemetry.ODL.SecurityTelemetryTcpExporter
{
    [ExcludeFromCodeCoverage]
    public class SecurityTelemetryHeartbeat
    {
        public string ApplicationName { get; set; }

        public string ApplicationId { get; set; }

        public string Ring { get; set; }

        public string SubscriptionId { get; set; }
    }
}
