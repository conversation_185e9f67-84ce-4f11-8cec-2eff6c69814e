﻿// <copyright file="FileExporterOptions.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System;
using Microsoft.R9.Extensions.SecurityTelemetry;

namespace Microsoft.M365.Core.Telemetry.FileExporter
{
    /// <summary>
    /// File logging options.
    /// </summary>
    public class FileExporterOptions : BaseFileLoggerOptions
    {
        /// <summary>
        /// Gets or sets the max queue size.
        /// </summary>
        /// <remarks>Default to 2048.</remarks>
        public int MaxQueueSize { get; set; } = 2048;

        /// <summary>
        /// Gets or sets the scheduled delay.
        /// </summary>
        /// <remarks>Default to 5 seconds.</remarks>
        public int ScheduledDelayMilliseconds { get; set; } = 5000;

        /// <summary>
        /// Gets or sets the exporter timeout.
        /// </summary>
        /// <remarks>Default to 30 seconds.</remarks>
        public int ExporterTimeoutMilliseconds { get; set; } = 30000;

        /// <summary>
        /// Gets or sets the max exporter batch size.
        /// </summary>
        /// <remarks>Default to 512.</remarks>
        public int MaxExportBatchSize { get; set; } = 512;

        /// <summary>
        /// Gets or sets the formatter for <see cref="SecurityRecord"/>.
        /// </summary>
        /// <remarks>Default output format:
        /// SchemaName:SchemaName
        /// StaticPropertyKey1:StaticPropertyValue1
        /// StaticPropertyKey2:StaticPropertyValue2
        /// DynamicPropertyKey1:DynamicPropertyValue1
        /// DynamicPropertyKey2:DynamicPropertyValue2
        /// ...
        /// </remarks>
        public Func<SecurityRecord, string, string> SecurityRecordFormatter { get; set; } = (securityRecord, staticString) => DefaultSecurityRecordFormatter
        .FormatSecurityRecord(securityRecord, staticString);
    }
}
