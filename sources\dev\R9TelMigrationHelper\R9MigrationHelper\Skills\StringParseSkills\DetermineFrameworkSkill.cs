﻿// <copyright file="DetermineFrameworkSkill.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace R9MigrationHelper.Skills.StringParseSkills
{
    /// <summary>
    /// DetermineFrameworkSkill.
    /// </summary>
    public class DetermineFrameworkSkill
    {
        /// <summary>
        /// Determine if service is a NET Core service
        /// </summary>
        /// <param name="targetFramework">service config file content</param>
        public bool IsNetCore(string targetFramework)
        {
            if (string.IsNullOrEmpty(targetFramework))
            {
                throw new Exception("TargetFramework should not be null or empty");
            }
            if (targetFramework.StartsWith("netcoreapp", StringComparison.InvariantCulture))
            {
                return true;
            }
            List<string> netCoreTargetFrameworks = new List<string>() { "net5.0", "net6.0", "net7.0" };
            if (netCoreTargetFrameworks.Contains(targetFramework))
            {
                return true;
            }
            return false;
        }
    }
}
