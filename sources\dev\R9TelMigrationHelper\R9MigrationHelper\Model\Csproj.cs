﻿// <copyright file="Csproj.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Linq;
using R9MigrationHelper.Model;

namespace R9MigrationHelper.Model
{
    /// <summary>
    /// Csproj
    /// </summary>
    public class Csproj
    {
        /// <summary>
        /// Doc
        /// </summary>
        public XDocument Doc { get; set; }

        /// <summary>
        /// Path
        /// </summary>
        public string Path { get; set; }

        /// <summary>
        /// Style
        /// </summary>
        public Style Style { get; set; }

        /// <summary>
        /// Whether xml document needs encoding declaration when save as string
        /// </summary>
        public bool NeedEncoding { get; set; }

        /// <summary>
        /// Public Constructor
        /// </summary>
        /// <param name="doc"></param>
        /// <param name="path"></param>
        /// <param name="needEncoding"></param>
        public Csproj(XDocument doc, string path, bool needEncoding)
        {
            Doc = doc;
            Path = path;
            DetermineStyle();
            NeedEncoding = needEncoding;
        }

        private string ToStringWithDeclaration(XDocument doc)
        {
            if (doc == null)
            {
                throw new ArgumentNullException("doc");
            }

            StringBuilder builder = new StringBuilder();
            using (TextWriter writer = new Utf8Writer(builder))
            {
                doc.Save(writer);
            }
            return builder.ToString();
        }

        /// <summary>
        /// ToString
        /// </summary>
        /// <returns></returns>
        public override string ToString()
        {
            return NeedEncoding ? ToStringWithDeclaration(Doc) : Doc.ToString();
        }

        private void DetermineStyle()
        {
            var root = Doc.Descendants().Where(e => e.Name == "Project");
            if (root.Attributes().Any(a => a.Name == "Sdk"))
            {
                Style = Style.SDKSTYLE;
            }
            else
            {
                Style = Style.NONSDKSTYLE;
            }
        }

        private sealed class Utf8Writer : StringWriter
        {
#pragma warning disable CA1305 // Specify IFormatProvider
            public Utf8Writer(StringBuilder sb) : base(sb)
            {
            }
#pragma warning restore CA1305 // Specify IFormatProvider

            public override Encoding Encoding => Encoding.UTF8;
        }
    }
}

/// <summary>
/// Style
/// </summary>
public enum Style
{
    SDKSTYLE,
    NONSDKSTYLE
}

/// <summary>
/// Csproj for drop
/// </summary>
public class CsprojForDrop : Csproj
{
    /// <summary>
    /// Related references
    /// </summary>
    public List<string> RelatedReferences { get; set; }

    /// <summary>
    /// Refernce node to parent, used for quick adding reference in same parent
    /// </summary>
    public Dictionary<XElement, XElement> ReferenceNodeToParent { get; set; }

    /// <summary>
    /// Whether this csproj use script sharp
    /// </summary>
    public bool UseScriptSharp { get; set; }

    /// <summary>
    /// Public constructor
    /// </summary>
    /// <param name="doc"></param>
    /// <param name="path"></param>
    /// <param name="targetDlls"></param>
    /// <param name="needEncoding"></param>
    public CsprojForDrop(XDocument doc, string path, List<string> targetDlls, bool needEncoding) : base(doc, path, needEncoding)
    {
        RelatedReferences = new List<string>();
        ReferenceNodeToParent = new Dictionary<XElement, XElement>();
        FilterRelatedReferences(targetDlls);
        UseScriptSharp = IsUseScriptSharp();
    }

    private bool IsUseScriptSharp()
    {
        var nodes = Doc.Descendants().Where(e => e.Name.LocalName == "UseScriptSharp").ToList();
        if (nodes.Count == 0)
        {
            return false;
        }
        else
        {
            return Boolean.Parse(nodes.First().Value);
        }
    }

    private void FilterRelatedReferences(List<string> targetDlls)
    {
        var nodesWithInclude = Doc.Descendants().Where(e => e.Attribute("Include") != null).ToList(); // get all nodes contain Include attribute
        foreach (var node in nodesWithInclude)
        {
            var attr = node.Attribute("Include")!.Value;
            switch (node.Name.LocalName)
            {
                case "AssemblyRef" or "WsdlGeneratorDependencies":
                    if (targetDlls.Any(d => attr.EndsWith($"{d}.dll", StringComparison.OrdinalIgnoreCase))) // end with dll
                    {
                        RelatedReferences.Add(attr);
                        ReferenceNodeToParent.Add(node, node.Parent!);
                    }

                    break;
                case "ProjectReference":
                    if (targetDlls.Any(d => attr.EndsWith($"{d}.csproj", StringComparison.OrdinalIgnoreCase))) // end with csproj
                    {
                        RelatedReferences.Add(attr);
                        ReferenceNodeToParent.Add(node, node.Parent!);
                    }
                    break;
                case "Reference" or "PackageReference":
                    if (targetDlls.Any(d => attr.EndsWith(d, StringComparison.OrdinalIgnoreCase))) // end with nothing
                    {
                        RelatedReferences.Add(attr);
                        ReferenceNodeToParent.Add(node, node.Parent!);
                    }
                    break;
                default:
                    break;
            }
        }
    }
}