﻿// <copyright file="Utility.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System;
using System.Globalization;
using System.IO;
#if NETFRAMEWORK
using System.Net;
using System.Web;
#else
using System.Net.Http;
using Microsoft.AspNetCore.Http;
#endif

namespace Microsoft.M365.Core.Telemetry.R9.Tracing.Instrumentation.Accelerator
{
    /// <summary>
    /// Utility
    /// </summary>
    internal class Utility
    {
#if NETFRAMEWORK
        /// <summary>
        /// DeriveHttpUrl
        /// </summary>
        /// <param name="request"></param>
        /// <param name="redactedHttpPath"></param>
        /// <returns></returns>
        internal static string DeriveHttpUrl(HttpWebRequest request, string redactedHttpPath)
        {
            return string.Format(CultureInfo.InvariantCulture, "{0}://{1}:{2}/{3}", request?.RequestUri?.Scheme, request?.RequestUri?.Host, request?.RequestUri?.Port, redactedHttpPath);
        }

        /// <summary>
        /// DeriveHttpUrl
        /// </summary>
        /// <param name="request"></param>
        /// <param name="redactedHttpPath"></param>
        /// <returns></returns>
        internal static string DeriveHttpUrl(HttpRequest request, string redactedHttpPath)
        {
            return string.Format(CultureInfo.InvariantCulture, "{0}://{1}:{2}/{3}", request?.Url.Scheme, request?.Url.Host, request?.Url.Port, redactedHttpPath);
        }
#else
        /// <summary>
        /// DeriveHttpUrl
        /// </summary>
        /// <param name="request"></param>
        /// <param name="redactedHttpPath"></param>
        /// <returns></returns>
        internal static string DeriveHttpUrl(HttpRequest request, string redactedHttpPath)
        {
            return string.Format(CultureInfo.InvariantCulture, "{0}://{1}/{2}", request?.Scheme, request?.Host, redactedHttpPath);
        }

        /// <summary>
        /// DeriveHttpUrl
        /// </summary>
        /// <param name="request"></param>
        /// <param name="redactedHttpPath"></param>
        /// <returns></returns>
        internal static string DeriveHttpUrl(HttpRequestMessage request, string redactedHttpPath)
        {
            return string.Format(CultureInfo.InvariantCulture, "{0}://{1}:{2}/{3}", request?.RequestUri?.Scheme, request?.RequestUri?.Host, request?.RequestUri?.Port, redactedHttpPath);
        }
#endif

        /// <summary>
        /// ExtractDeploymentFromPodName
        /// </summary>
        /// <param name="podName"></param>
        /// <returns></returns>
        // Copied from https://domoreexp.visualstudio.com/R9/_git/SDK?path=/src/Extensions/ClusterMetadata.Cosmic/CosmicClusterMetadataSource.cs&version=GBmain&line=106&lineEnd=136&lineStartColumn=1&lineEndColumn=6&lineStyle=plain&_a=contents
        internal static string ExtractDeploymentFromPodName(string podName)
        {
            string deployment;
            if (string.IsNullOrEmpty(podName))
            {
                deployment = string.Empty;
            }
            else
            {
                var lastDashIndex = podName!.LastIndexOf('-');
                if (lastDashIndex == -1)
                {
                    deployment = podName;
                }
                else
                {
                    var penultimateDashIndex = lastDashIndex > 0
                        ? podName.LastIndexOf('-', lastDashIndex - 1)
                        : -1;

                    if (penultimateDashIndex == -1)
                    {
                        deployment = podName.Substring(0, lastDashIndex);
                    }
                    else
                    {
                        deployment = podName.Substring(0, penultimateDashIndex);
                    }
                }
            }

            return deployment;
        }
    }
}
