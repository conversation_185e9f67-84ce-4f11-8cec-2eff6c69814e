﻿// <copyright file="BlockListTest.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using Microsoft.M365.Core.Telemetry.R9;
using Microsoft.M365.Core.Telemetry.TestCommon;
using Xunit;
using Xunit.Abstractions;

namespace Microsoft.M365.Core.Telemetry.R9Test
{
    /// <summary>
    /// BlockListTest
    /// </summary>
    public class BlockListTest : BaseTest
    {
        private IBlockList blockList;

        /// <summary>
        /// BlockListTest
        /// </summary>
        /// <param name="output"></param>
        public BlockListTest(ITestOutputHelper output) : base(output)
        {
            blockList = new BlockList();
        }

        /// <summary>
        /// TestNewMetricNotBlocked
        /// </summary>
        [Fact]
        public void TestNewMetricNotBlocked()
        {
            Assert.False(blockList.ShouldBlockMetric("Foo", "FooCategory"));
        }

        /// <summary>
        /// TestExistingMetricSameCategoryNotBlocked
        /// </summary>
        [Fact]
        public void TestExistingMetricSameCategoryNotBlocked()
        {
            Assert.False(blockList.ShouldBlockMetric("Foo", "FooCategory"));
            Assert.False(blockList.ShouldBlockMetric("Foo", "FooCategory"));
        }

        /// <summary>
        /// TestExistingMetricDifferentCategoryBlocked
        /// </summary>
        [Fact]
        public void TestExistingMetricDifferentCategoryBlocked()
        {
            Assert.False(blockList.ShouldBlockMetric("Foo", "FooCategory"));
            Assert.True(blockList.ShouldBlockMetric("Foo", "BarCategory"));
        }
    }
}
