﻿// <copyright file="B2PassiveEnricherDimensions.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;

namespace Microsoft.M365.Core.Telemetry.Enrichment
{
    /// <summary>
    /// R9 boilerplate. List of enricher dimension names.
    /// </summary>
    internal class B2PassiveEnricherDimensions
    {
        /// <summary>
        /// DeployRing
        /// </summary>
        public const string DeployRing = "DeployRing";

        /// <summary>
        /// Role
        /// </summary>
        public const string Role = "Role";

        /// <summary>
        /// Forest
        /// </summary>
        public const string Forest = "Forest";

        /// <summary>
        /// Region
        /// </summary>
        public const string Region = "Region";

        /// <summary>
        /// AvailabilityGroup
        /// </summary>
        public const string AvailabilityGroup = "AvailabilityGroup";

        /// <summary>
        /// Machine
        /// </summary>
        public const string Machine = "Machine";

        /// <summary>
        /// BuildVersion dimension name
        /// </summary>
        public const string BuildVersion = "BuildVersion";

        /// <summary>
        /// MachineProvisioningState dimension name
        /// </summary>
        public const string MachineProvisioningState = "MachineProvisioningState";

        /// <summary>
        /// True if the log/metric is issued by R9.
        /// </summary>
        public const string IsR9 = "IsR9";

        /// <summary>
        /// R9 boilerplate.
        /// </summary>
        public static IReadOnlyList<string> DimensionNames { get; } =
            Array.AsReadOnly(
                new[]
                {
                    DeployRing, Role, Forest, Region, AvailabilityGroup, Machine, BuildVersion,
                    MachineProvisioningState, IsR9
                });
    }
}
