﻿// <copyright file="B2PassiveLogEnricher.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using Microsoft.R9.Extensions.Enrichment;

namespace Microsoft.M365.Core.Telemetry.Enrichment
{
    /// <summary>
    /// Log enricher containing substrate B2 common dimensions.
    /// </summary>
    public class B2PassiveLogEnricher : ILogEnricher
    {
        /// <summary>
        /// Add dimension values.
        /// </summary>
        /// <param name="enrichmentBag">Dimension value container.</param>
        public void Enrich(IEnrichmentPropertyBag enrichmentBag)
        {
            if (enrichmentBag == null)
            {
                return;
            }

            /* IMPORTANT NOTE: DON'T change the dimension nor the order
             * Any dimension change will affect the schema of odl export result
             *  and break downstream process.
             * Make sure to inform the PassiveMonitoring(@passmon) team
             *  before editing dimensions.
             */
            enrichmentBag.Add(B2PassiveEnricherDimensions.IsR9, true);
            enrichmentBag.Add("env_cloud_environment", DimensionValues.DeployRing);
            enrichmentBag.Add("env_cloud_role", DimensionValues.Role);
            enrichmentBag.Add("env_cloud_deploymentUnit", DimensionValues.Forest);
            enrichmentBag.Add("env_cloud_location", DimensionValues.Region);
            enrichmentBag.Add("env_cloud_name", DimensionValues.AvailabilityGroup);
            enrichmentBag.Add("env_cloud_roleInstance", DimensionValues.Machine);
            enrichmentBag.Add("env_cloud_roleVer", DimensionValues.BuildVersion);
            enrichmentBag.Add("buildVersion", DimensionValues.BuildVersion);
        }
    }
}
