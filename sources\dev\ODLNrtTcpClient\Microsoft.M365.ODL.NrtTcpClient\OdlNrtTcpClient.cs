﻿// <copyright file="OdlNrtTcpClient.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

namespace Microsoft.M365.ODL.NrtTcpClient
{
    using System;
    using System.Collections.Concurrent;
    using System.Collections.Generic;
    using System.Diagnostics.CodeAnalysis;
    using System.Security.Cryptography.X509Certificates;
    using Microsoft.Office.BigData.DataLoader;

    /// <summary>
    /// TcpClient for ODL TCP NRT pipeline
    /// </summary>
    [ExcludeFromCodeCoverage]
    public sealed class OdlNrtTcpClient : IDisposable
    {
        /// <summary>
        /// default health monitor running millis
        /// </summary>
        public const int DefaultThreadMonitorRunIntervalMillis = 30000;

        /// <summary>
        /// logger
        /// </summary>
        private IOdlLogger logger = OdlLogger.Instance;

        /// <summary>
        /// The local host server IP
        /// </summary>
        public const string Localhost = @"127.0.0.1";

        /// <summary>
        /// list of threads for async data sending
        /// </summary>
        private List<NrtTcpClientThread> asyncSendThreads;

        /// <summary>
        /// The sender for sync sending
        /// </summary>
        private NrtTcpClientThread syncSender;

        /// <summary>
        /// the message queue for async sending
        /// </summary>
        private BlockingCollection<KeyValuePair<ODLNRTRequest, Dictionary<string, long>>> messageQueue;

        /// <summary>
        /// the data sending processors chain for before/post processing during data sending
        /// </summary>
        private ConcurrentQueue<ISendDataProcessor> sendDataProcessors = new ConcurrentQueue<ISendDataProcessor>();

        /// <summary>
        /// the monitor for TCP connection health of NrtTcpClientThread
        /// </summary>
        private NrtTcpClientMonitor nrtTcpClientMonitor;

        /// <summary>
        /// The client cert collection used for tls authentication if needed
        /// </summary>
        public X509Certificate2Collection CertCollection
        {
            get;
            private set;
        }
        = null;

        /// <summary>
        /// The server side ca certificate used for tls authentication validation if needed
        /// </summary>
        public X509Certificate2 CaCertificate
        {
            get;
            private set;
        }
        = null;

        /// <summary>
        /// tlsOptions for m-Tls auth
        /// </summary>
        public TlsOptions TlsOptions
        {
            get;
            private set;
        }
        = new TlsOptions();

        /// <summary>
        /// whether using sync sending with a single sending thread,default to true
        /// </summary>
        public bool IsSyncSend
        {
            get;
            private set;
        }
        = true;

        /// <summary>
        /// NRT Server IP,default is localhost
        /// </summary>
        public string NRTServerIP
        {
            get;
            private set;
        }
        = Localhost;

        /// <summary>
        /// ODL TCP Port
        /// </summary>
        public int Port
        {
            get;
            private set;
        }

        /// <summary>
        /// the number of threads for async data sending
        /// </summary>
        public int ThreadNum
        {
            get;
            private set;
        }

        /// <summary>
        /// the size of in-memory async queue
        /// </summary>
        public int QueueSize
        {
            get;
            private set;
        }

        /// <summary>
        /// sending timeout in milli sec
        /// </summary>
        public int SendTimeoutMilliSec
        {
            get;
            private set;
        }

        /// <summary>
        /// TCP connect timeout in milli sec
        /// </summary>
        public int ConnectTimeoutMilliSec
        {
            get;
            private set;
        }

        /// <summary>
        /// healthy monitor run interval in millis
        /// </summary>
        public int HealthMonitorRunInterval
        {
            get;
            private set;
        }

        /// <summary>
        /// EnableCleanUpArchivedFiles
        /// </summary>
        public bool EnableCleanUpArchivedFiles { get; private set; }

        /// <summary>
        /// RetentionInterval
        /// </summary>
        public TimeSpan RetentionInterval { get; private set; }

        /// <summary>
        /// RetentionCount
        /// </summary>
        public long RetentionCount { get; private set; }

        /// <summary>
        /// The ctor with sync sending as default
        /// </summary>
        /// <param name="ip">the server ip</param>
        /// <param name="port">the tcp port</param>
        /// <param name="sendTimeoutMilliSec">send timeout</param>
        /// <param name="connectTimeoutMilliSec">tcp connect timeout</param>
        /// <param name="monitorRunInterval">health monitor run interval millis</param>
        /// <param name="tlsOptions">tls auth related settings</param>
        public OdlNrtTcpClient(int port, int sendTimeoutMilliSec, int monitorRunInterval, int connectTimeoutMilliSec
             = 0, string ip = Localhost, TlsOptions tlsOptions = null) : this(ip, port, true, -1, 0, sendTimeoutMilliSec, connectTimeoutMilliSec, monitorRunInterval, tlsOptions)
        {
        }

        /// <summary>
        /// The ctor with default ip
        /// </summary>
        /// <param name="port">the tcp port</param>
        /// <param name="threadNum">the thread number for async sending data</param>
        /// <param name="isSyncSend">is sync send</param>
        /// <param name="queueSize">the async queue size</param>
        /// <param name="sendTimeoutMilliSec">timeout millis sec</param>
        /// <param name="connectTimeoutMilliSec">timeout millis sec</param>
        /// <param name="monitorRunInterval">health monitor run interval millis</param>
        public OdlNrtTcpClient(int port, bool isSyncSend, int threadNum, int queueSize, int sendTimeoutMilliSec, int connectTimeoutMilliSec
             = 0,
            int monitorRunInterval = DefaultThreadMonitorRunIntervalMillis) : this(Localhost, port, isSyncSend, threadNum, queueSize, sendTimeoutMilliSec, connectTimeoutMilliSec, monitorRunInterval)
        {
        }

        /// <summary>
        /// The ctor with full params
        /// </summary>
        /// <param name="ip"></param>
        /// <param name="port"></param>
        /// <param name="isSyncSend"></param>
        /// <param name="threadNum"></param>
        /// <param name="queueSize"></param>
        /// <param name="sendTimeoutMilliSec"></param>
        /// <param name="connectTimeoutMilliSec"></param>
        /// <param name="monitorRunInterval"></param>
        /// <param name="tlsOptions"></param>
        public OdlNrtTcpClient(string ip, int port, bool isSyncSend, int threadNum, int queueSize, int sendTimeoutMilliSec, int connectTimeoutMilliSec = 0,
            int monitorRunInterval = DefaultThreadMonitorRunIntervalMillis, TlsOptions tlsOptions = null)
        {
            NRTServerIP = ip;
            Port = port;
            ThreadNum = threadNum;
            IsSyncSend = isSyncSend;
            QueueSize = queueSize;
            SendTimeoutMilliSec = sendTimeoutMilliSec;
            ConnectTimeoutMilliSec = connectTimeoutMilliSec == 0 ? sendTimeoutMilliSec : connectTimeoutMilliSec;
            HealthMonitorRunInterval = monitorRunInterval;
            if (tlsOptions != null)
            {
                TlsOptions = tlsOptions;
            }            

            if (IsSyncSend)
            {
                InitSyncSend();
            }
            else
            {
                RefreshThreadPool();
            }
        }

        /// <summary>
        /// Load cert for tls authentication
        /// </summary>
        private void InitTlsCertForAuthentication()
        {
            if (TlsOptions == null || !TlsOptions.EnableTlsAuth)
            {
                return;
            }

            try
            {
                var certCollection = CertUtils.GetTlsCertificates(TlsOptions.TlsCertsFolderPath, TlsOptions.TlsPfxPassword, TlsOptions.TlsCertFilePath, TlsOptions.TlsCertSubjectName, TlsOptions.StoreLocation, TlsOptions.StoreName);
                
                if (certCollection != null)
                {
                    CertCollection = certCollection;
                }

                if (!string.IsNullOrEmpty(TlsOptions.TlsCACertFilePath) || !string.IsNullOrEmpty(TlsOptions.TlsCertsFolderPath))
                {
                    var caCertificate = CertUtils.GetCACertificate(TlsOptions.TlsCACertFilePath, TlsOptions.TlsCertsFolderPath);
                    if (caCertificate != null)
                    {
                        CaCertificate = caCertificate;
                    }
                }                
            }
            catch (Exception e)
            {
                logger.Log(LogLevel.Error, LogEventId.CertManagementError, $"failed to load tls cert suite for authentication: {e}");
            }
        }

        /// <summary>
        /// Init sync send
        /// </summary>
        private void InitSyncSend()
        {
            try
            {
                // sync sending is executed on the working thread instead of a dedicated thread
                InitTlsCertForAuthentication();

                syncSender = new NrtTcpClientThread(ip: NRTServerIP, port: Port, timeoutMilliSeconds: SendTimeoutMilliSec, enableTlsAuth: TlsOptions.EnableTlsAuth, x509Certificate2s: CertCollection, caCertificate: CaCertificate, isNPE: TlsOptions.IsNPE, tlsCertWhiteList: TlsOptions.TlsCertWhiteList);
                syncSender.SetSendDataProcessors(this.sendDataProcessors);

                nrtTcpClientMonitor = new NrtTcpClientMonitor(new List<NrtTcpClientThread>() { syncSender }, runIntervalMilliSec: HealthMonitorRunInterval, timeoutForLiveness: ConnectTimeoutMilliSec, isCosmic: TlsOptions.IsCosmic);
                nrtTcpClientMonitor.StartWorkAfterDelay(HealthMonitorRunInterval);
            }
            catch (Exception ex)
            {
                logger.Log(LogLevel.Error, LogEventId.TcpClientCommonError, $"Error when init sync send: {ex}");
            }
        }

        /// <summary>
        /// Refresh the sending thread pool for async sending
        /// </summary>
        private void RefreshThreadPool()
        {
            if (IsSyncSend)
            {
                return;
            }

            InitTlsCertForAuthentication();
            asyncSendThreads = new List<NrtTcpClientThread>(ThreadNum);
            messageQueue = new BlockingCollection<KeyValuePair<ODLNRTRequest, Dictionary<string, long>>>(QueueSize);

            try
            {
                for (int i = 0; i < ThreadNum; i++)
                {
                    NrtTcpClientThread newThread = new NrtTcpClientThread(ip: NRTServerIP, port: Port, this.messageQueue, enableTlsAuth: TlsOptions.EnableTlsAuth, timeoutMilliSeconds: SendTimeoutMilliSec, x509Certificate2s: CertCollection, caCertificate: CaCertificate, isNPE: TlsOptions.IsNPE, tlsCertWhiteList: TlsOptions.TlsCertWhiteList);
                    asyncSendThreads.Add(newThread);
                    newThread.SetSendDataProcessors(this.sendDataProcessors);

                    newThread.StartThread();
                }

                nrtTcpClientMonitor = new NrtTcpClientMonitor(asyncSendThreads, runIntervalMilliSec: HealthMonitorRunInterval, timeoutForLiveness: ConnectTimeoutMilliSec, isCosmic: TlsOptions.IsCosmic);
                nrtTcpClientMonitor.StartWorkAfterDelay(HealthMonitorRunInterval);
            }
            catch (Exception ex)
            {
                logger.Log(LogLevel.Error, LogEventId.TcpClientCommonError, $"Error when init async send: {ex}");
            }
        }

        /// <summary>
        /// Set the read/write buffer size for data sending
        /// </summary>
        /// <param name="bufferSize">the buffer size</param>
        public void SetBufferSize(int bufferSize)
        {
            if (IsSyncSend)
            {
                this.syncSender.BufferSize = bufferSize;
            }
            else
            {
                foreach (var thread in asyncSendThreads)
                {
                    thread.BufferSize = bufferSize;
                }
            }
        }

        /// <summary>
        /// Sets a value indicating whether to enable archived files clean up or not.
        /// </summary>
        /// <param name="enableCleanUpArchivedFiles"></param>
        public void SetEnableCleanUpArchivedFiles(bool enableCleanUpArchivedFiles)
        {
            this.EnableCleanUpArchivedFiles = enableCleanUpArchivedFiles;

            if (IsSyncSend)
            {
                this.syncSender.EnableCleanUpArchivedFiles = enableCleanUpArchivedFiles;
            }
            else
            {
                foreach (var thread in asyncSendThreads)
                {
                    thread.EnableCleanUpArchivedFiles = enableCleanUpArchivedFiles;
                }
            }
        }

        /// <summary>
        /// sets the max retention <see cref="TimeSpan"/>.
        /// </summary>
        /// <param name="retentionInterval"></param>
        public void SetRetentionInterval(TimeSpan retentionInterval)
        {
            this.RetentionInterval = retentionInterval;

            if (IsSyncSend)
            {
                this.syncSender.RetentionInterval = retentionInterval;
            }
            else
            {
                foreach (var thread in asyncSendThreads)
                {
                    thread.RetentionInterval = retentionInterval;
                }
            }
        }

        /// <summary>
        /// Gets or sets the max retention count.
        /// </summary>
        /// <param name="retentionCount"></param>
        public void SetRetentionCount(long retentionCount)
        {
            this.RetentionCount = retentionCount;

            if (IsSyncSend)
            {
                this.syncSender.RetentionCount = retentionCount;
            }
            else
            {
                foreach (var thread in asyncSendThreads)
                {
                    thread.RetentionCount = retentionCount;
                }
            }
        }

        /// <summary>
        /// Set BatchSizeForSendingMetric
        /// </summary>
        /// <param name="batchSize"></param>
        public void SetBatchSizeForSendingMetric(int batchSize)
        {
            if (IsSyncSend)
            {
                this.syncSender.MaxRecordCountForStatic = batchSize;
            }
            else
            {
                foreach (var thread in asyncSendThreads)
                {
                    thread.MaxRecordCountForStatic = batchSize;
                }
            }
        }

        /// <summary>
        /// Interval for sending metric
        /// </summary>
        /// <param name="sendStatisticsInterval"></param>
        public void SetSendStatisticsInterval(TimeSpan sendStatisticsInterval)
        {
            if (IsSyncSend)
            {
                this.syncSender.SendStatisticsInterval = sendStatisticsInterval;
            }
            else
            {
                foreach (var thread in asyncSendThreads)
                {
                    thread.SendStatisticsInterval = sendStatisticsInterval;
                }
            }
        }

        /// <summary>
        /// Send data in sync/async mode, thread safe
        /// </summary>
        /// <param name="data">the data to be sent</param>
        /// <param name="schemaToMessages">buffer used when falling back-to disk</param>
        /// <param name="countPerSdksource">log count per sdksouce</param>
        public void Send(ODLNRTRequest data, Dictionary<string, List<string>> schemaToMessages = null, Dictionary<string, long> countPerSdksource = null)
        {
            if (IsSyncSend)
            {
                DoSyncSend(data, schemaToMessages, countPerSdksource);
            }   
            else
            {
                var defaultSendThread = this.asyncSendThreads[0];
                if (!TryEnqueueWithTimeout(this.messageQueue, data, SendTimeoutMilliSec, defaultSendThread, countPerSdksource))
                {
                    NrtTcpClientThread.FallbackToDisk(data, defaultSendThread?.FallbackRecordCount, this.EnableCleanUpArchivedFiles, this.RetentionInterval, this.RetentionCount, schemaToMessages, countPerSdksource);
                }
            }
        }

        /// <summary>
        /// Register DataSendProcessor
        /// </summary>
        /// <param name="sendDataProcessor">the data processor</param>
        public void RegisterDataSendProcessor(ISendDataProcessor sendDataProcessor)
        {
            if (sendDataProcessor != null)
            {
                this.sendDataProcessors.Enqueue(sendDataProcessor);
            }
        }

        /// <summary>
        /// method for sync send
        /// </summary>
        /// <param name="data">the data</param>
        /// <param name="schemaToMessages">buffer used for fallback</param>
        private void DoSyncSend(ODLNRTRequest data, Dictionary<string, List<string>> schemaToMessages, Dictionary<string, long> countPerSdksource)
        {
            NrtTcpClientThread.CountRecordAndAggregate(data, syncSender.TotalRecordCount, countPerSdksource);

            if (!syncSender.IsAlive)
            {
                NrtTcpClientThread.FallbackToDisk(data, syncSender.FallbackRecordCount, this.EnableCleanUpArchivedFiles, this.RetentionInterval, this.RetentionCount, schemaToMessages, countPerSdksource);
            }
            else
            {
                bool succ = false;
                try
                {
                    succ = syncSender.SendData(data);
                }
                catch (Exception e)
                {
                    logger.Log(LogLevel.Error, LogEventId.TcpClientCommonError, $"Error when sync sending data:{e}");
                    succ = false;
                }

                if (!succ)
                {
                    NrtTcpClientThread.FallbackToDisk(data, syncSender.FallbackRecordCount, this.EnableCleanUpArchivedFiles, this.RetentionInterval, this.RetentionCount, schemaToMessages, countPerSdksource);
                }
            }
        }

        /// <summary>
        /// try to enqueue in timeout manner
        /// </summary>
        /// <param name="collection">the queue</param>
        /// <param name="item">the data to be sent</param>
        /// <param name="timeoutMilliseconds">the enqueue timeout</param>
        /// <param name="defaultSendThread">one of the threads as default</param>
        /// <returns>true if enqueue success</returns>
        private bool TryEnqueueWithTimeout(BlockingCollection<KeyValuePair<ODLNRTRequest, Dictionary<string, long>>> collection, ODLNRTRequest item, int timeoutMilliseconds, NrtTcpClientThread defaultSendThread, Dictionary<string, long> nrtMessageCount)
        {
            try
            {
                NrtTcpClientThread.CountRecordAndAggregate(item, defaultSendThread.TotalRecordCount, nrtMessageCount);
                var tmpNRTMessageCountKvp = new KeyValuePair<ODLNRTRequest, Dictionary<string, long>>(item, nrtMessageCount);
                return collection.TryAdd(tmpNRTMessageCountKvp, timeoutMilliseconds);
            }
            catch (Exception e)
            {
                this.logger.Log(LogLevel.Error, LogEventId.TcpClientCommonError, $"Error when enqueue data:{e}");
                return false;
            }
        }

        /// <summary>
        /// Dispose and release resources
        /// </summary>
        public void Dispose()
        {
            nrtTcpClientMonitor?.Dispose();
            nrtTcpClientMonitor = null;

            if (asyncSendThreads != null)
            {
                foreach (NrtTcpClientThread thread in asyncSendThreads)
                {
                    thread?.Dispose();
                }
            }

            asyncSendThreads = null;

            syncSender?.Dispose();
            syncSender = null;
            messageQueue = null;
        }
    }
}
