﻿// <copyright file="DummyConfiguration.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System.Collections.Generic;
using Microsoft.Extensions.Configuration;

namespace R9.Logging.SubstrateTests
{
    // A dummpy provider only for triggering the reload event
    public class DummyConfigurationProvider : ConfigurationProvider
    {
        public DummyConfigurationProvider()
        {
            Load();
        }

        public override void Load()
        {
            Data = new Dictionary<string, string?>() { ["dummy"] = "dummy" };
        }

        public void Update()
        {
            Load();
            OnReload();
        }
    }

    // A dummpy source only for triggering the reload event
    public class DummyConfigurationSource : IConfigurationSource
    {
        public DummyConfigurationSource()
        {
        }

        public IConfigurationProvider Build(IConfigurationBuilder builder)
        {
            return new DummyConfigurationProvider();
        }
    }
}
