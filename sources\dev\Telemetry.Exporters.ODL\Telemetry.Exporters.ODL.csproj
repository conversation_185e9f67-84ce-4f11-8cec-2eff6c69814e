﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <AssemblyName>Microsoft.M365.Core.Telemetry.Exporters.ODL</AssemblyName>
    <RootNamespace>$(AssemblyName)</RootNamespace>
    <TargetFrameworks>net472;net6.0;net8.0</TargetFrameworks>
    <Description>ODLNRT logging exporter.</Description>
    <Workstream>Telemetry</Workstream>
    <Category>Telemetry</Category>
    <UseR9Generators>true</UseR9Generators>
    <GeneratePackageOnBuild>true</GeneratePackageOnBuild>
    <PlatformTarget>anycpu</PlatformTarget>
    <PackageVersion>$(R9TelemetryExtensionsCorePackageVersion)</PackageVersion>
    <PackageReleaseNotes>$(R9TelemetryExtensionsCoreReleaseNotes)</PackageReleaseNotes>
    <Nowarn>CA1305,CA1819,CA1801</Nowarn>
		<LangVersion>9</LangVersion>
	</PropertyGroup>

  <PropertyGroup>
    <!-- See: https://aka.ms/r9-package-staging -->
    <Stage>dev</Stage>

    <MinCodeCoverage>100</MinCodeCoverage>
    <MinMutationScore>95</MinMutationScore>
    <PerformanceTests>no</PerformanceTests>
    <IntegrationTests>no</IntegrationTests>
    <EndToEndTests>no</EndToEndTests>

    <ConceptualDocUid>missing-uid</ConceptualDocUid>
    <SampleDocUid>missing-uid</SampleDocUid>
    <HowToDocUid>missing-uid</HowToDocUid>
    <CaseStudyDocUid>missing-uid</CaseStudyDocUid>
    <ChronicleInsights>n/a</ChronicleInsights>
    <DocsHavePrereleaseNote>n/a</DocsHavePrereleaseNote>

    <LoggingIsPresent>n/a</LoggingIsPresent>
    <MeteringIsPresent>n/a</MeteringIsPresent>
    <TracingIsPresent>n/a</TracingIsPresent>
    <ConfigIsInjectable>yes</ConfigIsInjectable>
    <ConfigIsValidated>yes</ConfigIsValidated>

    <UsedInProduction>yes</UsedInProduction>
    <ApiApproval>disapproved</ApiApproval>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.Extensions.Logging" />
    <PackageReference Include="Microsoft.R9.Extensions.Essentials" />
    <PackageReference Include="Microsoft.R9.Extensions.Logging" />
    <PackageReference Include="Microsoft.R9.Extensions.Tracing" />
    <PackageReference Include="Newtonsoft.Json" />
    <PackageReference Include="System.ServiceProcess.ServiceController" />
  </ItemGroup>

  <ItemGroup>
    <InternalsVisibleToTest Include="$(AssemblyName).Test" />
  </ItemGroup>

</Project>