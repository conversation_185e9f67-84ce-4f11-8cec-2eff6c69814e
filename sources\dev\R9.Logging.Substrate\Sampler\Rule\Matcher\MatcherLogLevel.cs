// <copyright file="MatcherLogLevel.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;

namespace Microsoft.M365.Core.Telemetry.R9.Logging.Substrate.Sampler
{
    /// <summary>
    /// LogLevel related methods for matcher.
    /// <see cref="TryMatch{TState}(in Extensions.Logging.Abstractions.LogEntry{TState})"/> for matching logic.
    /// </summary>
    internal partial class Matcher
    {
        private static bool CompareLogLevels(LogLevel logValue, string fieldValue, Func<LogLevel, LogLevel, bool> compareFunc)
        {
            if (!Enum.TryParse(fieldValue, true, out LogLevel constraintValue))
            {
                return false;
            }
            return compareFunc(logValue, constraintValue);
        }

        private static MatchFunc GenerateLogLevelMatchFunc(Constraint constraint)
        {
            var fieldValue = constraint.Value;
            var ruleOperator = constraint.RuleOperator;

            return ruleOperator switch
            {
                OperatorType.GreaterThan => (logLevel, _, _) =>
                    CompareLogLevels(logLevel, fieldValue, (l, p) => l > p),

                OperatorType.GreaterThanOrEqual => (logLevel, _, _) =>
                    CompareLogLevels(logLevel, fieldValue, (l, p) => l >= p),

                OperatorType.LessThan => (logLevel, _, _) =>
                    CompareLogLevels(logLevel, fieldValue, (l, p) => l < p),

                OperatorType.LessThanOrEqual => (logLevel, _, _) =>
                    CompareLogLevels(logLevel, fieldValue, (l, p) => l <= p),

                OperatorType.NumericEquals => (logLevel, _, _) =>
                    CompareLogLevels(logLevel, fieldValue, (l, p) => l == p),

                OperatorType.NumericNotEquals => (logLevel, _, _) =>
                    CompareLogLevels(logLevel, fieldValue, (l, p) => l != p),

                _ => throw new NotImplementedException($"Unsupported operator type: {ruleOperator}") // This should never happen, we should have validated the constraints before.
            };
        }
    }
}
