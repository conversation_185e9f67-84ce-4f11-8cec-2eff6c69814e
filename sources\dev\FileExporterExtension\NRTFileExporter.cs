﻿// <copyright file="NRTFileExporter.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using Microsoft.R9.Extensions.SecurityTelemetry;

namespace Microsoft.M365.Core.Telemetry.FileExporter
{
    /// <summary>
    /// Implements processor that batches telemetry objects before calling exporter.
    /// </summary>
    public sealed class NRTFileExporter : IDisposable
    {
        private readonly TextFileLogger fileLogger;

        /// <summary>
        /// Initializes a new instance of the <see cref="NRTFileExporter"/> class.
        /// </summary>
        /// <param name="fileLogger"><see cref="TextFileLogger"/> </param>
        internal NRTFileExporter(TextFileLogger fileLogger)
        {
            this.fileLogger = fileLogger ?? throw new ArgumentNullException(nameof(fileLogger));
        }

        /// <summary>
        /// Use injected logger to write logs to file.
        /// </summary>
        /// <param name="batch">Batch of log records.</param>
        public void BatchExport(in Batch<string> batch)
        {
            fileLogger.Log(in batch);
        }

        /// <summary>
        /// Use injected logger to write logs to file.
        /// </summary>
        /// <param name="records">List<string> of log records.</param>
        public void BatchExport(List<string> records)
        {
            fileLogger.Log(records);
        }

        /// <summary>
        /// Export single line log
        /// </summary>
        /// <param name="logline"></param>
        public void Export(string logline)
        {
            fileLogger.Log(logline);
        }

        /// <summary>
        /// Releases the unmanaged resources used by this class and optionally
        /// releases the managed resources.
        /// </summary>
        public void Dispose() => fileLogger.Dispose();
    }
}
