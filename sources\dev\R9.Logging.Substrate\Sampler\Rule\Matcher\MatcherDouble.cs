// <copyright file="MatcherDouble.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Microsoft.M365.Core.Telemetry.R9.Logging.Substrate.Sampler
{
    /// <summary>
    /// Double related methods for matcher.
    /// <see cref="TryMatch{TState}(in Extensions.Logging.Abstractions.LogEntry{TState})"/> for matching logic.
    /// </summary>
    internal partial class Matcher
    {
        private const double DoubleEpsilon = 1e-10;

        private static bool CompareDoubleValues(IReadOnlyList<KeyValuePair<string, object?>> state, string fieldName, string fieldValue, Func<double, double, bool> compareFunc)
        {
            var fieldKVP = state.FirstOrDefault(kv => kv.Key == fieldName);
            if (fieldKVP.Value == null)
            {
                return false;
            }

            var valueStr = fieldKVP.Value.ToString();
            if (valueStr == null)
            {
                return false;
            }

            if (!double.TryParse(valueStr, System.Globalization.NumberStyles.Any, System.Globalization.CultureInfo.InvariantCulture, out double logValue) ||
                !double.TryParse(fieldValue, System.Globalization.NumberStyles.Any, System.Globalization.CultureInfo.InvariantCulture, out double constraintValue))
            {
                return false;
            }

            return compareFunc(logValue, constraintValue);
        }

        private static MatchFunc GenerateDoubleMatchFunc(Constraint constraint)
        {
            var fieldName = constraint.Field;
            var fieldValue = constraint.Value;

            return constraint.RuleOperator switch
            {
                OperatorType.GreaterThan => (_, _, state) =>
                    CompareDoubleValues(state, fieldName, fieldValue, (s, c) => s - c > DoubleEpsilon),

                OperatorType.GreaterThanOrEqual => (_, _, state) =>
                    CompareDoubleValues(state, fieldName, fieldValue, (s, c) => s - c >= -DoubleEpsilon),

                OperatorType.LessThanOrEqual => (_, _, state) =>
                    CompareDoubleValues(state, fieldName, fieldValue, (s, c) => s - c <= DoubleEpsilon),
                    
                OperatorType.LessThan => (_, _, state) =>
                    CompareDoubleValues(state, fieldName, fieldValue, (s, c) => s - c < -DoubleEpsilon),

                OperatorType.NumericEquals => (_, _, state) =>
                    CompareDoubleValues(state, fieldName, fieldValue, (s, c) => Math.Abs(s - c) <= DoubleEpsilon),

                OperatorType.NumericNotEquals => (_, _, state) =>
                    CompareDoubleValues(state, fieldName, fieldValue, (s, c) => Math.Abs(s - c) > DoubleEpsilon),

                _ => throw new NotImplementedException($"Unsupported operator type: {constraint.RuleOperator}") // This should never happen, we should have validated the constraints before.
            };
        }
    }
}
