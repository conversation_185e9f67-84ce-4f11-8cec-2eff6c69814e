﻿// <copyright file="NRTMessageSerilizer.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using Google.Protobuf;
using Microsoft.Extensions.Logging;
using Microsoft.Office.BigData.DataLoader;

namespace Microsoft.M365.ODL.NrtTcpClient
{
    /// <summary>
    /// The serilizer for nrt messages
    /// </summary>
    public class NRTMessageSerilizer
    {
        private static IOdlLogger logger = OdlLogger.Instance;

        /// <summary>
        /// The serilization method for nrt related messages, user can try to reuse outputBuffer for memory saving
        /// </summary>
        /// <param name="nrtMessage">message to be serilized</param>
        /// <param name="outputBuffer">the reuseable buffer, if provided will use this to perform serilization</param>
        /// <param name="resultBuffer">the result buffer</param>
        /// <returns>the size of the serilization bytes</returns>
        public static int Serilize(ODLNRTRequest nrtMessage, byte[] outputBuffer, out byte[] resultBuffer)
        {
            resultBuffer = outputBuffer;

            if (null == outputBuffer || outputBuffer.Length == 0)
            {
                resultBuffer = nrtMessage.ToByteArray();
                return resultBuffer.Length;
            }

            try
            {
                using (CodedOutputStream output = new CodedOutputStream(outputBuffer))
                {
                    nrtMessage.WriteTo(output);
                    output.Flush();

                    return (int)output.Position;
                }
            }
            catch (Exception e)
            {
                logger.Log(LogLevel.Error, LogEventId.MessageSerilizerError, $"Error when performing serilization for {nrtMessage}:{e}");

                // the serilized bytes are larger than the size of the buffer
                resultBuffer = nrtMessage.ToByteArray();
                return resultBuffer.Length;
            }
        }
    }
}
