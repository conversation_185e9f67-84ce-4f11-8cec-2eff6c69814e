﻿// <copyright file="HttpTraceEUIIRedactEnricherTest.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

#if !NETFRAMEWORK
using System.Diagnostics;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.R9.Extensions.Tracing.Http;
using Xunit;

namespace Microsoft.M365.Core.Telemetry.R9.Tracing.Instrumentation.Accelerator.Tests
{
    /// <summary>
    /// HttpTraceEUIIRedactEnricherTest
    /// </summary>
    public class HttpTraceEUIIRedactEnricherTest
    {
        private IHttpTraceEnricher enricher;

        /// <summary>
        /// HttpTraceEUIIRedactEnricherTest
        /// </summary>
        public HttpTraceEUIIRedactEnricherTest()
        {
            var container = new ServiceCollection();
            var options = new HttpTracingOptionsInherited
            {
                IsEnabled = true,
                RedactionStrategyType = RedactionStrategyType.Default
            };

            container.AddSingleton(options);
            container.AddSingleton<IHttpTraceEnricher, HttpTraceEUIIRedactEnricher>();

            var provider = container.BuildServiceProvider();
            this.enricher = provider.GetService<IHttpTraceEnricher>();
        }

        /// <summary>
        /// EnrichFailWithNullInput
        /// </summary>
        [Fact]
        public void EnrichFailWithNullInput()
        {
            Assert.NotNull(this.enricher);

            var activity = new Activity("test");
            enricher.Enrich(activity, null);

            Assert.Null(activity.GetTagItem(Constants.HttpUrlBackup));
        }

        /// <summary>
        /// EnrichSucceed
        /// </summary>
        [Fact]
        public void EnrichSucceed()
        {
            var activity = new Activity("test");
            var httpContext = new DefaultHttpContext();
            httpContext.Request.Method = "GET";
            httpContext.Request.Path = "/api/values";
            httpContext.Request.Host = new HostString("localhost", 8080);
            httpContext.Request.Scheme = "https";
            httpContext.Request.QueryString = new QueryString("?name=foo");
            enricher.Enrich(activity, httpContext.Request);

            Assert.Equal("https://localhost:8080/" + Constants.RedactedPlacholder, activity.GetTagItem(Constants.HttpUrlBackup));
            Assert.Equal($"/{Constants.RedactedPlacholder}", activity.DisplayName);
            Assert.Equal($"/{Constants.RedactedPlacholder}", activity.GetTagItem(Constants.HttpRoute));
            Assert.Equal($"/{Constants.RedactedPlacholder}", activity.GetTagItem(Constants.HttpPath));
        }

        /// <summary>
        /// EnrichSucceedWithNoPathAndQuery
        /// </summary>
        [Fact]
        public void EnrichSucceedWithNoPathAndQuery()
        {
            var activity = new Activity("test");
            var httpContext = new DefaultHttpContext();
            httpContext.Request.Method = "GET";
            httpContext.Request.Host = new HostString("localhost", 8080);
            httpContext.Request.Scheme = "https";
            enricher.Enrich(activity, httpContext.Request);

            Assert.Equal("https://localhost:8080/" + Constants.RedactedPlacholder, activity.GetTagItem(Constants.HttpUrlBackup));
            Assert.Equal("/" + Constants.RedactedPlacholder, activity.GetTagItem(Constants.HttpRoute));
            Assert.Equal("/" + Constants.RedactedPlacholder, activity.GetTagItem(Constants.HttpPath));

            enricher.Enrich(null, httpContext.Request);
        }
    }
}
#endif