﻿// <copyright file="StringConstraintTest.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>
using System;
using System.Collections.Generic;
using System.IO;
using System.Text;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.M365.Core.Telemetry.R9.Logging.Substrate.Sampler;
using OpenTelemetry.Logs;
using Xunit;

namespace Microsoft.M365.Core.Telemetry.R9.Logging.Substrate.Test.SamplingTest
{
    public class StringConstraintTest
    {
        private const string LoggerName = "StringConstraintTest";

        private const string StringConstraintsConfig = @"
{
    ""SubstrateLogging"": {
        ""RuleBasedSampler"": {
            ""StringConstraintTest"": [
                {
                    ""Constraints"": [
                        {
                            ""Field"": ""FieldPlaceholder"",
                            ""Type"": ""TypePlaceholder"",
                            ""Operator"": ""OperatorPlaceholder"",
                            ""Value"": ""ValuePlaceholder""
                        }
                    ],
                    ""Strategy"": {
                        ""Type"": ""Random"",
                        ""SampleRate"": 1.0
                    }
                },
                {
                    ""Constraints"": [],
                    ""Strategy"": {
                        ""Type"": ""Random"",
                        ""SampleRate"": 0.0
                    }
                }
            ]
        }
    }
}";

        private static string ReplaceConfigPlaceholders(string field, string type, string op, string value)
        {
            return StringConstraintsConfig
                .Replace("FieldPlaceholder", field)
                .Replace("TypePlaceholder", type)
                .Replace("OperatorPlaceholder", op)
                .Replace("ValuePlaceholder", value);
        }

        private static void GenerateLog(ILogger logger, string fieldName, string fieldValue)
        {
            logger.LogInformation($"Test message {{{fieldName}}}", fieldValue);
        }   

        [Fact]
        public void StringEquals_ShouldMatchAndSample()
        {
            var config = ReplaceConfigPlaceholders("TestField", ConstraintType.String, OperatorType.StringEquals, "TestValue");
            var exportedItems = new List<LogRecord>();
            var logger = LoggerTestUtils.CreateLoggerWithConfig(config, exportedItems, LoggerName);

            GenerateLog(logger, "TestField", "TestValue");

            Assert.Single(exportedItems);
        }

        [Fact]
        public void StringNotEquals_ShouldMatchAndSample()
        {
            var config = ReplaceConfigPlaceholders("TestField", ConstraintType.String, OperatorType.StringNotEquals, "InvalidTestValue");
            var exportedItems = new List<LogRecord>();
            var logger = LoggerTestUtils.CreateLoggerWithConfig(config, exportedItems, LoggerName);

            GenerateLog(logger, "TestField", "TestValue");

            Assert.Single(exportedItems);
        }

        [Fact]
        public void StartsWith_ShouldMatchAndSample()
        {
            var config = ReplaceConfigPlaceholders("TestField", ConstraintType.String, OperatorType.StartsWith, "Test");
            var exportedItems = new List<LogRecord>();
            var logger = LoggerTestUtils.CreateLoggerWithConfig(config, exportedItems, LoggerName);

            GenerateLog(logger, "TestField", "TestValue");

            Assert.Single(exportedItems);
        }

        [Fact]
        public void NotStartsWith_ShouldMatchAndSample()
        {
            var config = ReplaceConfigPlaceholders("TestField", ConstraintType.String, OperatorType.NotStartsWith, "InvalidTest");
            var exportedItems = new List<LogRecord>();
            var logger = LoggerTestUtils.CreateLoggerWithConfig(config, exportedItems, LoggerName);

            GenerateLog(logger, "TestField", "TestValue");

            Assert.Single(exportedItems);
        }

        [Fact]
        public void EndsWith_ShouldMatchAndSample()
        {
            var config = ReplaceConfigPlaceholders("TestField", ConstraintType.String, OperatorType.EndsWith, "Value");
            var exportedItems = new List<LogRecord>();
            var logger = LoggerTestUtils.CreateLoggerWithConfig(config, exportedItems, LoggerName);

            GenerateLog(logger, "TestField", "TestValue");

            Assert.Single(exportedItems);
        }

        [Fact]
        public void NotEndsWith_ShouldMatchAndSample()
        {
            var config = ReplaceConfigPlaceholders("TestField", ConstraintType.String, OperatorType.NotEndsWith, "ValueInvalid");
            var exportedItems = new List<LogRecord>();
            var logger = LoggerTestUtils.CreateLoggerWithConfig(config, exportedItems, LoggerName);

            GenerateLog(logger, "TestField", "TestValue");

            Assert.Single(exportedItems);
        }

        [Fact]
        public void InvalidType_ShouldNotMatch()
        {
            var config = ReplaceConfigPlaceholders("TestField", "InvalidString", OperatorType.StringEquals, "TestValue");
            var exportedItems = new List<LogRecord>();
            var logger = LoggerTestUtils.CreateLoggerWithConfig(config, exportedItems, LoggerName);

            GenerateLog(logger, "TestField", "TestValue");

            Assert.Empty(exportedItems);
        }

        [Fact]
        public void InvalidOperator_ShouldNotMatch()
        {
            var config = ReplaceConfigPlaceholders("TestField", ConstraintType.String, "InvalidOperator", "TestValue");
            var exportedItems = new List<LogRecord>();
            var logger = LoggerTestUtils.CreateLoggerWithConfig(config, exportedItems, LoggerName);

            GenerateLog(logger, "TestField", "TestValue");

            Assert.Empty(exportedItems);
        }

        [Fact]
        public void OperatorWithWhitespace_ShouldMatchAndSample()
        {
            var config = ReplaceConfigPlaceholders("TestField", ConstraintType.String, "  " + OperatorType.StringEquals + "  ", "TestValue");
            var exportedItems = new List<LogRecord>();
            var logger = LoggerTestUtils.CreateLoggerWithConfig(config, exportedItems, LoggerName);

            GenerateLog(logger, "TestField", "TestValue");

            Assert.Single(exportedItems);
        }

        [Fact]
        public void TypeWithWhitespace_ShouldMatchAndSample()
        {
            var config = ReplaceConfigPlaceholders("TestField", "  " + ConstraintType.String + "  ", OperatorType.StringEquals, "TestValue");
            var exportedItems = new List<LogRecord>();
            var logger = LoggerTestUtils.CreateLoggerWithConfig(config, exportedItems, LoggerName);

            GenerateLog(logger, "TestField", "TestValue");

            Assert.Single(exportedItems);
        }

        [Fact]
        public void StringEquals_WithNullValue_ShouldNotMatch()
        {
            var config = ReplaceConfigPlaceholders("TestField", ConstraintType.String, OperatorType.StringEquals, "TestValue");
            var exportedItems = new List<LogRecord>();
            var logger = LoggerTestUtils.CreateLoggerWithConfig(config, exportedItems, LoggerName);

            GenerateLog(logger, "TestField", null);

            Assert.Empty(exportedItems);
        }

        [Fact]
        public void StringEquals_WithEmptyValue_ShouldMatchAndSample()
        {
            var config = ReplaceConfigPlaceholders("TestField", ConstraintType.String, OperatorType.StringEquals, string.Empty);
            var exportedItems = new List<LogRecord>();
            var logger = LoggerTestUtils.CreateLoggerWithConfig(config, exportedItems, LoggerName);

            GenerateLog(logger, "TestField", string.Empty);

            Assert.Single(exportedItems);
        }

        [Fact]
        public void StringEquals_WithLongString_ShouldMatchAndSample()
        {
            var longString = new string('a', 10000);
            var config = ReplaceConfigPlaceholders("TestField", ConstraintType.String, OperatorType.StringEquals, longString);
            var exportedItems = new List<LogRecord>();
            var logger = LoggerTestUtils.CreateLoggerWithConfig(config, exportedItems, LoggerName);

            GenerateLog(logger, "TestField", longString);

            Assert.Single(exportedItems);
        }

        [Fact]
        public void StringEquals_WithUnicode_ShouldMatchAndSample()
        {
            var unicodeString = "测试值";
            var config = ReplaceConfigPlaceholders("TestField", ConstraintType.String, OperatorType.StringEquals, unicodeString);
            var exportedItems = new List<LogRecord>();
            var logger = LoggerTestUtils.CreateLoggerWithConfig(config, exportedItems, LoggerName);

            GenerateLog(logger, "TestField", unicodeString);

            Assert.Single(exportedItems);
        }
    }
}
