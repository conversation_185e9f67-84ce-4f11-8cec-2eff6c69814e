// <copyright file="FileLoggerTests.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.IO;
using Moq;
using Xunit;

namespace Microsoft.M365.Core.Telemetry.FileExporter.Tests
{
    /// <summary>
    /// SecurityRecordFileLoggerTests
    /// </summary>
    public class SecurityRecordFileLoggerTests
    {
        /// <summary>
        /// Test LogToFileEmptyEntries
        /// </summary>
        [Fact]
        public void LogToFileEmptyEntries()
        {
            var fileOpDelegate = new Mock<IFileManager>();

            using var fileLogger = new TextFileLogger(
                new TextFileLoggerOptions
                {
                    Directory = "logtofile",
                    FileName = "app",
                    FileExtension = ".log",
                    FileDateTimePattern = "yyyy-MM-dd"
                }, fileOpDelegate.Object);

            var logEntries = new List<string>();

            fileLogger.LogToFile(logEntries);

            fileOpDelegate.Verify(mock => mock.DirectoryExists(It.IsAny<string>()), Times.Never);
            fileOpDelegate.Verify(mock => mock.CreateDirectory(It.IsAny<string>()), Times.Never);
            fileOpDelegate.Verify(mock => mock.FileExists(It.IsAny<string>()), Times.Never);
            fileOpDelegate.Verify(
                mock => mock.GetFiles(It.IsAny<string>()),
                Times.Never);
        }

        /// <summary>
        /// Test LogToFileNewLogFile
        /// </summary>
        [Fact]
        public void LogToFileNewLogFile()
        {
            if (File.Exists("logtofile/app.log1"))
            {
                File.Delete("logtofile/app.log1");
            }

            if (File.Exists("logtofile/app.log2"))
            {
                File.Delete("logtofile/app.log2");
            }

            if (Directory.Exists("logtofile"))
            {
                Directory.Delete("logtofile");
            }

            Directory.CreateDirectory("logtofile");
            File.Create("logtofile/app.log1").Close();
            File.Create("logtofile/app.log2").Close();

            using var fakeFileStream = File.Create("fakefile");

            var fileOpDelegate = new Mock<IFileManager>();
            fileOpDelegate.Setup(mock => mock.FileExists(It.IsAny<string>()))
                .Returns(false);
            fileOpDelegate.Setup(mock => mock.DirectoryExists(It.IsAny<string>()))
                .Returns(false);
            fileOpDelegate.Setup(mock => mock.CreateLogFile(It.IsAny<string>()))
                .Returns(new LogFile("app.log1", Path.Combine(Directory.GetCurrentDirectory(), "logtofile/app.log1"), 1,
                    DateTime.UtcNow));
            fileOpDelegate.Setup(mock => mock.CreateFile(It.IsAny<string>())).Returns(fakeFileStream);

            using var fileLogger =
                new TextFileLogger(
                    new TextFileLoggerOptions { Directory = "logtofile", FileName = "app.log" },
                    fileOpDelegate.Object);

            var logEntries = new List<string> { "1", "2" };

            fileLogger.LogToFile(logEntries);
            var fullFileName = fileLogger.GetFormattedFileName("app.log");

            //fileOpDelegate.Verify(mock => mock.FileExists(It.IsAny<string>()), Times.Once);
            fileOpDelegate.Verify(mock => mock.CreateDirectory(Path.Combine(Directory.GetCurrentDirectory(), "logtofile")), Times.Once);
            fileOpDelegate.Verify(
                mock => mock.WriteAllLines(
                    Path.Combine(Directory.GetCurrentDirectory(), "logtofile", fullFileName), logEntries), Times.Once);
        }

        /// <summary>
        /// Test CleanUpArchivedFilesOverRetentionCount
        /// </summary>
        [Fact]
        public void CleanUpArchivedFilesOverRetentionCount()
        {
            var fileOpDelegate = new Mock<IFileManager>();

            using var fileLogger = new TextFileLogger(
                new TextFileLoggerOptions
                {
                    Directory = "logs",
                    FileName = "app.log",
                    EnableCleanUpArchivedFiles = true,
                    RetentionCount = 2,
                }, fileOpDelegate.Object);

            var existingLogFiles = new List<LogFile>();
            var logFile1 = new LogFile("app.log1", Path.Combine(Directory.GetCurrentDirectory(), "logs", "app.log1"), 100,
                DateTime.UtcNow.AddDays(-1));
            var logFile2 = new LogFile("app.log2", Path.Combine(Directory.GetCurrentDirectory(), "logs", "app.log2"), 100,
                DateTime.UtcNow.AddDays(-2));
            var logFile3 = new LogFile("app.log3", Path.Combine(Directory.GetCurrentDirectory(), "logs", "app.log3"), 100,
                DateTime.UtcNow.AddDays(-3));

            existingLogFiles.Add(logFile1);
            existingLogFiles.Add(logFile2);
            existingLogFiles.Add(logFile3);

            fileLogger.CleanUpArchivedFiles(existingLogFiles);

            fileOpDelegate.Verify(mock => mock.DeleteFile(It.IsAny<string>()), Times.Once);
            fileOpDelegate.Verify(
                mock => mock.DeleteFile(Path.Combine(Directory.GetCurrentDirectory(), "logs", "app.log3")),
                Times.Once);
        }

        /// <summary>
        /// Test CleanUpArchivedFilesNotOverRetentionCount
        /// </summary>
        [Fact]
        public void CleanUpArchivedFilesNotOverRetentionCount()
        {
            var fileOpDelegate = new Mock<IFileManager>();

            using var fileLogger = new TextFileLogger(
                new TextFileLoggerOptions
                {
                    Directory = "logs",
                    FileName = "app.log",
                    EnableCleanUpArchivedFiles = true,
                    RetentionCount = 4,
                }, fileOpDelegate.Object);

            var existingLogFiles = new List<LogFile>();
            var logFile1 = new LogFile("app.log1", Path.Combine(Directory.GetCurrentDirectory(), "logs", "app.log1"), 100,
                DateTime.UtcNow.AddDays(-1));
            var logFile2 = new LogFile("app.log2", Path.Combine(Directory.GetCurrentDirectory(), "logs", "app.log2"), 100,
                DateTime.UtcNow.AddDays(-2));
            var logFile3 = new LogFile("app.log3", Path.Combine(Directory.GetCurrentDirectory(), "logs", "app.log3"), 100,
                DateTime.UtcNow.AddDays(-3));

            existingLogFiles.Add(logFile1);
            existingLogFiles.Add(logFile2);
            existingLogFiles.Add(logFile3);

            fileLogger.CleanUpArchivedFiles(existingLogFiles);

            fileOpDelegate.Verify(mock => mock.DeleteFile(It.IsAny<string>()), Times.Never);
        }

        /// <summary>
        /// Test LogToFileCatchCleanUpFileException
        /// </summary>
        [Fact]
        public void LogToFileCatchCleanUpFileException()
        {
            using var fakeFileStream = File.Create("fakefile");

            var fileOpDelegate = new Mock<IFileManager>();
            fileOpDelegate.Setup(mock => mock.FileExists(It.IsAny<string>()))
                .Returns(false);
            fileOpDelegate.Setup(mock => mock.DirectoryExists(It.IsAny<string>()))
                .Returns(false);
            fileOpDelegate.Setup(mock => mock.GetFiles(It.IsAny<string>())).Returns(new string[] { "cleanUpException.log1", "cleanUpException.log2" });
            fileOpDelegate.Setup(mock => mock.CreateLogFile(It.IsAny<string>()))
                .Returns(new LogFile("cleanUpException.log1", Path.Combine(Directory.GetCurrentDirectory(), "logtofile2/cleanUpException.log1"), 1,
                    DateTime.UtcNow));
            fileOpDelegate.Setup(mock => mock.CreateFile(It.IsAny<string>())).Returns(fakeFileStream);
            fileOpDelegate.Setup(mock => mock.DeleteFile(It.IsAny<string>())).Throws(new UnauthorizedAccessException());

            using var fileLogger =
                new TextFileLogger(
                    new TextFileLoggerOptions { Directory = "logtofile2", FileName = "cleanUpException.log", EnableCleanUpArchivedFiles = true, RetentionCount = 1 },
                    fileOpDelegate.Object);

            var logEntries = new List<string> { "1", "2" };

            fileLogger.LogToFile(logEntries);
            var fullFileName = fileLogger.GetFormattedFileName("cleanUpException.log");

            fileOpDelegate.Verify(mock => mock.DeleteFile(Path.Combine(Directory.GetCurrentDirectory(), "logtofile2/cleanUpException.log1")), Times.Once);
        }
    }
}
