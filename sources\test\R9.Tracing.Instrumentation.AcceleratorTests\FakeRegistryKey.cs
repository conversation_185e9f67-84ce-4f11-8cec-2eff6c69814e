﻿// <copyright file="FakeRegistryKey.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Microsoft.M365.Core.Telemetry.R9.Tracing.Instrumentation.Accelerator.Utilities;

namespace Microsoft.M365.Core.Telemetry.R9.Tracing.Instrumentation.Accelerator.Tests
{
    /// <summary>
    /// fake registry key for testing
    /// </summary>
    public class FakeRegistryKey : IRegistryKey
    {
        private string currentPath;

        private Dictionary<string, object> registries = new Dictionary<string, object>()
        {
            { @"MsiProductMajor", 15 },
            { @"MsiProductMinor", 20 },
            { @"MsiBuildMajor", 5678 },
            { @"MsiBuildMinor", 0 },
            { @"FastTrainVersion", "15.20.5678.000" },
        };

        /// <summary>
        /// Custom Constructor
        /// </summary>
        public FakeRegistryKey()
        {
            this.currentPath = string.Empty;
        }

        /// <summary>
        /// Custom Constructor with given path
        /// </summary>
        /// <param name="path"> The path to open in registry </param>
        public FakeRegistryKey(string path)
        {
            this.currentPath = path;
        }

        /// <inheritDoc/>
        /// <summary>
        /// Gets a registry value given a name
        /// </summary>
        /// <param name="name"> Name to lookup in registry</param>
        /// <param name="defaultValue"> The default value to set</param>
        /// <returns> Returns a registry value</returns>
        public object GetValue(string name, object defaultValue = null)
        {
            if (registries.TryGetValue(name, out object result))
            {
                return result;
            }
            return defaultValue;
        }

        /// <inheritDoc/>
        /// <summary> 
        /// Opens the registry subkey
        /// </summary>
        /// <param name="subkey">The path to open in registry</param>
        /// <returns> Returns an Iregistry Key </returns>
        public IRegistryKey OpenSubKey(string subkey)
        {
            this.currentPath = Path.Combine(currentPath, subkey);
            return this;
        }

        /// <summary> 
        /// Set registry dictionary
        /// </summary>
        /// <param name="registries"> Registry dictionary </param>
        public void SetRegistries(Dictionary<string, object> registries)
        {
            this.registries = registries;
        }
    }
}
