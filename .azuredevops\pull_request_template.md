# \[Major / Minor / Patch\] PR title

Items marked with '[]' are **required** in targeted type and **optional** in other types. Don't forget to delete the type mark before you submit the PR. For example, the title should be like "\[Major\] Upgrade the SDK to .NET8".

## Description

### Release Notes

Brief summary of PR

### \[Major/Patch\] Actions required by partners

For example, upgrade the package at once, pay attention to dependency conflicts, pre-requirements to use new version... 

### \[Major\] Internal Notes

Everything that needs internal attention.

### Why we need this PR?

Background / Work Items / Bugs / ...

**For breaking changes, explain why we need to do these changes.**

### What is changed in this PR?

List features changes here for quick review.

### \[Major\] What are the breaking changes in this PR?

Refer to [breaking change checklist](https://o365exchange.visualstudio.com/O365%20Core/_wiki/wikis/O365%20Core.wiki/330461/General-Breaking-Change-Checklist) and list changes and violated rules here.

You can paste screenshots or code snippets and give some simple explanations.

## Test

### Sample output from TDS / Local E2E test

logs / screenshots from TDS, local test results, ...

## \[Major\] Reviewer checklist

- Is there a better way to avoid breaking changes?
- Are actions above enough to prevent incidents?
