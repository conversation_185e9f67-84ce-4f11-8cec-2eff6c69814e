﻿// <copyright file="JsonLogSerializer.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Newtonsoft.Json;

namespace Microsoft.M365.Core.Telemetry.Exporters.ODLTCP
{
    /// <summary>
    /// Json Log Serializer
    /// </summary>
    public class JsonLogSerializer : ILogSerializer
    {
        /// <summary>
        /// Singleton Serializer
        /// </summary>
        public static readonly JsonLogSerializer Instance = new ();

        /// <summary>
        /// Constructor
        /// </summary>
        private JsonLogSerializer()
        {
        }

        /// <inheritdoc/>
        public string Serialize(object? rawValue)
        {
            if (rawValue == null)
            {
                return string.Empty;
            }

            switch (rawValue)
            {
                case string str:
                    return StringSerialize(str);
                case int i:
                    return i.ToString();
                case uint ui:
                    return ui.ToString();
                case long l:
                    return l.ToString();
                case ulong ul:
                    return ul.ToString();
                case short s:
                    return s.ToString();
                case ushort us:
                    return us.ToString();
                case double d:
                    return d.ToString();
                case float f:
                    return f.ToString();
                case bool b:
                    return b.ToString();
                case DateTime dt:
                    return dt.ToString("o");
                case DateTimeOffset dt:
                    return dt.ToString("o");
                case ActivityTraceId activityTraceId:
                    return activityTraceId.ToHexString();
                case ActivitySpanId activitySpanId:
                    return activitySpanId.ToHexString();
#pragma warning disable CS8603
                case Object o:
                    return StringSerialize(JsonConvert.SerializeObject(o));
#pragma warning disable CS8603
                default:
                    return string.Empty;
            }
        }

        /// <summary>
        /// For internal serialization when need to collapse dictionary values into one single col.
        /// </summary>
        /// <param name="dic"></param>
        /// <param name="delimiter"></param>
        /// <returns></returns>
        public string SerializeDic(Dictionary<string, object> dic, string delimiter)
        {
            if (dic == null || dic.Count == 0)
            {
                return string.Empty;
            }

            return string.Join(delimiter, dic.Select(i => i.Value is string ? Serialize(i.Value) : JsonConvert.SerializeObject(i.Value)));
        }

        private string StringSerialize(string str)
        {
            if (string.IsNullOrEmpty(str))
            {
                return str;
            }
#pragma warning disable CA1307
            str = str.Replace("\"", "\"\"");
#pragma warning disable CA1307
            str = $"\"{str}\"";
            return str;
        }
    }
}
