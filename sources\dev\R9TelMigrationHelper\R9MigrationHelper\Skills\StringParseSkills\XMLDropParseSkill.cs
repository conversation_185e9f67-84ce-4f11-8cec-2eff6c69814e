﻿// <copyright file="XMLDropParseSkill.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Linq;
using Kusto.Cloud.Platform.Utils;
using R9MigrationHelper.Model;

namespace R9MigrationHelper.Skills.StringParseSkills
{
    /// <summary>
    /// XMLDropParseSkill.
    /// </summary>
    public class XMLDropParseSkill
    {
        /// <summary>
        /// GetDestinationPath.
        /// </summary>
        /// <param name="xmlDropConfig">xmlDropConfig.</param>
        public string GetDestinationPath(string xmlDropConfig)
        {
            XElement xelement = XElement.Parse(xmlDropConfig);
            List<XElement> dest = xelement.Descendants("DEST").ToList();
            if (dest.Count == 0)
            {
                return string.Empty;
            }

            // if there are multiple destinations, return the most frequent one
            return dest.GroupBy(i => i.Value).MaxBy(g => g.Count())!.Key;
        }

        /// <summary>
        /// GetFullDestinationPath.
        /// </summary>
        /// <param name="xmlDropConfig">xmlDropConfig.</param>
        public List<XmlFullDestination> GetFullDestinationPath(string xmlDropConfig)
        {
            XElement xelement = XElement.Parse(xmlDropConfig);
            List<XElement> dest = xelement.Descendants("DEST").ToList();

            List<XmlFullDestination> fullDestinationsList = GetDestination(dest, "FILENUPKG");
            if (fullDestinationsList.Count == 0)
            {
                fullDestinationsList = GetDestination(dest, "FILESOURCE");
                if (fullDestinationsList.Count == 0)
                {
                    fullDestinationsList = GetDestination(dest, "FILEBUILD");
                }
            }
            return fullDestinationsList;
        }

        private List<XmlFullDestination> GetDestination(List<XElement> dest, string ancestorsTag)
        {
            List<XmlFullDestination> fullDestinationsList = new List<XmlFullDestination>();
            HashSet<string> fullDestinations = new HashSet<string>();
            foreach (XElement d in dest)
            {
                if (d.Ancestors(ancestorsTag).FirstOrDefault() != null)
                {
#pragma warning disable CS8602 // Dereference of a possibly null reference.
                    string root = d.Parent.Parent.Name.ToString();
                    string destination = d.Value;
#pragma warning restore CS8602 // Dereference of a possibly null reference.
                    if (!fullDestinations.Contains(root + destination))
                    {
                        XmlFullDestination fullDestination = new XmlFullDestination()
                        {
                            Root = root,
                            Destination = destination,
                        };
                        fullDestinationsList.Add(fullDestination);
                        fullDestinations.Add(root + destination);
                    }
                }
            }
            return fullDestinationsList;
        }
    }
}
