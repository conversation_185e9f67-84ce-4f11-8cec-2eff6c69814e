# Release Note for Substrate R9 Logging Extension

*<div style="text-align: right; font-size:12px">Last Modified: @@LastModified</div>*

## 1.1
### 1.1.2

> [!Note]
> This package is built on [R9 8.6 + ECS 16] for Substrate Repo.

Release Date: 2025/03/25

[Package Link](https://o365exchange.visualstudio.com/O365%20Core/_artifacts/feed/M365Common/NuGet/Microsoft.M365.Core.Telemetry.R9.Logging.Substrate/overview/1.1.2-rc.1)

**Summary**
A stable version of `1.1.2-rc.1`.

### 1.1.2-rc.1

> [!Note]
> This package is built on [R9 8.6 + ECS 16] for Substrate Repo.

Release Date: 2025/02/24

[Package Link](https://o365exchange.visualstudio.com/O365%20Core/_artifacts/feed/M365Common/NuGet/Microsoft.M365.Core.Telemetry.R9.Logging.Substrate/overview/1.1.2-rc.1)

**Summary**

Roll-back dependency versions to align with current Substrate Repo & Finish local end to end test. Ready for Beta.

**Details**
- Simplify sample app for step-by-step guide.
- Force OdlTcp export format
- Enhance UT for legacy use.
- Fix usage on UT.
- Add note on enrichment to protect odl export schema unchanged.

**Pull Requests**
- [Logging - Merge logging extension and related packages from master](https://o365exchange.visualstudio.com/O365%20Core/_git/TelemetryCore/pullrequest/3985072)
- [[Minor] Sample for e2e test and some enhancement](https://o365exchange.visualstudio.com/O365%20Core/_git/TelemetryCore/pullrequest/3981076)

---

### 1.1.1-rc
Release Date: 2025/02/10

**Summary**

Introduce Configuration APIs.
Allow customizing ECS-related parameters in `appsettings.json`.
Bug fix.

**Details**
- Introduce 2 APIs to fetch configuration from appsettings.json and ECS for DI and Non-DI scenarios specifically.
- Modifying the usage of ECS API. Switch to provide configuration sections to ECS API instead of parsing them and building ECSConfiguration object manually.
- Fix the issue that the `CompositeExporter` does not work as expected when adding new export.

**Pull Requests**
- [[Minor] Configuration worker API](https://o365exchange.visualstudio.com/O365%20Core/_git/TelemetryCore/pullrequest/3892331)
- [[Minor] Allow customized identifiers](https://o365exchange.visualstudio.com/O365%20Core/_git/TelemetryCore/pullrequest/3925542)
- [[Minor] Logging - Refresh exporters when config updated](https://o365exchange.visualstudio.com/O365%20Core/_git/TelemetryCore/pullrequest/3950871)

---

### 1.1.0-rc
Release Date: 2024/12

**Summary**

Introduce `CompositeExporter`.

**Details**
- New `CompositeExporter` that wraps multiple exporters and routes log records to them according to the configuration. (Hard code to Geneva and OdlTcp for now)
- Use config `SubstrateLogging:UseCompositeExporter` to decide the configured exporter with `AddSubstrateLogging()` and `ConfigureSubstrateLogging()`. If not set, use the simple Geneva exporter (keep behavior of previous versions).

**Pull Requests**
- [[Major] Logging - Composite Exporter for Substrate R9 Logging Extension](https://o365exchange.visualstudio.com/O365%20Core/_git/TelemetryCore/pullrequest/3871642)

---

## 1.0

### 1.0.1-rc
Release Date: 2024/11/28

**Summary**

Update R9 to 8.11.0

---

### 1.0.0-rc
Release Date: 2024/11/11

**Summary**

New package released.

**Details**
- Add extension methods for `ILoggingBuilder` and `IServiceCollection` to configure logging.
- It configures Open Telemetry logging with a `Geneva Exporter`. The configuration is input from function parameter as `IConfiguration`.

**Pull Requests**
- [Init version for Substrate R9 Telemetry Logging Extension](https://o365exchange.visualstudio.com/O365%20Core/_git/TelemetryCore/pullrequest/3788197)
