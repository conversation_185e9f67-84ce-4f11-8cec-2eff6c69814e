﻿// <copyright file="ModifyXMLDropSkill.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml;
using System.Xml.Linq;
using PackageUpgradeHelper.ADO;

namespace PackageUpgradeHelper.ProjectFile
{
    /// <summary>
    /// Modify XMLDrop skill
    /// </summary>
    class ModifyXMLDropSkill
    {
        /// <summary>
        /// Find XMLDrop files contain root dll
        /// </summary>
        /// <param name="rootDllName"></param>
        /// <returns></returns>
        public async Task<List<ADOFileInfo>> FindTargetXMLDropFilesAsync(string rootDllName)
        {
            var searchCodeSkill = new SearchCodeSkill();
            var searchText = $"file:XMLDrop ext:xml {rootDllName}";
            return await searchCodeSkill.SearchCodeAsync(searchText).ConfigureAwait(false);
        }

        /// <summary>
        /// Update dll source path
        /// </summary>
        /// <param name="xmlDropFileInfo"></param>
        /// <param name="rootDllName"></param>
        /// <param name="unSupportedVersion"></param>
        /// <param name="newVersion"></param>
        /// <param name="branch"></param>
        public string UpdateDllSourcePath(ADOFileInfo xmlDropFileInfo, string rootDllName, string unSupportedVersion, string newVersion, string branch)
        {
            var getFileContentSkill = new GetFileContentSkill();
            var odlContent = getFileContentSkill.GetFileContent(xmlDropFileInfo.Path, branch).Result;
            var xml = new XmlDocument();
            xml.LoadXml(odlContent);
            var root = xml.SelectSingleNode("ROOT");
            if (root != null)
            {
                foreach (XmlNode node in root.ChildNodes)
                {
                    var nodeName = node.Name;
                    if (nodeName.Equals("FILENUPKG") || nodeName.Equals("FILESOURCE") || nodeName.Equals("FILEBUILD"))
                    {
                        if (node.Attributes?["filename"]?.Value.Equals(rootDllName) == true)
                        {
                            var sourceNode = node.SelectSingleNode("SOURCE");
                            if (sourceNode != null)
                            {
                                sourceNode.InnerText = sourceNode.InnerText.Replace(unSupportedVersion, newVersion);
                            }
                        }
                    }
                }
            }
            return XDocument.Parse(xml.OuterXml).ToString();
        }

        /// <summary>
        /// Add XMLDrop entry for new introduced dll
        /// Copy all configurations except source path from root dll
        /// </summary>
        /// <param name="xmlDropFileInfo"></param>
        /// <param name="rootDllName"></param>
        /// <param name="dependencyDllName"></param>
        /// <param name="dependencySourcePath"></param>
        /// <param name="branch"></param>
        public string AddXMLDropEntryForNewDependency(ADOFileInfo xmlDropFileInfo, string rootDllName, string dependencyDllName, string dependencySourcePath, string branch)
        {
            var getFileContentSkill = new GetFileContentSkill();
            var odlContent = getFileContentSkill.GetFileContent(xmlDropFileInfo.Path, branch).Result;
            var xml = new XmlDocument();
            xml.LoadXml(odlContent);
            var root = xml.SelectSingleNode("ROOT");
            if (root != null)
            {
                foreach (XmlNode node in root.ChildNodes)
                {
                    var nodeName = node.Name;
                    if (nodeName.Equals("FILENUPKG") || nodeName.Equals("FILESOURCE"))
                    {
                        if (node.Attributes?["filename"]?.Value.Equals(rootDllName) == true)
                        {
                            var newNode = node.CloneNode(true);
                            newNode.Attributes!["filename"]!.Value = dependencyDllName;
                            var sourceNode = newNode.SelectSingleNode("SOURCE");
                            if (sourceNode != null)
                            {
                                sourceNode.InnerText = dependencySourcePath;
                            }
                            root.AppendChild(newNode);
                            break;
                        }
                    }
                }
            }
            return XDocument.Parse(xml.OuterXml).ToString();
        }

        /// <summary>
        /// Add XMLDrop entries for multiple new introduced dlls
        /// Copy all configurations except source path from root dll
        /// </summary>
        /// <param name="xmlDropFileInfo"></param>
        /// <param name="modifications">List of tuples containing (rootDllName, newDllName, newDllPath)</param>
        /// <param name="branch"></param>
        public string AddXMLDropEntriesForNewDependencies(ADOFileInfo xmlDropFileInfo, List<(string rootDllName, string newDllName, string newDllPath)> modifications, string branch)
        {
            var getFileContentSkill = new GetFileContentSkill();
            var odlContent = getFileContentSkill.GetFileContent(xmlDropFileInfo.Path, branch).Result;
            var xml = new XmlDocument();
            xml.LoadXml(odlContent);
            var root = xml.SelectSingleNode("ROOT");
            if (root != null)
            {
                // Create a dictionary to store node templates for each root DLL
                var rootDllTemplates = new Dictionary<string, XmlNode>();
                
                // First find and save all nodes corresponding to root DLLs
                foreach (XmlNode node in root.ChildNodes)
                {
                    var nodeName = node.Name;
                    if (nodeName.Equals("FILENUPKG") || nodeName.Equals("FILESOURCE"))
                    {
                        var filename = node.Attributes?["filename"]?.Value;
                        if (filename != null && modifications.Any(m => m.rootDllName.Equals(filename)))
                        {
                            rootDllTemplates[filename] = node;
                        }
                    }
                }

                // Then create corresponding nodes for each new DLL
                foreach (var (rootDllName, newDllName, newDllPath) in modifications)
                {
                    if (rootDllTemplates.TryGetValue(rootDllName, out var templateNode))
                    {
                        var newNode = templateNode.CloneNode(true);
                        newNode.Attributes!["filename"]!.Value = newDllName;
                        var sourceNode = newNode.SelectSingleNode("SOURCE");
                        if (sourceNode != null)
                        {
                            sourceNode.InnerText = newDllPath;
                        }
                        root.AppendChild(newNode);
                    }
                }
            }
            return XDocument.Parse(xml.OuterXml).ToString();
        }
    }
}
