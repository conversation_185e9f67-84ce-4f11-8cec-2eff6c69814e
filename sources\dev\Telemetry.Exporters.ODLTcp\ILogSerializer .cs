﻿// <copyright file="ILogSerializer .cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Microsoft.M365.Core.Telemetry.Exporters.ODLTCP
{
    /// <summary>
    /// For Input log serilization
    /// </summary>
    public interface ILogSerializer
    {
        /// <summary>
        /// Serilize
        /// </summary>
        /// <param name="rawValue"></param>
        /// <returns></returns>
        public string Serialize(Object? rawValue);

        /// <summary>
        /// For internal serialization when need to collapse dictionary values into one single col.
        /// </summary>
        /// <param name="dic"></param>
        /// <param name="delimiter"></param>
        /// <returns></returns>
        public string SerializeDic(Dictionary<string, object> dic, string delimiter);
    }
}
