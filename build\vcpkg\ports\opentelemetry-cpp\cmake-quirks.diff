diff --git a/CMakeLists.txt b/CMakeLists.txt
index fd41fa7..cde4566 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -24,7 +24,6 @@ project(opentelemetry-cpp)
 mark_as_advanced(CMAKE_TOOLCHAIN_FILE)
 
 # Prefer cmake CONFIG to auto resolve dependencies.
-set(CMAKE_FIND_PACKAGE_PREFER_CONFIG TRUE)
 
 # Don't use customized cmake modules if vcpkg is used to resolve dependence.
 if(NOT DEFINED CMAKE_TOOLCHAIN_FILE)
@@ -96,7 +95,6 @@ if(DEFINED ENV{VCPKG_ROOT} AND NOT DEFINED CMAKE_TOOLCHAIN_FILE)
 endif()
 
 if(VCPKG_CHAINLOAD_TOOLCHAIN_FILE)
-  include("${VCPKG_CHAINLOAD_TOOLCHAIN_FILE}")
 endif()
 
 option(WITH_ABI_VERSION_1 "ABI version 1" ON)
