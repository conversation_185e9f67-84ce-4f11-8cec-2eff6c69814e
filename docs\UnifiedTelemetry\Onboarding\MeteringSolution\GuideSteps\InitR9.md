# Step 2 - Initialize R9 Telemetry
R9 Telemetry requires explicit initialization. Unified Telemetry team has implemented an extension **Microsoft.M365.Core.Telemetry.R9.Metering.Substrate** to initialize R9 in the Substrate conveniently. 
Before calling the initialization method, there **must be** an IConfiguration that has loaded the configurations like the above example.

R9 makes extensive use of the .NET [dependency injection](https://learn.microsoft.com/dotnet/core/extensions/dependency-injection) (DI) model. While older applications often don't use dependency injection. There are two different R9 telemetry initialization styles supported for different application scenarios: DI and non-DI.

With our [Substrate R9 metering extension layer](https://o365exchange.visualstudio.com/O365%20Core/_git/TelemetryCore?path=/sources/dev/R9.Metering.Substrate/ReadMe.md&_a=preview), you can easily onboard R9 metering whether your applications use DI or not. Please choose the appropriate one based on your applications/libraries.

- **DI**: Your application is built based on [Microsoft.Extensions.Hosting](https://www.nuget.org/packages/Microsoft.Extensions.Hosting). For example, [AspNetCore](https://learn.microsoft.com/aspnet/core/fundamentals/host/web-host) applications are the most common to use the hosting model. Or your application has a [Generic Host](https://learn.microsoft.com/dotnet/core/extensions/generic-host). The code is called using DI style. There is an existing service collection. Please leverage the DI initialization style.
- **Non-DI**: You are onboarding R9 metering for console applications or libraries what don’t have a host or service collection. Please leverage the Non-DI initialization style.


# [DI](#tab/DI)
If you are building an application/library with DI, call `AddSubstrateMetering()` to set up Metering. The DI container manages the whole lifecycle of its services.

```csharp
serviceCollection.AddSubstrateMetering(configuration);
```
# [Non-DI](#tab/nonDI)

If your application/library doesn't use DI, please call `ConfigureSubstrateMetering()` to set up Metering. The lifetime of the meter provider is managed by the extension method automatically. You don't need to worry about the “dispose thing”.

```csharp
SubstrateMeteringExtension.ConfigureSubstrateMetering(configuration);
```

---

For advanced usage like customizing MeterProviderBuilder, please [see ReadMe.md - Substrate R9 metering extension layer](https://o365exchange.visualstudio.com/O365%20Core/_git/TelemetryCore?path=/sources/dev/R9.Metering.Substrate/ReadMe.md).

**Next Step**: [Instrument Data](./Instrumentation.md)