﻿// <copyright file="XmlEntry.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml;

namespace R9MigrationHelper.Model
{
    /// <summary>
    /// XmlEntry
    /// </summary>
    public class XmlEntry
    {
        /// <summary>
        /// Deliverables
        /// </summary>
        public List<Deliverable> Deliverables { get; set; }

        /// <summary>
        /// Paths
        /// </summary>
        public List<string> Paths { get; set; }

        /// <summary>
        /// FileName
        /// </summary>
        public string FileName { get; set; }

        /// <summary>
        /// XMLEntry
        /// </summary>
        /// <param name="node"></param>
        public XmlEntry(XmlNode node)
        {
            FileName = node.Attributes!["filename"] !.Value.Trim();
            var deliverablesSet = node.SelectSingleNode("DELIVERABLES_SET");
            Deliverables = new List<Deliverable>();
            foreach (XmlNode deliverable in deliverablesSet!.ChildNodes)
            {
                Deliverables.Add(new Deliverable(deliverable));
            }
            Paths = new List<string>();
            foreach (var deliverable in Deliverables)
            {
                Paths.AddRange(deliverable.Paths);
            }
        }
    }
}
