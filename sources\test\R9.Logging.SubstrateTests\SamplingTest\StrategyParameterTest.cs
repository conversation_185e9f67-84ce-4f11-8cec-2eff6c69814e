// <copyright file="StrategyParameterTest.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using Microsoft.Extensions.Logging;
using Microsoft.M365.Core.Telemetry.R9.Logging.Substrate.Sampler;
using Xunit;

namespace Microsoft.M365.Core.Telemetry.R9.Logging.Substrate.Test.SamplingTest
{
    public class StrategyParameterTest
    {
        [Fact]
        public void EmptyType_ShouldBeInvalid()
        {
            var parameter = new StrategyParameter
            {
                Type = string.Empty,
                SampleRate = 0.5
            };
            bool isValid = IsStrategyParameterValid(parameter);
            Assert.False(isValid);
        }

        [Fact]
        public void NullType_ShouldBeInvalid()
        {
            var parameter = new StrategyParameter
            {
                Type = null,
                SampleRate = 0.5
            };
            bool isValid = IsStrategyParameterValid(parameter);
            Assert.False(isValid);
        }

        [Fact]
        public void NegativeSampleRate_ShouldBeInvalid()
        {
            var parameter = new StrategyParameter
            {
                Type = StrategyType.Random,
                SampleRate = -0.1
            };
            bool isValid = IsStrategyParameterValid(parameter);
            Assert.False(isValid);
        }

        [Fact]
        public void SampleRateGreaterThanOne_ShouldBeInvalid()
        {
            var parameter = new StrategyParameter
            {
                Type = StrategyType.Random,
                SampleRate = 1.1
            };
            bool isValid = IsStrategyParameterValid(parameter);
            Assert.False(isValid);
        }

        [Fact]
        public void SampleRateEqualToZero_ShouldBeValid()
        {
            var parameter = new StrategyParameter
            {
                Type = StrategyType.Random,
                SampleRate = 0.0
            };
            bool isValid = IsStrategyParameterValid(parameter);
            Assert.True(isValid);
        }

        [Fact]
        public void SampleRateEqualToOne_ShouldBeValid()
        {
            var parameter = new StrategyParameter
            {
                Type = StrategyType.Random,
                SampleRate = 1.0
            };
            bool isValid = IsStrategyParameterValid(parameter);
            Assert.True(isValid);
        }

        [Fact]
        public void SampleRateBetweenZeroAndOne_ShouldBeValid()
        {
            var parameter = new StrategyParameter
            {
                Type = StrategyType.Random,
                SampleRate = 0.5
            };
            bool isValid = IsStrategyParameterValid(parameter);
            Assert.True(isValid);
        }

        [Fact]
        public void HashBasedRandomWithEmptyHashKey_ShouldBeInvalid()
        {
            var parameter = new StrategyParameter
            {
                Type = StrategyType.HashBasedRandom,
                SampleRate = 0.5,
                HashKey = string.Empty
            };
            bool isValid = IsStrategyParameterValid(parameter);
            Assert.False(isValid);
        }

        [Fact]
        public void HashBasedRandomWithNullHashKey_ShouldBeInvalid()
        {
            var parameter = new StrategyParameter
            {
                Type = StrategyType.HashBasedRandom,
                SampleRate = 0.5,
                HashKey = null
            };
            bool isValid = IsStrategyParameterValid(parameter);
            Assert.False(isValid);
        }

        /// <summary>
        /// Tests that a HashBasedRandom strategy with valid HashKey is valid.
        /// </summary>
        [Fact]
        public void HashBasedRandomWithValidHashKey_ShouldBeValid()
        {
            var parameter = new StrategyParameter
            {
                Type = StrategyType.HashBasedRandom,
                SampleRate = 0.5,
                HashKey = "UserId"
            };
            bool isValid = IsStrategyParameterValid(parameter);
            Assert.True(isValid);
        }

        /// <summary>
        /// Tests that a Random strategy doesn't require a HashKey.
        /// </summary>
        [Fact]
        public void RandomStrategyWithoutHashKey_ShouldBeValid()
        {
            var parameter = new StrategyParameter
            {
                Type = StrategyType.Random,
                SampleRate = 0.5,
                HashKey = null
            };
            bool isValid = IsStrategyParameterValid(parameter);
            Assert.True(isValid);
        }

        private static bool IsStrategyParameterValid(StrategyParameter parameter)
        {
            // Create a rule with the parameter to test
            var rule = new Rule(new List<Constraint>(), parameter);

            // If the parameter is invalid, the rule will be invalid
            // If the parameter is valid but there are no constraints, the rule will be valid
            return rule != null && !rule.ShouldSkip();
        }
    }
}