﻿// <copyright file="OdlEtwLogger.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Diagnostics.CodeAnalysis;
using System.Diagnostics.Tracing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Microsoft.SqlServer.Server;

namespace Microsoft.M365.ODL.NrtTcpClient
{
    /// <summary>
    /// ETW based odl logger, sharing the same event source with ODL in substrate
    /// </summary>
    [ExcludeFromCodeCoverage]
    public sealed class OdlEtwLogger : EventSource, IOdlLogger
    {
        /// <summary>
        /// the singlton instance
        /// </summary>
        public static readonly OdlEtwLogger Instance = new OdlEtwLogger();

        /// <summary>
        /// EventSource V2 (TraceLogging)
        /// </summary>
        private EventSource traceLogger;

        /// <summary>
        /// Max size of message
        /// </summary>
        private const int MaxSize = 31 * 1024;

        private OdlEtwLogger()
        {
            this.traceLogger = new EventSource("OfficeDataLoader");
        }

        /// <summary>
        /// log method for critical log
        /// </summary>
        /// <param name="level"></param>
        /// <param name="id"></param>
        /// <param name="message"></param>
        public void Log(LogLevel level, int id, string message)
        {
            this.LogImpl(true, level, id, message);
        }

        /// <summary>
        /// Implementation of writing trace logs.
        /// </summary>
        /// <param name="severity">the log severity</param>
        /// <param name="traceId">the trace ID</param>
        /// <param name="message">log content</param>
        public void TraceLog(LogLevel severity, int traceId, string message)
        {
            this.LogImpl(false, severity, traceId, message);
        }

        /// <summary>
        /// Implementation of writing critical logs.
        /// </summary>
        /// <param name="isCriticalLog">critical or trace log</param>
        /// <param name="severity">the log severity</param>
        /// <param name="messageId">the event ID or trace ID</param>
        /// <param name="message">log content</param>
        private void LogImpl(bool isCriticalLog, LogLevel severity, int messageId, string message)
        {
            try
            {
                string eventName;
                string trimedMessage = TrimMessage(message), prodVersion = CommonSettings.ProductVersion,
                    odlInstance = CommonSettings.InstanceName;

                if (isCriticalLog)
                {
                    eventName = "CriticalLogs";
                    this.traceLogger.Write(
                       eventName,
                       new
                       {
                           Level = severity,
                           EventId = messageId,
                           Message = trimedMessage,
                           ProductVersion = prodVersion,
                           ODLInstance = odlInstance
                       });
                }
                else
                {
                    eventName = "TraceLogs";
                    this.traceLogger.Write(
                       eventName,
                       new
                       {
                           Level = severity,
                           TraceId = messageId,
                           Message = trimedMessage,
                           ProductVersion = prodVersion,
                           ODLInstance = odlInstance
                       });
                }
            }
            catch (Exception)
            {
                // Don't throw on log errors.
            }
        }

        /// <summary>
        /// trim message
        /// </summary>
        /// <param name="message">raw message</param>
        /// <returns>trimed message</returns>
        private string? TrimMessage(string message)
        {
            return message?.Length > MaxSize ? message?.Substring(0, MaxSize) : message;
        }
    }
}

