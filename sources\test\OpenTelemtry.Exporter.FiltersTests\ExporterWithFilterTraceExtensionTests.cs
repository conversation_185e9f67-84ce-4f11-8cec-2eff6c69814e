﻿// <copyright file="ExporterWithFilterTraceExtensionTests.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System;
using System.Diagnostics;
using System.Diagnostics.CodeAnalysis;
using OpenTelemetry;
using OpenTelemetry.Exporter;
using OpenTelemetry.Exporter.Filters;
using OpenTelemetry.Exporter.Geneva;
using OpenTelemetry.Trace;
using Xunit;

namespace Microsoft.M365.Core.Telemetry.OpenTelemetry.Exporter.Filters.Test
{
    [ExcludeFromCodeCoverage]
    public class ExporterWithFilterTraceExtensionTests
    {
        const string SOURCE = "TestSource";
        ActivitySource activitySource = new ActivitySource(SOURCE); 
        
        [Fact]
        public void AddConsoleExporter_GivenInvalidArguments_ThrowsException()
        {
            Assert.Throws<ArgumentNullException>(() =>
           ((TracerProviderBuilder)null!).AddConsoleExporter());

            Assert.Throws<ArgumentNullException>(() =>
            {
                var traceProvider = Sdk.CreateTracerProviderBuilder()
                .AddConsoleExporter(filter: (BaseFilter<Activity>)null!).Build();
             });

            Assert.Throws<ArgumentNullException>(() =>
            {
                var traceProvider = Sdk.CreateTracerProviderBuilder()
                .AddConsoleExporter(sampler: (Sampler)null!).Build();
            });
            Assert.Throws<ArgumentNullException>(() =>
            {
                var traceProvider = Sdk.CreateTracerProviderBuilder()
                .AddConsoleExporter(sampler: (Sampler)null!, configure: null!).Build();
            });
            Assert.Throws<ArgumentNullException>(() =>
            {
                var traceProvider = Sdk.CreateTracerProviderBuilder()
                .AddConsoleExporter(filter: null!, configure: null!).Build();
            });
        }

        [Fact]
        public void AddGenevaExporter_GivenInvalidArguments_ThrowsException()
        {
            Assert.Throws<ArgumentNullException>(() =>
           ((TracerProviderBuilder)null!).AddGenevaTraceExporter(option =>
           {
               option.ConnectionString = "EtwSession=OpenTelemetry";
           }));

            Assert.Throws<ArgumentNullException>(() =>
            {
                var traceProvider = Sdk.CreateTracerProviderBuilder()
                .AddGenevaTraceExporter(configure: option => { option.ConnectionString = "EtwSession=OpenTelemetry"; }, filter: (BaseFilter<Activity>)null!).Build();
            });

            Assert.Throws<ArgumentNullException>(() =>
            {
                var traceProvider = Sdk.CreateTracerProviderBuilder()
                .AddGenevaTraceExporter(configure: option => { option.ConnectionString = "EtwSession=OpenTelemetry"; }, sampler: (Sampler)null!).Build();
            });
            Assert.Throws<ArgumentException>(() =>
            {
                var traceProvider = Sdk.CreateTracerProviderBuilder()
                .AddGenevaTraceExporter(sampler: new TestSampler(), configure: null!).Build();
            });
            Assert.Throws<ArgumentException>(() =>
            {
                var traceProvider = Sdk.CreateTracerProviderBuilder()
                .AddGenevaTraceExporter(filter: new SamplerFilter(new TestSampler()), configure: null!).Build();
            });
        }

        [Fact]
        public void AddGenevaExporter_AddFilter_FollowTheSampleResult()
        {
            var sampler = new TestSampler();
            using var traceProvider = Sdk.CreateTracerProviderBuilder()
                .AddGenevaTraceExporter(configure: option => { option.ConnectionString = "EtwSession=OpenTelemetry"; }, new SamplerFilter(sampler))
                .AddSource(SOURCE)
                .Build();
            CreateActivity("test1", true);
            CreateActivity("test2", true);
            CreateActivity("fail", false);

            Assert.Equal(2, sampler.SampleCount);
        }

        [Fact]
        public void AddGenevaExporter_AddSampler_FollowTheSampleResult()
        {
            var sampler = new TestSampler();
            using var traceProvider = Sdk.CreateTracerProviderBuilder()
                .AddGenevaTraceExporter(configure: option => { option.ConnectionString = "EtwSession=OpenTelemetry"; }, sampler)
                .AddSource(SOURCE)
                .Build();
            CreateActivity("test1", true);
            CreateActivity("test2", true);
            CreateActivity("fail", false);

            Assert.Equal(2, sampler.SampleCount);
        }

        [Fact]
        public void AddConsoleExporter_AddSampler_FollowTheSampleResult()
        {
            var sampler = new TestSampler();
            using var traceProvider = Sdk.CreateTracerProviderBuilder()
                .AddConsoleExporter(sampler)
                .AddSource(SOURCE)
                .Build();
            CreateActivity("test1", true);
            CreateActivity("test2", true); 
            CreateActivity("fail", false);

            Assert.Equal(2, sampler.SampleCount);
        }

        [Fact]
        public void AddConsoleExporter_AddFilter_FollowTheSampleResult()
        {
            var sampler = new TestSampler();
            using var traceProvider = Sdk.CreateTracerProviderBuilder()
                .AddConsoleExporter(new SamplerFilter(sampler))
                .AddSource(SOURCE)
                .Build();
            CreateActivity("test1", true);
            CreateActivity("test2", true);
            CreateActivity("fail", false);

            Assert.Equal(2, sampler.SampleCount);
        }

        [Fact]
        public void AddConsoleExporter_AddSamplerWithConfig_FollowTheSampleResult()
        {
            var sampler = new TestSampler();
            using var traceProvider = Sdk.CreateTracerProviderBuilder()
                .AddConsoleExporter(options => options.Targets = ConsoleExporterOutputTargets.Console, sampler)
                .AddSource(SOURCE)
                .Build();
            CreateActivity("test1", true);
            CreateActivity("test2", true);
            CreateActivity("fail", false);

            Assert.Equal(2, sampler.SampleCount);
        }

        [Fact]
        public void AddGenevaExporter_linux_BatchProcessor()
        {
            var sampler = new TestSampler();
            var traceProviderBuilder = Sdk.CreateTracerProviderBuilder();
            using var traceProvider = traceProviderBuilder
                .AddProcessor(GenevaExporterWithFilterTraceExtension.BuildGenevaTraceExporter(new GenevaExporterOptions(), configure: option => { option.ConnectionString = "EtwSession=OpenTelemetry"; }, false, new SamplerFilter(sampler)))
                .AddSource(SOURCE)
                .Build();
            CreateActivity("test1", true);
            CreateActivity("test2", true);
            CreateActivity("fail", false);

            Assert.Equal(2, sampler.SampleCount);
        }

        [Fact]
        public void AddGenevaExporter_Windows_ReentrantProcessor()
        {
            var sampler = new TestSampler();
            var traceProviderBuilder = Sdk.CreateTracerProviderBuilder();
            using var traceProvider = traceProviderBuilder
                .AddProcessor(GenevaExporterWithFilterTraceExtension.BuildGenevaTraceExporter(new GenevaExporterOptions(), configure: option => { option.ConnectionString = "EtwSession=OpenTelemetry"; }, true, new SamplerFilter(sampler)))
                .AddSource(SOURCE)
                .Build();
            CreateActivity("test1", true);
            CreateActivity("test2", true);
            CreateActivity("fail", false);

            Assert.Equal(2, sampler.SampleCount);
        }

        private void CreateActivity(string operationName, bool isSuccess)
        {
            using (var activity = activitySource.StartActivity(operationName))
            {
                activity?.SetTag("foo", 1);
                activity?.SetTag("bar", "Hello, World!");
                activity?.SetTag("baz", new int[] { 1, 2, 3 });
                activity?.SetStatus(ActivityStatusCode.Ok);
                activity?.AddTag("expectedResult", isSuccess ? "success" : "fail");
            }
        }
    }
}
