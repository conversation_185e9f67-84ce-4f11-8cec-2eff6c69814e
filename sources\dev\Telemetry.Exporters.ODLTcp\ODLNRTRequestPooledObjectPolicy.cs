﻿// <copyright file="ODLNRTRequestPooledObjectPolicy.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System.Diagnostics.CodeAnalysis;
using Microsoft.Extensions.ObjectPool;
using Microsoft.Office.BigData.DataLoader;

namespace Microsoft.M365.Core.Telemetry.Exporters.ODLTCP
{
    /// <summary>
    /// ODLNRTRequestPooledObject Policy
    /// </summary>
    [ExcludeFromCodeCoverage]
    public class ODLNRTRequestPooledObjectPolicy : PooledObjectPolicy<ODLNRTRequest>
    {
        /// <summary>
        /// Creates a new instance of the object to be placed in the pool.
        /// </summary>
        /// <returns></returns>
        public override ODLNRTRequest Create()
        {
            ODLNRTRequest oDLNRTRequest = new ODLNRTRequest();
            oDLNRTRequest.Head = new ODLNRTCommonHead();
            oDLNRTRequest.Head.CommandType = ODLNRTCommandType.OdlnrtcmdCommonMessageBatch;

            oDLNRTRequest.CommonMessageBatchReq = new ODLNRTCommonMessageBatchReq();
            oDLNRTRequest.CommonMessageBatchReq.Head = new ODLNRTCommonMessageHead();

            return oDLNRTRequest;
        }

        /// <summary>
        /// Return the object to the pool
        /// </summary>
        /// <param name="obj"></param>
        /// <returns></returns>
        public override bool Return(ODLNRTRequest obj)
        {
            if (obj == null || obj.Head == null || obj.CommonMessageBatchReq == null)
            {
                return false;
            }

            obj.CommonMessageBatchReq.Messages?.Clear();
            return true;
        }
    }
}
