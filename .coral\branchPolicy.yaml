reviewerCountPolicy:
   minimumApproverCount: 2
   creatorVoteCounts: true
   allowDownvotes: false
   resetOnSourcePush: false

requiredReviewerPolicies:
 
 - path: '*'
   reviewerName: "{{PipelineMaintainers}}"
   addedFilesOnly: false
   message: "Required reviewers"
 
 - path: '/.coral/*;/baselines/*;/build/*;/metadata/*;/QConfig/*;/Secrets/*;/CloudBuild.json;/Directory.Build.*;/NuGet.config;/README.md;/init.cmd;!/build/configuration/public/csv/*'
   reviewerName: "{{TemplateAdmins}}"
   addedFilesOnly: false
   message: "Default build file reviewers"

 - path: '*'
   reviewerName: "b8eed424-71cf-47bd-afee-4ebe2df679ad"
   addedFilesOnly: false
   message: "All changes in this repository must be approved by a user with Exchange Sources read write access."

 - path: '*.zip;*.wim;*.wav;*.vsd;*.vhd;*.sys;*.snk;*.ppt;*.pdb;*.nupkg;*.msu;*.msi;*.msg;*.mp;*.lzx;*.lib;*.jar;*.gz;*.exe;*.dll;*.datastore;*.bin;*.asmmeta;*.appx;*.7z;*.svgx'
   reviewerName: "6dd1dacb-0c48-4d9e-8ed1-d9d689645ea0"
   addedFilesOnly: true
   message: "Files of this type are not permitted due to known perf impacts in git. For more details, see the Micro Repo FAQs via README.md at the root of your repository."
   
 - path: '*.pfx;*.qencr;*.cer;'
   reviewerName: "6dd1dacb-0c48-4d9e-8ed1-d9d689645ea0"
   addedFilesOnly: true
   message: "Secrets and certificates are not allowed to be checked in."

resolvedCommentsPolicy:
   blocking: true

mergeRequirementPolicy:
   useSquashMerge: true

buildPolicies:
 - buildReference: "\\{{RepositoryName}}\\{{RepositoryName}} Precheckin_Debug"
   displayName: "Precheckin_Debug"
   validDuration: 120

 - buildReference: "\\{{RepositoryName}}\\{{RepositoryName}} Precheckin_Retail"
   displayName: "Precheckin_Retail"
   validDuration: 120

 - buildReference: "\\{{RepositoryName}}\\{{RepositoryName}} VerifyAutomaticReviewers"
   displayName: "VerifyAutomaticReviewers"
   validDuration: 120
   filenamePatterns: '/build/configuration/public/csv/AutomaticReviewers*csv;'

branches:
 - refName: refs/heads/master
   matchKind: Exact
 - refName: refs/heads/release
   matchKind: Prefix