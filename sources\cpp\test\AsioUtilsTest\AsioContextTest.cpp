#include "AsioContext.h"

#include <gtest/gtest.h>

using namespace Microsoft::M365::Exporters;

TEST(AsioContextTest, SetThreadCount) {
    int count = 0;
    AsioContext::GetIoContext().post([&count]() {
        count++;
    });
    std::this_thread::sleep_for(std::chrono::milliseconds(10));
    EXPECT_EQ(1, count);
    AsioContext::SetThreadCount(3);
    AsioContext::GetIoContext().post([&count]() {
        count++;
    });
    std::this_thread::sleep_for(std::chrono::milliseconds(10));
    EXPECT_EQ(2, count);
}