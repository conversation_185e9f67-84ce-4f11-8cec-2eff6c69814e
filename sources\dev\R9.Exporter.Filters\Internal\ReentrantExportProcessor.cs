﻿// <copyright file="ReentrantExportProcessor.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System;
using System.Linq.Expressions;
using System.Reflection;
using OpenTelemetry;

namespace Microsoft.R9.Extensions.Telemetry.Exporter.Filters;

/// <summary>
/// Reentrant export processor.
/// </summary>
/// <remarks>
/// This is copied from ReentrantExportProcessor from GenevaExporter repo as the class
/// is internal and not visible to this project. This will be removed from R9 library
/// in one of the two conditions below. Both of these conditions are planned items.
///  - GenevaLogExporter will make it internalVisible to R9 library.
///  - This class will be added to OpenTelemetry project as public.
/// </remarks>
/// <typeparam name="T">Type of data to be exported.</typeparam>
internal class ReentrantExportProcessor<T> : BaseExportProcessor<T>
    where T : class
{
    private static Func<T, Batch<T>> GetCreateBatchFunc()
    {
#pragma warning disable S3011 // Reflection should not be used to increase accessibility of classes, methods, or fields
        var ctor = typeof(Batch<T>).GetConstructor(BindingFlags.Instance | BindingFlags.NonPublic, null, new[] { typeof(T) }, null)!;
#pragma warning restore S3011 // Reflection should not be used to increase accessibility of classes, methods, or fields
        var value = Expression.Parameter(typeof(T), null);
        var lambda = Expression.Lambda<Func<T, Batch<T>>>(Expression.New(ctor, value), value);
        return lambda.Compile();
    }

    public ReentrantExportProcessor(BaseExporter<T> exporter)
        : base(exporter)
    {
    }

    protected override void OnExport(T data)
    {
        _ = exporter.Export(_createBatch(data));
    }

    private static readonly Func<T, Batch<T>> _createBatch = GetCreateBatchFunc();
}
