﻿// <copyright file="Program.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Microsoft.M365.Core.Telemetry.R9.Logging.Substrate;
using Microsoft.M365.Core.Telemetry.R9.Logging.Substrate.Configuration;

namespace NonDISampleConsoleApp
{
    /// <summary>
    /// Program
    /// </summary>
    internal class Program
    {
        static void Main()
        {
            IConfiguration configuration = ConfigurationHelper.LoadConfiguration(
                appsettingsPath: Path.Combine(Directory.GetCurrentDirectory(), "appsettings.json"));
            ILoggerFactory loggerFactory = LoggerFactory.Create(builder =>
            {
                builder.ConfigureSubstrateLogging(configuration);
                builder.AddConsole();
            });

            var logger1 = loggerFactory.CreateLogger("TableA");
            var logger2 = loggerFactory.CreateLogger("TableB");

            var cnt = 0;
            while (true)
            {
                logger1.LogInformation("Test TableA: {cnt}. Exported to {1}", ++cnt, configuration["SubstrateLogging:CompositeExporter:VirtualTableExports:TableA:0:ExportTable"]);
                logger2.LogInformation("Test TableB: {cnt}. Exported to {1}", cnt, configuration["SubstrateLogging:CompositeExporter:VirtualTableExports:TableB:0:ExportTable"]);
                Thread.Sleep(5000);
            }
        }
    }
}
