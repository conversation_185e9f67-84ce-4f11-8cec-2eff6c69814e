﻿// <copyright file="ReentranActivityExporterProcessor.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

// <copyright file="ReentrantActivityExportProcessor.cs" company="OpenTelemetry Authors">
// Copyright The OpenTelemetry Authors
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
// </copyright>
using System.Diagnostics;
using System.Diagnostics.CodeAnalysis;

namespace OpenTelemetry.Exporter.Filters.Internal
{
    /// <summary>
    /// Reentrant activity export processor.
    /// </summary>
    /// <remarks>
    /// This is copied from ReentrantExportProcessor from GenevaExporter repo as the class
    /// is internal and not visible to this project. This will be removed from this project
    /// in one of the two conditions below. Both of these conditions are planned items.
    ///  - GenevaLogExporter will make it internalVisible to this project.
    ///  - This class will be added to OpenTelemetry project as public.
    /// </remarks>
    [ExcludeFromCodeCoverage]
    internal class ReentrantActivityExportProcessor : ReentrantExportProcessor<Activity>
    {
        /// <summary>
        /// initial the instance with exporter
        /// </summary>
        /// <param name="exporter"></param>
        public ReentrantActivityExportProcessor(BaseExporter<Activity> exporter)
            : base(exporter)
        {
        }

        /// <summary>
        /// overide OnExporter function
        /// </summary>
        /// <param name="data"></param>
        protected override void OnExport(Activity data)
        {
            if (data.Recorded)
            {
                base.OnExport(data);
            }
        }
    }
}
