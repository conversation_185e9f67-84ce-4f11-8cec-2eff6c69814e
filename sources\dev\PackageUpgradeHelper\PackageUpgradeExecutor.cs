// <copyright file="PackageUpgradeExecutor.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System.Diagnostics;

namespace PackageUpgradeHelper
{
    /// <summary>
    /// Executes the package upgrade process.
    /// </summary>
    public static class PackageUpgradeExecutor
    {
        /// <summary>
        /// Executes the upgrade script with the specified target version and root path.
        /// </summary>
        /// <param name="targetVersion">The target version for the upgrade.</param>
        /// <exception cref="InvalidOperationException">Thrown when the script exits with a non-zero code.</exception>
        public static void ExecuteUpgrade(string targetVersion)
        {
            var currentDirectory = Directory.GetCurrentDirectory();
            var scriptPath = Path.Combine(currentDirectory, "Scripts", "StartPackageUpgrade.ps1");

            var arguments = $"-File \"{scriptPath}\" -TargetVersion \"{targetVersion}\"";

            var processInfo = new ProcessStartInfo
            {
                FileName = "powershell.exe",
                Arguments = arguments,
                RedirectStandardOutput = true,
                RedirectStandardError = true,
                UseShellExecute = false,
                CreateNoWindow = false
            };

            using var process = new Process { StartInfo = processInfo };
            process.OutputDataReceived += (sender, e) =>
            {
                if (!string.IsNullOrEmpty(e.Data))
                {
                    Console.WriteLine(e.Data);
                }
            };
            process.ErrorDataReceived += (sender, e) =>
            {
                if (!string.IsNullOrEmpty(e.Data))
                {
                    Console.Error.WriteLine(e.Data);
                }
            };

            process.Start();
            process.BeginOutputReadLine();
            process.BeginErrorReadLine();
            process.WaitForExit();

            if (process.ExitCode != 0)
            {
                throw new InvalidOperationException($"Script exited with code {process.ExitCode}");
            }
        }
    }
}
