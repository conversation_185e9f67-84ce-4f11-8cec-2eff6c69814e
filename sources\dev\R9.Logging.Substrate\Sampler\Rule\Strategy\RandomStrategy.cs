﻿// <copyright file="RandomStrategy.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging.Abstractions;

namespace Microsoft.M365.Core.Telemetry.R9.Logging.Substrate.Sampler
{
    /// <summary>
    /// Sampling strategy that samples log entries randomly based on a specified sample rate.
    /// </summary>
    internal class RandomStrategy : SamplingStrategy
    {
        /// <summary>
        /// Expected sample rate between [0,1].
        /// For example, 0.3 means 30% of logs are expected to be sampled.
        /// If sampled, keep the log. If not, discard the log.
        /// </summary>
        public double SampleRate { get; set; }

        /// <summary>
        /// Initializes a new instance of the <see cref="RandomStrategy"/> class.
        /// </summary>
        /// <param name="sampleRate">The expected sample rate.</param>
        public RandomStrategy(double sampleRate)
        {
            SampleRate = sampleRate;
        }

#if NET6_0_OR_GREATER
        /// <summary>
        /// Makes a sampling decision for the provided <paramref name="logEntry"/>.
        /// If sampled, keep the log. If not, discard the log.
        /// </summary>
        /// <param name="logEntry">The log entry used to make the sampling decision for.</param>
        /// <typeparam name="TState">The type of the log entry state.</typeparam>
        /// <returns><see langword="true" /> if the log record should be sampled; otherwise, <see langword="false" />.</returns>
        public override bool ShouldSample<TState>(in LogEntry<TState> logEntry)
        {
            return Random.Shared.NextDouble() < SampleRate;
        }
#else
        [ThreadStatic]
        private static Random? threadRandom;
        private static readonly Random Global = new Random();

        private static Random ThreadLocalRandom
        {
            get
            {
                if (threadRandom is null)
                {
                    int seed;
                    lock (Global)
                    {
                        seed = Global.Next(); // A global random instance is used to prevent multiple threads from getting the same seed.
                    }
                    threadRandom = new Random(seed);
                }
                return threadRandom;
            }
        }

        /// <summary>
        /// Makes a sampling decision for the provided <paramref name="logEntry"/>.
        /// If sampled, keep the log. If not, discard the log.
        /// </summary>
        /// <param name="logEntry">The log entry used to make the sampling decision for.</param>
        /// <typeparam name="TState">The type of the log entry state.</typeparam>
        /// <returns><see langword="true" /> if the log record should be sampled; otherwise, <see langword="false" />.</returns>
        public override bool ShouldSample<TState>(in LogEntry<TState> logEntry)
        {
            // For RandomStrategy, we don't need to check the state as it doesn't use the state for sampling decisions
            return ThreadLocalRandom.NextDouble() < SampleRate;
        }
#endif
    }
}
