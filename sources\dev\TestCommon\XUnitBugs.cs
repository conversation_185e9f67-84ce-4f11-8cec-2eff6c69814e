// <copyright file="XUnitBugs.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>
using System;
using System.Collections.Generic;
using System.Text;
using Xunit.Abstractions;

namespace Microsoft.M365.Core.Telemetry.Hacks
{
    // <summary>
    // Convenient helpers to facilitate tests.  
    // </summary>
    internal static class XUnitBugs
    {
        // XUnit doesn't allow writing to console. It only allow writing to an
        // ITestOutputHelper object owned by test class, which then write to
        // console. Export the object so that we can write to console everywhere.
        public static ITestOutputHelper Output;
    }
}
