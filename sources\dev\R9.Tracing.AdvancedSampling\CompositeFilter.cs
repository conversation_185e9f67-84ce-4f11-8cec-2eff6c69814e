﻿// <copyright file="CompositeFilter.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System;
using System.Diagnostics;
using OpenTelemetry.Exporter.Filters;

namespace Microsoft.M365.Core.Telemetry.R9.Tracing.AdvancedSampling
{
    /// <summary>
    /// Filter for Post Trace Sampling
    /// </summary>
    public class CompositeFilter : BaseFilter<Activity>
    {
        private const string Description = "This filter is a composition of other filters that samples an activity whenever any one of the filters indicates that the activity should be kept";

        private readonly BaseFilter<Activity>[] filters;

        /// <summary>
        /// This filter is a composition of other filters that samples an activity whenever any one of the filters indicates that the activity should be kept";
        /// </summary>
        /// <param name="filters"></param>
        public CompositeFilter(BaseFilter<Activity>[] filters)
        {
            if (filters == null || filters.Length == 0)
            {
                throw new ArgumentNullException(nameof(filters));
            }

            this.filters = filters;
        }

        /// <inheritdoc/>
        public override string GetDescription()
        {
            return Description;
        }

        /// <inheritdoc/>
        public override bool ShouldFilter(Activity activity)
        {
            foreach (var filter in filters)
            {
                if (filter.ShouldFilter(activity))
                {
                    return true;
                }
            }
             
            return false;
        }
    }
}
