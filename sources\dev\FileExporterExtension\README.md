
# File Exporter

An exporter implementation to export `SecurityRecord` to file targets.

## Configurable Exporter Options

- `Directory`: Directory to store log files (will be created if not exists).
- `FileName`: Log file name.
- `SecurityRecordFormatter`: The formatter used to convert `SecurityRecord` to string.
- `MinLogLevel`: Logs matching this minimum log level will be exported to files.
- `EnableRotation`: Whether to enable rotation.
- `MaxSizeBeforeRotation`: The current log file will be rotated if it exceeds this size threshold.
- `RotationFileDateTimePattern`: The pattern used to format date time postfix in archived file names.
- `RetentionInterval`: Time interval to keep archived files.
- `RetentionCount`: Number of archived files to keep.
- `EnableBatchProcessing`: Whether to enable batch processing of log records.
- `MaxQueueSize`: Refer to [SecurityTelemetry `BatchExporter`](https://domoreexp.visualstudio.com/R9/_git/SDK?path=/src/Extensions/SecurityTelemetry/BatchExporter.cs&version=GBmain&_a=contents).
- `ScheduledDelay`: Refer to [SecurityTelemetry `BatchExporter`](https://domoreexp.visualstudio.com/R9/_git/SDK?path=/src/Extensions/SecurityTelemetry/BatchExporter.cs&version=GBmain&_a=contents).
- `ExporterTimeout`: Refer to [SecurityTelemetry `BatchExporter`](https://domoreexp.visualstudio.com/R9/_git/SDK?path=/src/Extensions/SecurityTelemetry/BatchExporter.cs&version=GBmain&_a=contents).
- `MaxExportBatchSize`: Refer to [SecurityTelemetry `BatchExporter`](https://domoreexp.visualstudio.com/R9/_git/SDK?path=/src/Extensions/SecurityTelemetry/BatchExporter.cs&version=GBmain&_a=contents).

## How to use

For SecurityRecord Exporter:

```c#
IHostBuilder hostbuilder = new HostBuilder()
        .ConfigureServices((context, services) =>
        {           
            _ = services.AddSecurityTelemetry(builder =>
            {
                _ = services.Configure<SecurityTelemetryOptions>(option =>
                {
                    option.PrepopulatedFields = new Dictionary<string, object>
                        {
                            { "ComputerName", "TestComputerName" },
                            { "ClusterId", "TestClusterId" }
                        };
                });
                _ = services.Configure<FileExporterOptions>(option =>
                {
                    option.FileName = "app.log";
                    option.RotationFileDateTimePattern = "yyyy-MM-dd";
                });
                _ = builder.WithExporter<ODLFileExporter>();
            });
        });
```

For NRT fallback to disk exporter:

```c#
FileExporterExtensions.CreatNRTFileExporter(new TextFileLoggerOptions { Directory = "FakeDirectory" }).Export("SingleLineString.");
```


## Rotation Samples

If the `FileName` is `app.log` and the specified `RotationFileDateTimePattern` is `yyyy/MM/dd`, the following files may be created after rotation:

```shell
app.log_2023/03/01
app.log_2023/03/02
app.log_2023/03/03
```

In case of there are conflicts (multiple rotation happened during the same period of time), ticks will be appended to the end of the file name:

```shell
app.log_2023/03/03
app.log_2023/03/03_01234567
app.log_2023/03/03_23156789
```
