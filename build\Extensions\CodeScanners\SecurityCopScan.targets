<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <VersionGeneration>$(EnlistmentRoot)\VersionGeneration</VersionGeneration>
    <SecurityCopExe>$(PkgMicrosoft_SecurityCop)\lib\net45\securitycop.exe</SecurityCopExe>
    <SecurityCopDictionary>$(EnlistmentRoot)\build\configuration\public\txt\SecurityCopDictionary.txt</SecurityCopDictionary>
    <SecurityCopBaseline>$(EnlistmentRoot)\build\configuration\public\txt\SecurityCopBaseline.txt</SecurityCopBaseline>
    <PowerShellInjectionHunterConfig>$(EnlistmentRoot)\build\configuration\public\xml\PowerShellInjectionHunterConfiguration.xml</PowerShellInjectionHunterConfig>
    <PowerShellInjectionHunterBaseline>$(EnlistmentRoot)\build\configuration\public\xml\PowerShellInjectionHunterBaseline.xml</PowerShellInjectionHunterBaseline>
    <PowerShellInjectionHunterExt>ps1,psm1,xoml</PowerShellInjectionHunterExt>
  </PropertyGroup>
  <ItemGroup>
    <PackageReference Include="Microsoft.SecurityCop" GeneratePathProperty="true"/>
    <ToplevelFolder Include="$(EnlistmentRoot)\.corext" />
    <ToplevelFolder Include="$(EnlistmentRoot)\build" />
    <ToplevelFolder Include="$(EnlistmentRoot)\private" />
    <ToplevelFolder Include="$(EnlistmentRoot)\public" />
    <ToplevelFolder Include="$(EnlistmentRoot)\sources" />
    <ToplevelFolder Include="$(EnlistmentRoot)\tools" />
    <WhitelistPath Include="$([System.IO.Directory]::GetDirectories('$(EnlistmentRoot)'))" Exclude="$(EnlistmentRoot)\sources">
      <RelativePath>$([MSBuild]::MakeRelative($(EnlistmentRoot), %(Identity)))</RelativePath>
    </WhitelistPath>
    <WhitelistPath Include="$([System.IO.Directory]::GetDirectories('$(EnlistmentRoot)\sources'))">
      <RelativePath>$([MSBuild]::MakeRelative($(EnlistmentRoot), %(Identity)))</RelativePath>
    </WhitelistPath>
    <QCustomInput Include="@(ToplevelFolder -> '%(Identity)\**')" Exclude="$(MSBuildProjectDirectory)\**\*.*">
      <Visible>false</Visible>
    </QCustomInput>
    <QCustomInput Include="$(SecurityCopDictionary)">
      <Visible>false</Visible>
    </QCustomInput>
    <QCustomInput Include="$(SecurityCopBaseline)">
      <Visible>false</Visible>
    </QCustomInput>
    <QCustomInput Include="$(PowerShellInjectionHunterConfig)">
      <Visible>false</Visible>
    </QCustomInput>
    <QCustomInput Include="$(PowerShellInjectionHunterBaseline)">
      <Visible>false</Visible>
    </QCustomInput>
  </ItemGroup>
  <Target Name="RunSecurityCop" BeforeTargets="Build" Inputs="@(QCustomInput)" Outputs="$(SecurityCopOutput)">
    <Error
      Condition=" !Exists('$(SecurityCopExe)') "
      Text="Nuget package directory '$(SecurityCopExe)' does not exist." />
    <!-- Ensure output directory is available and clean. -->
    <MakeDir Directories="$(OutputPath)" />
    <Delete Files="$(SecurityCopOutput)" />
    <!-- Create augmented SecurityCop dictionary. -->
    <Message Text="Whitelist path: %(WhitelistGroup.FilePath)" />
    <Copy SourceFiles="$(SecurityCopDictionary)" DestinationFiles="%(WhitelistGroup.SecurityCopDictionary)" />
    <WriteLinesToFile File="%(WhitelistGroup.SecurityCopDictionary)" Lines="%0D%0A%0D%0A%3B Skip folders scanned by other SecurityCop MSBuild projects" />
    <WriteLinesToFile File="%(WhitelistGroup.SecurityCopDictionary)" Lines="%(WhitelistGroup.FilePath)" />
    <!-- Run SecurityCop to scan enlistment. -->
    <Exec Command="&quot;$(SecurityCopExe)&quot; scan -path $(EnlistmentRoot) -dictionary %(WhitelistGroup.SecurityCopDictionary) -baselineSnapshot $(SecurityCopBaseline) -violations $(SecurityCopOutput)"
          IgnoreExitCode="true">
      <Output TaskParameter="ExitCode" PropertyName="ErrorCode"/>
    </Exec>
    <ReadLinesFromFile File="$(SecurityCopOutput)" >
      <Output TaskParameter="Lines" ItemName="ScanErrorMessage"/>
    </ReadLinesFromFile>
    <!-- Show violations found by SecurityCop. -->
    <Error
      Condition=" '@(ScanErrorMessage)' != '' "
      Text="SecurityCop found potential secrets (see below). For information on how to handle this, visit http://aka.ms/ExNoPasswordsInSource. See file '$(SecurityCopOutput)' for a full list of secrets. Found potential secrets: @(ScanErrorMessage -> '%(Identity)', ' ')" />
    <!-- No violations found by SecurityCop, but it exited with error. -->
    <Error
      Condition=" '$(ErrorCode)' != '0' "
      Text="SecurityCop exited with error code $(ErrorCode)." />
    <WriteLinesToFile File="$(SecurityCopOutput)" Lines="No new violations found." />
  </Target>
  <Target Name="RunSecurityCopForPowerShell" Inputs="@(QCustomInput)" Outputs="$(SecurityCopOutputForPowerShell)">
    <Error
      Condition=" !Exists('$(SecurityCopExe)') "
      Text="Nuget package directory '$(PkgMicrosoft_SecurityCop)' does not exist." />
    <!-- Ensure output directory is available. -->
    <MakeDir Directories="$(OutputPath)" />
    <Delete Files="$(SecurityCopOutputForPowerShell);$(SecurityCopErrorForPowerShell)" />
    <!-- Write paths of all PowerShell scripts in a text file. -->
    <Exec Command="dir /b /s *.ps1 *.psm1 *.xoml" WorkingDirectory="$(EnlistmentRoot)" ConsoleToMSBuild="true" >
      <Output TaskParameter="ConsoleOutput" ItemName="PowerShellScripts" />
    </Exec>
    <ItemGroup>
      <FilteredPowerShellScripts Include="@(PowerShellScripts)"
                                 Condition="!$([System.Text.RegularExpressions.Regex]::IsMatch('%(Identity)', '$(FilePathExclusionPattern)', System.Text.RegularExpressions.RegexOptions.IgnoreCase))" />
    </ItemGroup>
    <WriteLinesToFile File="$(PowerShellScriptList)" Lines="@(FilteredPowerShellScripts)" Overwrite="true" />
    <WriteLinesToFile File="$(PowerShellScriptList)" Lines=" " Overwrite="false" Condition="'@(FilteredPowerShellScripts)' == ''"/>
    <!-- Run SecurityCop to scan PowerShell scripts. -->
    <Exec Command="&quot;$(SecurityCopExe)&quot; hunt -pathsFile $(PowerShellScriptList) -ext $(PowerShellInjectionHunterExt) -config $(PowerShellInjectionHunterConfig) -baselineSnapshot $(PowerShellInjectionHunterBaseline) -violations $(SecurityCopErrorForPowerShell)"
          IgnoreExitCode="true" WorkingDirectory="$(EnlistmentRoot)">
      <Output TaskParameter="ExitCode" PropertyName="ErrorCode"/>
    </Exec>
    <ReadLinesFromFile File="$(SecurityCopErrorForPowerShell)" >
      <Output TaskParameter="Lines" ItemName="ScanErrorMessage"/>
    </ReadLinesFromFile>
    <!-- Show violations found by SecurityCop. -->
    <Error
      Condition=" '@(ScanErrorMessage)' != '' "
      Text="SecurityCop found potential PowerShell injection vulnerabilities (see below). For information on how to handle this, visit http://aka.ms/psinjection. See file '$(SecurityCopErrorForPowerShell)' for a full list of vulnerabilities. Found potential vulnerabilities: @(ScanErrorMessage -> '%(Identity)', ' ')" />
    <!-- No violations found by SecurityCop, but it exited with error. -->
    <Error
      Condition=" '$(ErrorCode)' != '0' "
      Text="SecurityCop exited with error code $(ErrorCode)." />
    <WriteLinesToFile File="$(SecurityCopOutputForPowerShell)" Lines="No new violations found." />
  </Target>
</Project>
