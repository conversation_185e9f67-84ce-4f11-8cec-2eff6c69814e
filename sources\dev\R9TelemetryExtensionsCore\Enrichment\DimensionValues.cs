﻿// <copyright file="DimensionValues.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.Globalization;
using System.Text;
using System.Threading;
#if !NETFRAMEWORK
using Microsoft.M365.Core.Portable.Registry;
#else
using Microsoft.Win32;
#endif

namespace Microsoft.M365.Core.Telemetry.Enrichment
{
    /// <summary>
    /// Helper class for getting dimension values from windows registry.
    /// </summary>
    internal static class DimensionValues
    {
        private const string VariantConfigurationKeyBasePath = @"SOFTWARE\Microsoft\ExchangeServer\v15\VariantConfiguration";

        /// <summary>
        /// Registry key path for machine provisioning info
        /// </summary>
        private const string DiagnosticsTopologyKeyPath = @"SOFTWARE\Microsoft\ExchangeServer\V15\Diagnostics\Topology";

        /// <summary>
        /// Registry key path for Exchange Setup info
        /// </summary>
        private const string SetupConfigurationKeyBasePath = @"SOFTWARE\Microsoft\ExchangeServer\v15\Setup";

        private const string Unknown = "Unknown";

        /// <summary>
        /// Registry value name for IsLive
        /// </summary>
        private const string IsLiveValueName = "IsLive";

        /// <summary>
        /// DeployRingWithSipRotationName
        /// </summary>
        private const string DeployRingWithSipRotationName = "DeployRingWithSipRotation";

        /// <summary>
        /// To check whether running in cosmic model D2 mode.
        /// </summary>
        private static readonly bool IsCosmicModelD2ModeOn = !string.IsNullOrEmpty(Environment.GetEnvironmentVariable("COSMIC_PODNAME"));

        // Current MachineProvisioningState value. Default Live.
        private static string maintenanceModeKeyValue = MachineProvisioningStateLive;

        /// <summary>
        /// Current EnablePassiveECSAADAuth value. Default false.
        /// </summary>
        private static string enablePassiveECSAADAuthValue = "false";

        /// <summary>
        /// Region Mapping for special forests
        /// </summary>
        private static readonly Dictionary<string, string> RegionMappingForSpecialForests = new Dictionary<string, string>(StringComparer.OrdinalIgnoreCase)
        {
            { "prod", "NAM" },
            { "prdmgt01", "NAM" }
        };

        private static IRegistryKey registryKey = new WindowsRegistryKey();

        /// <summary>
        /// MsiProductMajor value name in registry
        /// </summary>
        internal const string ProductMajor = @"MsiProductMajor";

        /// <summary>
        /// MsiProductMinor value name in registry
        /// </summary>
        internal const string ProductMinor = @"MsiProductMinor";

        /// <summary>
        /// MsiBuildMajor value name in registry
        /// </summary>
        internal const string BuildMajor = @"MsiBuildMajor";

        /// <summary>
        /// MsiBuildMinor value name in registry
        /// </summary>
        internal const string BuildMinor = @"MsiBuildMinor";

        /// <summary>
        /// AvailabilityGroup is called "Dag" in registry.
        /// </summary>
        internal const string AvailabilityGroupRegistryName = "Dag";

        /// <summary>
        /// EnablePassiveECSAADAuthName
        /// </summary>
        internal const string EnablePassiveECSAADAuthName = "EnablePassiveECSAADAuth";

        /// <summary>
        /// MachineProvisioningState dimension values. Possible values of maintenanceModeKeyValue.
        /// </summary>
        internal const string MachineProvisioningStateLive = "Live";

        /// <summary>
        /// MachineProvisioningStateMaintenance
        /// </summary>
        internal const string MachineProvisioningStateMaintenance = "Maintenance";

        /// <summary>
        /// Registry value name for FastTrainVersion
        /// </summary>
        internal const string FastTrainVersionValueName = "FastTrainVersion";

        /// <summary>
        /// DeployRing
        /// </summary>
        internal static string DeployRing { get; private set; } = Unknown;

        /// <summary>
        /// Role
        /// </summary>
        internal static string Role { get; private set; } = Unknown;

        /// <summary>
        /// Forest
        /// </summary>
        internal static string Forest { get; private set; } = Unknown;

        /// <summary>
        /// Region
        /// </summary>
        internal static string Region { get; private set; } = Unknown;

        /// <summary>
        /// AvailabilityGroup
        /// </summary>
        internal static string AvailabilityGroup { get; private set; } = Unknown;

        /// <summary>
        /// Machine
        /// </summary>
        internal static string Machine { get; private set; } = Unknown;

        /// <summary>
        /// BuildVersion
        /// </summary>
        internal static string BuildVersion { get; private set; } = Unknown;

        /// <summary>
        /// Service
        /// </summary>
        internal static string Service { get; private set; } = Unknown;

        /// <summary>
        /// EnablePassiveECSAADAuth
        /// </summary>
        internal static string EnablePassiveECSAADAuth
        {
            get
            {
                RegWatcher.Value.KeyChanged();
                return enablePassiveECSAADAuthValue;
            }
        }

        /// <summary>
        /// Check whethr current machine is a TDS.
        /// </summary>
        internal static string IsTDS { get; private set; } = Unknown;

        static DimensionValues()
        {
            InternalSetDimensionValues();
            InternalSetECSSwitch();
        }

        /// <summary>
        /// Set dimension values.
        /// </summary>
        /// <param name="registryKey">Fake IRegistryKey.</param>
        /// TODO(jiayiwang): Restrict visibility in this project.
        internal static void InternalSetRegistryKey(IRegistryKey registryKey)
        {
            DimensionValues.registryKey = registryKey;
            DeployRing = Unknown;
            Role = Unknown;
            Forest = Unknown;
            Region = Unknown;
            AvailabilityGroup = Unknown;
            Service = Unknown;
            Machine = Unknown;
            BuildVersion = Unknown;
            IsTDS = Unknown;
            maintenanceModeKeyValue = MachineProvisioningStateLive;
            enablePassiveECSAADAuthValue = "false";

            InternalSetDimensionValues();
            InternalSetECSSwitch();
        }

        private static void InternalSetECSSwitch()
        {
            IRegistryKey diagnosticsTopologyKey = registryKey.OpenSubKey(DiagnosticsTopologyKeyPath);
            if (diagnosticsTopologyKey == null)
            {
                // TODO(jiayiwang): add error log.
                return;
            }
            enablePassiveECSAADAuthValue = GetSanitizedRegistryValue(diagnosticsTopologyKey, EnablePassiveECSAADAuthName);
        }

        private static void InternalSetDimensionValues()
        {
            IRegistryKey variantConfigKey = registryKey.OpenSubKey(VariantConfigurationKeyBasePath);
            if (variantConfigKey == null)
            {
                // TODO(jiayiwang): add error log.
                return;
            }

            DeployRing = GetSanitizedRegistryValue(variantConfigKey, B2PassiveEnricherDimensions.DeployRing);

            var deployRingWithSipRotationValue = GetSanitizedRegistryValue(variantConfigKey, DeployRingWithSipRotationName);
            if (deployRingWithSipRotationValue != null && !string.Equals(deployRingWithSipRotationValue, Unknown, StringComparison.OrdinalIgnoreCase) && deployRingWithSipRotationValue != DeployRing)
            {
                DeployRing = deployRingWithSipRotationValue;
            }

            Role = GetSanitizedRegistryValue(variantConfigKey, B2PassiveEnricherDimensions.Role);
            Forest = GetSanitizedRegistryValue(variantConfigKey, B2PassiveEnricherDimensions.Forest);
            Region = GetSanitizedRegistryValue(variantConfigKey, B2PassiveEnricherDimensions.Region);
            AvailabilityGroup = GetSanitizedRegistryValue(variantConfigKey, AvailabilityGroupRegistryName);
            Service = GetSanitizedRegistryValue(variantConfigKey, "Service");
            Machine = Environment.MachineName;
            BuildVersion = GetBuildVersion();
            IsTDS = GetSanitizedRegistryValue(variantConfigKey, "Test");

            if (Region.Equals(Unknown, StringComparison.OrdinalIgnoreCase))
            {
                if (Forest != null && Forest.Length > 3 && !Forest.Equals(Unknown, StringComparison.OrdinalIgnoreCase))
                {
                    if (RegionMappingForSpecialForests.ContainsKey(Forest))
                    {
                        // Special case to set the Region name for the forests which don't follow the naming convention
                        Region = RegionMappingForSpecialForests[Forest];
                    }
                    else
                    {
                        // Generally first three letters of a forest represent Region.
                        Region = Forest.Substring(0, 3);
                    }
                }
            }
        }

        private static string GetBuildVersion()
        {
            IRegistryKey variantConfigKey = registryKey.OpenSubKey(SetupConfigurationKeyBasePath);

            int? productMajor = GetSanitizedRegistryIntValue(variantConfigKey, ProductMajor);
            int? productMinor = GetSanitizedRegistryIntValue(variantConfigKey, ProductMinor);
            int? buildMajor = GetSanitizedRegistryIntValue(variantConfigKey, BuildMajor);
            int? buildMinor = GetSanitizedRegistryIntValue(variantConfigKey, BuildMinor);
            string fastTrainVersion = GetSanitizedRegistryValue(variantConfigKey, FastTrainVersionValueName);
            string buildVersion = "0.00.0000.000";

            if (productMajor != null && productMinor != null && buildMajor != null && buildMinor != null)
            {
                StringBuilder sb = new StringBuilder(64);
                sb.AppendFormat(CultureInfo.InvariantCulture, "{0}.{1}.{2}.{3}",
                    productMajor,
                    productMinor.Value.ToString("D2", CultureInfo.InvariantCulture),
                    buildMajor.Value.ToString("D4", CultureInfo.InvariantCulture),
                    buildMinor.Value.ToString("D3", CultureInfo.InvariantCulture));
                buildVersion = sb.ToString();
            }

            if (string.IsNullOrWhiteSpace(fastTrainVersion) || fastTrainVersion == Unknown || string.Compare(fastTrainVersion, buildVersion, StringComparison.CurrentCultureIgnoreCase) < 0)
            {
                return buildVersion;
            }

            return fastTrainVersion;
        }

        /// <summary>
        /// Get the value from <paramref name="variantConfigKey"/>. If missing or empty, return <see cref="Unknown"/>
        /// </summary>
        /// <param name="registryKey">Registry key</param>
        /// <param name="keyName">Registry key name</param>
        /// <returns>Sanitized registry key</returns>
        private static string GetSanitizedRegistryValue(IRegistryKey registryKey, string keyName)
        {
            // TODO(jiayiwang): Gate null registryKey earlier.
            string value = registryKey?.GetValue(keyName) as string;
            return string.IsNullOrWhiteSpace(value) ? Unknown : value;
        }

        /// <summary>
        /// Get the value from <paramref name="variantConfigKey"/>. If missing or empty, return <see cref="Unknown"/>
        /// </summary>
        /// <param name="registryKey">Registry key</param>
        /// <param name="keyName">Registry key name</param>
        /// <returns>Sanitized registry key</returns>
        private static int? GetSanitizedRegistryIntValue(IRegistryKey registryKey, string keyName)
        {
            // TODO(jiayiwang): Gate null registryKey earlier.
            int? value = registryKey?.GetValue(keyName) as int?;
            return value;
        }

        /// <summary>
        /// MachineState value to indicate if this machine is live or in maintenancemode
        /// </summary>
        public static string MachineProvisioningState
        {
            get
            {
                if (RegWatcher.IsValueCreated == false)
                {
                    RegWatcher.Value.KeyChanged();
                }

                return maintenanceModeKeyValue;
            }
        }

        /// <summary>
        /// Lazy Registry Watcher instance to start a thread to watch changes under a registry key
        /// </summary>
        private static readonly Lazy<IRegistryWatcher> RegWatcher = new Lazy<IRegistryWatcher>(
        () =>
        {
            IRegistryKey topologyConfigKey = registryKey.OpenSubKey(DiagnosticsTopologyKeyPath);

            IRegistryWatcher rw = IsCosmicModelD2ModeOn ?
                NullRegistryWatcher.Instance :
                new RegistryWatcher(RegistryHive.LocalMachine, DiagnosticsTopologyKeyPath, true, RegistryChangeNotificationFilter.ValueChange);
            rw.KeyChanged = () =>
            {
                bool isLive = true;
                string isLiveValueText = GetSanitizedRegistryValue(topologyConfigKey, IsLiveValueName);
                isLiveValueText = (isLiveValueText == Unknown) ? "true" : isLiveValueText;
                bool.TryParse(isLiveValueText, out isLive);
                if (isLive)
                {
                    Interlocked.Exchange(ref maintenanceModeKeyValue, MachineProvisioningStateLive);
                }
                else
                {
                    Interlocked.Exchange(ref maintenanceModeKeyValue, MachineProvisioningStateMaintenance);
                }

                string enablePassiveECSAADAuthText = GetSanitizedRegistryValue(topologyConfigKey, EnablePassiveECSAADAuthName);
                enablePassiveECSAADAuthText = enablePassiveECSAADAuthText.Equals("true", StringComparison.OrdinalIgnoreCase) ? "true" : "false";
                if (!enablePassiveECSAADAuthText.Equals(enablePassiveECSAADAuthValue, StringComparison.OrdinalIgnoreCase))
                {
                    Interlocked.Exchange(ref enablePassiveECSAADAuthValue, enablePassiveECSAADAuthText);
                }
            };

            rw.Start();
            return rw;
        });
    }
}