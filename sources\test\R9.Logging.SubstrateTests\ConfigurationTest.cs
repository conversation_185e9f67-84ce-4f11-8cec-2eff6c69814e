﻿// <copyright file="ConfigurationTest.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

extern alias InternalizedDependencies;

using System;
using System.Collections.Generic;
using System.IO;
using System.Reflection;
using InternalizedDependencies::Microsoft.Extensions.Configuration;
using InternalizedDependencies::Microsoft.Extensions.Configuration.Ecs;
using InternalizedDependencies::Microsoft.Skype.ECS.Client;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Hosting;
using Microsoft.M365.Core.Telemetry.R9.Logging.Substrate.Configuration;
using Xunit;

namespace Microsoft.M365.Core.Telemetry.R9.Logging.Substrate.TestClass
{
    public sealed class ConfigurationHelperTest : IDisposable
    {
        private readonly string defaultConfigPath = Path.Combine(Directory.GetCurrentDirectory(), "ECSDefaultConfig", "ECSDefaultConfig.json");
        private readonly string appSettingsPath = Path.Combine(Directory.GetCurrentDirectory(), "appsettings.json");

        public void Dispose()
        {
            Clean();
        }

        [Fact]
        public void RegisterConfiguration_WithValidParameters_ShouldReturnHostBuilder()
        {
            var hostBuilder = new HostBuilder();

            var result = hostBuilder.ConfigureAppConfiguration((context, config) =>
            {
                config.SetBasePath(Directory.GetCurrentDirectory());
                config.AddJsonFile("appsettings.json", optional: false, reloadOnChange: true);
            }).RegisterConfiguration();

            var host = result.Build();
            Assert.NotNull(host);
            Assert.IsAssignableFrom<IHost>(host);
        }

        [Fact]
        public void RegisterConfiguration_WithUseDefaultOnly_ShouldReturnHostBuilder()
        {
            var hostBuilder = new HostBuilder();

            var result = hostBuilder.ConfigureAppConfiguration((context, config) =>
            {
                config.SetBasePath(Directory.GetCurrentDirectory());
                config.AddJsonFile("appsettings.json", optional: false, reloadOnChange: true);
            }).RegisterConfiguration();

            var host = result.Build();
            Assert.NotNull(host);
            Assert.IsAssignableFrom<IHost>(host);
        }

        [Fact]
        public void RegisterConfiguration_WithEmptyDefaultConfigBasePath_ShouldThrowArgumentException()
        {
            var hostBuilder = new HostBuilder();
            string defaultConfigPath = string.Empty;

            Assert.Throws<FileNotFoundException>(() => hostBuilder.ConfigureAppConfiguration((context, config) =>
            {
                config.SetBasePath(Directory.GetCurrentDirectory());
                config.AddJsonFile("appsettings.json", optional: false, reloadOnChange: true);
            }).RegisterConfiguration(defaultConfigPath, true).Build());
        }

        [Fact]
        public void LoadConfiguration_AppSettingsFileNotFound_ThrowsFileNotFoundException()
        {
            var invalidAppSettingsPath = "invalid/path/to/appsettings.json";

            Assert.Throws<FileNotFoundException>(() => ConfigurationHelper.LoadConfiguration(invalidAppSettingsPath));
        }

        [Fact]
        public void LoadConfiguration_ValidPaths_ReturnsIConfiguration()
        {
            var configuration = ConfigurationHelper.LoadConfiguration(appSettingsPath);

            Assert.NotNull(configuration);
            Assert.Equal("EtwSession=TestSeesion", configuration["SubstrateLogging:GenevaExporter:ConnectionString"]);
        }

        [Fact]
        public void LoadConfiguration_UseDefaultOnly_ReturnsIConfiguration()
        {
            var configuration = ConfigurationHelper.LoadConfiguration(appSettingsPath);

            Assert.NotNull(configuration);
        }

        [Fact]
        public void ConfigureECSSource_NullConfiguration_ThrowsInvalidOperationException()
        {
            var source = new EcsConfigurationSource();
            var method = typeof(EcsConfigurationExtensions).GetMethod("ConfigureECSSource", BindingFlags.NonPublic | BindingFlags.Static);
            try
            {
                method.Invoke(null, new object[] { source, defaultConfigPath, false, ECSType.CentralizedInt });
            }
            catch (TargetInvocationException ex)
            {
                Assert.IsType<InvalidOperationException>(ex.InnerException);
            }
        }

        [Fact]
        public void ConfigureECSSource_MissingServiceName_ThrowsArgumentException()
        {
            var source = new EcsConfigurationSource
            {
                Configuration = new ECSClientConfiguration
                {
                    DefaultRequestIdentifiers = { }
                }
            };
            var method = typeof(EcsConfigurationExtensions).GetMethod("ConfigureECSSource", BindingFlags.NonPublic | BindingFlags.Static);
            try
            {
                method.Invoke(null, new object[] { source, defaultConfigPath, false, ECSType.CentralizedInt });
            }
            catch (TargetInvocationException ex)
            {
                Assert.IsType<ArgumentException>(ex.InnerException);
            }
        }

        [Fact]
        public void ValidateAndDetermineECSType_NullConfiguration_ThrowsArgumentNullException()
        {
            Assert.Throws<ArgumentNullException>(() => EcsParameterValidatorExtension.ValidateAndDetermineECSType(null));
        }

        [Fact]
        public void ValidateAndDetermineECSType_MissingECSParametersSection_ThrowsArgumentException()
        {
            var configBuilder = new ConfigurationBuilder()
                .AddInMemoryCollection(new Dictionary<string, string?>());
            var config = configBuilder.Build();

            Assert.Throws<ArgumentException>(() => config.ValidateAndDetermineECSType());
        }

        [Fact]
        public void ValidateAndDetermineECSType_WithAllRequiredFields_ReturnsCentralizedProdType()
        {
            var configBuilder = new ConfigurationBuilder()
                .AddInMemoryCollection(new Dictionary<string, string?>
                {
                    ["ECSParameters:ECSIdentifiers:ServiceName"] = "TestService",
                    ["ECSParameters:Client"] = "UnifiedTelemetry",
                    ["ECSParameters:Agents:0"] = "Log",
                    ["ECSParameters:EnvironmentType"] = "Production"
                });
            var config = configBuilder.Build();

            var result = config.ValidateAndDetermineECSType();

            Assert.Equal(ECSType.CentralizedProd, result);

            IConfiguration validatedConfig = configBuilder.AddValidatedEcsConfiguration(config, defaultConfigPath).Build();

            Assert.NotNull(validatedConfig);
        }

        [Fact]
        public void ValidateAndDetermineECSType_WithIntegrationEnvironment_ReturnsCentralizedIntType()
        {
            var configBuilder = new ConfigurationBuilder()
                .AddInMemoryCollection(new Dictionary<string, string?>
                {
                    ["ECSParameters:ECSIdentifiers:ServiceName"] = "TestService",
                    ["ECSParameters:Client"] = "UnifiedTelemetry",
                    ["ECSParameters:Agents:0"] = "Log",
                    ["ECSParameters:EnvironmentType"] = "Integration"
                });
            var config = configBuilder.Build();

            var result = config.ValidateAndDetermineECSType();

            Assert.Equal(ECSType.CentralizedInt, result);
        }

        [Fact]
        public void ValidateAndDetermineECSType_WithCustomClientAndAgents_ReturnsCustomTypeAndRequiresAuth()
        {
            var configBuilder = new ConfigurationBuilder()
                .AddInMemoryCollection(new Dictionary<string, string?>
                {
                    ["ECSParameters:ECSIdentifiers:ServiceName"] = "TestService",
                    ["ECSParameters:Client"] = "CustomClient",
                    ["ECSParameters:Agents:0"] = "CustomAgent",
                    ["ECSParameters:EnvironmentType"] = "Production",
                    ["ECSParameters:AuthenticationOption:Type"] = "Test" // Auth settings must be present
                });
            var config = configBuilder.Build();

            var result = config.ValidateAndDetermineECSType();

            Assert.Equal(ECSType.Custom, result);

            Assert.Throws<ArgumentException>(() => configBuilder.AddValidatedEcsConfiguration(config, defaultConfigPath).Build());
        }

        [Fact]
        public void ValidateAndDetermineECSType_WithCustomClientAndMissingAuth_ThrowsArgumentException()
        {
            var configBuilder = new ConfigurationBuilder()
                .AddInMemoryCollection(new Dictionary<string, string?>
                {
                    ["ECSParameters:ECSIdentifiers:ServiceName"] = "TestService",
                    ["ECSParameters:Client"] = "CustomClient",
                    ["ECSParameters:Agents:0"] = "CustomAgent",
                    ["ECSParameters:EnvironmentType"] = "Production"

                    // Missing auth settings
                });
            var config = configBuilder.Build();

            Assert.Throws<ArgumentException>(() => config.ValidateAndDetermineECSType());
        }

        [Fact]
        public void ValidateAndDetermineECSType_WithIncompleteCustomization_ThrowsArgumentException()
        {
            var configBuilder = new ConfigurationBuilder()
                .AddInMemoryCollection(new Dictionary<string, string?>
                {
                    ["ECSParameters:ECSIdentifiers:ServiceName"] = "TestService",
                    ["ECSParameters:Client"] = "CustomClient",

                    // Missing Agents
                    // Missing EnvironmentType
                });
            var config = configBuilder.Build();

            Assert.Throws<ArgumentException>(() => config.ValidateAndDetermineECSType());
        }

        [Fact]
        public void ValidateAndDetermineECSType_WithInvalidEnvironmentType_ThrowsArgumentException()
        {
            var configBuilder = new ConfigurationBuilder()
                .AddInMemoryCollection(new Dictionary<string, string?>
                {
                    ["ECSParameters:ECSIdentifiers:ServiceName"] = "TestService",
                    ["ECSParameters:Client"] = "CustomClient",
                    ["ECSParameters:Agents:0"] = "CustomAgent",
                    ["ECSParameters:EnvironmentType"] = "InvalidEnvironment" // Not "Production" or "Integration"
                });
            var config = configBuilder.Build();

            Assert.Throws<ArgumentException>(() => config.ValidateAndDetermineECSType());
        }

        [Fact]
        public void ValidateAndDetermineECSType_WithMissingIdentifier_ThrowsArgumentException()
        {
            var configBuilder = new ConfigurationBuilder()
                .AddInMemoryCollection(new Dictionary<string, string?>
                {
                    ["ECSParameters:Client"] = "UnifiedTelemetry",
                    ["ECSParameters:Agents:0"] = "Logs",
                    ["ECSParameters:EnvironmentType"] = "Integration"
                });
            var config = configBuilder.Build();

            Assert.Throws<ArgumentException>(() => config.ValidateAndDetermineECSType());
        }

        [Fact]
        public void AddValidatedEcsConfiguration_NullBuilder_ThrowsArgumentNullException()
        {
            var validationSource = new ConfigurationBuilder().Build();

            Assert.Throws<ArgumentNullException>(() =>
                EcsConfigurationExtensions.AddValidatedEcsConfiguration(null, validationSource, defaultConfigPath));
        }

        [Fact]
        public void AddValidatedEcsConfiguration_NullValidationSource_ThrowsArgumentNullException()
        {
            var builder = new ConfigurationBuilder();

            Assert.Throws<ArgumentNullException>(() =>
                builder.AddValidatedEcsConfiguration(null, defaultConfigPath));
        }

        [Fact]
        public void EcsParametersOptions_DefaultProperties_InitializedCorrectly()
        {
            var options = new EcsParametersOptions();

            Assert.NotNull(options.ECSIdentifiers);
            Assert.Empty(options.EnvironmentType);
            Assert.Empty(options.Client);
            Assert.NotNull(options.Agents);
            Assert.Empty(options.Agents);
        }

        [Fact]
        public void EcsIdentifiersOptions_DefaultProperties_InitializedCorrectly()
        {
            var options = new EcsIdentifiersOptions();

            Assert.Empty(options.ServiceName);
        }

        private void Clean()
        {
            if (Directory.Exists("SubstrateR9ExtensionECSCache"))
            {
                Directory.Delete("SubstrateR9ExtensionECSCache", true);
            }
        }
    }
}
