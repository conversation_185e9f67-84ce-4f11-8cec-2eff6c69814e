<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" />

  <PropertyGroup>
    <TargetFrameworkVersion>v4.5</TargetFrameworkVersion>
    <GitHooksPath>$(EnlistmentRoot)\.git\hooks</GitHooksPath>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.Azure.CredentialScanner" />
    <PackageReference Include="Exchange.GitHooks" />
  </ItemGroup>

  <Import Project="$(MSBuildToolsPath)\Microsoft.Common.targets" />

  <ItemGroup>  
    <CredScanFiles Include="$(NuGetPackageRoot)\microsoft.azure.credentialscanner\1.0.23\tools\**\*.*"/>
    <GitHookFiles Include="$(NuGetPackageRoot)\Exchange.GitHooks\1.0.13\hooks\**\*.*"/>
    <Content Include="CredScan.ps1" />
    <Content Include="pre-push" />
  </ItemGroup>

  <Target Name="Uninstall">
    <Message Text="Clearing existing git hooks in '$(GitHooksPath)'..." Importance="high" />
    <RemoveDir Directories="$(GitHooksPath)" />
  </Target>

  <Target Name="Install" DependsOnTargets="Uninstall">
    <Message Text="Clearing existing git hooks in '$(GitHooksPath)'..." Importance="high" />
    <RemoveDir Directories="$(GitHooksPath)" />
    <Message Text="Copying CredScan tools to '$(GitHooksPath)'..." Importance="high" />
    <Copy  
      SourceFiles="@(CredScanFiles)"  
      DestinationFiles="@(CredScanFiles->'$(GitHooksPath)\credscan\%(RecursiveDir)%(Filename)%(Extension)')" />
    <Message Text="Copying GitHooks content to '$(GitHooksPath)'..." Importance="high" />
    <Copy 
      SourceFiles="@(Content)"  
      DestinationFolder="$(GitHooksPath)" />
    <Copy  
      SourceFiles="@(GitHookFiles)"  
      DestinationFiles="@(GitHookFiles->'$(GitHooksPath)\%(RecursiveDir)%(Filename)%(Extension)')" Condition="'%(Filename)%(Extension)' != 'CredScan.ps1'" />
	<Message Text="Git hooks successfully installed!" Importance="high" />
  </Target>
</Project>