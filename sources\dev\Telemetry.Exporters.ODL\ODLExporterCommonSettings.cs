﻿// <copyright file="ODLExporterCommonSettings.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

namespace Microsoft.R9.Extensions.Telemetry.Exporters.Base
{
    /// <summary>
    /// Common settings for NRTLoggingSdk.
    /// </summary>
    public static class ODLExporterCommonSettings
    {
        /// <summary>
        /// Event source name for odl.
        /// </summary>
        public const string EventSourceName = "NRTLoggingSdk";

        /// <summary>
        /// ODL service name.
        /// </summary>
        public const string OdlServiceName = "MSOfficeDataLoader";

        /// <summary>
        /// Event source name for exporter's internal log.
        /// </summary>
        public const string InternalDebugEventSourceName = "OfficeDataLoader";

        /// <summary>
        /// version for trace exporter to serialize activity.
        /// </summary>
        public const string ActivitySchemaVersion = "1.0";
    }
}