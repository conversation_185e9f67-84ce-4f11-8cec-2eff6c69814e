﻿// <copyright file="ServiceHelper.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System.ServiceProcess;

namespace Microsoft.R9.Extensions.Telemetry.Exporters.Base
{
    /// <summary>
    /// Service helper class
    /// </summary>
    public static class ServiceHelper
    {
        /// <summary>
        /// Get a windows service state
        /// </summary>
        /// <param name="serviceName">Service name</param>
        /// <returns>Service state</returns>
        public static ServiceControllerStatus GetServiceStatus(string serviceName)
        {
            // TODO: need to add the actual logic to get status of the ODL instance. 
            return ServiceControllerStatus.Running;
        }

        //TODO: will monitor the service status and try to call back to ServiceDenpendentLogger to do the fallback logic.
    }
}