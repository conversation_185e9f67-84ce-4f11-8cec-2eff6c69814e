﻿// <copyright file="PostTraceController.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System.Diagnostics;
using Microsoft.AspNetCore.Mvc;
using Microsoft.M365.Core.Telemetry.R9.Tracing.AdvancedSampling;
using Newtonsoft.Json;

namespace Microsoft.M365.Core.Tracing.AdvancedSamplingClient
{
    /// <summary>
    /// PostTraceController
    /// </summary>
    [ApiController]
    [Route("api/")]
    public class PostTraceController : ControllerBase
    {
        /// <summary>
        /// client
        /// </summary>
        /// <returns></returns>
        [HttpGet("postclient")]
        public async Task<IActionResult> Client()
        {
            Activity.Current?.SetTag("SampleTag", "True");
            var url = "https://localhost:5001/api/postinternal";

            var client = new HttpClient();
            var response = await client.GetAsync(url).ConfigureAwait(true);
            Console.ForegroundColor = ConsoleColor.Yellow;
            Console.WriteLine($"*****************PostTraceController::Client::PostInternal Response: {JsonConvert.SerializeObject(response)}**************");
            Console.WriteLine($"*****************PostTraceController::Client::PostClient Baggage: {Activity.Current?.GetBaggageItem(Constants.PostTraceBaggageName)}**************");
            Console.ForegroundColor = ConsoleColor.White;
            return Ok("PostTraceController::Client");
        }

        /// <summary>
        /// internal
        /// </summary>
        /// <returns></returns>
        [HttpGet("postinternal")]
        public async Task<IActionResult> Internal()
        {
            var url = "https://localhost:5002/api/postserver";

            var client = new HttpClient();
            var response = await client.GetAsync(url).ConfigureAwait(true);
            Console.ForegroundColor = ConsoleColor.Yellow;
            Console.WriteLine($"*****************PostTraceController::Internal::PostServer Response: {JsonConvert.SerializeObject(response)}**************");
            Console.WriteLine($"*****************PostTraceController::Internal::PostInternal Baggage: {Activity.Current?.GetBaggageItem(Constants.PostTraceBaggageName)}**************");
            Console.ForegroundColor = ConsoleColor.White;
            return Ok("PostTraceController::Internal");
        }
    }
}
