﻿// <copyright file="DyeBasedTraceEnricher.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System;
using System.Diagnostics;
using OpenTelemetry;

namespace Microsoft.M365.Core.Telemetry.R9.Tracing.AdvancedSampling.DyeBasedTraceSampling
{
    /// <summary>
    /// DyeBasedTraceEnricher
    /// </summary>
    public class DyeBasedTraceEnricher
    {
        /// <summary>
        /// EnrichDyeTraceBaggage
        /// </summary>
        /// <param name="activity"></param>
        /// <param name="eventName"></param>
        /// <param name="tags"></param>
        public static void EnrichDyeTraceBaggage(Activity activity, string eventName, Tags tags)
        {
            if (activity == null)
            {
                return;
            }

            if ("OnStartActivity".Equals(eventName, StringComparison.Ordinal))
            {
                if (activity.GetBaggageItem(Constants.SampleBaggageName) != null || Baggage.GetBaggage(Constants.SampleBaggageName) != null)
                {
                    return;
                }

                TagsHandler.Invoke(activity, tags);                
            }
        }
    }
}
