#include "FileManager.h"

#include <cassert>
#include <iostream>

namespace Microsoft {
namespace M365 {
namespace Exporters {

FileManager::FileManager(boost::asio::io_context& io_context, const std::string& file_path, std::chrono::seconds reset_duration) noexcept
    : file_(io_context, file_path, boost::asio::stream_file::write_only | boost::asio::stream_file::create | boost::asio::stream_file::append),
      io_context_(io_context), strand_(boost::asio::make_strand(io_context)), timer_(io_context), reset_duration_(reset_duration) {
    assert(file_.is_open());
    start_reset_timer();
}

void FileManager::ShutDown() noexcept {
    std::lock_guard<std::mutex> lock(mutex_);
    timer_.cancel();
    if (file_.is_open()) {
        file_.close();
    }
}

void FileManager::Write(std::shared_ptr<std::string> data) noexcept {
    boost::asio::post(strand_, [this, data]() {
        do_write(data);
    });
}

void FileManager::do_write(std::shared_ptr<std::string> data) {
    std::lock_guard<std::mutex> lock(mutex_);
    if (file_.is_open()) {
        boost::asio::async_write(file_, boost::asio::buffer(*data), [data](const boost::system::error_code&, std::size_t) {
        });
    }
}

// Called within the lock.
void FileManager::start_reset_timer() {
    timer_.expires_after(reset_duration_);
    timer_.async_wait(boost::asio::bind_executor(strand_, [this](const boost::system::error_code& error) {
        reset_file_position(error);
    }));
}

void FileManager::reset_file_position(const boost::system::error_code& error) {
    std::lock_guard<std::mutex> lock(mutex_);
    if (!error) {
        if (file_.is_open()) {
            file_.seek(0, boost::asio::stream_file::seek_set);
        }
    } else if (error.value() == 995) {
        // Message: The I/O operation has been aborted because of either a thread exit or an application request.
        // Hard stop. The TcpClient object may not be valid anymore.
        return;
    }
    start_reset_timer();
}

} // namespace Exporters
} // namespace M365
} // namespace Microsoft
