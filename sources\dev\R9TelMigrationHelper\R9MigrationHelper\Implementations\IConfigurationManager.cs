﻿// <copyright file="IConfigurationManager.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace R9MigrationHelper.Implementations
{
    /// <summary>
    /// Configuration manager interface
    /// </summary>
    public interface IConfigurationManager
    {
        /// <summary>
        /// Load configuration
        /// </summary>
        /// <param name="key"></param>
        /// <returns></returns>
        public string GetConfiguration(string key);
    }
}
