﻿// <copyright file="SDKLoggerChunkTest.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using FluentAssertions;
using Microsoft.M365.Core.Telemetry.SDKLogger;
using Microsoft.M365.Core.Telemetry.TestCommon;
using Xunit;
using Xunit.Abstractions;

namespace Microsoft.M365.Core.Telemetry.SDKLoggerTest
{
    /// <summary>
    /// SDKLoggerChunkTest
    /// </summary>
    public class SDKLoggerChunkTest : BaseTest
    {
        /// <summary>
        /// SDKLoggerChunkTest
        /// </summary>
        /// <param name="output"></param>
        public SDKLoggerChunkTest(ITestOutputHelper output) : base(output)
        {
        }

        /// <summary>
        /// ChunkTestEmpty
        /// </summary>
        [Fact]
        public void ChunkTestEmpty()
        {
            var chunks = SDKLog.Chunk(string.Empty, 3);
            chunks.Count.Should().Be(1);
            chunks[0].Should().Be(string.Empty);
        }

        /// <summary>
        /// ChunkTestPartialChunk
        /// </summary>
        [Fact]
        public void ChunkTestPartialChunk()
        {
            var chunks = SDKLog.Chunk("a\nb", 3);
            chunks.Count.Should().Be(1);
            chunks[0].Should().Be("a\nb");
        }

        /// <summary>
        /// ChunkTestFullChunks
        /// </summary>
        [Fact]
        public void ChunkTestFullChunks()
        {
            var chunks = SDKLog.Chunk("a\nb\nc", 3);
            chunks.Count.Should().Be(1);
            chunks[0].Should().Be("a\nb\nc");
        }

        /// <summary>
        /// ChunkTestFullChunksWithPartialChunk
        /// </summary>
        [Fact]
        public void ChunkTestFullChunksWithPartialChunk()
        {
            var chunks = SDKLog.Chunk("a\nb\nc\nd\n", 3);
            chunks.Count.Should().Be(2);
            chunks[0].Should().Be("a\nb\nc");
            chunks[1].Should().Be("d\n");
        }
    }
}
