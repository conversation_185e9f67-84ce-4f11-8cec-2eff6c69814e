﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFrameworks>net472;net6.0;net8.0</TargetFrameworks>
    <Nullable>enable</Nullable>
    <LangVersion>9.0</LangVersion>
    <AssemblyName>Microsoft.M365.Core.Telemetry.FileExporter</AssemblyName>
    <RootNamespace>Microsoft.M365.Core.Telemetry.FileExporter</RootNamespace>
    <GeneratePackageOnBuild>true</GeneratePackageOnBuild>
    <PackageId>Microsoft.M365.Core.Telemetry.FileExporter</PackageId>
    <PackageVersion>8.0.5</PackageVersion>
    <PackageReleaseNotes>Add process id into fallback to disk filename</PackageReleaseNotes>
    <Authors>SOTELS</Authors>
    <Product>Microsoft.M365.Core.Telemetry</Product>
    <Copyright>Copyright 2021 Microsoft Corp. All rights reserved.</Copyright>
    <Description>FileExporter for SecurityTelemetry and ODL NRT Fallback.</Description>
    <PlatformTarget>AnyCPU</PlatformTarget>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.Extensions.Options" />
    <PackageReference Include="Microsoft.Extensions.ObjectPool" />
    <PackageReference Include="Microsoft.R9.Extensions.SecurityTelemetry" />
  </ItemGroup>

  <ItemGroup>
    <InternalsVisibleTo Include="FileExporterTest, PublicKey=002400000c800000140100000602000000240000525341310008000001000100db34a0ca5a2deee1a902251eda636ae9cc4db80f1fee44cbee596531e754b9d271092ad22c7011d1a4aded2b3e65c61037de0626a6beefd388afdbef44b659f4d076da6259adf74efcd92a84ec48f8e2090e8675457aa169a31a4156d5a777977c7b71b38b351e18465e6d2f72c7a237338425f213b26f42d82883209c511767c4556079db825cd81e27ee08c05c488cc35a1c5d30eba07bd84a2f7196ac98ca0278ec7c7d56303265ff9a24ebd6195ccf271bf1400cb960087d378eb499c34b09d5d07be2afba29357c85953dd7e5f31452f56bbddfbe3b67426f79d5013302f5e3c150446b71ccd103d5abba819aad8331a366509a6688a765091c4f26f1cf" />
    <InternalsVisibleTo Include="DynamicProxyGenAssembly2, PublicKey=0024000004800000940000000602000000240000525341310004000001000100c547cac37abd99c8db225ef2f6c8a3602f3b3606cc9891605d02baa56104f4cfc0734aa39b93bf7852f7d9266654753cc297e7d2edfe0bac1cdcf9f717241550e0a7b191195b7667bb4f64bcb8e2121380fd1d9d46ad2d92d2d15605093924cceaf74c4861eff62abf69b9291ed0a340e113be11e6a7d3113e92484cf7045cc7" />
  </ItemGroup>

</Project>