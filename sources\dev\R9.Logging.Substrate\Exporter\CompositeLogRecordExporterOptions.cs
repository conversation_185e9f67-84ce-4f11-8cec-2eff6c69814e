// <copyright file="CompositeLogRecordExporterOptions.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System.Collections.Generic;

namespace Microsoft.M365.Core.Telemetry.R9.Logging.Substrate
{
    /// <summary>
    /// Represents the destination of an event.
    /// </summary>
    public class TableExport
    {
        /// <summary>
        /// export platform of the event. Geneva or OdlTcp
        /// </summary>
        public string ExporterType { get; set; } = string.Empty;

        /// <summary>
        /// export table name. Geneva Event name or OdlTcp log type
        /// </summary>
        public string ExportTable { get; set; } = string.Empty;
    }

    /// <summary>
    /// Options for configuring the SubstrateLogRecordExporter.
    /// </summary>
    public class CompositeLogRecordExporterOptions
    {
        /// <summary>
        /// table name mappings. log category to virtual table
        /// </summary>
        public Dictionary<string, string> VirtualTableMappings { get; } = new ();

        /// <summary>
        /// export setting for each virtual table. table name to geneva eventName/odl logType
        /// </summary>
        public Dictionary<string, List<TableExport>> VirtualTableExports { get; } = new ();
    }
}
