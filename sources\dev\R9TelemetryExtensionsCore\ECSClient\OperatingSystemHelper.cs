﻿// <copyright file="OperatingSystemHelper.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System.Runtime.InteropServices;

namespace Microsoft.M365.Core.Telemetry.ECSClient
{
    /// <summary>
    /// Helper Class to cook variable once to save cost
    /// </summary>
    internal class OperatingSystemHelper
    {
        private static bool isRunningOnLinux = RuntimeInformation.IsOSPlatform(OSPlatform.Linux);

        /// <summary>
        /// Return true when it is running on Linux
        /// </summary>
        internal static bool IsLinux => isRunningOnLinux;
    }
}
