// ---------------------------------------------------------------------------
// <copyright file="HomeController.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>
// ---------------------------------------------------------------------------

namespace Microsoft.M365.Core.Telemetry.NetCoreWebApi
{
    using System;
    using System.Diagnostics;
    using System.Globalization;
    using System.Net.Http;
    using Microsoft.AspNetCore.Authentication;
    using Microsoft.AspNetCore.Mvc;

    /// <summary>
    /// HomeController
    /// </summary>
    [ApiController]
    [Route("Home")]
    public class HomeController : ControllerBase
    {
        private static readonly ActivitySource activitySource = new ActivitySource("TA");

        /// <summary>
        /// Index
        /// </summary>
        /// <returns>string</returns>
        [HttpGet]
        [Route("Index")]
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Performance", "CA1822:Mark members as static", Justification = "d")]
        public string Index()
        {
            // test outgoing http tracing
            HttpClient client = new HttpClient();
            _ = client.GetAsync("http://www.bing.com");

            using var activity = activitySource.StartActivity("ActivityName");
            _ = activity?.SetTag(nameof(HomeController), "AA");
            
            return "OK";
        }
    }
}