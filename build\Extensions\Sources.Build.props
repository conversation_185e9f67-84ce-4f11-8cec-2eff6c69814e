<?xml version="1.0" encoding="utf-8"?>
<!--
  This file is imported for all projects in 'src' root directory
-->
<Project xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  
  <PropertyGroup>
    <MSBuildAllProjects>$(MSBuildAllProjects);$(MSBuildThisFileFullPath)</MSBuildAllProjects>
    <DebugType>pdbonly</DebugType>
    <DebugSymbols>true</DebugSymbols>
    <IsPacked Condition="'$(MSBuildProjectExtension)' == '.nuproj' Or '$(MSBuildProjectDefaultTargets)' == 'Build;Pack'">true</IsPacked>
    <ProjTypeBuildProps Condition=" '$(ProjTypeBuildProps)' == '' And '$(MSBuildProjectExtension)' != '' ">$(MSBuildProjectExtension.Substring(1)).$(MSBuildThisFile)</ProjTypeBuildProps>
    <!-- Assign the appropriate group. Dev or Test -->
    <GROUP Condition="'$(GROUP)' == ''">$([System.Text.RegularExpressions.Regex]::Replace($(MSBuildProjectFullPath.ToLower()), `.*?\\sources\\([^\\]+)\\.+`, `$1`))</GROUP>

    <GroupBuildProps Condition=" '$(GroupBuildProps)' == '' ">$(GROUP).$(MSBuildThisFile)</GroupBuildProps>
  </PropertyGroup>

  <Import Project="$(ProjTypeBuildProps)" Condition="'$(ProjTypeBuildProps)' != '' and Exists('$(ProjTypeBuildProps)')" />
  <Import Project="$(GroupBuildProps)" Condition="'$(GroupBuildProps)' != '' and Exists('$(GroupBuildProps)')" />

</Project>
