﻿// <copyright file="ManifestEntry.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace R9MigrationHelper.Model
{
    /// <summary>
    /// ManifestEntry
    /// </summary>
    class ManifestEntry
    {
        /// <summary>
        /// Deliverable
        /// </summary>
        public string Deliverable;

        /// <summary>
        /// Language
        /// </summary>
        public string Language;

        /// <summary>
        /// Flavors
        /// </summary>
        public string Flavors;

        /// <summary>
        /// Platforms
        /// </summary>
        public string Platforms;

        /// <summary>
        /// Destination
        /// </summary>
        public string Destination;

        /// <summary>
        /// Prefix
        /// </summary>
        public string Prefix;

        /// <summary>
        /// FileName
        /// </summary>
        public string FileName;

        /// <summary>
        /// RecommendedDeliverable
        /// </summary>
        public string? RecommendedDeliverable;

        /// <summary>
        /// Public constructor
        /// </summary>
        /// <param name="path"></param>
        public ManifestEntry(string path)
        {
            Deliverable = path[(NthIndexOf(path, '/', 1) + 1)..NthIndexOf(path, '/', 2)];
            Language = path[(NthIndexOf(path, '/', 2) + 1)..NthIndexOf(path, '/', 3)];
            Flavors = path[(NthIndexOf(path, '/', 3) + 1)..NthIndexOf(path, '/', 4)];
            Platforms = path[(NthIndexOf(path, '/', 4) + 1)..NthIndexOf(path, '/', 5)];
            if ((NthIndexOf(path, '/', 5) + 1) < path.LastIndexOf('/'))
            {
                Destination = path[(NthIndexOf(path, '/', 5) + 1)..path.LastIndexOf('/')];
                Destination = Destination.Replace('/', '\\');
            }
            else
            {
                Destination = string.Empty; // Destination can be empty
            }
            Prefix = path[..path.LastIndexOf('/')];
            FileName = path[(path.LastIndexOf('/') + 1)..];
            RecommendedDeliverable = Recommend();
        }

        /// <summary>
        /// Find Nth index of c in s, 1-based 
        /// </summary>
        /// <param name="s"></param>
        /// <param name="c"></param>
        /// <param name="n"></param>
        /// <returns></returns>
        private static int NthIndexOf(string s, char c, int n)
        {
            if (string.IsNullOrEmpty(s) || n <= 0)
            {
                return -1;
            }

            int num = 0;
            int startIndex = 0;
            while (n-- > 0 && num >= 0)
            {
                num = s.IndexOf(c, startIndex);
                startIndex = num + 1;
            }

            return num;
        }

        private string? Recommend()
        {
            return Deliverable switch
            {
                "core" => "CORE",
                "components" => "COMPONENTS",
                "dsl" or "exchange14" => "DATACENTER",
                "datacenterenv" => "DATACENTERENV",
                "datacenterops" => "DATACENTEROPS",
                "deployment" => "DEPLOYMENT",
                "exchange14_client_langpacks" => "PRODUCT_CLIENT_LANGPACKS",
                "exchange14_server_langpacks" => "PRODUCT_SERVER_LANGPACKS",
                "exchange14_test" => "PRODUCT_TEST",
                "fips" => "FIPS",
                "holidaycalendars" => "HOLIDAYCALENDARS",
                "internalpackages" => "INTERNALPACKAGES",
                "owaplus_langpacks" => "PRODUCT_OWAPLUS_LANGPACKS",
                "parters" => "PARTNERS",
                "partners_test" => "PARTNERS_TEST",
                "patches_exchange" => "PATCHES_EXCHANGE",
                "patches_msfte" => "PATCHES_MSFTE",
                "patches_update" => "PATCHES_UPDATE",
                "tools" => "TOOLS",
                "version_files" => "VERSION_FILES",
                _ => null
            };
        }
    }
}
