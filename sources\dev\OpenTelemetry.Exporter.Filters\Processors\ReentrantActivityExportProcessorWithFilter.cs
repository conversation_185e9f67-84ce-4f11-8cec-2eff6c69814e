﻿// <copyright file="ReentrantActivityExportProcessorWithFilter.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>
using System.Diagnostics;
using OpenTelemetry.Exporter.Filters.Internal;

namespace OpenTelemetry.Exporter.Filters
{
    /// <summary>
    /// ReentrantActivityExportProcessor with a filter internal and do filtering before export
    /// </summary>
    internal class ReentrantActivityExportProcessorWithFilter : ReentrantActivityExportProcessor
    {
        internal readonly BaseFilter<Activity> Filter;

        /// <summary>
        /// initial the instance with exporter and filter
        /// </summary>
        /// <param name="exporter"></param>
        /// <param name="filter"></param>
        public ReentrantActivityExportProcessorWithFilter(BaseExporter<Activity> exporter, BaseFilter<Activity> filter)
            : base(exporter)
        {
            Guard.ThrowIfNull(filter, nameof(filter));  
            this.Filter = filter;
        }

        /// <summary>
        /// filter the data before they are actually exported.
        /// </summary>
        /// <param name="data">completed activity</param>
        protected override void OnExport(Activity data)
        {
            if (this.Filter.ShouldFilter(data))
            {
                base.OnExport(data);
            }
        }
    }
}
