param (
    [Parameter(Mandatory=$true)]
    [string]$TargetVersion,
    
    [string]$OutputFile = ".\OutputFiles\targetPackages.txt",
    
    [string]$PackagesPropsPath = "q:/src/Substrate/Packages.props"
)

# Check if output file exists
if (Test-Path -Path $OutputFile) {
    Write-Host "Output file exists. Skip this step."
    return
}

# Load the Packages.props file
[xml]$packagesXml = Get-Content -Path $PackagesPropsPath

# Get all package version elements
$packageVersions = $packagesXml.Project.ItemGroup.PackageVersion

# Filter for Microsoft.R9.Extensions.* packages
$r9Packages = $packageVersions | Where-Object { 
    $_.Include -like "Microsoft.R9.Extensions.*" -and
    $_.Include -notlike "Microsoft.R9.Extensions.Security*" -and
    $_.Include -ne "Microsoft.R9.Extensions.Telemetry.Internal" -and
    $_.Include -ne "Microsoft.R9.Extensions.Telemetry.Internal.Http"
} | Sort-Object -Property Include

# Create the output content
$outputContent = ""
foreach ($package in $r9Packages) {
    $outputContent += "$($package.Include) $TargetVersion`r`n"
}

# Write to the output file
Set-Content -Path $OutputFile -Value $outputContent

# Display summary
$count = $r9Packages.Count
Write-Host "Generated list of $count Microsoft.R9.Extensions.* packages with version $TargetVersion"
Write-Host "Output saved to: $OutputFile"