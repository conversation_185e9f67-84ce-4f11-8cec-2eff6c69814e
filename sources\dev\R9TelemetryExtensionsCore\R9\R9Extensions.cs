﻿// <copyright file="R9Extensions.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.M365.Core.Telemetry.ECSClient;
using Microsoft.M365.Core.Telemetry.SDKLogger;

namespace Microsoft.M365.Core.Telemetry.R9
{
    /// <summary>
    /// R9Extensions
    /// </summary>
    public static class R9Extensions
    {
        /// <summary>
        /// InitTelemetry
        /// </summary>
        /// <param name="services"></param>
        /// <param name="configuration"></param>
        /// <returns></returns>
        public static IServiceCollection InitTelemetry(this IServiceCollection services, IConfiguration configuration)
        {
            return InitTelemetry(services, configuration, false);
        }

        /// <summary>
        /// InitTelemetry
        /// </summary>
        /// <param name="services"></param>
        /// <param name="configuration"></param>
        /// <param name="alwaysEnableR9"></param>
        /// <returns></returns>
        public static IServiceCollection InitTelemetry(this IServiceCollection services, IConfiguration configuration, bool alwaysEnableR9)
        {
            SDKLog.Info("InitTelemetry");
            services.AddSingleton<IBlockList, BlockList>();
            services.AddSingleton<IPassiveR9Config>(serviceProvider => new PassiveR9Config(configuration));
            services.AddSingleton<ITelemetryEmitConfig>(serviceProvider => new TelemetryEmitConfig(configuration));
            Enrichment.CommonInit.InitSubstrateEnrichers(services);
            R9Services.InitR9Services(services, alwaysEnableR9);
            return services;
        }
    }
}
