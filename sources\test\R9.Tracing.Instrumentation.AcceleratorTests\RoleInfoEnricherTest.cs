﻿// <copyright file="RoleInfoEnricherTest.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System.Collections.Generic;
using System.Diagnostics;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.R9.Extensions.Enrichment;
using Xunit;

namespace Microsoft.M365.Core.Telemetry.R9.Tracing.Instrumentation.Accelerator.Tests
{
    /// <summary>
    /// RoleInfoEnricherTest
    /// </summary>
    public class RoleInfoEnricherTest
    {
        private const string Name = "name";

        /// <summary>
        /// EnrichSucceedWithCosmic
        /// </summary>
        [Fact]
        public void EnrichSucceedWithCosmic()
        {
            var services = new ServiceCollection();

            var configBuilder = new ConfigurationBuilder().AddInMemoryCollection(new Dictionary<string, string>
                {
                    { "ServiceName", Name },
                    { "RuntimeModel", "Cosmic" }
                });

            services.Configure<ServiceMetaDataOptions>(configBuilder.Build());
            services.AddSingleton<ITraceEnricher, RoleInfoEnricher>();
            var enricher = services.BuildServiceProvider().GetRequiredService<ITraceEnricher>();
            Assert.NotNull(enricher);

            var activity = new Activity("test");
            Assert.Null(activity.GetTagItem(Constants.CloudRole));

            enricher.Enrich(activity);
            Assert.Equal(Name, activity.GetTagItem(Constants.CloudRole));

            enricher.Enrich(null);
        }

        /// <summary>
        /// EnrichSucceedWithModelA
        /// </summary>
        [Fact]
        public void EnrichSucceedWithModelA()
        {
            var services = new ServiceCollection();

            var configBuilder = new ConfigurationBuilder().AddInMemoryCollection(new Dictionary<string, string>
                {
                    { "ServiceName", Name },
                    { "RuntimeModel", "ModelA" }
                });

            services.Configure<ServiceMetaDataOptions>(configBuilder.Build());
            services.AddSingleton<ITraceEnricher, RoleInfoEnricher>();
            var enricher = services.BuildServiceProvider().GetRequiredService<ITraceEnricher>();
            Assert.NotNull(enricher);

            var activity = new Activity("test");
            Assert.Null(activity.GetTagItem(Constants.CloudRole));

            enricher.Enrich(activity);
            Assert.Equal(Name, activity.GetTagItem(Constants.CloudRole));

            enricher.Enrich(null);
        }
    }
}
