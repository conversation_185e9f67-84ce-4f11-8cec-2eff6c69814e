﻿// <copyright file="TelemetryEmitConfig.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Diagnostics.CodeAnalysis;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading;
using Microsoft.Extensions.Configuration;
using Microsoft.M365.Core.Telemetry.Enrichment;
using Microsoft.M365.Core.Telemetry.SDKLogger;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;

namespace Microsoft.M365.Core.Telemetry.ECSClient
{
    /// <summary>
    /// Hold the items which are reuqired to control emitting events&metrics.
    /// </summary>
    internal class TelemetryEmitConfig : ITelemetryEmitConfig
    {
        /// <summary>
        /// The changed agents.
        /// </summary>
        private readonly HashSet<string> changedAgents = new HashSet<string>(StringComparer.OrdinalIgnoreCase)
        {
            Constants.PassiveTeamName,
        };

        /// <summary>
        /// The ECS context filter.
        /// </summary>
        private Dictionary<string, string> ecsContext;

        /// <summary>
        /// ecsClient instance.
        /// </summary>
        private readonly UnifiedTelemetryECSClient ecsClient;

        // TODO(chenzejun): Ensure fallbackConfig is json format.
        private readonly string fallbackConfig = string.Empty;

        private ConcurrentDictionary<string, Dictionary<string, object>> cachedDict;

        /// <summary>
        /// ctor
        /// </summary>
        /// <param name="configuration">Current process name.</param>
        /// <param name="deployRing">DeployRing of machine.</param>
        public TelemetryEmitConfig(IConfiguration configuration)
        {
            SDKLog.Info("Initializing TelemetryEmitConfig.");

            Dictionary<string, string> identifiers = new Dictionary<string, string>
            {
                { "DeployRing", DimensionValues.DeployRing },
                { "ServiceName", configuration.GetValue<string>("Microsoft_m365_core_telemetry:ServiceMetadata:ServiceName") },
                { "RuntimeModel", configuration.GetValue<string>("Microsoft_m365_core_telemetry:ServiceMetadata:RuntimeModel") }
            };
            this.ecsClient = UnifiedTelemetryECSClient.EcsClientInstance(identifiers);
            this.InitializeECSContext();
            this.ecsClient.RegularUpdateEventWrapper.UpdateConfig += (sender, args) =>
            {
                args.WaitHandleCounter.AddCount();
                UpdateConfigValue(sender, args.ChangedAgents);
                args.WaitHandleCounter.Signal();
            };

            UpdateConfigValue(default, changedAgents);
        }

        /// <summary>
        /// Get certain config of certain event.
        /// </summary>
        /// <typeparam name="T">The type to parse.</typeparam>
        /// <param name="eventName">The event name.</param>
        /// <param name="configName">The config name.</param>
        /// <returns>The parsed config value.</returns>
        public T QueryEventConfig<T>(string eventName, string configName)
        {
            string entryName = string.Format(CultureInfo.InvariantCulture, Constants.EventTemplate, eventName);

            if (cachedDict.TryGetValue(entryName, out Dictionary<string, object> config))
            {
                if (config.TryGetValue(configName, out object configValue) && configValue is not null)
                {
                    return (T)configValue;
                }
            }

            return default;
        }

        /// <summary>
        /// Get certain config of certain metric.
        /// </summary>
        /// <typeparam name="T">The type to parse.</typeparam>
        /// <param name="metricName"></param>
        /// <param name="configName"></param>
        /// <returns>The parsed config value.</returns>
        public T QueryMetricConfig<T>(string metricName, string configName)
        {
            string entryName = string.Format(CultureInfo.InvariantCulture, Constants.MetricTemplate, metricName);

            if (cachedDict.TryGetValue(entryName, out Dictionary<string, object> config))
            {
                if (config.TryGetValue(configName, out object configValue) && configValue is not null)
                {
                    return (T)configValue;
                }
            }

            return default;
        }

        /// <summary>
        /// Initialize the ECS context.
        /// </summary>
        private void InitializeECSContext()
        {
            ecsContext = new Dictionary<string, string>()
            {
                { "DeployRing", DimensionValues.DeployRing },
                { "Service", DimensionValues.Service },
                { "IsEOP",  ECSClientUtilities.IsEOP() }
            };

            string ecsContextMsg = string.Join(";", ecsContext.Select(x => x.Key + "=" + x.Value).ToArray());
            SDKLog.Info($"TelemetryEmitConfig ECSContext. {ecsContextMsg}");
        }

        private void UpdateConfigValue(object sender, HashSet<string> changedAgents)
        {
            if (sender == null) { }
            try
            {
                if (changedAgents == null || !changedAgents.Contains(Constants.PassiveTeamName))
                {
                    return;
                }

                JObject configRoot = ECSClientUtilities.GetUnifiedTelemetryConfig(Constants.PassiveTeamName, ecsContext, ecsClient.EcsRequester);
                if (configRoot == null)
                {
                    SDKLog.Warning("UpdateConfigValue for PassiveMon V2 failed : configRoot is null");
                    return;
                }

                cachedDict = ECSClientUtilities.ConvertJObjectToNestedDictionary(configRoot);

                UpdateECSManagedServiceCollection();
            }
            catch (Exception e)
            {
                SDKLog.Error($"UpdateConfigValue for PassiveMon failed : {e}");
            }
        }

        /// <summary>
        /// Update the ECSManagedServiceCollection with latest config from ECS
        /// </summary>
        /// <param name="configRoot"></param>
        private void UpdateECSManagedServiceCollection()
        {
            var serviceDict = new Dictionary<string, Dictionary<string, object>>(2);
            var tableMappingDict = new Dictionary<string, object>();
            var monitoringAccountDict = new Dictionary<string, object>();
            var monitoringNamespaceDict = new Dictionary<string, object>();

            foreach (var item in cachedDict)
            {
                string[] elements = item.Key.Split('_');
                string entryType = elements[0];
                string entryName = elements[1];
                if (entryType.Equals("Event", StringComparison.OrdinalIgnoreCase))
                {
                    tableMappingDict[entryName] = item.Value["TableNameMapsTo"];
                }
                else if (entryType.Equals("Metric", StringComparison.OrdinalIgnoreCase))
                {
                    monitoringAccountDict[entryName] = item.Value["MonitoringAccount"];
                    monitoringNamespaceDict[entryName] = item.Value["MonitoringNamespace"];
                }
            }

            serviceDict["GenevaLogging"] = new Dictionary<string, object>(2)
            {
                { "TableNameMappings", tableMappingDict },
                { "ConnectionString", Constants.ConnectionString },
            };

            serviceDict["GenevaMetering"] = new Dictionary<string, object>(5)
            {
                { "Protocol", Constants.EtwProtocol },
                { "MonitoringAccount", Constants.MonitoringAccount },
                { "MonitoringNamespace", Constants.MonitoringNamespace },
                { "MonitoringAccountOverrides", monitoringAccountDict },
                { "MonitoringNamespaceOverrides", monitoringNamespaceDict },
            };

            if (serviceDict.Count > 0)
            {
                using MemoryStream serviceStream = new MemoryStream(Encoding.ASCII.GetBytes(JsonConvert.SerializeObject(serviceDict)));
                IConfigurationRoot serviceOption = new ConfigurationBuilder().AddJsonStream(serviceStream).Build();
                ECSManagedServiceCollection.Instance.UpdateInstanceV2(serviceOption);
            }
            else
            {
                ECSManagedServiceCollection.Instance.UpdateInstanceV2(GetConfigFromFallback());
            }
        }

        // TODO(chenzejun): Implement this method when confirmed.

        /// <summary>
        /// Get the table mappings from fallback list if ECS doesn't configure this value.
        /// Won't test this method since it's placeholder method and won't call this method.
        /// </summary>
        /// <returns></returns>
        [ExcludeFromCodeCoverage]
        private IConfigurationRoot GetConfigFromFallback()
        {
            using MemoryStream serviceStream = new MemoryStream(Encoding.ASCII.GetBytes(JsonConvert.SerializeObject(this.fallbackConfig)));
            IConfigurationRoot serviceOption = new ConfigurationBuilder().AddJsonStream(serviceStream).Build();
            return serviceOption;
        }
    }
}
