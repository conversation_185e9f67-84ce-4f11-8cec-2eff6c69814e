syntax = "proto3";

package Microsoft.M365.ODLExporter;

option csharp_namespace = "Microsoft.Office.BigData.DataLoader.Common";

message ODLNRTMessage
{
    string  ID = 1;         // message ID
    int64   EventID = 2;    // event ID
	string  Source  = 3;    // source
    string  Message = 4;    // message
}

message ODLNRTMessageDimension
{
    string  Name = 1;       // dimension name
    string  Value = 2;      // dimension value
}

message ODLNRTMessageHead
{
    int64   Timestamp = 1;  // timestamp
    repeated ODLNRTMessageDimension Dimensions = 3; // dimensions
}

message ODLNRTSecurityMessageBatchReq
{
    ODLNRTMessageHead Head = 1; // message head
    repeated ODLNRTMessage Messages = 2; // messages
}

message ODLNRTSecurityMessageBatchRsp
{
}

message ODLNRTHeartbeatReq
{
}

message ODLNRTHeartbeatRsp
{
}

message ODLNRTCommonMessageHead
{
    int64   Timestamp = 1;  // timestamp
    repeated ODLNRTMessageDimension Dimensions = 3; // dimensions
}

enum ODLNRTCommonMessageType
{
    ODLNRTMessageType_Unknown = 0;
    ODLNRTMessageType_String = 1;
}

message ODLNRTCommonMessage
{
    string  ID = 1;         // message ID
    int64   EventID = 2;    // event ID
	string  Source  = 3;    // source
    ODLNRTCommonMessageType Type = 4; // message type
    oneof Value
    {
        string  ValueString = 101; // string value type == ODLNRTMessageType_String
    }

}

message ODLNRTCommonMessageBatchReq
{
    ODLNRTCommonMessageHead Head = 1; // message head
    repeated ODLNRTCommonMessage Messages = 2; // messages
}

message ODLNRTCommonMessageBatchRsp
{
}

enum ODLNRTCommandType
{
    ODLNRTCmd_Unknown = 0;
    ODLNRTCmd_Heartbeat = 1;
    ODLNRTCmd_SecurityMessageBatch = 2;
    ODLNRTCmd_CommonMessageBatch = 3;
}

message ODLNRTCommonHead
{
    ODLNRTCommandType   CommandType = 1; // command type
    int64               Timestamp   = 2; // timestamp
    int64               Sequence    = 3; // sequence
}

message ODLNRTRequest
{
    ODLNRTCommonHead    Head = 1;   // Head
    oneof Body
    {
        ODLNRTHeartbeatReq      		HeartbeatReq = 101; // heartbeat request
        ODLNRTSecurityMessageBatchReq   SecurityMessageBatchReq = 102; // message batch request
        ODLNRTCommonMessageBatchReq     CommonMessageBatchReq = 103; // common message batch request
    }
}

message ODLNRTResponse
{
    ODLNRTCommonHead    Head    = 1;    // Head
    int32               RetCode = 2;    // return code
    oneof Body
    {
        ODLNRTHeartbeatRsp      		HeartbeatRsp = 101; // heartbeat response
        ODLNRTSecurityMessageBatchRsp   SecurityMessageBatchRsp = 102; // message batch request
        ODLNRTCommonMessageBatchRsp     CommonMessageBatchRsp = 103; // common message batch request
    }
}
