$ErrorActionPreference = "Stop" # http://technet.microsoft.com/en-us/library/dd347731.aspx
Set-StrictMode -Version "2.0"   # http://technet.microsoft.com/en-us/library/dd347614.aspx

$PSScriptRoot = Split-Path -Parent -Path $MyInvocation.MyCommand.Definition

function GetRepoRoot()
{
	$repoRoot = &git rev-parse --show-toplevel
	return $repoRoot -replace '/', '\'	
}

function CreateScanList($OutputFile)
{
	$repoRoot = GetRepoRoot
	
	$currentCommit = &git rev-parse HEAD
	$files = &git diff-tree --no-commit-id --name-only -r $currentCommit
	
	$gitOrigin = &git config --get remote.origin.url
	$gitBranch = &git rev-parse --abbrev-ref HEAD

	if ($gitBranch -eq 'HEAD')
	{
		Write-Host "WARNING: Branch not detected. Skipping." -foregroundcolor Yellow
		return $false
	}

	$gitUpstreamBranch = $null
	
	$ErrorActionPreference = "Continue"
	$gitUpstreamBranchRef = &git rev-parse --symbolic-full-name "@{u}" 2> $null	
	$ErrorActionPreference = "Stop"
	
	if ($gitUpstreamBranchRef -ne $null)
	{
		$gitUpstreamBranch = $gitUpstreamBranchRef -replace 'refs/remotes/', ''
	}
	else
	{
		$gitUpstreamBranch = "origin/master"
	}
	
	Write-Host "Comparing to branch $gitUpstreamBranch"
	
	$files = &git diff --stat --cached --name-only $gitUpstreamBranch
	
	if ($files -eq $null)
	{
		return $false
	}
	
	$files = $files -replace '/', '\' | foreach{ join-path $repoRoot $psitem }
	$files -join [Environment]::NewLine | out-file $OutputFile -append -encoding ascii
	
	return $true
}

function RunCredScan($ScanFile, $OutputPath)
{
	$scannerPath = "$PSScriptRoot\credscan"
	$scannerExe = "$scannerPath\CredentialScanner.exe"
	$scannerOutput = "$OutputPath\scanner-output.log"

	$suppressionsFilePath = $PSScriptRoot + "\..\..\baselines\CredScanSuppressions.json"
	
	if (!(Test-Path $scannerExe))
	{
		Write-Host "WARNING: CredScan does not exist on the path $scannerExe. Try reinstalling the git hook" -foregroundcolor Yellow
		return $false
	}
	
	$supSwitch = ""
	$supFile = ""
	
	if (Test-Path $suppressionsFilePath)
	{
		$supSwitch = "-Sp"
		$supFile = "$suppressionsFilePath"
	}
	
	$scan = &"$ScannerExe" -I $ScanFile -S "$scannerPath\Searchers\buildsearchers.xml" -O "$OutputPath\credscan" -t $supSwitch $supFile 2>&1	
	
	if ($LASTEXITCODE -ne 0)
	{
		Write-Host "WARNING: CredScan failed to run:" -foregroundcolor Yellow
		Write-Host "`"$ScannerExe`" -I $ScanFile -S `"$scannerPath\Searchers\buildsearchers.xml`" -O `"$OutputPath\credscan`" -t $supSwitch $supFile"
		$scan | % {Write-Host $_}
		return $false
	}	

	$matchFile = "$OutputPath\credscan-matches.tsv"

	$lines = Import-Csv -Delimiter "`t" -Path $matchFile
		
	if ($lines -eq $null)
	{
		Write-Host "No violations found"
		return $false
	}

	$violationsList = $lines | Format-List | Out-String

$message = @"

************************************************************
VIOLATION: Your changes contain files with secrets!
 
Clean the secrets listed above, or suppress in the build\CredScanSuppressions.json
Wiki: https://microsoft.sharepoint.com/teams/AzureSecurityCompliance/Security/SitePages/SecretsManagement.aspx

Any secret - dev, test, encrypted, temporary - should be removed NOW to prevent them from being preserved in history.
************************************************************'

"@
	Write-Host $violationsList
	Write-Host $message -foregroundcolor "red"
	
	return $true
}

function FindViolations($Path)
{
	Write-Host "Scanning for violations"

	$scanfile = "$Path\scanfile.tsv"
	
	Write-Host "Creating scanfile"
	if (!(CreateScanList $scanfile))
	{
		Write-Host "Nothing to scan"
		return $false
	}
	
	Write-Host "Running CredScan"
	return RunCredScan $scanfile $Path
}

$tempFolder = join-path $env:TEMP ([guid]::NewGuid())
New-Item $tempFolder -type Directory | Out-Null

$res = FindViolations $tempFolder

Remove-Item $tempFolder -Force -Recurse

if ($res)
{
	exit 1
}