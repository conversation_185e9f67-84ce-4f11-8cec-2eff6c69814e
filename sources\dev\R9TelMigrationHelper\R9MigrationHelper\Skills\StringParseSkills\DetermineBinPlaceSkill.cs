﻿// <copyright file="DetermineBinPlaceSkill.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Linq;

namespace R9MigrationHelper.Skills.StringParseSkills
{
    /// <summary>
    /// DetermineBinPlaceSkill.
    /// </summary>
    public class DetermineBinPlaceSkill
    {
        /// <summary>
        /// Determine if drop to BinPlace by service config file
        /// </summary>
        /// <param name="serviceConfig">service config file content</param>
        public bool DetermineBinPlace(string serviceConfig)
        {
            // BinPlace config is:
            // <assemblyBinding xmlns=""urn:schemas-microsoft-com:asm.v1"" >
            //    <linkedConfiguration href=""file://%ExchangeInstallDir%ClientAccess\SharedWebConfig.config"" />
            // </assemblyBinding>
            XNamespace aw = "urn:schemas-microsoft-com:asm.v1";
            XElement xelement = XElement.Parse(serviceConfig);
            var linkedConfiguration = xelement.Descendants(aw + "linkedConfiguration").FirstOrDefault();
#pragma warning disable CS8602 // Dereference of a possibly null reference.
            return linkedConfiguration != null && (linkedConfiguration.Parent.Name == aw + "assemblyBinding") && linkedConfiguration.Attribute("href") != null && (linkedConfiguration.Attribute("href").Value == @"file://%ExchangeInstallDir%ClientAccess\SharedWebConfig.config");
#pragma warning restore CS8602 // Dereference of a possibly null reference.
        }
    }
}
