﻿// <copyright file="ConsoleTracingExporterOptionsInherited.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

namespace Microsoft.M365.Core.Telemetry.R9.Tracing.Instrumentation.Accelerator
{
    using OpenTelemetry.Exporter;

    /// <summary>
    /// ConsoleTracingExporterOptionsInherited
    /// </summary>
    public class ConsoleTracingExporterOptionsInherited : ConsoleExporterOptions
    {
        /// <summary>
        /// IsEnabled
        /// </summary>
        public bool IsEnabled { get; set; }
    }
}