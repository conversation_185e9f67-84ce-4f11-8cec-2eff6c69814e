﻿// <copyright file="ODLTcpLogExporterExtensionTest.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Microsoft.M365.Core.Telemetry.Exporters.ODLTCP.Log;
using Microsoft.M365.Core.Telemetry.Exporters.ODLTCP.Trace;
using Microsoft.R9.Extensions.Logging;
using Newtonsoft.Json;
using OpenTelemetry;
using OpenTelemetry.Logs;
using Xunit;
using Assert = Xunit.Assert;

namespace Microsoft.R9.Extensions.Telemetry.Exporters.ODLTcp.Test
{
    public class ODLTcpLogExporterExtensionTest
    {
        [Fact]
        public void AddODLExporter_GivenInvalidArguments_ThrowsException()
        {
            Assert.Throws<ArgumentNullException>(() =>
           ((ILoggingBuilder)null!).AddODLTcpExporter((Action<ODLTcpLogExporterOptions>)null!, true));

            Assert.Throws<ArgumentNullException>(() =>
           new HostBuilder().ConfigureLogging(builder => builder
                            .AddOpenTelemetryLogging()
                            .AddODLTcpExporter((Action<ODLTcpLogExporterOptions>)null!, true))
                        .Build());
        }

        [Fact]
        public void AddODLExporter_GivenValidOptions_RegistersRequiredServices()
        {
            using var host = new HostBuilder()
                        .ConfigureLogging(builder => builder
                            .AddOpenTelemetryLogging()
                            .AddODLTcpExporter(
                                options =>
                                {
                                    options.LogTypeMappings = new Dictionary<string, string>
                                    {
                                        ["*"] = "TestEVR",
                                    };
                                }, false))
                        .Build();

            var options = host.Services.GetService<IOptions<ODLTcpLogExporterOptions>>();
            Assert.NotNull(options);
            Assert.IsAssignableFrom<IOptions<ODLTcpLogExporterOptions>>(options);
        }

        [Fact]
        public void AddODLExporter_UseSection_RegistersRequiredServices()
        {
            var exception = Record.Exception(() =>
            {
                IConfigurationRoot jsonConfigRoot = new ConfigurationBuilder().AddJsonFile("appsettings.json").Build();
                var config = jsonConfigRoot.GetSection("ODLTraceExporterWithParam");

                var options = new ODLTcpLogExporterOptions();
                jsonConfigRoot.Bind("ODLTraceExporterWithParam", options);
                config.Value = JsonConvert.SerializeObject(options, new JsonSerializerSettings()
                {
                    ReferenceLoopHandling = Newtonsoft.Json.ReferenceLoopHandling.Ignore
                });

                using var host = new HostBuilder()
                            .ConfigureServices(service => service
                                .AddOpenTelemetry().WithTracing(builder => builder
                                .AddODLTcpExporter(config, true)))
                            .Build();
            });
            Assert.Null(exception);
        }

        [Fact]
        public void AddODLExporter_UseInvalidSection_RegistersRequiredServices()
        {
            var exception = Record.Exception(() =>
            {
                IConfigurationRoot jsonConfigRoot = new ConfigurationBuilder().AddJsonFile("appsettings.json").Build();
                var config = jsonConfigRoot.GetSection("ODLExporterEmpty");

                var options = new ODLTcpLogExporterOptions();
                jsonConfigRoot.Bind("ODLExporterEmpty", options);
                config.Value = JsonConvert.SerializeObject(options, new JsonSerializerSettings()
                {
                    ReferenceLoopHandling = Newtonsoft.Json.ReferenceLoopHandling.Ignore
                });

                //settings not exist
                var configEmpty = jsonConfigRoot.GetSection("ODLExporterNotExist");
                var optionsEmpty = new ODLTcpLogExporterOptions();
                jsonConfigRoot.Bind("ODLExporterNotExist", optionsEmpty);
                configEmpty.Value = JsonConvert.SerializeObject(optionsEmpty, new JsonSerializerSettings()
                {
                    ReferenceLoopHandling = Newtonsoft.Json.ReferenceLoopHandling.Ignore
                });

                //settings invalid
                var configInvalid = jsonConfigRoot.GetSection("ODLExporterInvalid");
                var optionInvalid = new ODLTcpLogExporterOptions();
                jsonConfigRoot.Bind("ODLExporterInvalid", optionInvalid);
                configInvalid.Value = JsonConvert.SerializeObject(optionInvalid, new JsonSerializerSettings()
                {
                    ReferenceLoopHandling = Newtonsoft.Json.ReferenceLoopHandling.Ignore
                });

                //whenever empty or invalid or not exist, will use the default value of the options.
                Assert.Equal(configEmpty.Value, config.Value);
                Assert.Equal(configInvalid.Value, config.Value);

                using var host = new HostBuilder()
                            .ConfigureLogging(builder => builder
                                .AddOpenTelemetryLogging()
                                .AddODLTcpExporter(
                                    config, true))
                            .Build();
            });
            Assert.NotNull(exception);
        }

        [Fact]
        public void AddODLExporter_UseInvalidFile_ThrowException()
        {
            var exception = Record.Exception(() =>
            {
                IConfigurationRoot jsonConfigRoot = new ConfigurationBuilder().AddJsonFile("appsettings2.json").Build();
            });
            Assert.NotNull(exception);
        }

        [Fact]
        public void AddODLExporter_GivenInvalidPrepopulatedFields_ThrowsException()
        {
            Assert.Throws<ArgumentNullException>(() =>
           new HostBuilder().ConfigureLogging(builder => builder
                            .AddOpenTelemetryLogging()
                            .AddODLTcpExporter(
                                options =>
                                {
                                    options.PrepopulatedFields = new Dictionary<string, object>
                                    {
                                        ["Test"] = null!,
                                    };
                                    options.LogTypeMappings = new Dictionary<string, string>
                                    {
                                        ["*"] = "TestEVR",
                                    };
                                }, false))
                        .Build());
            Assert.Throws<ArgumentException>(() =>
           new HostBuilder().ConfigureLogging(builder => builder
                            .AddOpenTelemetryLogging()
                            .AddODLTcpExporter(
                                options =>
                                {
                                    options.PrepopulatedFields = new Dictionary<string, object>
                                    {
                                        ["Test"] = new TestObject(),
                                    };
                                    options.LogTypeMappings = new Dictionary<string, string>
                                    {
                                        ["*"] = "TestEVR",
                                    };
                                }, false))
                        .Build());
        }

        [Fact]
        public void AddODLExporter_Dispose()
        {
            using var host = new HostBuilder()
                        .ConfigureLogging(builder => builder
                            .AddOpenTelemetryLogging()
                            .AddODLTcpExporter(
                                options =>
                                {
                                    options.LogTypeMappings = new Dictionary<string, string>
                                    {
                                        ["*"] = "TestEVR",
                                    };
                                    options.LogSchema = LogSchema.GenevaSchema4WithScope;
                                }, false))
                        .Build();

            var options = host.Services.GetService<IOptions<ODLTcpLogExporterOptions>>();
            var exporter = host.Services.GetService<BaseExporter<LogRecord>>();
            exporter.Shutdown();
            Assert.NotNull(options);
            Assert.IsAssignableFrom<IOptions<ODLTcpLogExporterOptions>>(options);
        }
    }

    internal class TestObject
    {
        public string? Test { get; set; }
    }
}
