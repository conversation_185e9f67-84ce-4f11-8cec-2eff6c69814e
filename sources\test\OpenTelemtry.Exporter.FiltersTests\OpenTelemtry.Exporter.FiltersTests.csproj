<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <AssemblyName>Microsoft.M365.Core.Telemetry.OpenTelemetry.Exporter.Filters.Test</AssemblyName>
    <RootNamespace>Microsoft.M365.Core.Telemetry.OpenTelemetry.Exporter.Filters.Test</RootNamespace>
    <TargetFrameworks>net6.0;net8.0;net472</TargetFrameworks>
    <IsCodedUITest>False</IsCodedUITest>
    <TestProjectType>UnitTest</TestProjectType>
    <LangVersion>Latest</LangVersion>
    <NoWarn>SA1600</NoWarn>
    <NoWarn>$(NoWarn),CA1707,CA1307</NoWarn>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.NET.Test.Sdk" />
    <PackageReference Include="xunit" />
    <PackageReference Include="xunit.runner.visualstudio" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\dev\OpenTelemetry.Exporter.Filters\OpenTelemetry.Exporter.Filters.csproj" />
  </ItemGroup>

</Project>
