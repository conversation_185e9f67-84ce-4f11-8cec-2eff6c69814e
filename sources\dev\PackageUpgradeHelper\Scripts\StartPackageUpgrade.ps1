param (
    [Parameter(Mandatory=$false)]
    [string]$TargetVersion = "8.11.0",
    
    [Parameter(Mandatory=$false)]
    [string]$RootPath = "q:/src"
)

function Write-StepHeader {
    param (
        [string]$Title,
        [int]$Step,
        [int]$TotalSteps
    )
    
    Write-Host "`n"
    Write-Host ("=" * 80) -ForegroundColor Cyan
    Write-Host "STEP $Step/$TotalSteps $Title" -ForegroundColor Cyan
    Write-Host ("=" * 80) -ForegroundColor Cyan
}

function Invoke-ScriptWithErrorHandling {
    param (
        [string]$ScriptPath,
        [string]$Description,
        [hashtable]$Parameters = @{}
    )
    
    Write-Host "Running: $Description..." -ForegroundColor Yellow
    
    try {
        # Build the parameter display for logging purposes only
        $paramDisplay = ($Parameters.GetEnumerator() | ForEach-Object { "-$($_.Key) `"$($_.Value)`"" }) -join " "
        Write-Host "Executing: $ScriptPath $paramDisplay" -ForegroundColor DarkGray
        
        # Execute the script safely using the call operator and splatting
        & $ScriptPath @Parameters
        
        Write-Host "Completed: $Description" -ForegroundColor Green
        return $true
    } 
    catch {
        Write-Host "ERROR in $Description $_" -ForegroundColor Red
        return $false
    }
}

# Define paths
$targetPackagesPath = Join-Path -Path $RootPath -ChildPath "TelemetryCore\sources\dev\PackageUpgradeHelper\OutputFiles\targetPackages.txt"
$resultPath = Join-Path -Path $RootPath -ChildPath "TelemetryCore\sources\dev\PackageUpgradeHelper\OutputFiles\result.txt"
$upgradeOnlyListPath = Join-Path -Path $RootPath -ChildPath "TelemetryCore\sources\dev\PackageUpgradeHelper\OutputFiles\UpgradeOnlyList.txt"
$bindingRedirectCommandsPath = Join-Path -Path $RootPath -ChildPath "TelemetryCore\sources\dev\PackageUpgradeHelper\Scripts\complete-bindingredirect-commands.ps1"
$ScriptPath = Join-Path -Path $RootPath -ChildPath "TelemetryCore\sources\dev\PackageUpgradeHelper\Scripts"

# Start logging
$logFile = Join-Path -Path $RootPath -ChildPath "upgrade-workflow-log.txt"
if ($PSCmdlet.ShouldProcess($logFile, "Start transcript logging")) {
    Start-Transcript -Path $logFile -Append
}

Write-Host "Starting R9 Extensions Upgrade Workflow" -ForegroundColor Yellow
Write-Host "Target Version: $TargetVersion" -ForegroundColor Yellow
Write-Host "Working Directory: $RootPath" -ForegroundColor Yellow

$totalSteps = 7
$currentStep = 0


# Step 1: Generate target packages list
$currentStep++
Write-StepHeader -Title "Generate target R9 Extensions packages list" -Step $currentStep -TotalSteps $totalSteps
$step1Success = Invoke-ScriptWithErrorHandling -ScriptPath "$ScriptPath/GenerateTargetPackages.ps1" -Description "Generating target packages list" -Parameters @{
    TargetVersion = $TargetVersion
    OutputFile = $targetPackagesPath
    PackagesPropsPath = "$RootPath/Substrate/Packages.props"
}
if (-not $step1Success) { exit 1 }

# Step 2: Analyze package dependencies for target packages
$currentStep++
Write-StepHeader -Title "Analyze package dependencies for target packages" -Step $currentStep -TotalSteps $totalSteps
$step2Success = Invoke-ScriptWithErrorHandling -ScriptPath "$ScriptPath/AnalyzePackageDependencies.ps1" -Description "Analyzing package dependencies" -Parameters @{
    InputFile = $targetPackagesPath
    OutputFile = $resultPath
    SubstratePath = Join-Path -Path $RootPath -ChildPath "Substrate"
}
if (-not $step2Success) { exit 1 }

# Step 3: Analyze output to determine upgrade and dependency lists
$currentStep++
Write-StepHeader -Title "Analyze output to determine upgrade and dependency lists" -Step $currentStep -TotalSteps $totalSteps
$step3Success = Invoke-ScriptWithErrorHandling -ScriptPath "$ScriptPath/analyzeOutput.ps1" -Description "Analyzing output" -Parameters @{
    ResultFilePath = $resultPath
}
if (-not $step3Success) { exit 1 }

# Step 4: Analyze package dependencies for upgrade-only list
$currentStep++
Write-StepHeader -Title "Analyze package dependencies for upgrade-only list" -Step $currentStep -TotalSteps $totalSteps
$step4Success = Invoke-ScriptWithErrorHandling -ScriptPath "$ScriptPath/AnalyzePackageDependencies.ps1" -Description "Analyzing upgrade-only dependencies" -Parameters @{
    InputFile = $upgradeOnlyListPath
    OutputFile = $resultPath
    SubstratePath = Join-Path -Path $RootPath -ChildPath "Substrate"
}
if (-not $step4Success) { exit 1 }

# Step 5: Update packages.props and corext.config files
$currentStep++
Write-StepHeader -Title "Update packages.props and corext.config files" -Step $currentStep -TotalSteps $totalSteps
$step5Success = Invoke-ScriptWithErrorHandling -ScriptPath "$ScriptPath/updatePackagesProps.ps1" -Description "Updating packages.props and corext.config" -Parameters @{
    PackagesFilePath = "$RootPath/Substrate/Packages.props"
    CorextFilePath = "$RootPath/Substrate/build/corext/corext.config"
}
if (-not $step5Success) { exit 1 }

# Step 6: Extract binding redirect commands
$currentStep++
Write-StepHeader -Title "Extract binding redirect commands" -Step $currentStep -TotalSteps $totalSteps
$step6Success = Invoke-ScriptWithErrorHandling -ScriptPath "$ScriptPath/extractBindingRedirect.ps1" -Description "Extracting binding redirect commands" -Parameters @{
    InputFile = $resultPath
    OutputPath = $bindingRedirectCommandsPath
    ProjectPath = "$RootPath/Substrate"
}
if (-not $step6Success) { exit 1 }

# Step 7: Run binding redirect commands
$currentStep++
Write-StepHeader -Title "Run binding redirect commands" -Step $currentStep -TotalSteps $totalSteps

if (Test-Path $bindingRedirectCommandsPath) {
    # Execute the script directly with dot-sourcing instead of Invoke-Expression
    $step7Success = Invoke-ScriptWithErrorHandling -ScriptPath $bindingRedirectCommandsPath -Description "Running binding redirect commands"
    if (-not $step7Success) { exit 1 }
} else {
    Write-Host "WARNING: Binding redirect commands file not found at: $bindingRedirectCommandsPath" -ForegroundColor Yellow
    Write-Host "Skipping binding redirect execution step." -ForegroundColor Yellow
}

# Completion
Write-Host "`n"
Write-Host ("=" * 80) -ForegroundColor Green
Write-Host "UPGRADE WORKFLOW COMPLETED SUCCESSFULLY" -ForegroundColor Green
Write-Host ("=" * 80) -ForegroundColor Green
Write-Host "Summary of actions:"
Write-Host "1. Generated target packages with version $TargetVersion"
Write-Host "2. Analyzed package dependencies"
Write-Host "3. Analyzed output to determine upgrade needs"
Write-Host "4. Analyzed additional dependencies for upgrade-only packages"
Write-Host "5. Updated packages.props and corext.config files"
Write-Host "6. Extracted binding redirect commands"
Write-Host "7. Executed binding redirect commands"
Write-Host "`nLog saved to: $logFile"

if ($PSCmdlet.ShouldProcess($logFile, "Stop transcript logging")) {
    Stop-Transcript
}