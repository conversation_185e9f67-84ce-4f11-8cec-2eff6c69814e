// <copyright file="MatcherEnum.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Microsoft.M365.Core.Telemetry.R9.Logging.Substrate.Sampler
{
    /// <summary>
    /// Enum related methods for matcher methods.
    /// <see cref="TryMatch{TState}(in Extensions.Logging.Abstractions.LogEntry{TState})"/> for matching logic.
    /// </summary>
    internal partial class Matcher
    {
        private static bool CompareEnumValues(IReadOnlyList<KeyValuePair<string, object?>> state, string fieldName, string fieldValue, Func<string, List<string>, bool> compareFunc)
        {
            var fieldKVP = state.FirstOrDefault(kv => kv.Key == fieldName);
            if (fieldKVP.Value == null)
            {
                return false;
            }

            var logValue = fieldKVP.Value.ToString();
            if (logValue == null)
            {
                return false;
            }

            var constraintValues = fieldValue.Split(',').Select(v => v.Trim()).ToList();
            return compareFunc(logValue, constraintValues);
        }

        private static MatchFunc GenerateEnumMatchFunc(Constraint constraint)
        {
            var fieldName = constraint.Field;
            var fieldValue = constraint.Value;
            var ruleOperator = constraint.RuleOperator;

            return ruleOperator switch
            {
                OperatorType.In => (_, _, state) =>
                    CompareEnumValues(state, fieldName, fieldValue, (s, values) => values.Contains(s)),

                OperatorType.NotIn => (_, _, state) =>
                    CompareEnumValues(state, fieldName, fieldValue, (s, values) => !values.Contains(s)),

                _ => throw new NotImplementedException($"Unsupported operator type: {ruleOperator}") // This should never happen, we should have validated the constraints before.
            };
        }
    }
}
