<?xml version="1.0" encoding="utf-8"?>
<Project xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  
  <PropertyGroup>
    <MSBuildAllProjects>$(MSBuildAllProjects);$(MSBuildThisFileFullPath)</MSBuildAllProjects>
  </PropertyGroup>

  <!-- StrongName sign is not supported for unmanaged C++ assembly, use this to overrider the property in build/Extensions/CloudBuild/CodeSign.props -->
  <PropertyGroup Condition = "'$(ENABLE_CLOUDSIGN)' == '1'">
    <AssemblyCertificates Condition="'$(Language)'=='C++' and '$(CLRSupport)'=='false'">-6</AssemblyCertificates>
  </PropertyGroup>

  <PropertyGroup Condition = "'$(ENABLE_EPRS)' == '1'">
    <AssemblyCertificates Condition="'$(Language)'=='C++' and '$(CLRSupport)'=='false'">400</AssemblyCertificates>
  </PropertyGroup>

</Project>