﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <AssemblyName>Microsoft.M365.Core.Telemetry.R9.Tracing.Instrumentation.Accelerator</AssemblyName>
    <RootNamespace>$(AssemblyName)</RootNamespace>
    <TargetFrameworks>net8.0;net6.0;net472</TargetFrameworks>
    <GeneratePackageOnBuild>true</GeneratePackageOnBuild>
    <PackageId>Microsoft.M365.Core.Telemetry.R9.Tracing.Instrumentation.Accelerator</PackageId>
    <PackageVersion>$(R9TelemetryExtensionsCorePackageVersion)</PackageVersion>
    <PackageReleaseNotes>Add Email Redactor for HttpClient</PackageReleaseNotes>
    <Authors>SOTELS</Authors>
    <Product>Microsoft.M365.Core.Telemetry</Product>
    <Copyright>Copyright 2023 Microsoft Corp. All rights reserved.</Copyright>
    <Description>M365 SOTELS R9 based tracing onboarding tool.</Description>
    <PlatformTarget>anycpu</PlatformTarget>
    <LangVersion>9</LangVersion>
    <AssemblyVersion>********</AssemblyVersion>
    <NoWarn>NU5104,R9EXP0010</NoWarn>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.R9.Extensions.Essentials" />  
    <PackageReference Include="Microsoft.R9.Extensions.HttpClient.Tracing" />
    <PackageReference Include="Microsoft.R9.Extensions.Redaction" />
    <PackageReference Include="Microsoft.R9.Extensions.Tracing" />
    <PackageReference Include="Microsoft.R9.Extensions.Tracing.Exporters.Geneva" />
    <PackageReference Include="Microsoft.R9.Extensions.Tracing.Http" />
    <PackageReference Include="OpenTelemetry.Exporter.Console" />
    <PackageReference Include="OpenTelemetry.Exporter.Geneva" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="../R9TelemetryExtensionsCore/ECS.DynamicComponent/ECS.DynamicComponent.csproj" />
    <ProjectReference Include="../R9.Exporter.Filters/R9.Exporter.Filters.csproj"/>
  </ItemGroup>

  <ItemGroup Condition="'$(TargetFramework)' == 'net472'">
    <PackageReference Include="OpenTelemetry.Instrumentation.AspNet" />
  </ItemGroup>

  <ItemGroup>
    <AssemblyAttribute Include="System.Runtime.CompilerServices.InternalsVisibleTo">
      <_Parameter1>
        Microsoft.M365.Core.Telemetry.R9.Tracing.Instrumentation.Accelerator.Tests,PublicKey=002400000c800000140100000602000000240000525341310008000001000100db34a0ca5a2deee1a902251eda636ae9cc4db80f1fee44cbee596531e754b9d271092ad22c7011d1a4aded2b3e65c61037de0626a6beefd388afdbef44b659f4d076da6259adf74efcd92a84ec48f8e2090e8675457aa169a31a4156d5a777977c7b71b38b351e18465e6d2f72c7a237338425f213b26f42d82883209c511767c4556079db825cd81e27ee08c05c488cc35a1c5d30eba07bd84a2f7196ac98ca0278ec7c7d56303265ff9a24ebd6195ccf271bf1400cb960087d378eb499c34b09d5d07be2afba29357c85953dd7e5f31452f56bbddfbe3b67426f79d5013302f5e3c150446b71ccd103d5abba819aad8331a366509a6688a765091c4f26f1cf
    </_Parameter1>
    </AssemblyAttribute>
  </ItemGroup>

</Project>