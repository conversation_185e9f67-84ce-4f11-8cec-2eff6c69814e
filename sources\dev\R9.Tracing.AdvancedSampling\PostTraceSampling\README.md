## Post-Trace Sampling

### 0. Prerequisite:

1. Post-Trace Sampling using BaggageAPI to propagate the error state across tracing spans, the specified baggage name is `ErrorInTrace`
2. The main challenge for Post-Trace Sampling is how to back-propagate the error state from child spans to parent spans - we use HTTP Response Header to do this

There are 3 components for Post-Trace Sampling:

1. PostTraceFilter - Support ExporterWithFilter to filter traces with `ErrorInTrace` baggage
2. PostTraceMiddleware - Set HTTP Response Header `ErrorInTrace`  to back-propagate when detected Error HTTP Status Code or `ErrorInTrace` baggage for current Activity
3. PostTraceBuildExtensions - Enrich `ErrorInTrace` baggage for all parent spans when returned response has `ErrorInTrace` in its headers


### 1. Usage

#### 1.1 Middleware

```c#
public void Configure(IApplicationBuilder app)
{
    _ = app
        .UsePostTrace();
}
```



#### 1.2 PostTraceFilter & PostTraceBuildExtensions 

```c#
public void ConfigureServices(IServiceCollection services)
{
    _ = services
        .AddRouting()
        .AddControllers();

    var tracerProvider = Sdk.CreateTracerProviderBuilder()
        .AddSource(Utility.sourceName)
        .AddPostTracing()
        .AddGenevaTraceExporter(configure: option => { option.ConnectionString = "EtwSession=OpenTelemetry"; }, new PostTraceFilter())
        .Build();
}
```

