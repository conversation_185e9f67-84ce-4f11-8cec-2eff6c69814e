// <copyright file="Constants.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

namespace Microsoft.M365.Core.Telemetry.R9.Logging.Substrate.Sampler
{
    /// <summary>
    /// Constants for constraint types used in log entry matching.
    /// </summary>
#pragma warning disable SA1600 // Elements should be documented
    internal static class ConstraintType
    {
        public const string String = "String";

        public const string Long = "Long";

        public const string Double = "Double";

        public const string EventId = "EventId";

        public const string LogLevel = "LogLevel";

        public const string Enum = "Enum";
    }

    /// <summary>
    /// Constants for operator types used in log entry matching.
    /// </summary>
    internal static class OperatorType
    {
        public const string LessThan = "<";

        public const string LessThanOrEqual = "<=";

        public const string GreaterThan = ">";

        public const string GreaterThanOrEqual = ">=";

        public const string NumericEquals = "==";

        public const string NumericNotEquals = "!=";

        public const string StringEquals = "Equals";

        public const string StringNotEquals = "NotEquals";

        public const string StartsWith = "StartsWith";

        public const string NotStartsWith = "NotStartsWith";

        public const string EndsWith = "EndsWith";

        public const string NotEndsWith = "NotEndsWith";

        public const string In = "In";

        public const string NotIn = "NotIn";
    }

    /// <summary>
    /// Cconstant values for sampling strategy type.
    /// </summary>
    internal static class StrategyType
    {
        public const string Random = "Random";

        public const string HashBasedRandom = "HashBasedRandom";
    }
#pragma warning restore SA1600 // Elements should be documented
}