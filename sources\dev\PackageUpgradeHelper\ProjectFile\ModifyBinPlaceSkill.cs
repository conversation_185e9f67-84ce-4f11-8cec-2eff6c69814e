﻿// <copyright file="ModifyBinPlaceSkill.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Xml;
using System.Xml.Linq;

namespace PackageUpgradeHelper.ProjectFile
{
    /// <summary>
    /// ModifyBinPlaceSkill.
    /// </summary>
    public class ModifyBinPlaceSkill
    {
        /// <summary>
        /// ModifyBinPlace method for SK.
        /// </summary>
        /// <param name="dllList">dllList.</param>
        /// <param name="binPlaceContent">binPlaceContent.</param>
        public string ModifyBinPlace(List<DllRecord> dllList, string binPlaceContent)
        {
            if (dllList.Count == 0)
            {
                return binPlaceContent;
            }

            if (string.IsNullOrEmpty(binPlaceContent))
            {
                throw new ArgumentException("binPlaceContent cannot be null or empty");
            }

            XElement xelementBinPlaceForPackages = XElement.Parse(binPlaceContent);
            StringBuilder addedBinPlaceNetFramework = new StringBuilder();
            StringBuilder addedBinPlaceNetCore = new StringBuilder();
            HashSet<string> addedPackages = new HashSet<string>();

            foreach (DllRecord dll in dllList)
            {
                string dllName = dll.DllName; // ex. Microsoft.R9.Extensions.Metering.Geneva.dll
                string normalPackageName = dll.PackageName; // ex. Microsoft.R9.Extensions.Metering.Geneva
                string sourcePath = dll.LibraryDirectoryPath; // ex. lib\net461
                string framework = dll.TargetFramework; // ex. NetFramework
                string fullPath = dll.HintPath; // ex. $(PkgMicrosoft_R9_Extensions_Logging)\lib\net462\Microsoft.R9.Extensions.Logging.dll

                string netCoreElement = $"    <BinPlaceNetCore Include=\"{fullPath}\" />\r\n";
                bool netCoreInserted = false;
                string netFrameworkElement = $"    <BinPlaceNetFramework Include=\"{fullPath}\" />\r\n";
                bool netFrameworkInserted = false;
                string packageReferenceElement = $"    <PackageReference Include=\"{normalPackageName}\" GeneratePathProperty=\"true\" />\r\n";

                bool packageExists = xelementBinPlaceForPackages.Descendants("PackageReference")
                    .Any(e => e.Attribute("Include")?.Value == normalPackageName);

                if (!packageExists)
                {
                    XElement? itemGroup = xelementBinPlaceForPackages.Descendants("ItemGroup").FirstOrDefault(ig => ig.Elements("PackageReference").Any());

                    if (itemGroup != null)
                    {
                        XElement newPackageReference = XElement.Parse(packageReferenceElement);
                        bool inserted = false;

                        foreach (XElement packageReference in itemGroup.Elements("PackageReference").ToList())
                        {
                            if (string.Compare(packageReference.Attribute("Include")?.Value, normalPackageName, StringComparison.OrdinalIgnoreCase) > 0)
                            {
                                packageReference.AddBeforeSelf(newPackageReference);
                                inserted = true;
                                break;
                            }
                        }

                        if (!inserted)
                        {
                            itemGroup.Add(newPackageReference);
                        }
                    }
                }

                if (framework == "NetCore" && sourcePath.Contains("net8.0"))
                {
                    List<XElement> itemGroups = xelementBinPlaceForPackages.Descendants("ItemGroup")
                        .Where(ig => ig.Elements("BinPlaceNetCore").Any()).ToList();

                    if (itemGroups.Count > 0)
                    {
                        for (int i = 0; i < itemGroups.Count; i++)
                        {
                            XElement itemGroup = itemGroups[i];
                            XElement? existingElement = itemGroup.Elements("BinPlaceNetCore")
                                .FirstOrDefault(e => Path.GetFileName(e.Attribute("Include")?.Value) == dllName);

                            if (existingElement != null)
                            {
                                existingElement.SetAttributeValue("Include", fullPath);
                                netCoreInserted = true;
                            }
                            else
                            {
                                XElement newElement = XElement.Parse(netCoreElement);

                                foreach (XElement binPlaceElement in itemGroup.Elements("BinPlaceNetCore").ToList())
                                {
                                    if (string.Compare(binPlaceElement.Attribute("Include")?.Value, fullPath, StringComparison.OrdinalIgnoreCase) > 0)
                                    {
                                        binPlaceElement.AddBeforeSelf(newElement);
                                        netCoreInserted = true;
                                        break;
                                    }
                                }

                                if (!netCoreInserted && i == itemGroups.Count - 1)
                                {
                                    itemGroup.Add(newElement);
                                }
                            }

                            if (netCoreInserted)
                            {
                                break;
                            }
                        }
                    }
                }

                if (framework == "NetFramework")
                {
                    List<XElement> itemGroups = xelementBinPlaceForPackages.Descendants("ItemGroup")
                        .Where(ig => ig.Elements("BinPlaceNetFramework").Any()).ToList();

                    if (itemGroups.Count > 0)
                    {
                        for (int i = 0; i < itemGroups.Count; i++)
                        {
                            XElement itemGroup = itemGroups[i];
                            XElement? existingElement = itemGroup.Elements("BinPlaceNetFramework")
                                .FirstOrDefault(e => Path.GetFileName(e.Attribute("Include")?.Value) == dllName);

                            if (existingElement != null)
                            {
                                existingElement.SetAttributeValue("Include", fullPath);
                                netFrameworkInserted = true;
                            }
                            else
                            {
                                XElement newElement = XElement.Parse(netFrameworkElement);

                                foreach (XElement binPlaceElement in itemGroup.Elements("BinPlaceNetFramework").ToList())
                                {
                                    if (string.Compare(binPlaceElement.Attribute("Include")?.Value, fullPath, StringComparison.OrdinalIgnoreCase) > 0)
                                    {
                                        binPlaceElement.AddBeforeSelf(newElement);
                                        netFrameworkInserted = true;
                                        break;
                                    }
                                }

                                if (!netFrameworkInserted && i == itemGroups.Count - 1)
                                {
                                    itemGroup.Add(newElement);
                                }
                            }

                            if (netFrameworkInserted)
                            {
                                break;
                            }
                        }
                    }
                }
            }
            string updatedBinPlaceForPackages = xelementBinPlaceForPackages.ToString();
            return updatedBinPlaceForPackages;
        }
    }
}
