﻿// <copyright file="HttpClientEUIIRedactEnricher.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

#if !NETFRAMEWORK
namespace Microsoft.M365.Core.Telemetry.R9.Tracing.Instrumentation.Accelerator
{
    using System.Diagnostics;
    using System.Net.Http;
    using Microsoft.R9.Extensions.HttpClient.Tracing;
    using Microsoft.R9.Extensions.Telemetry;

    /// <summary>
    /// HttpClientEUIIRedactEnricher
    /// </summary>
    internal sealed class HttpClientEUIIRedactEnricher : IHttpClientTraceEnricher
    {
        private HttpClientTracingOptionsInherited Options { get; }

        /// <summary>
        /// HttpClientEUIIRedactEnricher
        /// </summary>
        /// <param name="options"></param>
        public HttpClientEUIIRedactEnricher(HttpClientTracingOptionsInherited options)
        {
            this.Options = options;
        }

        /// <summary>
        /// Enrich
        /// </summary>
        /// <param name="activity"></param>
        /// <param name="request"></param>
        public void Enrich(Activity activity, HttpRequestMessage request)
        {
            if (request == null)
            {
                return;
            }

            var redactedValue = EUIIRedactor.RedactEgressPath(request.RequestUri.AbsolutePath, Options.RedactionStrategyType);
            var httpUrl = Utility.DeriveHttpUrl(request, redactedValue);

            request.SetRequestMetadata(new RequestMetadata
            {
                RequestRoute = $"/{redactedValue}",
                RequestName = $"{request.Method} {httpUrl}",
            });

            activity?.SetTag(Constants.HttpUrlBackup, httpUrl);
        }
    }
}
#endif