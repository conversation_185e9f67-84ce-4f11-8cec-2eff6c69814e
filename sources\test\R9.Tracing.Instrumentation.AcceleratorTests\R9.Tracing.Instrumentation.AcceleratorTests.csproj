﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <AssemblyName>Microsoft.M365.Core.Telemetry.R9.Tracing.Instrumentation.Accelerator.Tests</AssemblyName>
    <RootNamespace>$(AssemblyName)</RootNamespace>
    <TargetFrameworks>net472;net8.0;net6.0</TargetFrameworks>
    <IsCodedUITest>False</IsCodedUITest>
    <TestProjectType>UnitTest</TestProjectType>
    <IsPackable>false</IsPackable>
    <PlatformTarget>anycpu</PlatformTarget>
    <LangVersion>9</LangVersion>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.NET.Test.Sdk" />
    <PackageReference Include="NSubstitute" />
    <PackageReference Include="xunit" />
    <PackageReference Include="xunit.runner.visualstudio" />
    <PackageReference Include="coverlet.collector" />
    <PackageReference Include="OpenTelemetry.Exporter.Geneva" />
  </ItemGroup>

  <ItemGroup Condition="'$(TargetFramework)' == 'net472'">
    <PackageReference Include="Microsoft.Extensions.Configuration.Json" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\..\dev\R9.Tracing.Instrumentation.Accelerator\R9.Tracing.Instrumentation.Accelerator.csproj" />
  </ItemGroup>

  <ItemGroup>
    <None Update="appsettings.json">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
  </ItemGroup>

</Project>
