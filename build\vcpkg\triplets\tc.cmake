# vcpkg vars
set(VCPKG_TARGET_ARCHITECTURE x64)
set(VCPKG_CRT_LINKAGE dynamic)
set(VCPKG_LIBRARY_LINKAGE static)
set(VCPKG_CXX_FLAGS "/Zc:__cplusplus /std:c++20 /guard:cf")
set(VCPKG_C_FLAGS "/Zc:__cplusplus /std:c++20 /guard:cf")
set(VCPKG_LINKER_FLAGS "/guard:cf")
set(VCPKG_LOAD_VCVARS_ENV 0)
set(VCPKG_CHAINLOAD_TOOLCHAIN_FILE ${VCPKG_ROOT_DIR}/scripts/toolchains/windows.cmake)
set(VCPKG_PLATFORM_TOOLSET v143)
set(VCPKG_ENV_PASSTHROUGH_UNTRACKED VS170COMNTOOLS NUGET_PACKAGES NETFXSDKDir)
set(ENV{NugetPackageLocation} "$ENV{NUGET_PACKAGES}")

# Compiler
set(ENV{DisableRegistryUse} "true")
set(ENV{VctPkgDir} "$ENV{NugetPackageLocation}\\VisualCppTools")
set(ENV{VctPkgVer} "14.36.32535")
set(ENV{VCInstallDir} "$ENV{VctPkgDir}\\$ENV{VctPkgVer}\\lib\\native\\")
set(ENV{VCToolsInstallDir} "$ENV{VCInstallDir}")
set(ENV{VCToolsInstallDir_170} "$ENV{VCInstallDir}")
set(ENV{VC_VC_IncludePath} "$ENV{VCToolsInstallDir}include")
set(ENV{VC_LibraryPath_VC_x86_Desktop} "$ENV{VCToolsInstallDir}lib;")
set(ENV{VC_LibraryPath_VC_x64_Desktop} "$ENV{VCToolsInstallDir}lib\\amd64;")
set(ENV{VC_LibraryPath_VC_arm64_Desktop} "$ENV{VCToolsInstallDir}lib\\arm64;")
set(ENV{VCToolsVersion} "$ENV{VctPkgVer}")
set(ENV{CheckMSVCComponents} "false")
set(ENV{VC_ExecutablePath_x86_x86} "$ENV{VCInstallDir}bin")
set(ENV{VC_ExecutablePath_x64_x86} "$ENV{VCInstallDir}bin\\amd64_x86")
set(ENV{VC_ExecutablePath_x86_x64} "$ENV{VCInstallDir}bin\\x86_amd64")
set(ENV{VC_ExecutablePath_x64_x64} "$ENV{VCInstallDir}bin\\amd64")
set(ENV{VC_ExecutablePath_x86_arm64} "$ENV{VCInstallDir}bin\\x86_arm64")
set(ENV{VC_ExecutablePath_x64_arm64} "$ENV{VCInstallDir}bin\\amd64_arm64")
set(ENV{VC_LibraryPath_VC_x86} "$ENV{VCInstallDir}lib")
set(ENV{VC_LibraryPath_VC_x64} "$ENV{VCInstallDir}lib\\amd64")
set(ENV{VC_LibraryPath_VC_arm64} "$ENV{VCInstallDir}lib\\arm64")
set(ENV{VC_RedistPath_Release_x64} "$ENV{VCInstallDir}redist\\x64\\Microsoft.VC143.CRT")
set(ENV{VC_RedistPath_Debug_x64} "$ENV{VCInstallDir}redist\\debug_nonredist\\x64\\Microsoft.VC143.DebugCRT")

# Windows SDK
# Microsoft.Windows.SDK.cpp.props
set(ENV{WinPkgDir} "$ENV{NugetPackageLocation}\\Microsoft.Windows.SDK.CPP")
set(ENV{WinPkgVer} "10.0.20348.19")
set(ENV{TargetPlatformVersion} "10.0.20348.0")
set(ENV{WindowsTargetPlatformVersion} "$ENV{TargetPlatformVersion}")
set(ENV{XesUwpHomeBuild} "$ENV{WinPkgDir}\\$ENV{WinPkgVer}\\build")
set(ENV{UwpRootOverride} "$ENV{WinPkgDir}\\$ENV{WinPkgVer}")
set(ENV{TargetPlatformSdkRootOverride} "$ENV{WinPkgDir}\\$ENV{WinPkgVer}\\c")
set(ENV{UwpSDKOverRide} "$ENV{WinPkgDir}\\$ENV{WinPkgVer}\\c")
set(ENV{TargetPlatformWinMDLocation} "$ENV{TargetPlatformSdkRootOverride}\\References")
set(ENV{WindowsSDK_MetadataFoundationPath} "$ENV{TargetPlatformWinMDLocation}\\$ENV{TargetPlatformVersion}")
set(ENV{WindowsSDKInstalled} "true")
set(ENV{WindowsSDK_UAP_Support} "true")
set(ENV{WindowsSDK_Desktop_Support} "true")
set(ENV{WindowsSDKVersionedBinRoot} "$ENV{TargetPlatformSdkRootOverride}\\bin\\$ENV{TargetPlatformVersion}")
set(ENV{WindowsSdkDir} "$ENV{TargetPlatformSdkRootOverride}\\")
set(ENV{WindowsSdkPath} "$ENV{WindowsSdkDir}")
set(ENV{WindowsSdkDir_10} "$ENV{WindowsSdkDir}")
set(ENV{WindowsSdkDir} "$ENV{WindowsSdkDir}")
set(ENV{UniversalCRTSdkDir_10} "$ENV{WindowsSdkDir}")
set(ENV{UCRTContentRoot} "$ENV{WindowsSdkDir}")
# Microsoft.Windows.SDK.cpp.x64.props
set(ENV{winsdk_cpp_x64_root} "$ENV{NugetPackageLocation}\\Microsoft.Windows.SDK.CPP.X64\\10.0.20348.19\\c")
set(ENV{WindowsSdkLibDir} "$ENV{winsdk_cpp_x64_root}")
set(ENV{DotNetSdk_LibraryPath_x64} "$ENV{NETFXSDKDir}lib\\um\\x64")
set(ENV{WindowsSDK_LibraryPath_x64} "$ENV{winsdk_cpp_x64_root}\\um\\x64;$ENV{DotNetSdk_LibraryPath_x64};")
set(ENV{UniversalCRT_LibraryPath_x64} "$ENV{winsdk_cpp_x64_root}\\ucrt\\x64;")
set(ENV{IlcTargetPlatformSdkLibPath} "$ENV{winsdk_cpp_x64_root}\\um")
# Vars from Kits
set(ENV{UCRTVersion} "$ENV{TargetPlatformVersion}")
set(ENV{WindowsSDKVersion} "$ENV{TargetPlatformVersion}\\")  # Used to deduce UCRT path.
set(ENV{WindowsSDKLibVersion} "$ENV{TargetPlatformVersion}\\")  # Used to deduce UCRT path.
set(ENV{WindowsSdkBinPath} "$ENV{WindowsSdkDir}bin\\")
set(ENV{WindowsSdkVerBinPath} "$ENV{WindowsSdkBinPath}$ENV{TargetPlatformVersion}\\")
set(ENV{WindowsLibPath} "$ENV{WindowsSdkDir}UnionMetadata\\$ENV{TargetPlatformVersion}\\;$ENV{WindowsSdkDir}References\\$ENV{TargetPlatformVersion}\\")
set(ENV{WindowsSDK_ExecutablePath_x64} "$ENV{WindowsSdkVerBinPath}x64")
set(ENV{WindowsSDK_ExecutablePath_x86} "$ENV{WindowsSdkVerBinPath}x86")
set(ENV{UCRTDebug} "$ENV{WindowsSDK_ExecutablePath_x64}\\ucrt")

## Some paths
set(ENV{ClearDevCommandPromptEnvVars} "false")
set(ENV{PATH} "$ENV{VS170COMNTOOLS}\\..\\..\\MSBuild\\Current\\Bin;$ENV{VC_ExecutablePath_x64_x64};$ENV{VC_ExecutablePath_x86_x86};$ENV{WindowsSDK_ExecutablePath_x64};$ENV{WindowsSDK_ExecutablePath_x86};$ENV{PATH}")
set(ENV{LIB} "$ENV{VC_LibraryPath_VC_x64_Desktop};$ENV{WindowsSDK_LibraryPath_x64};$ENV{UniversalCRT_LibraryPath_x64};$ENV{LIB}")
set(ENV{LibraryPath} "$ENV{LIB}")
set(ENV{WindowsSdkIncludeDir} "$ENV{WindowsSdkDir}Include\\$ENV{WindowsTargetPlatformVersion}\\")
set(ENV{INCLUDE} "$ENV{VC_VC_IncludePath};$ENV{VCInstallDir}\\atlmfc\\include;$ENV{WindowsSdkIncludeDir}ucrt;$ENV{WindowsSdkIncludeDir}winrt;$ENV{WindowsSdkIncludeDir}shared;$ENV{WindowsSdkIncludeDir}um;$ENV{INCLUDE}")


#get_cmake_property(_variables VARIABLES)
#list (SORT _variables)
#message(STATUS "=============================================")
#message(STATUS "Printing all cmake variables")
#foreach (_variable ${_variables})
#    message("${_variable} = ${${_variable}}")
#endforeach()
#message(STATUS "=============================================")
#message(STATUS "Printing all env variables")
#execute_process(COMMAND cmd /c set)
#message(STATUS "=============================================")