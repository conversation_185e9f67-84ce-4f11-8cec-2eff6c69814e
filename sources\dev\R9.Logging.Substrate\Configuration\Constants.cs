﻿// <copyright file="Constants.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System.Collections.Generic;
using System.Diagnostics.CodeAnalysis;

namespace Microsoft.M365.Core.Telemetry.R9.Logging.Substrate.Configuration
{
    /// <summary>
    /// Constants class
    /// </summary>
    [ExcludeFromCodeCoverage]
    internal static class Constants
    {
        /// <summary>
        /// The ECS client name for Unified Telemetry.
        /// </summary>
        internal const string ECSClientName = "UnifiedTelemetry";

        /// <summary>
        /// The application id used to connect to AAD.
        /// </summary>
        internal const string ApplicationId = "a65f7e1e-db61-4b02-b524-882bef441e18";

        /// <summary>
        /// The ECS team name for Logging.
        /// </summary>
        internal static readonly HashSet<string> ECSTeamNameInt = new HashSet<string>() { "Log" };

        /// <summary>
        /// THe ECS team names in prod environment.
        /// </summary>
        internal static readonly HashSet<string> ECSTeamNameProd = new HashSet<string>() { "Log" };

        /// <summary>
        /// The certificate names in different environments.
        /// </summary>
        internal static readonly Dictionary<string, string> CertificateNames = new Dictionary<string, string>
        {
            { "EXO", "unifiedtelemetry-prod-outlook-com" },
            { "EOP", "unifiedtelemetry-prod-eop-outlook-com" },
            { "Gallatin", "unifiedtelemetry-prod-gallatin-outlook-com" },
            { "Itar", "unifiedtelemetry-prod-itar-outlook-com" },
            { "Cosmic", "unifiedtelemetry-prod-cosmic-outlook-com" },
            { "ModelD", "unifiedtelemetry-prod-modeld-outlook-com" }
        };

        /// <summary>
        /// The host to request authority.
        /// </summary>
        internal const string MicrosoftHost = "https://login.microsoftonline.com";

        /// <summary>
        /// The tenant id of application.
        /// </summary>
        internal const string TenantId = "cdc5aeea-15c5-4db6-b079-fcadd2505dc2";
    }
}
