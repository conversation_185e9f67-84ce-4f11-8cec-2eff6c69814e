// <copyright file="CompositeLogRecordExporterOptionsValidator.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using Microsoft.Extensions.Options;

namespace Microsoft.M365.Core.Telemetry.R9.Logging.Substrate
{
    /// <summary>
    /// Validates the <see cref="CompositeLogRecordExporterOptions"/>.
    /// </summary>
    public class CompositeLogRecordExporterOptionsValidator : IValidateOptions<CompositeLogRecordExporterOptions>
    {
        /// <summary>
        /// Validates the specified <see cref="CompositeLogRecordExporterOptions"/>.
        /// </summary>
        /// <param name="name">The name of the options instance being validated.</param>
        /// <param name="options">The options instance to validate.</param>
        /// <returns>The result of the validation.</returns>
        public ValidateOptionsResult Validate(string? name, CompositeLogRecordExporterOptions options)
        {
            ValidateOptionsResultBuilder validateOptionsResultBuilder = new ValidateOptionsResultBuilder();

            if (options.VirtualTableMappings.Count == 0)
            {
                validateOptionsResultBuilder.AddError("At least one virtual table mapping must be provided.");
            }
            foreach (var mapping in options.VirtualTableMappings)
            {
                if (string.IsNullOrWhiteSpace(mapping.Key))
                {
                    validateOptionsResultBuilder.AddError("Virtual table mapping key cannot be null or empty.");
                }
                if (string.IsNullOrWhiteSpace(mapping.Value.Trim('*')))
                {
                    validateOptionsResultBuilder.AddError("Virtual table mapping value cannot be null, empty or '*'.");
                }
            }

            return validateOptionsResultBuilder.Build();
        }
    }
}
