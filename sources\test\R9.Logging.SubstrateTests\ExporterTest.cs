// <copyright file="ExporterTest.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading;
using FluentAssertions;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Microsoft.M365.Core.Telemetry.R9.Logging.Substrate;
using Microsoft.R9.Extensions.Logging;
using Microsoft.R9.Extensions.Logging.Exporters;
using OpenTelemetry;
using OpenTelemetry.Logs;
using Xunit;

namespace R9.Logging.SubstrateTests
{
    public class ExporterTest
    {
        private const string BasicConfig = @"
{
    ""SubstrateLogging"": {
        ""UseCompositeExporter"": true,
        ""CompositeExporter"": {
            ""VirtualTableMappings"": {
                ""Test.TestEvent.*"": ""TestEventTable"",
                ""Test.Event*"": ""TestEventTable"",
                ""R9.Logging.SubstrateTests.ExporterTest"": ""MyServiceTable"",
                ""*"": ""MyServiceTable""
            },
            ""Geneva"": {
                ""ConnectionString"": ""EtwSession=test""
            },
            ""OdlTcp"": {
                ""ConnectionString"": ""tcp://localhost:1234""
            },
            ""VirtualTableExports"": {
                ""TestEventTable"": [
                    {
                        ""ExporterType"": ""Geneva"",
                        ""ExportTable"": ""TestEvent""
                    },
                    {
                        ""ExporterType"": ""OdlTcp"",
                        ""ExportTable"": ""TestLogType""
                    }
                ],
                ""MyServiceTable"": [
                    {
                        ""ExporterType"": ""Geneva"",
                        ""ExportTable"": ""ServiceEvent""
                    },
                    {
                        ""ExporterType"": ""OdlTcp"",
                        ""ExportTable"": ""TestLogType""
                    }
                ]
            }
        }
    }
}";

        [Fact]
        public void LogAndExport_Success()
        {
            IConfiguration configuration = new ConfigurationBuilder()
                .AddJsonStream(new MemoryStream(Encoding.UTF8.GetBytes(BasicConfig)))
                .Build();
            IServiceCollection? services = null;

            var factory = LoggerFactory.Create(builder =>
            {
                builder.AddConsoleExporter().ConfigureSubstrateLogging(configuration);
                services = builder.Services;
            });
            var logger = factory.CreateLogger<ExporterTest>();

            Assert.NotNull(logger);
            Assert.True(services?.Any(services => services.ImplementationType == typeof(CompositeLogRecordExportProcessor)));
            var writer = new StringWriter();
            Console.SetOut(writer);

            MockSendingLog(logger);

            // Assert.
            string output = writer.GetStringBuilder().ToString();
            output.Should().Contain("Test Event");
        }

        [Fact]
        public void ChooseExporter_Composite()
        {
            IConfiguration configuration = new ConfigurationBuilder()
                .AddJsonStream(new MemoryStream(Encoding.UTF8.GetBytes(BasicConfig)))
                .Build();
            IServiceCollection services = new ServiceCollection();
            services.AddSubstrateLogging(configuration);
            Assert.Contains(services, services => services.ImplementationType == typeof(CompositeLogRecordExportProcessor));
        }

        [Fact]
        public void ChooseExporter_SimpleGeneva()
        {
            // Explicitly set to use geneva exporter
            var updatedJson = BasicConfig
                .UpdateJsonSection("SubstrateLogging:UseCompositeExporter", "false")
                .UpdateJsonSection("SubstrateLogging:CompositeExporter")
                .UpdateJsonSection("SubstrateLogging:GenevaExporter", @"{""ConnectionString"": ""EtwSession=test""}")
                .UpdateJsonSection("SubstrateLogging:GenevaExporter:TableNameMappings", @"{""*"": ""A""}");
            IConfiguration configuration = new ConfigurationBuilder()
                .AddJsonStream(new MemoryStream(Encoding.UTF8.GetBytes(updatedJson)))
                .Build();
            IServiceCollection services = new ServiceCollection();
            services.AddSubstrateLogging(configuration);
            Assert.Contains(services, s => s.ImplementationType is not null && s.ImplementationType.Name.Contains("Geneva"));
            Assert.DoesNotContain(services, s => s.ImplementationType is not null && s.ImplementationType.Name.Contains("CompositeExporter"));
            var serviceProvider = services.BuildServiceProvider();
            var logger1 = serviceProvider.GetRequiredService<ILogger<ExporterTest>>();
            MockSendingLog(logger1);

            // Default action: use geneva exporter
            updatedJson = updatedJson
                .UpdateJsonSection("SubstrateLogging:UseCompositeExporter");
            configuration = new ConfigurationBuilder()
                .AddJsonStream(new MemoryStream(Encoding.UTF8.GetBytes(updatedJson)))
                .Build();
            var factory = LoggerFactory.Create(builder =>
            {
                builder.ConfigureSubstrateLogging(configuration);
                services = builder.Services;
            });
            Assert.Contains(services, s => s.ImplementationType is not null && s.ImplementationType.Name.Contains("Geneva"));
            Assert.DoesNotContain(services, s => s.ImplementationType is not null && s.ImplementationType.Name.Contains("CompositeExporter"));
            var logger2 = factory.CreateLogger<ExporterTest>();
            MockSendingLog(logger2);
        }

        [Theory]
        [InlineData("SubstrateLogging:CompositeExporter:VirtualTableMappings", @"{}", false)] // empty mapping
        [InlineData("SubstrateLogging:CompositeExporter:VirtualTableMappings", @"{""*"": """"}", false)] // invalid mapping destination
        [InlineData("SubstrateLogging:CompositeExporter:VirtualTableMappings", @"{""*"": ""*""}", false)] // invalid mapping destination
        [InlineData("SubstrateLogging:CompositeExporter:VirtualTableMappings", @"{""*"": ""AnyEvent""}", true)]
        public void ValidateOptions_Fail(string section, string content, bool isValid)
        {
            var updatedJson = BasicConfig
                            .UpdateJsonSection(section, content);
            IConfiguration configuration = new ConfigurationBuilder()
                .AddJsonStream(new MemoryStream(Encoding.UTF8.GetBytes(updatedJson)))
                .Build();

            var buildAction = (ILoggingBuilder builder) =>
            {
                builder.ConfigureSubstrateLogging(configuration);
            }; 

            if (isValid)
            {
                LoggerFactory.Create(buildAction);
            }
            else
            {
                Assert.Throws<OptionsValidationException>(() => LoggerFactory.Create(buildAction));
            }
        }

        [Fact]
        public void RefreshConfig()
        {
            IConfiguration configuration = new ConfigurationBuilder()
                .AddJsonStream(new MemoryStream(Encoding.UTF8.GetBytes(BasicConfig)))
                .Add(new DummyConfigurationSource())
                .Build();
            IServiceCollection? services = null;
            var provider = (configuration as IConfigurationRoot).Providers.First(p => p is DummyConfigurationProvider) as DummyConfigurationProvider;

            var factory = LoggerFactory.Create(builder =>
            {
                builder.ConfigureSubstrateLogging(configuration);
                services = builder.Services;
            });
            var logger = factory.CreateLogger<ExporterTest>();

            Assert.NotNull(logger);
            Assert.True(services?.Any(services => services.ImplementationType == typeof(CompositeLogRecordExportProcessor)));
            logger.LogInformation("Test Event");

            // mock Refresh
            provider.Update();
            MockSendingLog(logger);
        }

        /// <summary>
        /// Test the TryMatchValue extension method. Match rules for the routing map.
        /// </summary>
        /// <param name="matchKey"></param>
        /// <param name="matchResult"></param>
        /// <param name="eventCategory"></param>
        /// <param name="expectedExport"></param>
        [Theory]
        [InlineData(new string[] { "Test1.Test2" }, new LogExportType[] { LogExportType.Geneva }, "Test1.Test2", LogExportType.Geneva )] // Success
        [InlineData(new string[] { "Test1.Test3" }, new LogExportType[] { LogExportType.Geneva }, "Test1.Test2", LogExportType.None )] // Fail
        [InlineData(new string[] { "Test1" },      new LogExportType[] { LogExportType.Geneva }, "Test1.Test2", LogExportType.None)] // Fail, need full match
        [InlineData(new string[] { "Test1.test2" },      new LogExportType[] { LogExportType.Geneva }, "Test1.Test2", LogExportType.None)] // Fail, case-sensitive
        [InlineData(new string[] { "*" },           new LogExportType[] { LogExportType.Geneva }, "Test1.Test2", LogExportType.Geneva)] // Success
        [InlineData( // * for all other not matched categories
            new string[] { "Test1.Test3", "*" },
            new LogExportType[] { LogExportType.Geneva, LogExportType.OdlTcp },
            "Test1.Test2", LogExportType.OdlTcp )]
        [InlineData( // If a category has been defined, it will ignore the * rule
            new string[] { "Test1.Test2", "*" },
            new LogExportType[] { LogExportType.OdlTcp, LogExportType.Geneva },
            "Test1.Test2", LogExportType.OdlTcp )]
        [InlineData( // The order does not matter, * will always be the last rule
            new string[] { "*", "Test1.Test2" },
            new LogExportType[] { LogExportType.Geneva, LogExportType.OdlTcp },
            "Test1.Test2", LogExportType.OdlTcp )]
        internal void MatchingRule_Success(string[] matchKey, LogExportType[] matchResult, string eventCategory, LogExportType expectedExport)
        {
            // Initialize the map
            var routingMap = new ConcurrentDictionary<string, LogExportType>();
            for (int i = 0; i < matchKey.Length; i++)
                { routingMap[matchKey[i]] = matchResult[i]; }

            var result = routingMap.TryMatchValue(eventCategory, out LogExportType actualExport);

            Assert.True(result == (expectedExport != LogExportType.None));

            Assert.True(expectedExport == actualExport);
        }

        /// <summary>
        /// Rule of building a routing map. Test with category: TestMetric.MyService
        /// </summary>
        /// <param name="updateSection"></param>
        /// <param name="updateContent"></param>
        /// <param name="expectedLogExportType"></param>
        /// <param name="expectedGenevaEvent"></param>
        /// <param name="expectedOdlLogType"></param>
        [Theory]
        [InlineData("dummy", @"{}", LogExportType.Geneva | LogExportType.OdlTcp, "ServiceEvent", "TestLogType")] // Success
        [InlineData( // The virtual table is not defined
            "SubstrateLogging:CompositeExporter:VirtualTableExports",
            @"{""MyServiceTables"": [{""ExporterType"": ""geneva""}]}",
            LogExportType.None, "", "")]
        [InlineData( // Only one export is defined
            "SubstrateLogging:CompositeExporter:VirtualTableExports:MyServiceTable",
            @"[{""ExporterType"": ""OdlTcp"", ""ExportTable"": ""NewLogType""}]",
            LogExportType.OdlTcp, "", "NewLogType")]
        [InlineData( // Build rule with wildcard
            "SubstrateLogging:CompositeExporter:VirtualTableMappings",
            @"{""*"": ""MyServiceTable""}",
            LogExportType.Geneva | LogExportType.OdlTcp, "ServiceEvent", "TestLogType")]
        internal void BuildRoutingMaps_Success(string updateSection, string updateContent, LogExportType expectedLogExportType, string expectedGenevaEvent, string expectedOdlLogType)
        {
            var updatedConfig = BasicConfig.UpdateJsonSection(updateSection, updateContent);
            IConfiguration configuration = new ConfigurationBuilder()
                .AddJsonStream(new MemoryStream(Encoding.UTF8.GetBytes(updatedConfig)))
                .Build();

            var options = new CompositeLogRecordExporterOptions();
            configuration.GetSection("SubstrateLogging:CompositeExporter").Bind(options);

            (var routingMap, var genevaMapping, var odlMapping) = options.BuildRoutingMaps();

            routingMap.TryMatchValue("TestMetric.MyService", out var actualLogExportType);
            Assert.Equal(expectedLogExportType, actualLogExportType);
            if ((expectedLogExportType & LogExportType.Geneva) == LogExportType.Geneva)
            {
                if (genevaMapping.TryGetValue("TestMetric.MyService", out string? value))
                {
                    Assert.Equal(expectedGenevaEvent, value);
                }
                else
                {
                    Assert.Equal(expectedGenevaEvent, genevaMapping["*"]);
                }
            }
            if ((expectedLogExportType & LogExportType.OdlTcp) == LogExportType.OdlTcp)
            {
                if (odlMapping.TryGetValue("TestMetric.MyService", out string? value))
                {
                    Assert.Equal(expectedOdlLogType, value);
                }
                else
                {
                    Assert.Equal(expectedOdlLogType, odlMapping["*"]);
                }
            }
        }

        private static void MockSendingLog(ILogger logger)
        {
            Enumerable.Range(0, 100).ToList().ForEach(_ =>
            {
                logger.LogInformation("Test Event");
                Thread.Sleep(10);
            });
        }
    }
}
