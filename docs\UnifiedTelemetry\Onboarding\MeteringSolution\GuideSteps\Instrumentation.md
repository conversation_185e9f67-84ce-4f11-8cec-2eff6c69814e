# Step 3 - Instrument Data
There are two steps to instrument your application/library to track important metrics. The first step is to create meters using the [System.Diagnostics.Metrics](https://learn.microsoft.com/en-us/dotnet/api/system.diagnostics.metrics) APIs. The second step is to add metrics with the help of code generators.

## Create a meter

We can leverage System.Diagnostics.Metrics APIs to create meters whether in DI or Non-DI environments. [Creating Metrics - .NET](https://learn.microsoft.com/en-us/dotnet/core/diagnostics/metrics-instrumentation) shows how to leverage .NET meter to instrument your applications/library.

# [DI](#tab/DI)

Create the `Meter` object using [IMeterFactory](https://learn.microsoft.com/en-us/dotnet/api/system.diagnostics.metrics.imeterfactory). If your application/library uses .NET 8 or above, hosts will automatically register `IMeterFactory` in the service container. If not, please first manually register `IMeterFactory` in the `IServiceCollection` by calling [AddMetrics](https://learn.microsoft.com/en-us/dotnet/api/microsoft.extensions.dependencyinjection.metricsserviceextensions.addmetrics).	

```csharp
// Skip this step if you use .NET 8 or above
serviceCollection.AddMetrics();
```

To obtain a Meter in a type designed for DI, add an `IMeterFactory` parameter to the constructor, then call [Create](https://learn.microsoft.com/en-us/dotnet/api/system.diagnostics.metrics.meterfactoryextensions.create).

```csharp
public HatCoMetrics(IMeterFactory meterFactory)
{
	var meter = meterFactory.Create("HatCo.Store");
}
```

# [Non-DI](#tab/nonDI)
In non-DI, the Meter can be obtained by constructing it with new and assigning it to a static field.

```csharp
static Meter s_meter = new Meter("HatCo.Store");
```
---

## Create a metric and record measurement

There are two options to create a metric given a meter. One is creating a metric directly from a meter. It is easy and straightforward. While we recommend creating a metric using Fast metering which is more effective.

### Direct creation
```csharp
static Counter<int> s_hatsSold = s_meter.CreateCounter<int>("hats_sold");
// add the measurement to the counter
s_hatsSold.Add(4, new("name", "apple"), new("color", "red"));
```

### [Recommended] Fast metering

Effective with .NET 8, Fast metering API has been integrated into the .NET platform. To use this feature, you need to enable the [Code generation](https://eng.ms/docs/experiences-devices/r9-sdk/docs/code-generation). Source code generation is a feature that was introduced with C# 9.0. You must use the .NET 7 SDK or later toolchain, and C# 9.0 or later to compile your code.

Here is an example that shows how to add metrics with the help of [fast metering](https://github.com/dotnet/extensions-samples/blob/main/src/Telemetry/Metering/Metrics.Generators/README.md). We briefly explain the example here. 

1. Define metric dimension information.

	Define a class that contains the metering dimension information. `TagNameAttribute` is applied to fields or properties of a class to override default tag (dimension) names and provide custom tag names. By default, the tag name is the same as the respective field or property. 

	```csharp
	internal struct RequestInfo
	{
		// This annotated property will be used as a tag for the RequestStats histogram.
		[TagName("Target")]
		public RequestTarget Target { get; set; }

    	// This annotated property will be used as a tag for the RequestStats histogram.
		// You can omit the [TagName] attribute if the tag name is the same as the property name.
		public string DayOfWeek { get; set; }
	}
	```
2. Define the metrics that will be used by the application. 

	Only metric definitions here, the actual metrics are created in the generated code. The method has the following constraints:
		- Must be a partial method.
	- Must return **metricName** as the type. A class with that name will be generated.
	- Must not be generic.
	- Must have `System.Diagnostics.Metrics.Meter` as first parameter.
	- Must have all the keys provided in **staticTagNames** as string type parameters.

	There are several types of metric attribute which can be found on [Microsoft.Extensions.Diagnostics.Metrics](https://learn.microsoft.com/en-us/dotnet/api/microsoft.extensions.diagnostics.metrics?view=net-9.0-pp). The metric attribute can be initialized either by a string array which contains dimension names or a type which provides metric dimension names like above `RequestInfo`. The dimensions are taken from the type's public fields and properties.

	`Name` property can be used to set metric name. When `Name` is not provided the return type of the method is used as metric name. 

	```csharp
	internal sealed partial class Metric
	{
		// This shows how to define a counter metric with a single tag.
		[Counter("Target", Name = "sample.total_requests")]
		public static partial TotalRequestCounter CreateTotalRequestCounter(Meter meter);

		// This shows how to define a counter metric with two tags.
		[Counter("Target", "FailureReason")]
		public static partial FailedRequestCounter CreateFailedRequestCounter(Meter meter);

		// This shows how to define a histogram metric with tags based on the RequestInfo type.
		// All tags for this metric will be automatically generated from the the properties
		// of the RequestInfo type which are annotated with the [TagName] attribute.
		[Histogram(typeof(RequestInfo))]
		public static partial RequestStatsHistogram CreateRequestStatsHistogram(Meter meter);
	}
	```

3. Use metric
   
	First, create metrics by leveraging above metric creation methods. Then records metric measurement accordingly.

	```csharp
	static Meter s_meter = new Meter("HatCo.Store");

	// Create metering instruments using the auto-generated code:
	static _requestsStatsHistogram = Metric.CreateRequestStatsHistogram(_meter);
	static _totalRequestCounter = Metric.CreateTotalRequestCounter(_meter);
	static _failedRequestCounter = Metric.CreateFailedRequestCounter(_meter);

	.....
	//record the metrics
	_totalRequestCounter.Add(1, "target");
	_requestsStatsHistogram.Record(
		123,
		new RequestInfo
		{
			Target = "target",
			DayOfWeek = DateTimeOffset.UtcNow.DayOfWeek.ToString()
		});
	_failedRequestCounter.Add(1, "target", StatusCode.ToString());
	```

## Types of instruments

The .NET runtime provides several types of instruments. The types of instruments currently available can be seen here: [Types of instruments](https://learn.microsoft.com/en-us/dotnet/core/diagnostics/metrics-instrumentation#types-of-instruments). Please choose the right instrument type based on your metering requirements.

## Substrate metric dimension setting

We classify dimensions in current IFx metrics into 3 categories: common dimensions, customer dimensions and additional dimensions. Theoretically, the dimension change won’t affect your metric usage. Only 4 dimensions which have no use scenarios will be removed. R9 metering will have the same behavior as Passive SDK.

After migrating to R9, common dimension names and values keep the same as current ones. You should add the same customer dimensions as you did in IFx. **Four additional dimensions (Synthetic, ApplicationName, ApplicationVersion, HardwareSku)** are populated implicitly by Passive SDK. These 4 additional dimensions will be discarded after migrating to R9. One special dimension “IsR9”, its value will be true for R9 metrics.

If the dimension doesn’t meet your application/library usage, please don’t hesitate to contact us.

### Common dimension

Both in Passive SDK and Substrate R9 metering, the following dimensions / fields will be auto populated when metrics or events are getting logged to Geneva. It essentially means that all teams will get these dimensions in metric and columns in event automatically.

| Metric Dimension Name | Event Field Name | Description | Example value |
|-----------------------|------------------|-------------|---------------|
| Build Version | buildVersion | Build version of the machine running the component. | 15.01.0539.000-6.1.7PS3 |
| Machine | env_cloud_roleInstance | Name of the server running in the Datacenter | AM2MG01MS101 |
| Forest | env_cloud_deploymentUnit | Forest name of the server running the component | prdmgt01.prod.exchangelabs.com |
| AvailabilityGroup | env_cloud_name | Dag name of the server running the component for BE or CU for FE | EURPR01DG025 |
| Region | env_cloud_location | Region of the server running the | Nam |
| Deployment Ring | env_cloud_environment | Deployment Ring of the server | WW |
| Role | env_cloud_role | Role of the machine | BE or FE etc. |
| Timestamp | env_time | Timestamp of event | 2019-08-17T19:45:12.0634329Z |
| MachineProvisioningState | | Provisioning state of the server | Provisioned |

### Scenario metric common dimension

If you use Scenario based metric provided by Passive SDK before, we recommend you migrate to R9ScenarioMetric. For scenario-based metrics, we enforce the following 3 dimensions.

| Dimension Name | Description | Example value |
|----------------|-------------|---------------|
| ComponentName | Component name | OWS |
| ScenarioName | Name of the scenario | PopulateCalendarWithFlights |
| SubScenarioName | Name of the sub scenario | SouthWestFlights |

It is very easy to use R9ScenarioMetric directly.

#### Availability Metric & Latency Metric

Availability Metric and Latency Metric have all scenario based metric dimensions. The usage of Availability Metric and Latency Metric are same. The difference is that Availability Metric records 0 (Failed) or 1 (Succeed). Latency Metric records latency value in milliseconds.

```csharp
var meter = new Meter("test");
var latencyMetric = R9ScenarioMetric.CreateLatencyMetric(meter);
latencyMetric.Record(123, new ScenarioDimension("component", "scenario", "subScenario"));
```
#### Error Metric
Error Metric has all the scenario based metric dimensions. It has an additional dimension called Type. Error Metric is a counter which counts errors happened.
```csharp
var meter = new Meter("test");
var errorMetric = R9ScenarioMetric.CreateErrorMetric(meter);
errorMetric.Add(1, new ErrorScenario("component", "scenario", "subScenario", "test"));
```

#### Extend scenario based metric
You can extend the scenario based metric with custom dimensions. First define the custom scenario class with custom properties for custom dimensions. For example, ErrorScenario.
```csharp
public class ErrorScenario : ScenarioDimension
{
	public ErrorScenario(string component, string name, string subName) : base(component, name, subName)
	{
	}
	// Compared with three scenario-based dimensions, Type is a newly added dimension
	public string? Type { get; set; }
}
```

Then you can define your own scenario-based metric by fast metering.
```csharp
public static partial class YourScenarioMetric
{
	[Counter(typeof(ErrorScenario))]
	public static partial ErrorMetric CreateErrorMetric(this Meter meter);
}
```
Then you can create your metric and use it like the above AvailabilityMetric or ErrorMetric examples.

### Best practice for customer dimension values

- Never use random values as dimensions, e.g. guid, query url, duration, timestamp…
- Avoid long string as dimensions, e.g. error message, log message, response message…
- For a large enumerable dimension, merge unused candidates at sdk level as possible.
- Normalize dimension value before emitting measurement (lower-case or upper-case, trim extra space, check typo, etc)

## Migration Solution

To facilitate seamless migration, we have some examples showing migration from IFx SDK. See [IFx Metric Migration](./IfxMigration.md).

**Next Step**: [Validate Result in Local](./LocalValid.md)