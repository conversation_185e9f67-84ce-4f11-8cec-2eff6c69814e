<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFrameworks>net472;net6.0;net8.0</TargetFrameworks>
        <DocumentationFile>bin\$(Configuration)\$(TargetFramework)\$(AssemblyName).xml</DocumentationFile>
        <Authors>SOTELS</Authors>
        <RootNamespace>Microsoft.M365.Core.Telemetry.ECS.DynamicComponentTest</RootNamespace>
        <AssemblyName>Microsoft.M365.Core.Telemetry.ECS.DynamicComponentTest</AssemblyName>
        <IsCodedUITest>False</IsCodedUITest>
        <TestProjectType>UnitTest</TestProjectType>
        <PlatformTarget>anycpu</PlatformTarget>
        <LangVersion>9</LangVersion>
    </PropertyGroup>

    <ItemGroup>
        <ProjectReference
            Include="..\..\..\dev\R9TelemetryExtensionsCore\ECS.DynamicComponent\ECS.DynamicComponent.csproj" />
    </ItemGroup>

    <ItemGroup>
        <PackageReference Include="Microsoft.NET.Test.Sdk" />
        <PackageReference Include="NSubstitute" />
        <PackageReference Include="xunit" />
        <PackageReference Include="xunit.runner.visualstudio" />
        <PackageReference Include="coverlet.collector" />
    </ItemGroup>

</Project>
  