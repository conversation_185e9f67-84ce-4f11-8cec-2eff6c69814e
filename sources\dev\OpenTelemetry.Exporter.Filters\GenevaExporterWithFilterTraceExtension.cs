﻿// <copyright file="GenevaExporterWithFilterTraceExtension.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>
using System;
using System.Diagnostics;
using System.Runtime.InteropServices;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Options;
using OpenTelemetry.Exporter.Filters.Internal;
using OpenTelemetry.Exporter.Geneva;
using OpenTelemetry.Trace;

namespace OpenTelemetry.Exporter.Filters;
public static class GenevaExporterWithFilterTraceExtension
{
    /// <summary>
    /// Add Geneva Exporter with Filter.
    /// </summary>
    /// <param name="builder"></param>
    /// <param name="configure"></param>
    /// <param name="filter"></param>
    /// <returns></returns>
    public static TracerProviderBuilder AddGenevaTraceExporter(this TracerProviderBuilder builder, Action<GenevaExporterOptions> configure, BaseFilter<Activity> filter)
    => AddGenevaTraceExporter(builder, name: null, configure, filter);

    /// <summary>
    /// Add Geneva Exporter with Sampler.
    /// </summary>
    /// <param name="builder"></param>
    /// <param name="configure"></param>
    /// <param name="sampler"></param>
    /// <returns></returns>
    public static TracerProviderBuilder AddGenevaTraceExporter(this TracerProviderBuilder builder, Action<GenevaExporterOptions> configure, Sampler sampler)
    => AddGenevaTraceExporter(builder, name: null, configure, new SamplerFilter(sampler));

    /// <summary>
    /// Adds <see cref="GenevaTraceExporter"/> to the <see cref="TracerProviderBuilder"/>.
    /// </summary>
    /// <param name="builder"><see cref="TracerProviderBuilder"/> builder to use.</param>
    /// <param name="name">Name which is used when retrieving options.</param>
    /// <param name="configure">Exporter configuration options.</param>
    /// <param name="sampler">Sampler to apply to the exporter.</param>
    /// <returns>The instance of <see cref="TracerProviderBuilder"/> to chain the calls.</returns>
    public static TracerProviderBuilder AddGenevaTraceExporter(this TracerProviderBuilder builder, string name, Action<GenevaExporterOptions> configure, Sampler sampler)
    => AddGenevaTraceExporter(builder, name, configure, new SamplerFilter(sampler));

    /// <summary>
    /// Adds <see cref="GenevaTraceExporter"/> to the <see cref="TracerProviderBuilder"/>.
    /// </summary>
    /// <param name="builder"><see cref="TracerProviderBuilder"/> builder to use.</param>
    /// <param name="name">Name which is used when retrieving options.</param>
    /// <param name="configure">Exporter configuration options.</param>
    /// <param name="filter">Filter to apply to the exporter.</param>
    /// <returns>The instance of <see cref="TracerProviderBuilder"/> to chain the calls.</returns>
    public static TracerProviderBuilder AddGenevaTraceExporter(this TracerProviderBuilder builder, string name, Action<GenevaExporterOptions> configure, BaseFilter<Activity> filter)
    {
        Guard.ThrowIfNull(builder);

        name ??= Options.DefaultName;

        if (configure != null)
        {
            builder.ConfigureServices(services => services.Configure(name, configure));
        }

        return builder.AddProcessor(sp =>
        {
            var exporterOptions = sp.GetRequiredService<IOptionsMonitor<GenevaExporterOptions>>().Get(name);

            return BuildGenevaTraceExporter(exporterOptions, configure, RuntimeInformation.IsOSPlatform(OSPlatform.Windows), filter);
        });
    }

    internal static BaseProcessor<Activity> BuildGenevaTraceExporter(GenevaExporterOptions options, Action<GenevaExporterOptions> configure, bool isWindows, BaseFilter<Activity> filter)
    {
        configure?.Invoke(options);
        var exporter = new GenevaTraceExporter(options);

        //will change to exporter.IsUsingUnixDomainSocket while moving to OpenTelemetry
        if (!isWindows)
        {
            var batchOptions = new BatchExportActivityProcessorOptions();
            return new BatchActivityExportProcessorWithFilter(
                exporter,
                filter,
                batchOptions.MaxQueueSize,
                batchOptions.ScheduledDelayMilliseconds,
                batchOptions.ExporterTimeoutMilliseconds,
                batchOptions.MaxExportBatchSize);
        }
        else
        {
            return new ReentrantActivityExportProcessorWithFilter(exporter, filter);
        }
    }
}
