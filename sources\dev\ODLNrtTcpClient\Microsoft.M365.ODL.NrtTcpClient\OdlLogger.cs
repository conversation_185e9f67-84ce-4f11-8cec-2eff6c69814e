﻿// <copyright file="OdlLogger.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.Diagnostics.CodeAnalysis;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Microsoft.M365.ODL.NrtTcpClient
{
    /// <summary>
    /// The common logger for ODL
    /// </summary>
    [ExcludeFromCodeCoverage]
    public class OdlLogger : IOdlLogger
    {
        /// <summary>
        /// The singleton instance
        /// </summary>
        public static readonly OdlLogger Instance = new OdlLogger();

        /// <summary>
        /// Whether use event log as adhoc loggers, default to true
        /// </summary>
        public static bool UseAdhocLogger = true;

        private static IOdlLogger etwLogger = OdlEtwLogger.Instance;

        /// <summary>
        /// Logging for different levels
        /// </summary>
        /// <param name="level"></param>
        /// <param name="id"></param>
        /// <param name="message"></param>
        public void Log(LogLevel level, int id, string message)
        {
            etwLogger.Log(level, id, message);

            if (UseAdhocLogger)
            {
                OdlEventLogger.Log(message, id, level);
            }
        }

        /// <summary>
        /// Trace logging
        /// </summary>
        /// <param name="severity"></param>
        /// <param name="traceId"></param>
        /// <param name="message"></param>
        public void TraceLog(LogLevel severity, int traceId, string message)
        {
            etwLogger.TraceLog(severity, traceId, message);

            if (UseAdhocLogger)
            {
                OdlEventLogger.Log(message, traceId, severity);
            }
        }
    }
}
