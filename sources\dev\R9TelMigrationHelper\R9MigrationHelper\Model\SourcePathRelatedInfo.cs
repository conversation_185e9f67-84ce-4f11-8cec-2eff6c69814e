﻿// <copyright file="SourcePathRelatedInfo.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using static Kusto.Data.Net.Http.OneApiError;

namespace R9MigrationHelper.Model
{
    /// <summary>
    /// SourcePathRelatedInfo.
    /// </summary>
    public class SourcePathRelatedInfo
    {
        string packageName = string.Empty;
        string packageVersion = string.Empty;
        string assemblyVersion = string.Empty;

        /// <summary>
        /// Assembly Name.
        /// </summary>
        public string PackageName
        {
            get { return packageName; }
            set { packageName = value; }
        }

        /// <summary>
        /// Assembly Name.
        /// </summary>
        public string PackageVersion
        {
            get { return packageVersion; }
            set { packageVersion = value; }
        }

        /// <summary>
        /// Assembly Name.
        /// </summary>
        public string AssemblyVersion
        {
            get { return assemblyVersion; }
            set { assemblyVersion = value; }
        }

        /// <summary>
        /// SourcePathRelatedInfo.
        /// </summary>
        public bool Validate()
        {
            return !(String.IsNullOrEmpty(packageName) || String.IsNullOrEmpty(packageVersion) || String.IsNullOrEmpty(assemblyVersion));
        }

        /// <summary>
        /// Equals.
        /// </summary>
        /// <param name="obj">object</param>
#pragma warning disable CS8600, CS8602, CS8765 // Converting null literal or possible null value to non-nullable type.
        public override bool Equals(object obj)
        {
            if (obj == null || this == null) { return false; }
            if (obj is SourcePathRelatedInfo)
            {
                SourcePathRelatedInfo otherSourcePathRelatedInfo = obj as SourcePathRelatedInfo;
                return otherSourcePathRelatedInfo.PackageName == this.PackageName && otherSourcePathRelatedInfo.PackageVersion == this.PackageVersion && otherSourcePathRelatedInfo.AssemblyVersion == this.AssemblyVersion;
#pragma warning restore CS8600, CS8602, CS8765 // Converting null literal or possible null value to non-nullable type.
            }
            return false;
        }

        /// <summary>
        /// GetHashCode.
        /// </summary>
        public override int GetHashCode()
        {
            int hash = 13;
            hash = (hash * 7) + PackageName.GetHashCode(StringComparison.InvariantCulture);
            hash = (hash * 7) + PackageVersion.GetHashCode(StringComparison.InvariantCulture);
            hash = (hash * 7) + AssemblyVersion.GetHashCode(StringComparison.InvariantCulture);
            return hash;
        }
    }
}
