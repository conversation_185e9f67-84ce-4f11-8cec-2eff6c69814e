﻿// <copyright file="SubstrateMeteringExtension.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System;
using System.Linq;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.M365.Core.Telemetry.Enrichment;
using Microsoft.R9.Extensions.Enrichment;
using Microsoft.R9.Extensions.Metering;
using Microsoft.R9.Extensions.Metering.Exporters;
using OpenTelemetry;
using OpenTelemetry.Metrics;

namespace Microsoft.M365.Core.Telemetry.R9.Metering.Substrate
{
    /// <summary>
    /// Extension methods for Substrate metering
    /// </summary>
    public static class SubstrateMeteringExtension
    {
        /// <summary>
        /// Configures Substrate metering using the provided MeterProviderBuilder and service configuration.
        /// </summary>
        /// <param name="meterProviderBuilder">The MeterProviderBuilder to configure.</param>
        /// <param name="serviceConfiguration">The service configuration to use for metering options.</param>
        /// <returns>The configured MeterProviderBuilder.</returns>
        public static MeterProviderBuilder ConfigureSubstrateMetering(this MeterProviderBuilder meterProviderBuilder, IConfiguration serviceConfiguration)
        {
            meterProviderBuilder
            .ConfigureServices(services =>
            {
                // When add geneva exporter, the internal validator is also added
                if (services.Any(s => s.ImplementationType?.Name?.EndsWith("GenevaMeteringExporterOptionsValidator", StringComparison.CurrentCulture) == true))
                {
                    throw new Exception("Geneva Exporter is already added for metering. Adding multiple Geneva Exporters will cause data duplication.");
                }
            });

            meterProviderBuilder.AddMetering(options =>
            {
                options.UpdateAndValidateOptions(serviceConfiguration);
            });

            meterProviderBuilder.AddGenevaExporter(options =>
            {
                options.UpdateAndValidateOptions(serviceConfiguration);
            });

            meterProviderBuilder.ConfigureServices(services =>
            {
                services.AddMetricEnricher<B2PassiveMetricEnricher>();
            });
            return meterProviderBuilder;
        }

        /// <summary>
        /// Add metering for Substrate services
        /// </summary>
        /// <param name="serviceCollection"></param>
        /// <param name="serviceConfiguration"></param>
        /// <param name="configure"></param>
        /// <returns></returns>
        public static IServiceCollection AddSubstrateMetering(
            this IServiceCollection serviceCollection,
            IConfiguration serviceConfiguration,
            Action<MeterProviderBuilder>? configure = null)
        {
            serviceCollection.AddOpenTelemetry().WithMetrics(meterProviderBuilder =>
            {
                // Load user customized actions for logging builder if there is
                configure?.Invoke(meterProviderBuilder);

                ConfigureSubstrateMetering(meterProviderBuilder, serviceConfiguration);
            });

            return serviceCollection;
        }

        /// <summary>
        /// Configures Substrate metering using the provided service configuration and optional MeterProviderBuilder configuration.
        /// </summary>
        /// <param name="serviceConfiguration">The service configuration to use for metering options.</param>
        /// <param name="configure">An optional action to configure the MeterProviderBuilder.</param>
        public static void ConfigureSubstrateMetering(IConfiguration serviceConfiguration, Action<MeterProviderBuilder>? configure = null)
        {
            var meterBuilder = Sdk.CreateMeterProviderBuilder();
            configure?.Invoke(meterBuilder);
            meterBuilder.ConfigureSubstrateMetering(serviceConfiguration);

            var meterProvider = meterBuilder.Build();
            AppDomain.CurrentDomain.ProcessExit += (sender, args) =>
            {
                meterProvider?.Dispose();
            };
            AppDomain.CurrentDomain.UnhandledException += (sender, args) =>
            {
                meterProvider?.Dispose();
            };
        }
    }
}
