{"options": [{"enabled": false, "definition": {"id": "a9db38f9-9fdc-478c-b0f9-464221e58316"}, "inputs": {"workItemType": "399868", "assignToRequestor": "false", "additionalFields": "{}"}}, {"enabled": false, "definition": {"id": "5d58cc01-7c75-450c-be18-a388ddb129ec"}, "inputs": {"branchFilters": "[\"+refs/heads/*\"]", "additionalFields": "{}"}}], "variables": {"build.cloudbuildqueue": {"value": "OSS_{{RepositoryName}}_Retail_Drops_Signing_Git"}, "build.fetchtags": {"value": "true"}, "build.pipeline": {"value": "{{RepositoryName}}"}, "Build.Repository.Id": {"value": "{{RepositoryId}}"}, "BUILDTYPE": {"value": "Dogfood"}, "PathToVstsHelper": {"value": "\\\\redmond.corp.microsoft.com\\exchange\\Build\\Central\\Tools\\VstsBuildHelper.cmd"}, "system.debug": {"value": "false", "allowOverride": true}}, "retentionRules": [{"branches": ["+refs/heads/*"], "artifacts": [], "artifactTypesToDelete": [], "daysToKeep": 30, "minimumToKeep": 10, "deleteBuildRecord": false, "deleteTestResults": false}], "tags": [], "jobAuthorizationScope": 1, "jobTimeoutInMinutes": 240, "jobCancelTimeoutInMinutes": 5, "process": {"phases": [{"steps": [{"environment": {}, "enabled": true, "continueOnError": true, "alwaysRun": true, "displayName": "Task group: OfficialDogfoodBuildCBT $(build.cloudbuildqueue)", "timeoutInMinutes": 0, "condition": "succeededOrFailed()", "task": {"id": "238db605-7968-4f73-b28f-3afed535f2bc", "versionSpec": "1.*", "definitionType": "metaTask"}, "inputs": {"build.cloudbuildqueue": "$(build.cloudbuildqueue)", "build.pipeline": "$(build.pipeline)", "Build.Repository.Id": "$(Build.Repository.Id)"}}], "name": "Phase 1", "refName": "Phase_1", "condition": "succeeded()", "target": {"executionOptions": {"type": 0}, "allowScriptsAuthAccessOption": true, "type": 1}, "jobAuthorizationScope": 1, "jobCancelTimeoutInMinutes": 1}], "type": 1}, "repository": {"properties": {"labelSources": "0", "reportBuildStatus": "false", "fetchDepth": "0", "gitLfsSupport": "false", "skipSyncSource": "false", "cleanOptions": "0", "checkoutNestedSubmodules": "false", "labelSourcesFormat": "$(build.buildNumber)"}, "id": "{{RepositoryId}}", "type": "TfsGit", "name": "{{RepositoryName}}", "url": "{{RepositoryUrl}}", "defaultBranch": "refs/heads/master", "clean": "true", "checkoutSubmodules": false}, "queue": {"pool": {"id": 11, "name": "Official"}, "id": 26, "name": "Official"}, "name": "{{RepositoryName}} Official-Dogfood", "path": "\\{{RepositoryName}}\\Official Builds", "type": 2, "queueStatus": 0, "revision": 1}