#define HAVE_ABSEIL
#include "OdlTraceExporterRecordable.h"

#include <nlohmann/json.hpp>
#include <opentelemetry/common/attribute_value.h>
#include <opentelemetry/common/key_value_iterable.h>
#include <opentelemetry/common/timestamp.h>
#include <opentelemetry/nostd/span.h>
#include <opentelemetry/nostd/string_view.h>
#include <opentelemetry/nostd/variant.h>
#include <opentelemetry/sdk/common/attribute_utils.h>
#include <opentelemetry/sdk/instrumentationscope/instrumentation_scope.h>
#include <opentelemetry/sdk/resource/resource.h>
#include <opentelemetry/trace/span_context.h>
#include <opentelemetry/trace/span_id.h>
#include <opentelemetry/trace/span_metadata.h>
#include <opentelemetry/trace/trace_flags.h>
#include <opentelemetry/trace/trace_id.h>
#include <opentelemetry/version.h>

#include <chrono>
#include <format>
#include <iomanip>
#include <map>
#include <sstream>
#include <stdint.h>
#include <string>
#include <time.h>
#include <type_traits>
#include <unordered_map>
#include <utility>

using namespace opentelemetry;
using ordered_json = nlohmann::ordered_json;

namespace Microsoft::M365::Exporters
{
//
// See `attribute_value.h` for details.
//
const int kAttributeValueSize = 16;

std::string TimestampStr(const opentelemetry::common::SystemTimestamp &timestamp)
{
    auto nano_since_epoch = timestamp.time_since_epoch();
    auto seconds = std::chrono::duration_cast<std::chrono::seconds>(nano_since_epoch);
    auto nanoseconds = std::chrono::duration_cast<std::chrono::nanoseconds>(nano_since_epoch - seconds);

    std::time_t time_t_seconds = seconds.count();
    std::tm tm_utc;
    gmtime_s(&tm_utc, &time_t_seconds);

    std::ostringstream oss;
    oss << std::put_time(&tm_utc, "%Y-%m-%dT%H:%M:%S");
    oss << '.' << (nanoseconds.count() / 100) << 'Z';  // same with C#, keep 7 digits after seconds.

    return oss.str();
}   

std::string DurationStr(const std::chrono::nanoseconds &duration)
{
    auto hours = std::chrono::duration_cast<std::chrono::hours>(duration);
    auto minutes = std::chrono::duration_cast<std::chrono::minutes>(duration - hours);
    auto seconds = std::chrono::duration_cast<std::chrono::seconds>(duration - hours - minutes);
    auto microseconds = std::chrono::duration_cast<std::chrono::microseconds>(duration - hours - minutes - seconds);
    auto nanoseconds = std::chrono::duration_cast<std::chrono::nanoseconds>(duration - hours - minutes - seconds - microseconds);

    std::ostringstream oss;
    oss << std::setw(2) << std::setfill('0') << hours.count() << ":"
        << std::setw(2) << std::setfill('0') << minutes.count() << ":"
        << std::setw(2) << std::setfill('0') << seconds.count() << "."
        << std::setw(6) << std::setfill('0') << microseconds.count()
        << std::setw(1) << std::setfill('0') << nanoseconds.count() / 100;

    return oss.str();
}

void OdlTraceExporterRecordable::SetIdentity(const opentelemetry::trace::SpanContext &span_context,
                                        opentelemetry::trace::SpanId parent_span_id) noexcept
{
    char trace_id_lower_base16[opentelemetry::trace::TraceId::kSize * 2] = {0};
    span_context.trace_id().ToLowerBase16(trace_id_lower_base16);
    char span_id_lower_base16[opentelemetry::trace::SpanId::kSize * 2] = {0};
    span_context.span_id().ToLowerBase16(span_id_lower_base16);
    if (parent_span_id.IsValid())
    {
        char parent_span_id_lower_base16[opentelemetry::trace::SpanId::kSize * 2] = {0};
        parent_span_id.ToLowerBase16(parent_span_id_lower_base16);
        parent_id_ = std::string(parent_span_id_lower_base16, 16);
    }

    span_id_ = std::string(span_id_lower_base16, 16);
    trace_id_ = std::string(trace_id_lower_base16, 32);
}

void PopulateAttribute(ordered_json &attribute,
                       opentelemetry::nostd::string_view key,
                       const opentelemetry::common::AttributeValue &value, std::shared_ptr<opentelemetry::logs::Logger> logger) noexcept
{
    // ...existing code...
    // Assert size of variant to ensure that this method gets updated if the variant
    // definition changes
    static_assert(nostd::variant_size<common::AttributeValue>::value == kAttributeValueSize,
                  "AttributeValue contains unknown type");

    if (nostd::holds_alternative<bool>(value))
    {
        attribute[key.data()] = nostd::get<bool>(value);
    }
    else if (nostd::holds_alternative<int>(value))
    {
        attribute[key.data()] = nostd::get<int>(value);
    }
    else if (nostd::holds_alternative<int64_t>(value))
    {
        attribute[key.data()] = nostd::get<int64_t>(value);
    }
    else if (nostd::holds_alternative<unsigned int>(value))
    {
        attribute[key.data()] = nostd::get<unsigned int>(value);
    }
    else if (nostd::holds_alternative<uint64_t>(value))
    {
        attribute[key.data()] = nostd::get<uint64_t>(value);
    }
    else if (nostd::holds_alternative<double>(value))
    {
        attribute[key.data()] = nostd::get<double>(value);
    }
    else if (nostd::holds_alternative<const char *>(value))
    {
        attribute[key.data()] = nostd::get<const char *>(value);
    }
    else if (nostd::holds_alternative<nostd::string_view>(value))
    {
        attribute[key.data()] = nostd::string_view(nostd::get<nostd::string_view>(value).data(),
                                                   nostd::get<nostd::string_view>(value).size());
    }
    else if (nostd::holds_alternative<nostd::span<const uint8_t>>(value))
    {
        attribute[key.data()] = {};
        for (const auto &val : nostd::get<nostd::span<const uint8_t>>(value))
        {
            attribute[key.data()].push_back(val);
        }
    }
    else if (nostd::holds_alternative<nostd::span<const bool>>(value))
    {
        attribute[key.data()] = {};
        for (const auto &val : nostd::get<nostd::span<const bool>>(value))
        {
            attribute[key.data()].push_back(val);
        }
    }
    else if (nostd::holds_alternative<nostd::span<const int>>(value))
    {
        attribute[key.data()] = {};
        for (const auto &val : nostd::get<nostd::span<const int>>(value))
        {
            attribute[key.data()].push_back(val);
        }
    }
    else if (nostd::holds_alternative<nostd::span<const int64_t>>(value))
    {
        attribute[key.data()] = {};
        for (const auto &val : nostd::get<nostd::span<const int64_t>>(value))
        {
            attribute[key.data()].push_back(val);
        }
    }
    else if (nostd::holds_alternative<nostd::span<const unsigned int>>(value))
    {
        attribute[key.data()] = {};
        for (const auto &val : nostd::get<nostd::span<const unsigned int>>(value))
        {
            attribute[key.data()].push_back(val);
        }
    }
    else if (nostd::holds_alternative<nostd::span<const uint64_t>>(value))
    {
        attribute[key.data()] = {};
        for (const auto &val : nostd::get<nostd::span<const uint64_t>>(value))
        {
            attribute[key.data()].push_back(val);
        }
    }
    else if (nostd::holds_alternative<nostd::span<const double>>(value))
    {
        attribute[key.data()] = {};
        for (const auto &val : nostd::get<nostd::span<const double>>(value))
        {
            attribute[key.data()].push_back(val);
        }
    }
    else if (nostd::holds_alternative<nostd::span<const nostd::string_view>>(value))
    {
        attribute[key.data()] = {};
        for (const auto &val : nostd::get<nostd::span<const nostd::string_view>>(value))
        {
            attribute[key.data()].push_back(std::string(val.data(), val.size()));
        }
    }
}

void OdlTraceExporterRecordable::SetAttribute(opentelemetry::nostd::string_view key,
                                         const opentelemetry::common::AttributeValue &value) noexcept
{
    if (std::strncmp(key.data(), "m365.dt.", 8) == 0)
    {
        owned_attributes_[key.data()] = nostd::get<nostd::string_view>(value).data();
        attributes_[key.data()] = owned_attributes_[key.data()];
    }
    else
    {
        attributes_[key.data()] = value;
    }
}

void OdlTraceExporterRecordable::AddEvent(opentelemetry::nostd::string_view name,
                                     opentelemetry::common::SystemTimestamp timestamp,
                                     const opentelemetry::common::KeyValueIterable &attributes) noexcept
{
    Event event;
    event.name = name.data();
    event.timestamp = TimestampStr(timestamp);
    attributes.ForEachKeyValue([&](opentelemetry::nostd::string_view key, opentelemetry::common::AttributeValue value) noexcept {
        event.attributes[key.data()] = value;
        return true;
    });
    events_.push_back(event);
}

void OdlTraceExporterRecordable::AddLink(const opentelemetry::trace::SpanContext & /* span_context */,
                                    const opentelemetry::common::KeyValueIterable & /* attributes */) noexcept
{
    // TODO(jiayiwang): To be implemented. Not enabled for NanoProxy.
}

void OdlTraceExporterRecordable::SetStatus(opentelemetry::trace::StatusCode code,
                                      opentelemetry::nostd::string_view description) noexcept
{
    static const std::map<opentelemetry::trace::StatusCode, std::string> kSpanStatusMap = {
        {opentelemetry::trace::StatusCode::kUnset, "Unset"},
        {opentelemetry::trace::StatusCode::kOk, "Ok"},
        {opentelemetry::trace::StatusCode::kError, "Error"},
    };
    status_ = kSpanStatusMap.at(code);
}

void OdlTraceExporterRecordable::SetName(opentelemetry::nostd::string_view name) noexcept
{
    display_name_ = name.data();
}

void OdlTraceExporterRecordable::SetTraceFlags(opentelemetry::trace::TraceFlags /* flags */) noexcept
{
    // TODO(jiayiwang): To be implemented. C# currently doesn't send this.
    // However it's sent for links. However links are not enabled for NanoProxy yet.
}

void OdlTraceExporterRecordable::SetResource(const opentelemetry::sdk::resource::Resource &resource) noexcept
{
    // TODO(jiayiwang): To be implemented.
}

void OdlTraceExporterRecordable::SetStartTime(opentelemetry::common::SystemTimestamp start_time) noexcept
{
    start_time_utc_ = TimestampStr(start_time);
}

void OdlTraceExporterRecordable::SetDuration(std::chrono::nanoseconds duration) noexcept
{
    duration_ = DurationStr(duration);
}

void OdlTraceExporterRecordable::SetSpanKind(opentelemetry::trace::SpanKind span_kind) noexcept
{
    // Values come from c# ActivityKind.
    static const std::map<opentelemetry::trace::SpanKind, std::string> kSpanKindMap = {
        {opentelemetry::trace::SpanKind::kInternal, "Internal"},
        {opentelemetry::trace::SpanKind::kClient, "Client"},
        {opentelemetry::trace::SpanKind::kServer, "Server"},
        {opentelemetry::trace::SpanKind::kConsumer, "Consumer"},
        {opentelemetry::trace::SpanKind::kProducer, "Producer"},
    };

    kind_ = kSpanKindMap.at(span_kind);
}

void OdlTraceExporterRecordable::SetInstrumentationScope(
    const opentelemetry::sdk::instrumentationscope::InstrumentationScope &instrumentation_scope) noexcept
{
    source_name_ = instrumentation_scope.GetName();
    source_version_ = instrumentation_scope.GetVersion();
}

std::string OdlTraceExporterRecordable::DebugString() const noexcept
{
    std::string res = "Owned attrs: \n";
    for (const auto &key : owned_attributes_)
    {
        res += key.first + " : " + key.second + "\n";
    }
    return res;
}

std::string OdlTraceExporterRecordable::Serialize(const std::unordered_map<std::string, std::string> &common_dimensions, std::shared_ptr<opentelemetry::logs::Logger> logger) const noexcept
{
    ordered_json span;
    span["schemaVersion"] = "2.0";
    span["traceId"] = trace_id_;
    span["spanId"] = span_id_;
    span["kind"] = kind_;
    span["status"] = status_;
    span["operationName"] = "";
    span["displayName"] = display_name_;

    ordered_json source;
    source["Name"] = source_name_;
    source["Version"] = source_version_;
    span["source"] = source;

    span["duration"] = duration_;
    span["startTimeUtc"] = start_time_utc_;

    ordered_json tags;
    for (const auto &attribute : attributes_)
    {
        PopulateAttribute(tags, attribute.first, attribute.second, logger);
    }
    span["tags"] = tags;

    ordered_json events;
    for (const auto &event : events_)
    {
        ordered_json event_json;
        event_json["Name"] = event.name;
        event_json["Timestamp"] = event.timestamp;
        ordered_json event_tags;
        for (const auto &attribute : event.attributes)
        {
            PopulateAttribute(event_tags, attribute.first, attribute.second, logger);
        }
        event_json["Tags"] = event_tags;
        events.push_back(event_json);
    }
    span["events"] = events;
    span["parentId"] = parent_id_;

    for (const auto &common_dimension : common_dimensions)
    {
        span[common_dimension.first] = common_dimension.second;
    }

    std::string result = span.dump();

    return result;
}

} // namespace Microsoft::M365::Exporters