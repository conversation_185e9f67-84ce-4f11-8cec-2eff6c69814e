﻿// <copyright file="Program.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.M365.Core.Telemetry.R9.Logging.Substrate;
using Microsoft.M365.Core.Telemetry.R9.Logging.Substrate.Configuration;

namespace DISampleConsoleApp
{
    /// <summary>
    /// Program
    /// </summary>
    internal class Program
    {
        static async Task Main(string[] args)
        {
            var host = Host.CreateDefaultBuilder(args)
            .ConfigureAppConfiguration((context, config) =>
            {
                config.SetBasePath(Directory.GetCurrentDirectory());
                config.AddJsonFile("appsettings.json", optional: false, reloadOnChange: true);
            })
            .RegisterConfiguration()
            .ConfigureServices((context, services) =>
            {
                services.AddSubstrateLogging(context.Configuration);
                services.AddSingleton<IHostedService, SampleService>();
            })
            .Build();

            await host.RunAsync().ConfigureAwait(false);
        }
    }
}
