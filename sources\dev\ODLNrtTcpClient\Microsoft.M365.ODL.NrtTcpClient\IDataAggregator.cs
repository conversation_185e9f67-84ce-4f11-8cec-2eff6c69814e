﻿// <copyright file="IDataAggregator.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Microsoft.M365.ODL.NrtTcpClient
{
    /// <summary>
    /// Interface of data aggregator.
    /// </summary>
    /// <typeparam name="T">Data type in aggregator</typeparam>
    public interface IDataAggregator<T>
    {
        /// <summary>
        /// Get the current value being aggregated.
        /// </summary>
        T CurrentValue { get; }

        /// <summary>
        /// Track the value to be aggregated.
        /// </summary>
        /// <param name="value">The value to be aggregated</param>
        void Aggregate(T value);

        /// <summary>
        /// Gets the current value and reset the aggregator.
        /// </summary>
        /// <param name="value">Value returned</param>
        /// <returns>true if success, false if data is invalid or not exist</returns>
        bool GetAndReset(out T value);
    }
}
