{"properties": {"helpUri": "https://eng.ms/docs/microsoft-security/security/azure-security/cloudai-security-fundamentals-engineering/security-integration/guardian-wiki/microsoft-guardian/general/baselines"}, "version": "1.0.0", "baselines": {"cloudbuild.1espt": {"name": "cloudbuild.1espt", "createdDate": "2024-11-28 01:10:14Z", "lastUpdatedDate": "2024-11-28 01:10:14Z"}}, "results": {"b86d809e1806d370c7c18a82e3fd7482e840adc92999d60a1bc47a191fc2c1e5": {"signature": "b86d809e1806d370c7c18a82e3fd7482e840adc92999d60a1bc47a191fc2c1e5", "alternativeSignatures": ["47191ac087837ea372dbe301d9e6e5ec9d86c19e693cc417c7067974c2c5f98f"], "target": "target/distrib/Release/x64/OdlExporterCppTest/OdlExporterCppTest.exe", "memberOf": ["cloudbuild.1espt"], "tool": "binskim", "ruleId": "BA2008", "createdDate": "2024-12-22 05:19:08Z", "expirationDate": "2025-06-10 05:35:15Z", "justification": "This error is baselined with an expiration date of 180 days from 2024-12-22 05:35:15Z"}, "39e6972bc30c47326612a372d433d70f400650693389acbc198319e6081c807e": {"signature": "39e6972bc30c47326612a372d433d70f400650693389acbc198319e6081c807e", "alternativeSignatures": ["43bb15f69fb5c359a79f5399d475440eb6c90598c9ca00e11ce3c1e2db30ab33"], "target": "target/distrib/Release/x64/OdlExporterCpp/OdlExporterCpp.dll", "memberOf": ["cloudbuild.1espt"], "tool": "binskim", "ruleId": "BA2008", "createdDate": "2024-11-28 04:02:54Z", "expirationDate": "2025-05-17 04:24:14Z", "justification": "This error is baselined with an expiration date of 180 days from 2024-11-28 04:24:14Z"}, "9e2016b5a36ac8dc44e241ee8c3392af4efcf6b9a7530bdab51a5e001b2ca454": {"signature": "9e2016b5a36ac8dc44e241ee8c3392af4efcf6b9a7530bdab51a5e001b2ca454", "alternativeSignatures": ["eb95b6006bb3d477c041edf1050b8b3884d4952d52ab45604aad4ccf039f7151"], "target": "target/distrib/Release/x64/CppConsoleApp/CppConsoleApp.exe", "memberOf": ["cloudbuild.1espt"], "tool": "binskim", "ruleId": "BA2008", "createdDate": "2024-12-22 05:19:14Z", "expirationDate": "2025-06-10 05:35:15Z", "justification": "This error is baselined with an expiration date of 180 days from 2024-12-22 05:35:15Z"}, "46596f8dbe9b493085121b6fecb3eee21f5fb2500e98fcda4cae98b450a43336": {"signature": "46596f8dbe9b493085121b6fecb3eee21f5fb2500e98fcda4cae98b450a43336", "alternativeSignatures": ["61e9dcf076187c49c175a4b39840d34df3c3a890bae1a199780cdebe8536abbc"], "target": "target/distrib/Release/x64/FileExporterCppTest/FileExporterCppTest.exe", "memberOf": ["cloudbuild.1espt"], "tool": "binskim", "ruleId": "BA2007", "createdDate": "2025-03-24 07:21:40Z", "expirationDate": "2025-09-10 07:31:56Z", "justification": "This error is baselined with an expiration date of 180 days from 2025-03-24 07:31:56Z"}, "eb4365bc565a3907c58503356727c3beaa3efe8f57cf279febf93de922946aac": {"signature": "eb4365bc565a3907c58503356727c3beaa3efe8f57cf279febf93de922946aac", "alternativeSignatures": ["5b5c7aba12a2c2e5dcd5521f5e36a6263e1d1adac1ab7f294ea6c3dd04c25911"], "target": "target/distrib/Release/x64/FileExporterCppTest/FileExporterCppTest.exe", "memberOf": ["cloudbuild.1espt"], "tool": "binskim", "ruleId": "BA2008", "createdDate": "2025-03-24 07:21:40Z", "expirationDate": "2025-09-10 07:31:56Z", "justification": "This error is baselined with an expiration date of 180 days from 2025-03-24 07:31:56Z"}, "b96eb0857e0c4b13ae3f8bba18191b08bf1a6a01078c12190b4f4efd14b92ff6": {"signature": "b96eb0857e0c4b13ae3f8bba18191b08bf1a6a01078c12190b4f4efd14b92ff6", "alternativeSignatures": ["22d1fe0c867fccaaab0a1e758415fedec7c56c37d2a344199025acf88a96a039"], "target": "target/distrib/Release/x64/OdlExporterCppTest/OdlExporterCppTest.exe", "memberOf": ["cloudbuild.1espt"], "tool": "binskim", "ruleId": "BA2007", "createdDate": "2024-12-22 05:19:08Z", "expirationDate": "2025-06-10 05:35:15Z", "justification": "This error is baselined with an expiration date of 180 days from 2024-12-22 05:35:15Z"}, "d26844bfc032bc4f959d7c0ada24076fb7521855457580cc4cb49faf3135eecf": {"signature": "d26844bfc032bc4f959d7c0ada24076fb7521855457580cc4cb49faf3135eecf", "alternativeSignatures": ["36836f9f04c88b8e9d9b7adbb1b09e92e5ac1c482e81fe1c18c11661536cfdb4"], "target": "target/distrib/Release/x64/CppConsoleApp/CppConsoleApp.exe", "memberOf": ["cloudbuild.1espt"], "tool": "binskim", "ruleId": "BA2007", "createdDate": "2024-12-22 05:19:14Z", "expirationDate": "2025-06-10 05:35:15Z", "justification": "This error is baselined with an expiration date of 180 days from 2024-12-22 05:35:15Z"}, "9426d3bb4c50b02a2cbb7b92951e1c644c07b4c72a7ea83c77c823fdbefd8410": {"signature": "9426d3bb4c50b02a2cbb7b92951e1c644c07b4c72a7ea83c77c823fdbefd8410", "alternativeSignatures": ["c082dc995930fabe8627519492ae39e6018cfb64f8b0762b884404479ebe8741"], "target": "target/distrib/Release/x64/OdlTraceExporterCppTest/OdlTraceExporterCppTest.exe", "memberOf": ["cloudbuild.1espt"], "tool": "binskim", "ruleId": "BA2007", "createdDate": "2025-03-24 07:22:06Z", "expirationDate": "2025-09-10 07:31:56Z", "justification": "This error is baselined with an expiration date of 180 days from 2025-03-24 07:31:56Z"}, "c7ed31a5551e02cfea1ce751ab3b226f9298d99339729ace8141ec77a51def69": {"signature": "c7ed31a5551e02cfea1ce751ab3b226f9298d99339729ace8141ec77a51def69", "alternativeSignatures": ["ce0a40caf4a5095d4d429348139fc08d8eb736aa36413fbf149c1f0ea5e6d4b1"], "target": "target/distrib/Release/x64/OdlTraceExporterCppTest/OdlTraceExporterCppTest.exe", "memberOf": ["cloudbuild.1espt"], "tool": "binskim", "ruleId": "BA2008", "createdDate": "2025-03-24 07:22:06Z", "expirationDate": "2025-09-10 07:31:56Z", "justification": "This error is baselined with an expiration date of 180 days from 2025-03-24 07:31:56Z"}, "2784ad83bc3ede64a6c9f80e4d96449d91289874c3847602e652de37659e7ee0": {"signature": "2784ad83bc3ede64a6c9f80e4d96449d91289874c3847602e652de37659e7ee0", "alternativeSignatures": ["518f5e11a5d0b75850f947af6ef684baa71b3cbd5e8f4faefa3ab5da41c45df7"], "target": "target/distrib/Release/x64/AsioUtilsTest/AsioUtilsTest.exe", "memberOf": ["cloudbuild.1espt"], "tool": "binskim", "ruleId": "BA2008", "createdDate": "2025-03-24 07:21:46Z", "expirationDate": "2025-09-10 07:31:56Z", "justification": "This error is baselined with an expiration date of 180 days from 2025-03-24 07:31:56Z"}, "32b6807423e0c3b78584ab31a0d80f9e918223a6719fec42422e6f4f5b9b6b85": {"signature": "32b6807423e0c3b78584ab31a0d80f9e918223a6719fec42422e6f4f5b9b6b85", "alternativeSignatures": ["313817384de03e4d79804a9a46286f3a05f2c22abed625468364f6c0a2459a6f"], "target": "target/distrib/Release/x64/DemoApp/DemoApp.exe", "memberOf": ["cloudbuild.1espt"], "tool": "binskim", "ruleId": "BA2007", "createdDate": "2025-03-24 07:22:01Z", "expirationDate": "2025-09-10 07:31:56Z", "justification": "This error is baselined with an expiration date of 180 days from 2025-03-24 07:31:56Z"}, "312aefe369bf8fa4c9ec36f8f40de294fe4501eced15223a2d5d90877b24a588": {"signature": "312aefe369bf8fa4c9ec36f8f40de294fe4501eced15223a2d5d90877b24a588", "alternativeSignatures": ["1547cd41cd66f47b49bd4f2807c92ff3d971861828b9721c959516ffd4a6407c"], "target": "target/distrib/Release/x64/DemoApp/DemoApp.exe", "memberOf": ["cloudbuild.1espt"], "tool": "binskim", "ruleId": "BA2008", "createdDate": "2025-03-24 07:22:01Z", "expirationDate": "2025-09-10 07:31:56Z", "justification": "This error is baselined with an expiration date of 180 days from 2025-03-24 07:31:56Z"}, "787059e83c8915110c6782b65d80b27e2a9cc228d3ff81b674d3b7fcff14553b": {"signature": "787059e83c8915110c6782b65d80b27e2a9cc228d3ff81b674d3b7fcff14553b", "alternativeSignatures": ["85b00038748f2b183afc8e12d6335378b9a5e05f15f4d5e652cbdccf487aa59b"], "target": "target/distrib/Release/x64/AsioUtilsTest/AsioUtilsTest.exe", "memberOf": ["cloudbuild.1espt"], "tool": "binskim", "ruleId": "BA2007", "createdDate": "2025-02-10 08:16:42Z", "expirationDate": "2025-07-30 08:25:00Z", "justification": "This error is baselined with an expiration date of 180 days from 2025-02-10 08:25:00Z"}}}