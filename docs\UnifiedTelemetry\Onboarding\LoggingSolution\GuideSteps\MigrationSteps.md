# Step 7 - Switch Data Seamlessly

When we complete the sections above, migration will be continued with the following phases.

## Phase 1: Dual Ingestion

Turn on flight for R9 logging and keep IFx logging enabled at the same time. This is to prepare for validation between R9 logs and the original ones and ensure the data integrity.

1. Update the configuration to enable both R9 and IFx logging.
2. Deploy the updated configuration to the development environment.
3. Monitor the logs to ensure both R9 and IFx logs are being generated correctly.
4. Validate that the logs are being ingested into the appropriate systems (e.g., Geneva, Kusto).

## Phase 2: Data parity check

Validate functionality, data record numbers, consistency of columns, types, and values per pair of log records.
Here’s an example of parity check result: [Parity check report.docx](https://microsoftapc-my.sharepoint.com/:w:/g/personal/jiangwe_microsoft_com/EZ4UoQp0daJJnZ4qk2BWavMB5uut37MQyjeYGY7fGjADpw?e=6nKYgA).

1. Extract a sample of logs from both R9 and IFx.
2. Compare the logs to ensure they contain the same data.
3. Check for any discrepancies in the log records.

## Phase 3: Switch downstream consumers

Switch monitors and alerts to R9 event tables.

1. Identify all downstream consumers of the log data (e.g., monitoring systems, alerting systems).
2. Update the configuration of these systems to use the R9 event tables.
3. Test the updated configuration to ensure it works correctly.
4. Monitor the systems to ensure they are receiving and processing the R9 logs as expected.

## Phase 4: Disable Ifx logging

Turn off flight for Ifx logging and R9 will be the only logging method after this phase.

1. Update the configuration to disable IFx logging.
2. Deploy the updated configuration to the development environment.
3. Monitor the logs to ensure only R9 logs are being generated.
4. Validate that the logs are being ingested into the appropriate systems (e.g., Geneva, Kusto).

## Phase 5: Clean up legacy code

Remove any legacy code related to IFx logging.