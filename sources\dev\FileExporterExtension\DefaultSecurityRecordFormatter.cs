// <copyright file="DefaultSecurityRecordFormatter.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System.Text;
using Microsoft.Extensions.ObjectPool;
using Microsoft.R9.Extensions.SecurityTelemetry;

namespace Microsoft.M365.Core.Telemetry.FileExporter
{
    /// <summary>
    /// The default formatter for <see cref="SecurityRecord"/>.
    /// </summary>
    internal static class DefaultSecurityRecordFormatter
    {
        private const string SchemaName = "SchemaName";

        private static readonly ObjectPool<StringBuilder> stringBuilderPool = StringBuilderPoolFactory.CreateStringBuilderPool();

        /// <summary>
        /// Formatter of <see cref="SecurityRecord"/> class.
        /// </summary>
        /// <param name="securityRecord"><see cref="SecurityRecord"/>.</param>
        /// <param name="staticString"><see cref="string"/>.</param>
        internal static string FormatSecurityRecord(in SecurityRecord securityRecord, string staticString)
        {
            var logEntryBuilder = stringBuilderPool.Get();

            try
            {
                logEntryBuilder = logEntryBuilder
                .Append(SchemaName)
                .Append(':')
                .Append(securityRecord.SchemaName)
                .Append(',');

                logEntryBuilder = logEntryBuilder.Append(staticString);

                foreach (var property in securityRecord.Properties)
                {
                    var value = property.Value == null ? string.Empty : property.Value;
                    logEntryBuilder = logEntryBuilder
                    .Append(property.Key)
                    .Append(':')
                    .Append(value)
                    .Append(',');
                }

                logEntryBuilder.Length--;

                return logEntryBuilder.ToString();
            }
            finally
            {
                stringBuilderPool.Return(logEntryBuilder);
            }
        }
    }
}
