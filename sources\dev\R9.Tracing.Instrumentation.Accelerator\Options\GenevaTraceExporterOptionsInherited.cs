// <copyright file="GenevaTraceExporterOptionsInherited.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

namespace Microsoft.M365.Core.Telemetry.R9.Tracing.Instrumentation.Accelerator
{
    using Microsoft.R9.Extensions.Tracing.Exporters;

    /// <summary>
    /// GenevaTraceExporterOptionsInherited
    /// </summary>
    public class GenevaTraceExporterOptionsInherited : GenevaTraceExporterOptions
    {
        /// <summary>
        /// IsEnabled
        /// </summary>
        public bool IsEnabled { get; } = true;

        /// <summary>
        /// TraceIdBasedSampleRatio
        /// </summary>
        public double TraceIdBasedSampleRatio { get; set; } = 1.0;
    }
}
