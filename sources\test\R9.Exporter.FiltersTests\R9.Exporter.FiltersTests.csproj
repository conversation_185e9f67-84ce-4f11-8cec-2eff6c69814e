<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <AssemblyName>Microsoft.M365.Core.Telemetry.R9.Exporter.Filters.Test</AssemblyName>
    <RootNamespace>Microsoft.M365.Core.Telemetry.R9.Exporter.Filters.Test</RootNamespace>
    <TargetFrameworks>net472;net6.0;net8.0</TargetFrameworks>
    <IsCodedUITest>False</IsCodedUITest>
    <TestProjectType>UnitTest</TestProjectType>
    <LangVersion>Latest</LangVersion>
    <NoWarn>SA1600,CA1707,CA1801,CA1307</NoWarn>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.Extensions.Configuration.Json" />
    <PackageReference Include="Microsoft.Extensions.Hosting" />
    <PackageReference Include="Microsoft.NET.Test.Sdk" />
    <PackageReference Include="Microsoft.R9.Extensions.Tracing" />
    <PackageReference Include="xunit" />
    <PackageReference Include="xunit.runner.visualstudio" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\dev\R9.Exporter.Filters\R9.Exporter.Filters.csproj" />
  </ItemGroup>

  <ItemGroup>
    <None Update="appsettings.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
  </ItemGroup>
</Project>
