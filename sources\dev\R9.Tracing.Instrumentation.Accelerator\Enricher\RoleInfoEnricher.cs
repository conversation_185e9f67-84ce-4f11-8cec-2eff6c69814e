﻿// <copyright file="RoleInfoEnricher.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

namespace Microsoft.M365.Core.Telemetry.R9.Tracing.Instrumentation.Accelerator
{
    using System;
    using System.Diagnostics;
    using Microsoft.Extensions.Options;
    using Microsoft.R9.Extensions.Enrichment;

    /// <summary>
    /// Enrich role related information
    /// </summary>
    public class RoleInfoEnricher : ITraceEnricher
    {
        /// <summary>
        /// Service meta related configuration
        /// </summary>
        private ServiceMetaDataOptions Options { get; }

        /// <summary>
        /// the cloud role instance the service is running on
        /// </summary>
        private string cloudRoleInstance;

        /// <summary>
        /// Ctor of RoleInfoEnricher
        /// </summary>
        /// <param name="options">Service meta configuration</param>
        public RoleInfoEnricher(IOptions<ServiceMetaDataOptions> options)
        {
            this.Options = options.Value;
            var isCosmicService = ConfigurationUtility.IsCosmicService(this.Options.RuntimeModel);
            this.cloudRoleInstance = isCosmicService ? Environment.GetEnvironmentVariable("COSMIC_PODNAME") : Environment.MachineName;
        }

        /// <inheritdoc/>
        public void Enrich(Activity activity)
        {
            activity?.SetTag(Constants.CloudRole, Options.ServiceName);
            activity?.SetTag(Constants.CloudRoleInstance, this.cloudRoleInstance);       
        }
    }
}
