﻿// <copyright file="GlobalSuppressions.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System.Diagnostics.CodeAnalysis;

[assembly: SuppressMessage("Naming", "CA1707:Identifiers should not contain underscores", Justification = "Don't warn underscores for unit test", Scope = "member", Target = "~M:Microsoft.R9.Extensions.Telemetry.Exporters.ODLTcp.Test.ODLTcpTraceExporterExtensionTest.AddODLTcpExporter_GivenInvalidArguments_ThrowsException")]
