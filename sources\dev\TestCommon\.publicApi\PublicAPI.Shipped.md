﻿```csharp

namespace Microsoft.M365.Core.Telemetry.TestCommon
{
    public static class AddFakeExtensions
    {
        public static void EnableR9(System.Collections.Generic.List<string> logRecords = null, Microsoft.M365.Core.Telemetry.R9.IBlockList blockList = null);
        public static Microsoft.Extensions.DependencyInjection.IServiceCollection ConfigureServicesForR9Test(System.Collections.Generic.List<string> logRecords = null, Microsoft.M365.Core.Telemetry.R9.IBlockList blockList = null, Microsoft.M365.Core.Telemetry.ECSClient.IPassiveR9Config passiveConfig = null);
    }

    public class BaseTest : IDisposable
    {
        public BaseTest();
        public void Dispose();
    }
}

```
