// <copyright file="PostTraceSamplingTests.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System.Diagnostics;
using Microsoft.AspNetCore.Http;
using Microsoft.M365.Core.Telemetry.R9.Tracing.AdvancedSampling;
using Moq;
using OpenTelemetry;
using OpenTelemetry.Trace;
using Xunit;

namespace R9.Tracing.AdvancedSamplingTests
{
    /// <summary>
    /// PostTraceSamplingTests
    /// </summary>
    public class PostTraceSamplingTests
    {
        /// <summary>
        /// Test PostTrace Filter
        /// </summary>
        [Fact]
        public void TestPostTraceFilter()
        {
            BaggageFilter filter = new BaggageFilter();
            Assert.False(filter.ShouldFilter(null));

            var activity = new Activity("activity1");
            Assert.False(filter.ShouldFilter(activity));

            var activityWithBaggage = new Activity("activity2");
            activityWithBaggage.SetBaggage(Constants.PostTraceBaggageName, "true");
            Assert.True(filter.ShouldFilter(activityWithBaggage));

            Assert.NotEmpty(filter.GetDescription());
        }

        /// <summary>
        /// Test PostTraceBuilderExtension
        /// </summary>
        [Fact]
        public void TestPostTraceBuilderExtensionAsync()
        {
            var activity = new Activity("activity1");
            
            var message = new HttpResponseMessage();
            message.Headers.Add(Constants.PostTraceBaggageName, "True");

            PostTraceBuildExtensions.EnrichPostTraceBaggage(activity, message);
            var baggage = activity.GetBaggageItem(Constants.PostTraceBaggageName);
            
            Assert.True(bool.Parse(baggage == null ? "False" : baggage));

            Assert.Throws<ArgumentNullException>(() => ((TracerProviderBuilder)null!).AddPostTracing());

            var builder = Sdk.CreateTracerProviderBuilder().AddPostTracing().Build();
            Assert.NotNull(builder);
        }

        /// <summary>
        /// Test Post-Trace Middleware
        /// </summary>
        /// <returns></returns>
        [Fact]
        public async Task TestPostTraceMiddleware()
        {
            var parentActivity = new Activity("ParentActivity").Start();
            var activity = new Activity("PostTraceMiddleware").Start();
            activity.SetParentId(parentActivity.TraceId, parentActivity.SpanId);
            bool isNextDelegateCalled = false;
            var httpContextMock = new Mock<HttpContext>();
            var httpResponseMock = new Mock<HttpResponse>();

            Func<Task>? callbackMethod = null;

            httpResponseMock.Setup(x =>
                x.OnStarting(It.IsAny<Func<Task>>()))
                    .Callback<Func<Task>>(m => callbackMethod = m);
            httpResponseMock.SetupGet(x => x.StatusCode).Returns(400);

            httpContextMock.SetupGet(x => x.Response)
                .Returns(httpResponseMock.Object);

            httpContextMock.SetupGet(x => x.Response.Headers)
                .Returns(new HeaderDictionary());

            var fakeHttpContext = httpContextMock.Object;

            var requestDelegate = new RequestDelegate(async (innerContext) =>
            {
                isNextDelegateCalled = true;

                if (callbackMethod != null)
                {
                    await callbackMethod.Invoke().ConfigureAwait(true);
                }
                else
                {
                    await Task.CompletedTask.ConfigureAwait(true);
                }
            });

            var middelware = new PostTraceMiddleware(requestDelegate);

            await middelware.InvokeAsync(fakeHttpContext).ConfigureAwait(true);

            Assert.True(isNextDelegateCalled);
            Assert.True(fakeHttpContext.Response.Headers.TryGetValue(Constants.PostTraceBaggageName, out var value));
            Assert.Equal("True", value);
            Assert.True(activity.GetBaggageItem(Constants.PostTraceBaggageName)?.Equals("True", StringComparison.Ordinal));
            Assert.True(parentActivity.GetBaggageItem(Constants.PostTraceBaggageName)?.Equals("True", StringComparison.Ordinal));

            activity.Stop();
            parentActivity.Stop();
        }
    }
}