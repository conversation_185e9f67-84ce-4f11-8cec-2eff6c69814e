﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <AssemblyName>Microsoft.M365.Core.Telemetry.Exporters.ODLTCP.Test</AssemblyName>
    <TargetFrameworks>net472;net6.0;net8.0</TargetFrameworks>
    <Nullable>enable</Nullable>
    <TestProjectType>UnitTest</TestProjectType>
    <LangVersion>9.0</LangVersion>
    <IsPackable>false</IsPackable>
    <IsTestProject>true</IsTestProject>
    <NoWarn>SA1600,SA1512</NoWarn>
    <NoWarn>$(NoWarn),CA1707,CA1801,CA1307</NoWarn>
    <OutputType>Library</OutputType>
    <Platforms>AnyCPU</Platforms>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.NET.Test.Sdk" />
    <PackageReference Include="Microsoft.Extensions.Logging" />
    <PackageReference Include="xunit" />
    <PackageReference Include="Microsoft.R9.Extensions.Logging.Abstractions" />
    <PackageReference Include="Microsoft.R9.Extensions.Logging" />
    <PackageReference Include="Microsoft.R9.Extensions.Tracing" />
    <PackageReference Include="Microsoft.Extensions.Hosting" />
    <PackageReference Include="Moq" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Json" />
    <PackageReference Include="xunit.runner.visualstudio" />
  </ItemGroup>

    <ItemGroup>
        <ProjectReference Include="..\..\dev\Telemetry.Exporters.ODLTcp\Telemetry.Exporters.ODLTcp.csproj" />
    </ItemGroup>

    <ItemGroup>
        <RuntimeHostConfigurationOption Include="System.Diagnostics.DefaultActivityIdFormatIsHierarchial" Value="false" />
    </ItemGroup>

    <ItemGroup>
        <None Update="appsettings.json">
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
    </ItemGroup>

</Project>
