**Section Key**: `SubstrateLogging:GenevaExporter`

The default exporter to be set.
There **must** be:
- A `ConnectionString` to setup ETW session with Geneva Agent for data export.
- A non-empty dictionary `TableNameMappings` to map logs to Geneva Events.

For detail of each option, please refer [OpenTelemetry.Exporter.Geneva.GenevaExporterOption](https://github.com/open-telemetry/opentelemetry-dotnet-contrib/blob/main/src/OpenTelemetry.Exporter.Geneva/README.md#genevaexporteroptions-for-logs-and-traces) and [Advanced GenevaLogExporterOptions](https://eng.ms/docs/experiences-devices/r9-sdk/docs/telemetry/logging/geneva-log-export#advanced--configuration).

The `GenevaLogExporterOption` is a wrapper around the Geneva provided GenevaExporterOptions.
It provides extra options based on `GenevaExporterOption`.

**Example**

```json
{
    "SubstrateLogging":{
        "GenevaExporter": {
            "ConnectionString": "EtwSession=TestSession",
            "TableNameMappings": {
                "Test.Program": "TestEvent",
                "*": "ServiceEvent"
            }
        }
    }
}
```