﻿// This file is used by Code Analysis to maintain SuppressMessage
// attributes that are applied to this project.
// Project-level suppressions either have no target or are given
// a specific target and scoped to a namespace, type, member, etc.

using System.Diagnostics.CodeAnalysis;

[assembly: SuppressMessage("StyleCop.CSharp.DocumentationRules", "SA1600:Elements should be documented", Justification = "No need to documented for every element", Scope = "member", Target = "~M:Microsoft.M365.Core.Telemetry.ODL.SecurityTelemetryTcpExporter.SecurityTelemetryTcpExporter.ExportBatch(Microsoft.R9.Extensions.SecurityTelemetry.Batch{Microsoft.R9.Extensions.SecurityTelemetry.SecurityRecord}@)")]
