﻿// <copyright file="DyeBasedSamplingOption.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System.Collections.Generic;

namespace Microsoft.M365.Core.Telemetry.R9.Tracing.AdvancedSampling
{
    /// <Inheritdoc/>
    public class DyeBasedSamplingOption
    {
        /// <summary>
        /// constrcutor
        /// </summary>
        public DyeBasedSamplingOption()
        {
        }

        /// <summary>
        /// constrcutor
        /// </summary>
        /// <param name="activitySource"></param>
        /// <param name="sample"></param>
        /// <param name="drop"></param>
        public DyeBasedSamplingOption(ISet<string> activitySource, ISet<string> sample = null, ISet<string> drop = null)
        {
            this.ActivitySource = activitySource;
            this.Tags = new Tags(sample, drop);
        }

        /// <summary>
        /// DyeingTags
        /// </summary>
        public Tags Tags { get; set; } = new Tags();

        /// <summary>
        /// DyeBased ActivitySource name
        /// </summary>
        public ISet<string> ActivitySource { get; private set; } = new HashSet<string>(64);
    }
}
