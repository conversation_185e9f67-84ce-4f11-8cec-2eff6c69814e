<Project>
  <Import Project="Sdk.props" Sdk="Microsoft.Build.NoTargets" />
  <PropertyGroup>
    <TargetFramework>net472</TargetFramework>
  </PropertyGroup>
  <Import Project="$(EnlistmentRoot)\build\Extensions\CodeScanners\SecurityCopScan.targets" />
  
  <PropertyGroup>
    <ProjectGuid>{B132A9BD-7A81-43D9-B9F1-F5973B81D18F}</ProjectGuid>
    <SecurityCopErrorForPowerShell>$(OutputPath)powershell.err</SecurityCopErrorForPowerShell>
    <SecurityCopOutputForPowerShell>$(OutputPath)powershell.txt</SecurityCopOutputForPowerShell>
    <PowerShellScriptList>$(OutputPath)powershell_scripts.txt</PowerShellScriptList>
    <FilePathExclusionPattern>build\\|distrib\\|logs\\|QLocal\\|target\\|tmp\\|obj\\|objd\\|\\tools\\user\\</FilePathExclusionPattern>
  </PropertyGroup>
  <Import Project="Sdk.targets" Sdk="Microsoft.Build.NoTargets" />
  <Target Name="Build" DependsOnTargets="RunSecurityCopForPowerShell" Condition=" '$(DISABLE_SECURITYCOP)' == '' " />
</Project>