﻿// <copyright file="RuleBasedSamplerExtension.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;

namespace Microsoft.M365.Core.Telemetry.R9.Logging.Substrate.Sampler
{
    /// <summary>
    /// Extension methods for RuleBasedSampler
    /// </summary>
    public static class RuleBasedSamplerExtension
    {
        /// <summary>
        /// Configures the RuleBasedSampler using the specified logging builder and service configuration.
        /// </summary>
        /// <param name="loggingBuilder">The logging builder to configure.</param>
        /// <param name="configuration">The service configuration to bind options from.</param>
        /// <returns>The configured builder.</returns>
        public static ILoggingBuilder AddRuleBasedSampler(this ILoggingBuilder loggingBuilder, IConfiguration configuration)
        {
            loggingBuilder.Services.AddOptions<RuleBasedSamplerOptions>().Bind(configuration);
            return loggingBuilder.AddSampler<RuleBasedSampler>();
        }
    }
}
