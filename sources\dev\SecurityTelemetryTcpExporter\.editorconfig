﻿[*.cs]

# R9EXPDEV: Type is for evaluation purposes only and is subject to change or removal in future updates. Suppress this diagnostic to proceed.
dotnet_diagnostic.R9EXPDEV.severity = none

dotnet_diagnostic.R9EXP0008.severity = none

# SA1633: File should have header
dotnet_diagnostic.SA1633.severity = none

# CS0108: Member hides inherited member; missing new keyword
dotnet_diagnostic.CS0108.severity = none

# CS8625: Cannot convert null literal to non-nullable reference type.
dotnet_diagnostic.CS8625.severity = none

# SA1600: Elements should be documented
dotnet_diagnostic.SA1600.severity = none

# CS8618: Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
dotnet_diagnostic.CS8618.severity = none

# CS0219: Variable is assigned but its value is never used
dotnet_diagnostic.CS0219.severity = none

# CS8600: Converting null literal or possible null value to non-nullable type.
dotnet_diagnostic.CS8600.severity = none
