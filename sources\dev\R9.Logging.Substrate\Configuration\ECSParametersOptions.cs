﻿// <copyright file="ECSParametersOptions.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System.Collections.Generic;

namespace Microsoft.M365.Core.Telemetry.R9.Logging.Substrate.Configuration
{
    /// <summary>
    /// Represents the configuration options for ECS parameters.
    /// Corresponds to the "ECSParameters" section in configuration.
    /// </summary>
    public class EcsParametersOptions
    {
        /// <summary>
        /// Gets or sets the ECS identifiers.
        /// </summary>
        public EcsIdentifiersOptions ECSIdentifiers { get; set; } = new EcsIdentifiersOptions();

        /// <summary>
        /// Gets or sets the environment type (e.g., Production, Integration).
        /// </summary>
        public string EnvironmentType { get; set; } = string.Empty;

        /// <summary>
        /// Gets or sets the client identifier.
        /// </summary>
        public string Client { get; set; } = string.Empty;

        /// <summary>
        /// Gets or sets the list of agents (e.g., "Logs").
        /// </summary>
        public HashSet<string> Agents { get; } = new HashSet<string>();
    }

    /// <summary>
    /// Represents the ECS identifiers.
    /// Corresponds to the "ECSParameters:ECSIdentifiers" section.
    /// </summary>
    public class EcsIdentifiersOptions
    {
        /// <summary>
        /// Gets or sets the service identifier.
        /// This is a required field.
        /// </summary>
        public string ServiceName { get; set; } = string.Empty;
    }
}