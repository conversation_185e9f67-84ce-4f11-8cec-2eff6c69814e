﻿// ---------------------------------------------------------------------------
// <copyright file="ETWLogger.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>
// ---------------------------------------------------------------------------
using System;
using System.Collections.Generic;
using System.Diagnostics.Tracing;
using System.Linq.Expressions;
using LogLevel = Microsoft.Extensions.Logging.LogLevel;

namespace Microsoft.R9.Extensions.Telemetry.Exporters.Base
{
    /// <summary>
    /// DBA event trace logger
    /// </summary>
    [EventSource(Name = ODLExporterCommonSettings.EventSourceName)]
    internal sealed class ETWLogger : EventSource, IInternalLogger
    {
        /// <summary>
        /// Log info with customer dimensions
        /// </summary>
        /// <param name="logLevel">Log level</param>
        /// <param name="logtype">Log type</param>
        /// <param name="atguid">Audit trail guid</param>
        /// <param name="message">formatted message</param>
        /// <param name="dimensionPairs">customer dimension names and values</param>
        [NonEvent]
        public void Log(LogLevel logLevel, string logtype, string atguid, string message, Dictionary<string, string> dimensionPairs)
        {
            string customerDimensions = string.Empty;
            if (dimensionPairs != null)
            {
                customerDimensions = ODLLoggerUtils.ConstructDimensions(logtype, dimensionPairs);
            }
            try
            {
                if (string.IsNullOrEmpty(logtype)) { throw new ArgumentNullException(logtype); }

                switch (logLevel)
                {
                    case LogLevel.Information:
                        LogInfoImpl(logtype, atguid, message, DateTime.UtcNow, customerDimensions);
                        break;
                    case LogLevel.Warning:
                        LogWarningImpl(logtype, atguid, message, DateTime.UtcNow, customerDimensions);
                        break;
                    case LogLevel.Error:
                        LogErrorImpl(logtype, atguid, message, DateTime.UtcNow, customerDimensions);
                        break;
                    case LogLevel.Critical:
                        LogCriticalImpl(logtype, atguid, message, DateTime.UtcNow, customerDimensions);
                        break;
                    default:
                        LogDebugImpl(logtype, atguid, message, DateTime.UtcNow, customerDimensions);
                        break;
                }
            }
            catch (Exception e)
            {
                ExporterLogger.Log.LogWriteEventError(logtype, e.ToString());
            }
        }

        /// <summary>
        /// Log trace
        /// </summary>
        /// <param name="logtype">Log type</param>
        /// <param name="atguid">Audit trail guid</param>
        /// <param name="message">formatted message</param>
        [NonEvent]
        public void Trace(string logtype, string atguid, string message)
        {
            if (string.IsNullOrEmpty(logtype)) { throw new ArgumentNullException(logtype); }
            LogAlwaysImpl(logtype, atguid, message, DateTime.UtcNow, string.Empty);
        }
        #region EventSource

        [Event(1, Level = EventLevel.Verbose)]
        private void LogDebugImpl(string logtype, string atguid, string message, DateTime eventTime, string customerDimensions)
        {
            WriteEvent(1, logtype, atguid, message, eventTime, customerDimensions);
        }

        [Event(2, Level = EventLevel.Informational)]
        private void LogInfoImpl(string logtype, string atguid, string message, DateTime eventTime, string customerDimensions)
        {
            WriteEvent(2, logtype, atguid, message, eventTime, customerDimensions);
        }

        [Event(3, Level = EventLevel.Warning)]
        private void LogWarningImpl(string logtype, string atguid, string message, DateTime eventTime, string customerDimensions)
        {
            WriteEvent(3, logtype, atguid, message, eventTime, customerDimensions);
        }

        [Event(4, Level = EventLevel.Error)]
        private void LogErrorImpl(string logtype, string atguid, string message, DateTime eventTime, string customerDimensions)
        {
            WriteEvent(4, logtype, atguid, message, eventTime, customerDimensions);
        }

        [Event(5, Level = EventLevel.Critical)]
        private void LogCriticalImpl(string logtype, string atguid, string message, DateTime eventTime, string customerDimensions)
        {
            WriteEvent(5, logtype, atguid, message, eventTime, customerDimensions);
        }

        [Event(6, Level = EventLevel.LogAlways)]
        private void LogAlwaysImpl(string logtype, string atguid, string message, DateTime eventTime, string customerDimensions)
        {
            WriteEvent(6, logtype, atguid, message, eventTime, customerDimensions);
        }

        #endregion
    }
}