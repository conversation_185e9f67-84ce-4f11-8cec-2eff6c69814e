﻿// <copyright file="HttpTraceEUIIRedactEnricherTest.netfx.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>
#if NETFRAMEWORK
using System.Diagnostics;
using System.IO;
using System.Web;
using Xunit;

namespace Microsoft.M365.Core.Telemetry.R9.Tracing.Instrumentation.Accelerator.Tests
{
    /// <summary>
    /// AspNetEUIIRedactEnricherTest
    /// </summary>
    public class AspNetEUIIRedactEnricherTest
    {
        HttpContext httpContext;

        /// <summary>
        /// AspNetEUIIRedactEnricherTest
        /// </summary>
        public AspNetEUIIRedactEnricherTest()
        {
            HttpRequest request = new HttpRequest("foo", "http://localhost:8080/api/values?name=foo", "name=foo");
            this.httpContext = new HttpContext(request, new HttpResponse(new StringWriter()));
        }

        /// <summary>
        /// EnrichWithDefaultRedactionStrategyValidInputSuccess
        /// </summary>
        [Fact]
        public void EnrichWithDefaultRedactionStrategyValidInputSuccess()
        {
            var activity = new Activity("In");
            HttpTraceEUIIRedactEnricher.Enrich(activity, Constants.OnStartActivity, this.httpContext.Request, RedactionStrategyType.Default);
            Assert.Equal($"/{Constants.RedactedPlacholder}", activity.DisplayName);
            Assert.Equal("http://localhost:8080/" + Constants.RedactedPlacholder, activity.GetTagItem(Constants.HttpUrl));
            Assert.Equal("localhost:8080", activity.GetTagItem(Constants.HttpTarget));

            activity = null;
            HttpTraceEUIIRedactEnricher.Enrich(activity, Constants.OnStartActivity, this.httpContext.Request, RedactionStrategyType.Default);
            Assert.Null(activity);
        }
    }
}
#endif