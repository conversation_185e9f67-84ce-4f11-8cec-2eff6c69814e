// <copyright file="DoubleConstraintTest.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>
using System;
using System.Collections.Generic;
using System.Globalization;
using System.IO;
using System.Text;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.M365.Core.Telemetry.R9.Logging.Substrate.Sampler;
using OpenTelemetry.Logs;
using Xunit;

namespace Microsoft.M365.Core.Telemetry.R9.Logging.Substrate.Test.SamplingTest
{
    public class DoubleConstraintTest
    {
        private const string LoggerName = "DoubleConstraintTest";

        private const string DoubleConstraintsConfig = @"
{
    ""SubstrateLogging"": {
        ""RuleBasedSampler"": {
            ""DoubleConstraintTest"": [
                {
                    ""Constraints"": [
                        {
                            ""Field"": ""FieldPlaceholder"",
                            ""Type"": ""TypePlaceholder"",
                            ""Operator"": ""OperatorPlaceholder"",
                            ""Value"": ""ValuePlaceholder""
                        }
                    ],
                    ""Strategy"": {
                        ""Type"": ""Random"",
                        ""SampleRate"": 1.0
                    }
                },
                {
                    ""Constraints"": [],
                    ""Strategy"": {
                        ""Type"": ""Random"",
                        ""SampleRate"": 0.0
                    }
                }
            ]
        }
    }
}";

        private static string ReplaceConfigPlaceholders(string field, string type, string op, string value)
        {
            return DoubleConstraintsConfig
                .Replace("FieldPlaceholder", field)
                .Replace("TypePlaceholder", type)
                .Replace("OperatorPlaceholder", op)
                .Replace("ValuePlaceholder", value);
        }

        private static void GenerateLog(ILogger logger, string fieldName, double fieldValue)
        {
            logger.LogInformation($"Test message {{{fieldName}}}", fieldValue.ToString("R", CultureInfo.InvariantCulture));
        }

        [Fact]
        public void NumericEquals_ShouldMatchAndSample()
        {
            var config = ReplaceConfigPlaceholders("TestField", ConstraintType.Double, OperatorType.NumericEquals, "42.0");
            var exportedItems = new List<LogRecord>();
            var logger = LoggerTestUtils.CreateLoggerWithConfig(config, exportedItems, LoggerName);

            GenerateLog(logger, "TestField", 42.0);

            Assert.Single(exportedItems);
        }

        [Fact]
        public void NumericNotEquals_ShouldMatchAndSample()
        {
            var config = ReplaceConfigPlaceholders("TestField", ConstraintType.Double, OperatorType.NumericNotEquals, "42.0");
            var exportedItems = new List<LogRecord>();
            var logger = LoggerTestUtils.CreateLoggerWithConfig(config, exportedItems, LoggerName);

            GenerateLog(logger, "TestField", 42.1);

            Assert.Single(exportedItems);
        }

        [Fact]
        public void GreaterThan_ShouldMatchAndSample()
        {
            var config = ReplaceConfigPlaceholders("TestField", ConstraintType.Double, OperatorType.GreaterThan, "42.0");
            var exportedItems = new List<LogRecord>();
            var logger = LoggerTestUtils.CreateLoggerWithConfig(config, exportedItems, LoggerName);

            GenerateLog(logger, "TestField", 42.1);

            Assert.Single(exportedItems);
        }

        [Fact]
        public void LessThan_ShouldMatchAndSample()
        {
            var config = ReplaceConfigPlaceholders("TestField", ConstraintType.Double, OperatorType.LessThan, "42.0");
            var exportedItems = new List<LogRecord>();
            var logger = LoggerTestUtils.CreateLoggerWithConfig(config, exportedItems, LoggerName);

            GenerateLog(logger, "TestField", 41.9);

            Assert.Single(exportedItems);
        }

        [Fact]
        public void GreaterThanOrEqual_ShouldMatchAndSample()
        {
            var config = ReplaceConfigPlaceholders("TestField", ConstraintType.Double, OperatorType.GreaterThanOrEqual, "42.0");
            var exportedItems = new List<LogRecord>();
            var logger = LoggerTestUtils.CreateLoggerWithConfig(config, exportedItems, LoggerName);

            GenerateLog(logger, "TestField", 42.0);

            Assert.Single(exportedItems);
        }

        [Fact]
        public void LessThanOrEqual_ShouldMatchAndSample()
        {
            var config = ReplaceConfigPlaceholders("TestField", ConstraintType.Double, OperatorType.LessThanOrEqual, "42.0");
            var exportedItems = new List<LogRecord>();
            var logger = LoggerTestUtils.CreateLoggerWithConfig(config, exportedItems, LoggerName);

            GenerateLog(logger, "TestField", 42.0);

            Assert.Single(exportedItems);
        }

        [Fact]
        public void InvalidType_ShouldNotMatch()
        {
            var config = ReplaceConfigPlaceholders("TestField", "InvalidDouble", OperatorType.NumericEquals, "42.0");
            var exportedItems = new List<LogRecord>();
            var logger = LoggerTestUtils.CreateLoggerWithConfig(config, exportedItems, LoggerName);

            GenerateLog(logger, "TestField", 42.0);

            Assert.Empty(exportedItems);
        }

        [Fact]
        public void InvalidOperator_ShouldNotMatch()
        {
            var config = ReplaceConfigPlaceholders("TestField", ConstraintType.Double, "InvalidOperator", "42.0");
            var exportedItems = new List<LogRecord>();
            var logger = LoggerTestUtils.CreateLoggerWithConfig(config, exportedItems, LoggerName);

            GenerateLog(logger, "TestField", 42.0);

            Assert.Empty(exportedItems);
        }

        [Fact]
        public void OperatorWithWhitespace_ShouldMatchAndSample()
        {
            var config = ReplaceConfigPlaceholders("TestField", ConstraintType.Double, "  " + OperatorType.NumericEquals + "  ", "42.0");
            var exportedItems = new List<LogRecord>();
            var logger = LoggerTestUtils.CreateLoggerWithConfig(config, exportedItems, LoggerName);

            GenerateLog(logger, "TestField", 42.0);

            Assert.Single(exportedItems);
        }

        [Fact]
        public void TypeWithWhitespace_ShouldMatchAndSample()
        {
            var config = ReplaceConfigPlaceholders("TestField", "  " + ConstraintType.Double + "  ", OperatorType.NumericEquals, "42.0");
            var exportedItems = new List<LogRecord>();
            var logger = LoggerTestUtils.CreateLoggerWithConfig(config, exportedItems, LoggerName);

            GenerateLog(logger, "TestField", 42.0);

            Assert.Single(exportedItems);
        }

        [Fact]
        public void ScientificNotation_ShouldMatchAndSample()
        {
            var config = ReplaceConfigPlaceholders("TestField", ConstraintType.Double, OperatorType.NumericEquals, "4.2e1");
            var exportedItems = new List<LogRecord>();
            var logger = LoggerTestUtils.CreateLoggerWithConfig(config, exportedItems, LoggerName);

            GenerateLog(logger, "TestField", 42.0);

            Assert.Single(exportedItems);
        }

        [Fact]
        public void DecimalPrecision_ShouldMatchAndSample()
        {
            var config = ReplaceConfigPlaceholders("TestField", ConstraintType.Double, OperatorType.NumericEquals, "42.00000000000001");
            var exportedItems = new List<LogRecord>();
            var logger = LoggerTestUtils.CreateLoggerWithConfig(config, exportedItems, LoggerName);

            GenerateLog(logger, "TestField", 42.00000000000001);

            Assert.Single(exportedItems);
        }

        [Fact]
        public void NumericEquals_WithNaN_ShouldNotMatch()
        {
            var config = ReplaceConfigPlaceholders("TestField", ConstraintType.Double, OperatorType.NumericEquals, "NaN");
            var exportedItems = new List<LogRecord>();
            var logger = LoggerTestUtils.CreateLoggerWithConfig(config, exportedItems, LoggerName);

            GenerateLog(logger, "TestField", double.NaN);

            Assert.Empty(exportedItems);
        }

        [Fact]
        public void NumericEquals_WithLargeNumber_ShouldMatchAndSample()
        {
            var config = ReplaceConfigPlaceholders("TestField", ConstraintType.Double, OperatorType.NumericEquals, double.MaxValue.ToString("R", CultureInfo.InvariantCulture));
            var exportedItems = new List<LogRecord>();
            var logger = LoggerTestUtils.CreateLoggerWithConfig(config, exportedItems, LoggerName);

            GenerateLog(logger, "TestField", double.MaxValue);

            Assert.Single(exportedItems);
        }

        [Fact]
        public void NumericEquals_WithInvalidFormat_ShouldNotMatch()
        {
            var config = ReplaceConfigPlaceholders("TestField", ConstraintType.Double, OperatorType.NumericEquals, "invalid");
            var exportedItems = new List<LogRecord>();
            var logger = LoggerTestUtils.CreateLoggerWithConfig(config, exportedItems, LoggerName);

            GenerateLog(logger, "TestField", 42.0);

            Assert.Empty(exportedItems);
        }
    }
} 