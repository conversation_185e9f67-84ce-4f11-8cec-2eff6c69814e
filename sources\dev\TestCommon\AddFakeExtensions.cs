﻿// <copyright file="AddFakeExtensions.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.IO;
using System.Reflection;
using System.Text;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Logging.Testing;
using Microsoft.M365.Core.Telemetry.ECSClient;
using Microsoft.M365.Core.Telemetry.R9;
using Microsoft.R9.Extensions.Logging;
using Microsoft.R9.Extensions.Logging.Exporters;
using Microsoft.R9.Extensions.Metering;
using NSubstitute;

namespace Microsoft.M365.Core.Telemetry.TestCommon
{
    public static class AddFakeExtensions
    {
        public static void EnableR9(List<string> logRecords = null, IBlockList blockList = null)
        {
            var sc = ConfigureServicesForR9Test(null, logRecords, blockList);
            R9Services.InitR9Services(sc, true);
        }

        public static IServiceCollection ConfigureServicesForR9Test(ITelemetryEmitConfig telemetryEmitConfig, List<string> logRecords = null, IBlockList blockList = null, IPassiveR9Config passiveConfig = null)
        {
            blockList ??= Substitute.For<IBlockList>();

            if (passiveConfig == null)
            {
                passiveConfig = Substitute.For<IPassiveR9Config>();
                passiveConfig.R9MetricEnabled.Returns(true);
                passiveConfig.R9EventEnabled.Returns(true);
                passiveConfig.IfxEventEnabled.Returns(true);
                passiveConfig.IfxMetricEnabled.Returns(true);
            }

            if (telemetryEmitConfig == null)
            {
                telemetryEmitConfig = Substitute.For<ITelemetryEmitConfig>();
                telemetryEmitConfig.QueryEventConfig<bool>(Arg.Any<string>(), Arg.Any<string>()).ReturnsForAnyArgs(true);
                telemetryEmitConfig.QueryMetricConfig<bool>(Arg.Any<string>(), Arg.Any<string>()).ReturnsForAnyArgs(true);
            }

            var serviceCollection = new ServiceCollection();

            serviceCollection.AddSingleton<IPassiveR9Config>(passiveConfig)
                .AddSingleton<IBlockList>(blockList)
                .AddSingleton<ITelemetryEmitConfig>(telemetryEmitConfig);
            serviceCollection.AddFakeMetering();
            if (logRecords != null)
            {
                FakeLogCollector fc = new FakeLogCollector(
                    Microsoft.Extensions.Options.Options.Create(
                        new FakeLogCollectorOptions
                        {
                            OutputFormatter = FormatterEvent,
                            OutputSink = record =>
                            {
                                logRecords.Add(record);
                            }
                        }));
                serviceCollection.AddSingleton<ILoggerFactory>(new LoggerFactory(new[] { new FakeLoggerProvider(fc) }));
            }

            string defaultLibrary = "{\"Logging\": {\"GenevaLogging\": {\"ConnectionString\": \"EtwSession=o365PassiveMonitoringSessionR9\",\"TableNameMappings\": {\"Microsoft.M365.Core.Telemetry.R9Test.R9SDKTestEvent\": \"R9SDKTestEvent\"}}}, \"Metering\": {\"GenevaMetering\": {\"Protocol\": \"Etw\",\"MonitoringAccount\": \"O365_Monitoring_Pop\",\"MonitoringNamespace\": \"R9EventCounter\",\"MonitoringNamespaceOverrides\": {\"Microsoft.M365.Core.Telemetry.R9Test.FakeMetricSDK\": \"FakeMetricSDK\"}}}}";
            IConfigurationRoot tmpOption = new ConfigurationBuilder().AddJsonStream(new MemoryStream(Encoding.ASCII.GetBytes(defaultLibrary))).Build();
            var bindingFlags = BindingFlags.Public | BindingFlags.NonPublic | BindingFlags.Instance;
            typeof(LibraryManagedServiceCollection).GetField("serviceProvider", bindingFlags).SetValue(LibraryManagedServiceCollection.Instance, serviceCollection.BuildServiceProvider());
            typeof(LibraryManagedServiceCollection).GetField("r9Option", bindingFlags).SetValue(LibraryManagedServiceCollection.Instance, tmpOption);

            return serviceCollection;
        }

        public static IServiceCollection ConfigureServicesForR9Test(List<string> logRecords = null, IBlockList blockList = null, IPassiveR9Config passiveConfig = null)
        {
            blockList ??= Substitute.For<IBlockList>();

            if (passiveConfig == null)
            {
                passiveConfig = Substitute.For<IPassiveR9Config>();
                passiveConfig.R9MetricEnabled.Returns(true);
                passiveConfig.R9EventEnabled.Returns(true);
                passiveConfig.IfxEventEnabled.Returns(true);
                passiveConfig.IfxMetricEnabled.Returns(true);
            }

            var serviceCollection = new ServiceCollection();

            serviceCollection.AddSingleton<IPassiveR9Config>(passiveConfig)
                .AddSingleton<IBlockList>(blockList);
            serviceCollection.AddFakeMetering();
            if (logRecords != null)
            {
                FakeLogCollector fc = new FakeLogCollector(
                    Microsoft.Extensions.Options.Options.Create(
                        new FakeLogCollectorOptions
                        {
                            OutputFormatter = FormatterEvent,
                            OutputSink = record =>
                            {
                                logRecords.Add(record);
                            }
                        }));
                serviceCollection.AddSingleton<ILoggerFactory>(new LoggerFactory(new[] { new FakeLoggerProvider(fc) }));
            }

            string defaultLibrary = "{\"Logging\": {\"GenevaLogging\": {\"ConnectionString\": \"EtwSession=o365PassiveMonitoringSessionR9\",\"TableNameMappings\": {\"Microsoft.M365.Core.Telemetry.R9Test.R9SDKTestEvent\": \"R9SDKTestEvent\"}}}, \"Metering\": {\"GenevaMetering\": {\"Protocol\": \"Etw\",\"MonitoringAccount\": \"O365_Monitoring_Pop\",\"MonitoringNamespace\": \"R9EventCounter\",\"MonitoringNamespaceOverrides\": {\"Microsoft.M365.Core.Telemetry.R9Test.FakeMetricSDK\": \"FakeMetricSDK\"}}}}";
            IConfigurationRoot tmpOption = new ConfigurationBuilder().AddJsonStream(new MemoryStream(Encoding.ASCII.GetBytes(defaultLibrary))).Build();
            var bindingFlags = BindingFlags.Public | BindingFlags.NonPublic | BindingFlags.Instance;
            typeof(LibraryManagedServiceCollection).GetField("serviceProvider", bindingFlags).SetValue(LibraryManagedServiceCollection.Instance, serviceCollection.BuildServiceProvider());
            typeof(LibraryManagedServiceCollection).GetField("r9Option", bindingFlags).SetValue(LibraryManagedServiceCollection.Instance, tmpOption);

            return serviceCollection;
        }

        /// <summary>
        /// InjectV2ServiceProvider
        /// </summary>
        /// <param name="option"></param>
        public static void InjectV2ServiceProvider(IConfigurationRoot option)
        {
            IServiceProvider tmp = new HostBuilder()
                .ConfigureLogging(builder =>
                {
                    _ = builder
                        .AddOpenTelemetryLogging()
                        .AddGenevaExporter(option.GetSection("GenevaLogging"));
                })
                .ConfigureServices(services =>
                {
                    _ = services
                        .AddGenevaMetering(option.GetSection("GenevaMetering"));
                    Enrichment.CommonInit.InitSubstrateEnrichers(services);
                })
                .Build().Services;
            ECSManagedServiceCollection.Instance.SetService(tmp);
        }

        private static string FormatterEvent(FakeLogRecord record)
        {
            string formatStr = string.Empty;
            bool first = true;
            foreach (var str in record.StructuredState)
            {
                if (!first)
                {
                    formatStr += ", ";
                }

                formatStr += $"\"{str}\"";
                first = false;
            }
            return formatStr;
        }
    }
}
