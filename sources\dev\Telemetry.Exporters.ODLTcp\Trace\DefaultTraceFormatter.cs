﻿// <copyright file="DefaultTraceFormatter.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System.Diagnostics;
using System.Diagnostics.CodeAnalysis;
using Newtonsoft.Json;

namespace Microsoft.M365.Core.Telemetry.Exporters.ODLTCP.Trace
{
    /// <summary>
    /// The default formatter for <see cref="Activity"/>, this is for turning security record into strings 
    /// before packaging them into NRTRequest
    /// </summary>
    public static class DefaultTraceFormatter
    {
        /// <summary>
        /// version for trace exporter to serialize activity.
        /// </summary>
        public const string ActivitySchemaVersion = "2.0";

        /// <summary>
        /// serializes an <see cref="Activity"/> to json string
        /// </summary>
        /// <param name="activity"></param>
        /// <param name="fields"></param>
        /// <returns></returns>
        public static string Serialize(Activity activity, IReadOnlyDictionary<string, string> fields)
        {
            using (StringWriter sw = new ())
            {
                JsonTextWriter writer = new JsonTextWriter(sw);
                writer.WriteStartObject();

                //write schema version
                writer.WritePropertyName("schemaVersion");
                writer.WriteValue(ActivitySchemaVersion);

                // Write traceId
                writer.WritePropertyName("traceId");
                writer.WriteValue(activity.Context.TraceId.ToHexString());

                // Write spanId
                writer.WritePropertyName("spanId");
                writer.WriteValue(activity.Context.SpanId.ToHexString());

                //Write kind
                writer.WritePropertyName("kind");
                writer.WriteValue(activity.Kind.ToString());

                //Write status
                writer.WritePropertyName("status");
                writer.WriteValue(activity.Status.ToString());

                //Write operationName
                writer.WritePropertyName("operationName");
                writer.WriteValue(activity.OperationName.ToString());

                //Write displayName
                writer.WritePropertyName("displayName");
                writer.WriteValue(activity.DisplayName.ToString());

                //Write source
                writer.WritePropertyName("source");
                SerializeSource(activity.Source, writer);

                //Write duration
                writer.WritePropertyName("duration");
                writer.WriteValue(activity.Duration.ToString());

                //Write startTimeUtc
                writer.WritePropertyName("startTimeUtc");
                writer.WriteValue(activity.StartTimeUtc.ToString("o"));

                ////Write tags
                writer.WritePropertyName("tags");
                SerializeTags(activity.TagObjects, fields, writer);

                //TODO: enable this when links being used.
                //(disable now to minimize the size and improve the performance)
                //Write links
                //writer.WritePropertyName("links");
                //SerializeLinks(activity.Links, writer);

                //Write events
                writer.WritePropertyName("events");
                SerializeEvents(activity.Events, writer);

                //Write parentId
                writer.WritePropertyName("parentId");
                writer.WriteValue(activity.ParentSpanId == default ? String.Empty : activity.ParentSpanId.ToHexString());

                writer.WriteEndObject();
                return sw.ToString();
            }
        }

        private static void SerializeSource(ActivitySource source, JsonTextWriter writer)
        {
            writer.WriteStartObject();
            writer.WritePropertyName("Name");
            writer.WriteValue(source.Name);
            writer.WritePropertyName("Version");
            writer.WriteValue(source.Version);
            writer.WriteEndObject();
        }

        private static void SerializeTags(IEnumerable<KeyValuePair<string, object?>> tags, IReadOnlyDictionary<string, string> fields, JsonTextWriter writer)
        {
            writer.WriteStartObject();
            foreach (var tag in tags)
            {
                writer.WritePropertyName(tag.Key);
                switch (tag.Value)
                {
                    case string str:
                        writer.WriteValue(str);
                        break;
                    case int i:
                        writer.WriteValue(i);
                        break;
                    case long l:
                        writer.WriteValue(l);
                        break;
                    case double d:
                        writer.WriteValue(d);
                        break;
                    case bool b:
                        writer.WriteValue(b);
                        break;
                    default:
                        writer.WriteValue("unsupported type");
                        break;
                }
            }
            foreach (var field in fields)
            {
                writer.WritePropertyName(field.Key);
                writer.WriteValue(field.Value);
            }
            writer.WriteEndObject();
        }

        [ExcludeFromCodeCoverage]
        private static void SerializeLinks(IEnumerable<ActivityLink> links, JsonTextWriter writer)
        {
            writer.WriteStartArray();

            foreach (var link in links)
            {
                writer.WriteStartObject();
                writer.WritePropertyName("Context");
                writer.WriteStartObject();
                writer.WritePropertyName("TraceId");
                writer.WriteValue(link.Context.TraceId.ToHexString());
                writer.WritePropertyName("SpanId");
                writer.WriteValue(link.Context.SpanId.ToHexString());
                writer.WritePropertyName("TraceFlags");
                writer.WriteValue(link.Context.TraceFlags);
                writer.WritePropertyName("TraceState");
                writer.WriteValue(link.Context.TraceState);
                writer.WritePropertyName("IsRemote");
                writer.WriteValue(link.Context.IsRemote);
                writer.WriteEndObject();

                writer.WritePropertyName("Tags");
                if (link.Tags != null)
                {
                    SerializeTags(link.Tags, new Dictionary<string, string>(), writer);
                }
                else
                {
                    writer.WriteNull();
                }
                writer.WriteEndObject();
            }
            writer.WriteEndArray();
        }

        private static void SerializeEvents(IEnumerable<ActivityEvent> events, JsonTextWriter writer)
        {
            writer.WriteStartArray();

            foreach (var ev in events)
            {
                writer.WriteStartObject();
                writer.WritePropertyName("Name");
                writer.WriteValue(ev.Name);
                writer.WritePropertyName("Timestamp");
                writer.WriteValue(ev.Timestamp.ToString("o"));

                writer.WritePropertyName("Tags");
                SerializeTags(ev.Tags, new Dictionary<string, string>(), writer);

                writer.WriteEndObject();
            }
            writer.WriteEndArray();
        }
    }
}
