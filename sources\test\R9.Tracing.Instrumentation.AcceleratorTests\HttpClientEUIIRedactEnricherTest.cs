﻿// <copyright file="HttpClientEUIIRedactEnricherTest.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>
#if !NETFRAMEWORK
using System;
using System.Diagnostics;
using System.Net.Http;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.R9.Extensions.HttpClient.Tracing;
using Xunit;

namespace Microsoft.M365.Core.Telemetry.R9.Tracing.Instrumentation.Accelerator.Tests
{
    /// <summary>
    /// HttpClientEUIIRedactEnricherTest
    /// </summary>
    public class HttpClientEUIIRedactEnricherTest
    {
        private IHttpClientTraceEnricher enricher;

        /// <summary>
        /// HttpClientEUIIRedactEnricherTest
        /// </summary>
        public HttpClientEUIIRedactEnricherTest()
        {
            var container = new ServiceCollection();
            var options = new HttpClientTracingOptionsInherited
            {
                IsEnabled = true,
                RedactionStrategyType = RedactionStrategyType.Default
            };

            container.AddSingleton(options);
            container.AddSingleton<IHttpClientTraceEnricher, HttpClientEUIIRedactEnricher>();
            var provider = container.BuildServiceProvider();
            this.enricher = provider.GetService<IHttpClientTraceEnricher>();
        }

        /// <summary>
        /// EnrichFailWithNullInput
        /// </summary>
        [Fact]
        public void EnrichFailWithNullInput()
        {
            Assert.NotNull(this.enricher);

            var activity = new Activity("test");
            enricher.Enrich(activity, null, null);

            Assert.Null(activity.GetTagItem(Constants.HttpUrlBackup));
        }

        /// <summary>
        /// EnrichSucceed
        /// </summary>
        [Fact]
        public void EnrichSucceed()
        {
            var activity = new Activity("test");
            var httpRequest = new HttpRequestMessage();
            httpRequest.RequestUri = new Uri("https://localhost:8080/api/values?name=foo");
            enricher.Enrich(activity, httpRequest, null);

            Assert.Equal("https://localhost:8080/" + Constants.RedactedPlacholder, activity.GetTagItem(Constants.HttpUrlBackup));
        }

        /// <summary>
        /// EnrichSucceedWithNoPath
        /// </summary>
        [Fact]
        public void EnrichSucceedWithNoPath()
        {
            var activity = new Activity("test");
            var httpRequest = new HttpRequestMessage();
            httpRequest.RequestUri = new Uri("https://localhost:8080");
            enricher.Enrich(activity, httpRequest, null);

            Assert.Equal("https://localhost:8080/" + Constants.RedactedPlacholder, activity.GetTagItem(Constants.HttpUrlBackup));

            enricher.Enrich(null, httpRequest, null);
        }
    }
}
#endif