﻿// <copyright file="CustomGenevaTraceExporterOptionsValidator.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System;
using System.Diagnostics.CodeAnalysis;
using System.Runtime.InteropServices;
using System.Text.RegularExpressions;
using Microsoft.Extensions.Options;
using Microsoft.R9.Extensions.Tracing.Exporters;

namespace Microsoft.R9.Extensions.Telemetry.Exporter.Filters;

internal sealed class CustomGenevaTraceExporterOptionsValidator : IValidateOptions<GenevaTraceExporterOptions>
{
    // This path can't be covered in UTs running on Windows because the batch export
    // processor is only supported on Linux platform
    [ExcludeFromCodeCoverage]
    public ValidateOptionsResult Validate(string? name, GenevaTraceExporterOptions options) =>
        Validate(options, RuntimeInformation.IsOSPlatform(OSPlatform.Windows));

    internal static ValidateOptionsResult Validate(GenevaTraceExporterOptions options, bool isWindows)
    {
        var builder = new ValidateOptionsResultBuilder();

        if (options.ConnectionString is null)
        {
            builder.AddError(nameof(options.ConnectionString), $"is not configured");
        }
        else
        {
            if (isWindows && !Regex.IsMatch(options.ConnectionString, @"EtwSession=\w+"))
            {
                builder.AddError(
                    nameof(options.ConnectionString),
                    $"is invalid on Windows, should be: 'EtwSession=MYOPENTELEMETRYETWSESSION' with the ETW session name to match the OneDSProvider name in the Geneva agent's configuration");
            }

            if (!isWindows
                && !options.ConnectionString.StartsWith("Endpoint=unix:", StringComparison.Ordinal))
            {
                builder.AddError(
                    nameof(options.ConnectionString),
                    $"is invalid on Unix, should be: 'Endpoint=unix:<unix-domain-socket-path>'");
            }
        }

        if (options.MaxExportBatchSize > options.MaxQueueSize
            || options.MaxExportBatchSize < 1)
        {
            builder.AddError(nameof(options.MaxExportBatchSize), $"must be greater than 0 and less than the MaxQueueSize value '{options.MaxQueueSize}'");
        }

        return builder.Build();
    }
}
