﻿// <copyright file="QueryKustoTableSkill.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.Data;
using System.Diagnostics.CodeAnalysis;
using System.Linq;
using System.Reflection.PortableExecutable;
using System.Text;
using System.Threading.Tasks;
using Kusto.Data;
using Kusto.Data.Common;
using Kusto.Data.Net.Client;
using R9MigrationHelper.Model;
using static Microsoft.TeamFoundation.WorkItemTracking.WebApi.WitConstants;

namespace R9MigrationHelper.Skills.KustoQuerySkills
{
    /// <summary>
    /// QueryKustoTableSkill.
    /// </summary>
    [ExcludeFromCodeCoverage]
    public class QueryKustoTableSkill
    {
        private string kustoCluster = "https://resourcemanagement.westus2.kusto.windows.net";
        private string databaseName = "adhoc";
        private string connectionString = string.Empty;

        /// <summary>
        /// QueryKustoTableSkill.
        /// </summary>
        /// <param name="connectionString">connection String.</param>
        /// <param name="databaseName">database Name.</param>
        public QueryKustoTableSkill(string connectionString, string databaseName)
        {
            this.connectionString = connectionString;
            this.databaseName = databaseName;
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="QueryKustoTableSkill"/> class.
        /// </summary>
        public QueryKustoTableSkill()
        {
            string tenantId = "cdc5aeea-15c5-4db6-b079-fcadd2505dc2";
            string appId = "bad6c3f0-aa3c-4464-93e6-1560e8b43d4e";
            string appKey = UserInput.AppKey;

            this.connectionString = new KustoConnectionStringBuilder(kustoCluster, databaseName)
                .WithAadApplicationKeyAuthentication(appId, appKey, tenantId)
                .ConnectionString;
        }

        /// <summary>
        /// GetKustoDataAsync.
        /// </summary>
        /// <param name="query">query.</param>
        public async Task<IDataReader> GetKustoDataAsync(string query)
        {
            return await this.GetKustoDataAsync(query, new Dictionary<string, string>()).ConfigureAwait(false);
        }

        /// <summary>
        /// GetKustoDataAsync.
        /// </summary>
        /// <param name="query">query.</param>
        /// <param name="queryParameters">query Parameters.</param>
        public async Task<IDataReader> GetKustoDataAsync(string query, Dictionary<string, string> queryParameters)
        {
            if (string.IsNullOrEmpty(query))
            {
                throw new ArgumentNullException(query);
            }
            IDataReader reader = await this.ExecuteQueryAsync(query, queryParameters).ConfigureAwait(false);
            return reader;
        }

        private async Task<IDataReader> ExecuteQueryAsync(string query, Dictionary<string, string> queryParameters)
        {
            using (ICslQueryProvider queryProvider = KustoClientFactory.CreateCslQueryProvider(this.connectionString))
            {
                return await queryProvider.ExecuteQueryAsync(this.databaseName, query, this.CreateClientRequestProperties(queryParameters)).ConfigureAwait(false);
            }
        }

        private ClientRequestProperties CreateClientRequestProperties(IEnumerable<KeyValuePair<string, string>> queryParameters)
        {
            var clientRequestProperties = new ClientRequestProperties(null, queryParameters)
            {
                ClientRequestId = Guid.NewGuid().ToString()
            };

            clientRequestProperties.SetOption(ClientRequestProperties.OptionTruncationMaxSize, int.MaxValue);
            clientRequestProperties.SetOption(ClientRequestProperties.OptionTruncationMaxRecords, int.MaxValue);

            return clientRequestProperties;
        }
    }
}
