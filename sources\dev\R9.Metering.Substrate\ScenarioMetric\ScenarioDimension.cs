﻿// <copyright file="ScenarioDimension.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

namespace Microsoft.M365.Core.Telemetry.R9.Metering.Substrate
{
    /// <summary>
    /// Represents a scenario in the metering substrate.
    /// </summary>
    public class ScenarioDimension
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="ScenarioDimension"/> class.
        /// </summary>
        /// <param name="component">The component of the scenario.</param>
        /// <param name="scenario">The scenario name.</param>
        /// <param name="subScenario">The sub-scenario name.</param>
        public ScenarioDimension(string component, string scenario, string subScenario)
        {
            this.Component = component;
            this.Scenario = scenario;
            this.SubScenario = subScenario;
        }

        /// <summary>
        /// Gets or sets the component of the scenario.
        /// </summary>
        public string Component { get; }

        /// <summary>
        /// Gets or sets the scenario name.
        /// </summary>
        public string Scenario { get; }

        /// <summary>
        /// Gets or sets the sub-scenario name.
        /// </summary>
        public string SubScenario { get; }
    }
}
