﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{da9b5cf1-76a9-4436-bf4f-a09fe947fdcd}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <WindowsTargetPlatformVersion>10.0</WindowsTargetPlatformVersion>
    <ConfigurationType>Application</ConfigurationType>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
    <ResolveNuGetPackages>false</ResolveNuGetPackages>
    <CopyLocalProjectReference>true</CopyLocalProjectReference>
    <CopyLocalDebugSymbols>true</CopyLocalDebugSymbols>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <UseDebugLibraries>true</UseDebugLibraries>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <UseDebugLibraries>false</UseDebugLibraries>
    <WholeProgramOptimization>true</WholeProgramOptimization>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings" />
  <ImportGroup Label="Shared" />
  <ImportGroup Label="PropertySheets" />
  <PropertyGroup Label="UserMacros" />
  <ItemDefinitionGroup />
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <Optimization>Disabled</Optimization>
      <PreprocessorDefinitions>_ITERATOR_DEBUG_LEVEL=2;X64;_DEBUG;_CONSOLE;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <WarningLevel>Level3</WarningLevel>
      <AdditionalIncludeDirectories>
        $(DistribRoot)\$(Configuration)\$(Platform)\ODLNRTMessageLib;
        $(EnlistmentRoot)\sources\cpp\dev\OdlTraceExporterCpp;
        $(EnlistmentRoot)\sources\cpp\dev\AsioUtils;
        %(AdditionalIncludeDirectories)
      </AdditionalIncludeDirectories>
      <!--Shouldn't be necessary. Figure out why.-->
      <DisableSpecificWarnings>4018;%(DisableSpecificWarnings)</DisableSpecificWarnings>
    </ClCompile>
    <Link>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <SubSystem>Console</SubSystem>
      <AdditionalDependencies>
        Ole32.lib;
        gtest_main.lib;
        $(VcpkgDebugLibDir)\*.lib;
      </AdditionalDependencies>
      <AdditionalLibraryDirectories>
        $(DistribRoot)\$(Configuration)\$(Platform)\ODLNRTMessageLib;
        $(DistribRoot)\$(Configuration)\$(Platform)\OdlTraceExporterCpp;
        %(AdditionalLibraryDirectories)
      </AdditionalLibraryDirectories>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <PreprocessorDefinitions>_ITERATOR_DEBUG_LEVEL=0;X64;NDEBUG;_CONSOLE;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <WarningLevel>Level3</WarningLevel>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <AdditionalIncludeDirectories>
        $(DistribRoot)\$(Configuration)\$(Platform)\ODLNRTMessageLib;
        $(EnlistmentRoot)\sources\cpp\dev\OdlTraceExporterCpp;
        $(EnlistmentRoot)\sources\cpp\dev\AsioUtils;
        %(AdditionalIncludeDirectories)
      </AdditionalIncludeDirectories>
    </ClCompile>
    <Link>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <SubSystem>Console</SubSystem>
      <OptimizeReferences>true</OptimizeReferences>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <AdditionalDependencies>
        Ole32.lib;
        gtest_main.lib;
        $(VcpkgReleaseLibDir)\*.lib;
      </AdditionalDependencies>
      <AdditionalLibraryDirectories>
        $(DistribRoot)\$(Configuration)\$(Platform)\ODLNRTMessageLib;
        $(DistribRoot)\$(Configuration)\$(Platform)\OdlTraceExporterCpp;
        %(AdditionalLibraryDirectories)
      </AdditionalLibraryDirectories>
    </Link>
  </ItemDefinitionGroup>
  <ItemGroup>
    <ClCompile Include="OdlTraceExporterRecordableTest.cpp" />
    <ClCompile Include="OdlTraceExporterTest.cpp" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="$(EnlistmentRoot)\sources\cpp\dev\OdlTraceExporterCpp\OdlTraceExporterCpp.vcxproj">
      <Project>{dfa75dff-f6ea-45e8-90ce-7858ef65f042}</Project>
    </ProjectReference>
    <ProjectReference Include="$(EnlistmentRoot)\sources\cpp\dev\ODLNRTMessageLib\ODLNRTMessageLib.vcxproj">
      <Project>{d7d0b355-fe24-4880-873b-dbc953bda103}</Project>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <None Include="packages.config" />
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
</Project>