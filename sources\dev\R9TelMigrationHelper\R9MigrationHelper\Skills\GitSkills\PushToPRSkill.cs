﻿// <copyright file="PushToPRSkill.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Globalization;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Web;
using Microsoft.Azure.Pipelines.WebApi;
using Microsoft.Identity.Client;
using Microsoft.TeamFoundation.Build.WebApi;
using Microsoft.TeamFoundation.SourceControl.WebApi;
using Microsoft.VisualStudio.Services.Client;
using Microsoft.VisualStudio.Services.Common;
using Microsoft.VisualStudio.Services.Organization.Client;
using Microsoft.VisualStudio.Services.WebApi;
using R9MigrationHelper.Model;

namespace R9MigrationHelper.Skills.GitSkills
{
    /// <summary>
    /// PushToPRSkill.
    /// </summary>
    public class PushToPRSkill
    {
        /// <summary>
        /// Push commits to existing PR
        /// </summary>
        /// <param name="organization">SK Context.</param>
        /// <param name="project">SK Context.</param>
        /// <param name="repository">SK Context.</param>
        /// <param name="changeList">SK Context.</param>
        /// <param name="comments">SK Context.</param>
        /// <param name="pullRequestUrl">SK Context.</param>
        /// <returns>A <see cref="Task{TResult}"/> representing the result of the asynchronous operation.</returns>
        public async Task PushToPRAsync(string organization, string project, string repository, List<FileChange> changeList, string comments, string pullRequestUrl)
        {
            if (changeList.Count == 0)
            {
                throw new ArgumentException("updatedContent cannot be empty!");
            }

            string organizationUrl = $"https://dev.azure.com/{HttpUtility.UrlEncode(organization)}";

            string[] scopes = new string[] { "499b84ac-1321-427f-aa17-267ca6975798/user_impersonation" }; //Constant value to target Azure DevOps. Do not change  

            // Initialize the MSAL library by building a public client application
            IPublicClientApplication application = PublicClientApplicationBuilder.Create("872cd9fa-d31f-45e0-9eab-6e460a02d1f1")
                                       .WithAuthority("https://login.microsoftonline.com/72f988bf-86f1-41af-91ab-2d7cd011db47/v2.0")
                                       .WithDefaultRedirectUri()
                                       .Build();
            AuthenticationResult authResult;

            try
            {
                var accounts = await application.GetAccountsAsync().ConfigureAwait(false);
                authResult = await application.AcquireTokenSilent(scopes, accounts.FirstOrDefault())
                        .ExecuteAsync().ConfigureAwait(false);
            }
            catch (MsalUiRequiredException)
            {
                authResult = await application.AcquireTokenByIntegratedWindowsAuth(scopes).ExecuteAsync().ConfigureAwait(false);
            }

            Console.WriteLine($"organization: {organization}, project: {project}, repository: {repository}, pat:******, organizationUrl: {organizationUrl}.");

            VssCredentials creds = new VssAadCredential(new VssAadToken("Bearer", authResult.AccessToken));
            VssConnection connection = new VssConnection(new Uri(organizationUrl), creds);
            GitHttpClient gitClient = connection.GetClient<GitHttpClient>();
            GitRepository repo = await gitClient.GetRepositoryAsync(project, repository).ConfigureAwait(false);

            Guid repoId = repo.Id;
            Guid projectId = repo.ProjectReference.Id;

            var allRefs = await gitClient.GetRefsAsync(repoId).ConfigureAwait(false);
            var targetRef = allRefs.First(x => x.Name == repo.DefaultBranch);

            var pr = await gitClient.GetPullRequestAsync(repoId, int.Parse(pullRequestUrl.Split('/').Last(), CultureInfo.InvariantCulture)).ConfigureAwait(true);
            var upstreamBranchName = pr.SourceRefName;

            if (!upstreamBranchName.StartsWith(@"refs/heads/", StringComparison.InvariantCulture))
            {
                upstreamBranchName = @"refs/heads/" + upstreamBranchName.Trim('/');
            }

            foreach (var r in allRefs)
            {
                if (r.Name == upstreamBranchName)
                {
                    targetRef = r;
                    Console.WriteLine($"Found branch {upstreamBranchName}\n");
                    break;
                }
            }

            var updatedRefs = await gitClient.UpdateRefsAsync(
                 new[]
                 {
                    new GitRefUpdate
                    {
                        Name = upstreamBranchName,
                        NewObjectId = targetRef.ObjectId,
                        OldObjectId = new string('0', 40)
                    }
                 }, repoId).ConfigureAwait(false);

            var newBranch = updatedRefs[0];

            List<GitChange> changes = new List<GitChange>();

            int charCount = 0;
            GitPush push;
            foreach (FileChange change in changeList)
            {
                if (charCount + change.UpdatedFileContent.Length < 2621440) // Limit of pr max bytes: 26214400 
                {
                    changes.Add(new GitChange
                    {
                        ChangeType = VersionControlChangeType.Edit,
                        Item = new GitItem
                        {
                            Path = change.FilePath,
                        },
                        NewContent = new ItemContent
                        {
                            Content = change.UpdatedFileContent,
                            ContentType = ItemContentType.RawText
                        }
                    });
                    charCount += change.UpdatedFileContent.Length;
                }
                else
                {
                    push = new GitPush
                    {
                        RefUpdates = new[]
                        {
                            new GitRefUpdate { Name = upstreamBranchName, OldObjectId = targetRef.ObjectId }
                        },
                        Commits = new[]
                        {
                            new GitCommitRef
                            {
                            Comment = comments,
                            Changes = changes
                            }
                        }
                    };
                    Console.WriteLine($"Pushing change to branch {upstreamBranchName}...");
                    await gitClient.CreatePushAsync(push, repoId).ConfigureAwait(false);
                    Console.WriteLine($"Successfully commit new change to {pullRequestUrl}");
                    charCount = 0;
                    changes.Clear();
                    changes.Add(new GitChange
                    {
                        ChangeType = VersionControlChangeType.Edit,
                        Item = new GitItem
                        {
                            Path = change.FilePath,
                        },
                        NewContent = new ItemContent
                        {
                            Content = change.UpdatedFileContent,
                            ContentType = ItemContentType.RawText
                        }
                    });
                    charCount += change.UpdatedFileContent.Length;
                }
            }
            push = new GitPush
            {
                RefUpdates = new[]
                {
                            new GitRefUpdate { Name = upstreamBranchName, OldObjectId = targetRef.ObjectId }
                },
                Commits = new[]
                {
                            new GitCommitRef
                            {
                            Comment = comments,
                            Changes = changes
                            }
                }
            };
            Console.WriteLine($"Pushing change to branch {upstreamBranchName}...");
            await gitClient.CreatePushAsync(push, repoId).ConfigureAwait(false);
            Console.WriteLine($"Successfully commit new change to {pullRequestUrl}");
        }

        /// <summary>
        /// Create Pull Request method.
        /// </summary>
        /// <param name="organization">SK Context.</param>
        /// <param name="project">SK Context.</param>
        /// <param name="repository">SK Context.</param>
        /// <param name="updatedContent">SK Context.</param>
        /// <param name="updateFilePath">SK Context.</param>
        /// <param name="comments">SK Context</param>
        /// <param name="pullRequestUrl">SK Context.</param>
        public async Task PushToPRAsync(string organization, string project, string repository, string updatedContent, string updateFilePath, string comments, string pullRequestUrl)
        {
            if (string.IsNullOrEmpty(updatedContent))
            {
                throw new ArgumentException("updatedContent cannot be empty!");
            }

            FileChange change = new FileChange(updatedContent, updateFilePath);
            await PushToPRAsync(organization, project, repository, new List<FileChange> { change }, comments, pullRequestUrl).ConfigureAwait(true);
        }

        /// <summary>
        ///
        /// </summary>
        /// <param name="organization"></param>
        /// <param name="project"></param>
        /// <param name="repository"></param>
        /// <param name="pullRequestUrl"></param>
        /// <returns>A <see cref="Task"/> representing the result of the asynchronous operation.</returns>
        public async Task PullMasterBranchAsync(string organization, string project, string repository, string pullRequestUrl)
        {
            string organizationUrl = $"https://dev.azure.com/{HttpUtility.UrlEncode(organization)}";

            string[] scopes = new string[] { "499b84ac-1321-427f-aa17-267ca6975798/user_impersonation" }; //Constant value to target Azure DevOps. Do not change  

            // Initialize the MSAL library by building a public client application
            IPublicClientApplication application = PublicClientApplicationBuilder.Create("872cd9fa-d31f-45e0-9eab-6e460a02d1f1")
                                       .WithAuthority("https://login.microsoftonline.com/72f988bf-86f1-41af-91ab-2d7cd011db47/v2.0")
                                       .WithDefaultRedirectUri()
                                       .Build();
            AuthenticationResult authResult;

            try
            {
                var accounts = await application.GetAccountsAsync().ConfigureAwait(false);
                authResult = await application.AcquireTokenSilent(scopes, accounts.FirstOrDefault())
                        .ExecuteAsync().ConfigureAwait(false);
            }
            catch (MsalUiRequiredException)
            {
                authResult = await application.AcquireTokenByIntegratedWindowsAuth(scopes).ExecuteAsync().ConfigureAwait(false);
            }

            Console.WriteLine($"organization: {organization}, project: {project}, repository: {repository}, pat:******, organizationUrl: {organizationUrl}.");

            VssCredentials creds = new VssAadCredential(new VssAadToken("Bearer", authResult.AccessToken));
            VssConnection connection = new VssConnection(new Uri(organizationUrl), creds);
            GitHttpClient gitClient = connection.GetClient<GitHttpClient>();
            GitRepository repo = await gitClient.GetRepositoryAsync(project, repository).ConfigureAwait(false);

            Guid repoId = repo.Id;
            Guid projectId = repo.ProjectReference.Id;

            var allRefs = await gitClient.GetRefsAsync(repoId).ConfigureAwait(false);
            var targetRef = allRefs.First(x => x.Name == repo.DefaultBranch);

            var pr = await gitClient.GetPullRequestAsync(repoId, int.Parse(pullRequestUrl.Split('/').Last(), CultureInfo.InvariantCulture)).ConfigureAwait(true);
            var upstreamBranchName = pr.SourceRefName;

            if (!upstreamBranchName.StartsWith(@"refs/heads/", StringComparison.InvariantCulture))
            {
                upstreamBranchName = @"refs/heads/" + upstreamBranchName.Trim('/');
            }

            foreach (var r in allRefs)
            {
                if (r.Name == upstreamBranchName)
                {
                    targetRef = r;
                    Console.WriteLine($"Found branch {upstreamBranchName}\n");
                    break;
                }
            }
        }
    }
}
