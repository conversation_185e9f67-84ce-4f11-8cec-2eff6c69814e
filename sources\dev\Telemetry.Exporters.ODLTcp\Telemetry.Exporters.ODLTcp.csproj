﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFrameworks>net472;net6.0;net8.0</TargetFrameworks>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <AssemblyName>Microsoft.M365.Core.Telemetry.Exporters.ODLTCP</AssemblyName>
    <RootNamespace>Microsoft.M365.Core.Telemetry.Exporters.ODLTCP</RootNamespace>
    <Description>ODLTcpNRT logging exporter.</Description>
    <Workstream>Telemetry</Workstream>
    <Category>Telemetry</Category>
    <UseR9Generators>true</UseR9Generators>
    <GeneratePackageOnBuild>true</GeneratePackageOnBuild>
    <GeneratePackageOnBuild>true</GeneratePackageOnBuild>
    <PackageVersion>$(R9TelemetryExtensionsCorePackageVersion)</PackageVersion>
    <PackageReleaseNotes>Refine tcp log exporter to support multi-schema requirement from Geneva schema4.0.</PackageReleaseNotes>
    <PlatformTarget>AnyCPU</PlatformTarget>
    <Nowarn>CA1305,CA1819,CA1801</Nowarn>
    <LangVersion>10</LangVersion>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.Extensions.Logging" />
    <PackageReference Include="Microsoft.R9.Extensions.Essentials" />
    <PackageReference Include="Microsoft.R9.Extensions.Logging" />
    <PackageReference Include="Microsoft.R9.Extensions.Tracing" />
    <PackageReference Include="Newtonsoft.Json" />
    <PackageReference Include="System.ServiceProcess.ServiceController" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\ODLNrtTcpClient\Microsoft.M365.ODL.NrtTcpClient\Microsoft.M365.ODL.NrtTcpClient.csproj" />
  </ItemGroup>

  <ItemGroup>
    <InternalsVisibleToTest Include="$(AssemblyName).Test" />
  </ItemGroup>

</Project>
