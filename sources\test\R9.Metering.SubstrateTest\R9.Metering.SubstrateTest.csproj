﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFrameworks>net8.0</TargetFrameworks>
    <Nullable>enable</Nullable>
    <TestProjectType>UnitTest</TestProjectType>
    
    <AssemblyName>Microsoft.M365.Core.Telemetry.R9.Metering.Substrate.Test</AssemblyName>
    <RootNamespace>Microsoft.M365.Core.Telemetry.R9.Metering.Substrate.Test</RootNamespace>
    <IsCodedUITest>False</IsCodedUITest>
    <LangVersion>Latest</LangVersion>
    <NoWarn>R9EXP0014</NoWarn>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.Extensions.Configuration.Json" />
    <PackageReference Include="Microsoft.Extensions.Hosting" />
    <PackageReference Include="Microsoft.NET.Test.Sdk" />
    <PackageReference Include="xunit" />
    <PackageReference Include="xunit.runner.visualstudio" />
    <PackageReference Include="OpenTelemetry.Exporter.Console" />
    <PackageReference Include="System.Text.Json" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\dev\R9.Metering.Substrate\R9.Metering.Substrate.csproj" />
  </ItemGroup>

</Project>
