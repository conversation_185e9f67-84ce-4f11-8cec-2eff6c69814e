﻿// <copyright file="B2PassiveMetricEnricherTest.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System;
using Microsoft.M365.Core.Telemetry.Enrichment;
using Microsoft.M365.Core.Telemetry.TestCommon;
using Xunit;
using Xunit.Abstractions;

namespace Microsoft.M365.Core.Telemetry.EnrichmentTest
{
    /// <summary>
    /// B2PassiveMetricEnricherTest
    /// </summary>
    [Collection("Do not parallel")]
    public class B2PassiveMetricEnricherTest : BaseTest
    {
        /// <summary>
        /// B2PassiveMetricEnricherTest
        /// </summary>
        /// <param name="output"></param>
        public B2PassiveMetricEnricherTest(ITestOutputHelper output) : base(output)
        {
        }

        /// <summary>
        /// Default
        /// </summary>
        [Fact]
        public void Default()
        {
            var enricher = new B2PassiveMetricEnricher();
            var enrichedProperties = new TestPropertyBag();

            enricher.Enrich(enrichedProperties);
            var enrichedState = enrichedProperties.Properties;

            Assert.Equal(FakeRegistryKey.Registries[B2PassiveEnricherDimensions.DeployRing], enrichedState[B2PassiveEnricherDimensions.DeployRing]);
            Assert.Equal(FakeRegistryKey.Registries[B2PassiveEnricherDimensions.Role], enrichedState[B2PassiveEnricherDimensions.Role]);
            Assert.Equal(FakeRegistryKey.Registries[B2PassiveEnricherDimensions.Forest], enrichedState[B2PassiveEnricherDimensions.Forest]);
            Assert.Equal(FakeRegistryKey.Registries[B2PassiveEnricherDimensions.Region], enrichedState[B2PassiveEnricherDimensions.Region]);
            Assert.Equal(FakeRegistryKey.Registries[DimensionValues.AvailabilityGroupRegistryName], enrichedState[B2PassiveEnricherDimensions.AvailabilityGroup]);
            Assert.Equal(Environment.MachineName, enrichedState[B2PassiveEnricherDimensions.Machine]);
            Assert.Equal(FakeRegistryKey.DefaultBuildVersion, enrichedState[B2PassiveEnricherDimensions.BuildVersion]);
            Assert.Equal(DimensionValues.MachineProvisioningStateLive, enrichedState[B2PassiveEnricherDimensions.MachineProvisioningState]);
            Assert.Equal(true, enrichedState[B2PassiveEnricherDimensions.IsR9]);
        }

        /// <summary>
        /// LiveUpdate
        /// </summary>
        // TODO: Make registry watcher testable.
        [Fact]
        public void LiveUpdate()
        {
            var enricher = new B2PassiveMetricEnricher();
            var enrichedProperties = new TestPropertyBag();

            enricher.Enrich(enrichedProperties);
            var enrichedState = enrichedProperties.Properties;

            // Hardcoded in FakeRegistry.cs.
            Assert.Equal("15.20.5678.000", enrichedState[B2PassiveEnricherDimensions.BuildVersion]);

            FakeRegistryKey.Registries[DimensionValues.ProductMajor] = 16;
            DimensionValues.InternalSetRegistryKey(TestRegistryKey);
            enrichedProperties = new TestPropertyBag();
            enricher.Enrich(enrichedProperties);
            enrichedState = enrichedProperties.Properties;

            Assert.Equal("16.20.5678.000", enrichedState[B2PassiveEnricherDimensions.BuildVersion]);
        }
    }
}
