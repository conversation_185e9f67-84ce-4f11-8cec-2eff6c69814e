# Step 1 - Set up Configuration
The first step to onboard R9 metering is the configuration. Configuration controls metric behavior. For example, it specifies which Geneva Account/Namespace the metric data should go to.

You may store them in a local file (like appsettings.json) or remote config service (e.g. ECS). A top-level section SubstrateMetering is introduced to store the metric settings. Here is an example of user config.

```json
"SubstrateMetering": {
  "R9Metering": {
	"MeterState": "Disabled",
	"MeterStateOverrides": {
	  "Microsoft.M365.Substrate.Sdk.DsApi": "Enabled"
	}
  },
  "GenevaExporter": {
	"MonitoringAccount": "O365DsApi",
	"MonitoringNamespace": "DsApiSdk"
  }
}
```

**R9Metering**:

R9Metering contains config of R9 MeteringOptions, see details here [OpenTelemetry based metering | R9 SDK](https://eng.ms/docs/experiences-devices/r9-sdk/docs/telemetry/metering/otel-based-metering)

**MeterState** option can be used to enable or disable metering. Usually, an application may include some libraries inside it. Both the application and the included libraries can generate metrics. When onboarding R9 metering for an application, we only care metrics generated by the application. So, we should disable metering generated in the included libraries. Therefore, we set MeterState to **Disabled** by default.

**MeterStateOverrides** option is used to override the default meter state. This can be used to selectively enable metering for specific meter names after MeterState is Disabled. The above configuration enable "Microsoft.M365.Substrate.Sdk.DsApi" meter only.

**GenevaExporter**:

GenevaExporter contains config of GenevaMetricExporterOptions, see details here [OpenTelemetry Geneva metric export | R9 SDK](https://eng.ms/docs/experiences-devices/r9-sdk/docs/telemetry/metering/otel-geneva-metric-export)

**MonitoringAccount**:
Account for Geneva MDM.

**MonitoringNamespace**:
Namespace for Geneva MDM

**Next Step**: [Initialize R9 Telemetry](./InitR9.md)