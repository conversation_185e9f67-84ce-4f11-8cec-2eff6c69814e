// <copyright file="LongConstraintTest.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.IO;
using System.Text;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.M365.Core.Telemetry.R9.Logging.Substrate.Sampler;
using OpenTelemetry.Logs;
using Xunit;

namespace Microsoft.M365.Core.Telemetry.R9.Logging.Substrate.Test.SamplingTest
{
    public class LongConstraintTest
    {
        private const string LoggerName = "LongConstraintTest";

        private const string LongConstraintsConfig = @"
{
    ""SubstrateLogging"": {
        ""RuleBasedSampler"": {
            ""LongConstraintTest"": [
                {
                    ""Constraints"": [
                        {
                            ""Field"": ""FieldPlaceholder"",
                            ""Type"": ""TypePlaceholder"",
                            ""Operator"": ""OperatorPlaceholder"",
                            ""Value"": ""ValuePlaceholder""
                        }
                    ],
                    ""Strategy"": {
                        ""Type"": ""Random"",
                        ""SampleRate"": 1.0
                    }
                },
                {
                    ""Constraints"": [],
                    ""Strategy"": {
                        ""Type"": ""Random"",
                        ""SampleRate"": 0.0
                    }
                }
            ]
        }
    }
}";

        private static string ReplaceConfigPlaceholders(string field, string type, string op, string value)
        {
            return LongConstraintsConfig
                .Replace("FieldPlaceholder", field)
                .Replace("TypePlaceholder", type)
                .Replace("OperatorPlaceholder", op)
                .Replace("ValuePlaceholder", value);
        }

        private static void GenerateLog(ILogger logger, string fieldName, long fieldValue)
        {
            logger.LogInformation($"Test message {{{fieldName}}}", fieldValue);
        }

        [Fact]
        public void NumericEquals_ShouldMatchAndSample()
        {
            var config = ReplaceConfigPlaceholders("TestField", ConstraintType.Long, OperatorType.NumericEquals, "42");
            var exportedItems = new List<LogRecord>();
            var logger = LoggerTestUtils.CreateLoggerWithConfig(config, exportedItems, LoggerName);

            GenerateLog(logger, "TestField", 42);

            Assert.Single(exportedItems);
        }

        [Fact]
        public void NumericNotEquals_ShouldMatchAndSample()
        {
            var config = ReplaceConfigPlaceholders("TestField", ConstraintType.Long, OperatorType.NumericNotEquals, "42");
            var exportedItems = new List<LogRecord>();
            var logger = LoggerTestUtils.CreateLoggerWithConfig(config, exportedItems, LoggerName);

            GenerateLog(logger, "TestField", 43);

            Assert.Single(exportedItems);
        }

        [Fact]
        public void GreaterThan_ShouldMatchAndSample()
        {
            var config = ReplaceConfigPlaceholders("TestField", ConstraintType.Long, OperatorType.GreaterThan, "42");
            var exportedItems = new List<LogRecord>();
            var logger = LoggerTestUtils.CreateLoggerWithConfig(config, exportedItems, LoggerName);

            GenerateLog(logger, "TestField", 43);

            Assert.Single(exportedItems);
        }

        [Fact]
        public void LessThan_ShouldMatchAndSample()
        {
            var config = ReplaceConfigPlaceholders("TestField", ConstraintType.Long, OperatorType.LessThan, "42");
            var exportedItems = new List<LogRecord>();
            var logger = LoggerTestUtils.CreateLoggerWithConfig(config, exportedItems, LoggerName);

            GenerateLog(logger, "TestField", 41);

            Assert.Single(exportedItems);
        }

        [Fact]
        public void GreaterThanOrEqual_ShouldMatchAndSample()
        {
            var config = ReplaceConfigPlaceholders("TestField", ConstraintType.Long, OperatorType.GreaterThanOrEqual, "42");
            var exportedItems = new List<LogRecord>();
            var logger = LoggerTestUtils.CreateLoggerWithConfig(config, exportedItems, LoggerName);

            GenerateLog(logger, "TestField", 42);

            Assert.Single(exportedItems);
        }

        [Fact]
        public void LessThanOrEqual_ShouldMatchAndSample()
        {
            var config = ReplaceConfigPlaceholders("TestField", ConstraintType.Long, OperatorType.LessThanOrEqual, "42");
            var exportedItems = new List<LogRecord>();
            var logger = LoggerTestUtils.CreateLoggerWithConfig(config, exportedItems, LoggerName);

            GenerateLog(logger, "TestField", 42);

            Assert.Single(exportedItems);
        }

        [Fact]
        public void InvalidType_ShouldNotMatch()
        {
            var config = ReplaceConfigPlaceholders("TestField", "InvalidLong", OperatorType.NumericEquals, "42");
            var exportedItems = new List<LogRecord>();
            var logger = LoggerTestUtils.CreateLoggerWithConfig(config, exportedItems, LoggerName);

            GenerateLog(logger, "TestField", 42);

            Assert.Empty(exportedItems);
        }

        [Fact]
        public void InvalidOperator_ShouldNotMatch()
        {
            var config = ReplaceConfigPlaceholders("TestField", ConstraintType.Long, "InvalidOperator", "42");
            var exportedItems = new List<LogRecord>();
            var logger = LoggerTestUtils.CreateLoggerWithConfig(config, exportedItems, LoggerName);

            GenerateLog(logger, "TestField", 42);

            Assert.Empty(exportedItems);
        }

        [Fact]
        public void OperatorWithWhitespace_ShouldMatchAndSample()
        {
            var config = ReplaceConfigPlaceholders("TestField", ConstraintType.Long, "  " + OperatorType.NumericEquals + "  ", "42");
            var exportedItems = new List<LogRecord>();
            var logger = LoggerTestUtils.CreateLoggerWithConfig(config, exportedItems, LoggerName);

            GenerateLog(logger, "TestField", 42);

            Assert.Single(exportedItems);
        }

        [Fact]
        public void TypeWithWhitespace_ShouldMatchAndSample()
        {
            var config = ReplaceConfigPlaceholders("TestField", "  " + ConstraintType.Long + "  ", OperatorType.NumericEquals, "42");
            var exportedItems = new List<LogRecord>();
            var logger = LoggerTestUtils.CreateLoggerWithConfig(config, exportedItems, LoggerName);

            GenerateLog(logger, "TestField", 42);

            Assert.Single(exportedItems);
        }

        [Fact]
        public void StringValue_ShouldMatchAndSample()
        {
            var config = ReplaceConfigPlaceholders("TestField", ConstraintType.Long, OperatorType.NumericEquals, "42");
            var exportedItems = new List<LogRecord>();
            var logger = LoggerTestUtils.CreateLoggerWithConfig(config, exportedItems, LoggerName);

            logger.LogInformation("Test message {TestField}", "42");

            Assert.Single(exportedItems);
        }

        [Fact]
        public void NumericEquals_WithMaxValue_ShouldMatchAndSample()
        {
            var config = ReplaceConfigPlaceholders("TestField", ConstraintType.Long, OperatorType.NumericEquals, long.MaxValue.ToString());
            var exportedItems = new List<LogRecord>();
            var logger = LoggerTestUtils.CreateLoggerWithConfig(config, exportedItems, LoggerName);

            GenerateLog(logger, "TestField", long.MaxValue);

            Assert.Single(exportedItems);
        }

        [Fact]
        public void NumericEquals_WithMinValue_ShouldMatchAndSample()
        {
            var config = ReplaceConfigPlaceholders("TestField", ConstraintType.Long, OperatorType.NumericEquals, long.MinValue.ToString());
            var exportedItems = new List<LogRecord>();
            var logger = LoggerTestUtils.CreateLoggerWithConfig(config, exportedItems, LoggerName);

            GenerateLog(logger, "TestField", long.MinValue);

            Assert.Single(exportedItems);
        }

        [Fact]
        public void NumericEquals_WithOverflow_ShouldNotMatch()
        {
            var config = ReplaceConfigPlaceholders("TestField", ConstraintType.Long, OperatorType.NumericEquals, "9223372036854775808"); // MaxValue + 1
            var exportedItems = new List<LogRecord>();
            var logger = LoggerTestUtils.CreateLoggerWithConfig(config, exportedItems, LoggerName);

            GenerateLog(logger, "TestField", long.MaxValue);

            Assert.Empty(exportedItems);
        }

        [Fact]
        public void NumericEquals_WithCultureSpecificFormat_ShouldMatchAndSample()
        {
            var config = ReplaceConfigPlaceholders("TestField", ConstraintType.Long, OperatorType.NumericEquals, "1,234,567");
            var exportedItems = new List<LogRecord>();
            var logger = LoggerTestUtils.CreateLoggerWithConfig(config, exportedItems, LoggerName);

            GenerateLog(logger, "TestField", 1234567);

            Assert.Single(exportedItems);
        }

        [Fact]
        public void NumericEquals_WithScientificNotation_ShouldMatchAndSample()
        {
            var config = ReplaceConfigPlaceholders("TestField", ConstraintType.Long, OperatorType.NumericEquals, "1.234567E6");
            var exportedItems = new List<LogRecord>();
            var logger = LoggerTestUtils.CreateLoggerWithConfig(config, exportedItems, LoggerName);

            GenerateLog(logger, "TestField", 1234567);

            Assert.Single(exportedItems);
        }

        [Fact]
        public void NumericEquals_WithInvalidFormat_ShouldNotMatch()
        {
            var config = ReplaceConfigPlaceholders("TestField", ConstraintType.Long, OperatorType.NumericEquals, "invalid");
            var exportedItems = new List<LogRecord>();
            var logger = LoggerTestUtils.CreateLoggerWithConfig(config, exportedItems, LoggerName);

            GenerateLog(logger, "TestField", 42);

            Assert.Empty(exportedItems);
        }
    }
} 