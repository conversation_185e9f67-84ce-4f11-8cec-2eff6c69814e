﻿// <copyright file="EcsParameterValidatorExtension.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System;
using System.Linq;
using Microsoft.Extensions.Configuration;

namespace Microsoft.M365.Core.Telemetry.R9.Logging.Substrate.Configuration
{
    /// <summary>
    /// Provides extension methods for validating ECS parameter options.
    /// </summary>
    internal static class EcsParameterValidatorExtension
    {
        private const string EcsParametersSectionKey = "ECSParameters";
        private const string ServiceIdentifierKey = $"{EcsParametersSectionKey}:ECSIdentifiers:ServiceName";
        private const string EnvironmentTypeKey = $"{EcsParametersSectionKey}:EnvironmentType";
        private const string AuthOptionKey = $"{EcsParametersSectionKey}:AuthenticationOption";

        /// <summary>
        /// Binds ECS parameters from configuration to <see cref="EcsParametersOptions"/> and validates them.
        /// Determines if a customer group configuration (Client, Agents, EnvironmentType) is provided and valid.
        /// </summary>
        /// <param name="configuration">The configuration source.</param>
        /// <returns>The determined ECS type based on configuration.</returns>
        /// <exception cref="ArgumentNullException">Thrown if configuration is null.</exception>
        /// <exception cref="ArgumentException">Thrown if configuration validation fails.</exception>
        internal static ECSType ValidateAndDetermineECSType(this IConfiguration configuration)
        {
            if (configuration == null)
            {
                throw new ArgumentNullException(nameof(configuration));
            }

            var options = configuration.GetSection(EcsParametersSectionKey).Get<EcsParametersOptions>()
                ?? throw new ArgumentException($"Required configuration section '{EcsParametersSectionKey}' is missing or could not be parsed.");

            ValidateRequiredFields(options);

            bool hasAuthenticationSettings = configuration.GetSection(AuthOptionKey).Exists() &&
                                             configuration.GetSection(AuthOptionKey).GetChildren().Any();

            bool hasClientValue = !string.IsNullOrEmpty(options.Client);
            bool hasAgentsValue = options.Agents != null && options.Agents.Any();
            bool hasEnvironmentType = !string.IsNullOrEmpty(options.EnvironmentType);

            ValidateCustomizationCompleteness(hasClientValue, hasAgentsValue, hasEnvironmentType);

            if (hasEnvironmentType)
            {
                ValidateEnvironmentType(options.EnvironmentType);
            }

            if (UseCentralizedIntECSEndpoint(options))
            {
                return ECSType.CentralizedInt;
            }

            bool hasCustomClient = hasClientValue && !string.Equals(options.Client, Constants.ECSClientName, StringComparison.OrdinalIgnoreCase);
            bool hasCustomAgents = hasAgentsValue && !options.Agents!.SetEquals(Constants.ECSTeamNameProd);
            bool useCustomEcs = hasCustomClient || hasCustomAgents;

            if (useCustomEcs && !hasAuthenticationSettings)
            {
                throw new ArgumentException(
                    $"For custom ECS, '{AuthOptionKey}' configuration is required. " +
                    "Please provide authentication settings or use our Integration environment for testing.");
            }

            return useCustomEcs ? ECSType.Custom : ECSType.CentralizedProd;
        }

        /// <summary>
        /// Validates that required fields are present in the configuration.
        /// </summary>
        private static void ValidateRequiredFields(EcsParametersOptions options)
        {
            if (options.ECSIdentifiers == null || string.IsNullOrEmpty(options.ECSIdentifiers.ServiceName))
            {
                throw new ArgumentException($"Required configuration section '{ServiceIdentifierKey}' is missing or empty.");
            }
        }

        /// <summary>
        /// Determines if the configuration specifies the centralized integration ECS endpoint.
        /// </summary>
        private static bool UseCentralizedIntECSEndpoint(EcsParametersOptions options)
        {
            return options.Client.Equals(Constants.ECSClientName, StringComparison.OrdinalIgnoreCase) &&
                   options.Agents.SetEquals(Constants.ECSTeamNameInt) &&
                   options.EnvironmentType.Equals("Integration", StringComparison.OrdinalIgnoreCase);
        }

        /// <summary>
        /// Validates that if any customization is provided, all required customization fields are present.
        /// </summary>
        private static void ValidateCustomizationCompleteness(bool hasClientValue, bool hasAgentsValue, bool hasEnvironmentType)
        {
            bool hasAnyCustomization = hasClientValue || hasAgentsValue || hasEnvironmentType;
            bool allCustomizationsProvided = hasClientValue && hasAgentsValue && hasEnvironmentType;

            if (hasAnyCustomization && !allCustomizationsProvided)
            {
                throw new ArgumentException(
                    "Incomplete ECS configuration detected. When customizing any ECS setting, " +
                    "you must provide all three: Client, Agents, and EnvironmentType in the ECSParameters section.");
            }
        }

        /// <summary>
        /// Validates that the environment type is one of the allowed values.
        /// </summary>
        private static void ValidateEnvironmentType(string environmentType)
        {
            bool isValidEnvironment = string.Equals(environmentType, "Integration", StringComparison.OrdinalIgnoreCase) ||
                                      string.Equals(environmentType, "Production", StringComparison.OrdinalIgnoreCase);

            if (!isValidEnvironment)
            {
                throw new ArgumentException(
                    $"Invalid value '{environmentType}' for '{EnvironmentTypeKey}'. " +
                    "Supported values are 'Production' or 'Integration'.");
            }
        }
    }
}