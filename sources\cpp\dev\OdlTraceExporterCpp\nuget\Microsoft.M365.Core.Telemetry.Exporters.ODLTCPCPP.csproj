<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFrameworks>net8.0</TargetFrameworks>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform>x64</Platform>
    <GeneratePackageOnBuild>true</GeneratePackageOnBuild>
    <IncludeBuildOutput>false</IncludeBuildOutput>
    <AppendTargetFrameworkToOutputPath>true</AppendTargetFrameworkToOutputPath>
    <GenerateNuspec>false</GenerateNuspec>
    <NoWarn>$(NoWarn)</NoWarn>
    <!--This pack only csproj will also generate a dll, since we don't have good way to stop it, we have to rename it to avoid msbuild get confused -->
    <AssemblyName>cls</AssemblyName>
  </PropertyGroup>
  <PropertyGroup>
    <Version>1.0.8</Version>
    <ReleaseNotes>Use OWN. to prefix owned attrs.</ReleaseNotes>
    <PackageOutputPath>$(DistribRoot)\$(Configuration)\$(Platform)\Microsoft.M365.Core.Telemetry.Exporters.ODLTCPCPP.Nuget\</PackageOutputPath>
    <NuspecBasePath>$(EnlistmentRoot)</NuspecBasePath>
    <NuspecFile>Microsoft.M365.Core.Telemetry.Exporters.ODLTCPCPP.nuspec</NuspecFile>
    <NuspecProperties>$(NuspecProperties);version=$(Version)</NuspecProperties>
    <NuspecProperties>$(NuspecProperties);releaseNotes=$(ReleaseNotes)</NuspecProperties>
  </PropertyGroup>
  <ItemGroup>
    <ProjectReference Include="..\OdlTraceExporterCpp.vcxproj" PrivateAssets="all">
      <SetConfiguration>Configuration=Debug</SetConfiguration>
    </ProjectReference>
    <ProjectReference Include="..\OdlTraceExporterCpp.vcxproj" PrivateAssets="all">
      <SetConfiguration>Configuration=Release</SetConfiguration>
    </ProjectReference>
  </ItemGroup>
</Project>