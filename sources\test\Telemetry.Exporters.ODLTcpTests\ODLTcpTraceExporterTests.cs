﻿// <copyright file="ODLTcpTraceExporterTests.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System.Collections.Generic;
using System.Diagnostics;
using System.Threading.Tasks;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Options;
using Microsoft.M365.Core.Telemetry.Exporters.ODLTCP.Trace;
using OpenTelemetry.Logs;
using OpenTelemetry.Resources;
using OpenTelemetry.Trace;
using Xunit;

namespace Microsoft.R9.Extensions.Telemetry.Exporters.ODLTcp.Test
{
    public class ODLTcpTraceExporterTests
    {
        static string sourceName = "ODLTraceExporterTest";

        [Fact]
        public void AddODLTcpExporter_GivenOptions_ExportToLogType()
        {
            var exception = Record.Exception(() =>
            {
                var host = new HostBuilder().ConfigureServices((context, service) =>
                {
                    service.AddOpenTelemetry().WithTracing(builder =>
                    {
                        builder.AddSource(sourceName)
                               .SetResourceBuilder(ResourceBuilder
                                .CreateDefault()
                                .AddService(serviceName: "ODLTestService", serviceVersion: "1.0.0"))
                               .AddODLTcpExporter(configure => configure.TCPPort = 9527, false);
                    });
                }).Build();
                RunHostAndActivityAsync(host).ConfigureAwait(false);
            });
            Assert.Null(exception);
        }

        [Fact]
        public void AddODLTcpExporter_EnableBatch_UsingBatchProcessor()
        {
            var exception = Record.Exception(() =>
            {
                var host = new HostBuilder().ConfigureServices((context, service) =>
                {
                    service.AddOpenTelemetry().WithTracing(builder =>
                    {
                        builder.AddSource(sourceName)
                               .SetResourceBuilder(ResourceBuilder
                                .CreateDefault()
                                .AddService(serviceName: "ODLTestService", serviceVersion: "1.0.0"))
                               .AddODLTcpExporter(configure => configure.TCPPort = 9527, true);
                    });
                }).Build();
                RunHostAndActivityAsync(host).ConfigureAwait(false);
            });
            Assert.Null(exception);
        }

        [Fact]
        public void AddODLExporter_PrepopulatedFields_AddToTrace()
        {
            var exception = Record.Exception(() =>
            {
                var host = new HostBuilder().ConfigureServices((context, service) =>
                {
                    service.AddOpenTelemetry().WithTracing(builder =>
                    {
                        builder.AddSource(sourceName)
                               .SetResourceBuilder(ResourceBuilder
                                .CreateDefault()
                                .AddService(serviceName: "ODLTestService", serviceVersion: "1.0.0"))
                               .AddODLTcpExporter(
                               options =>
                               {
                                   options.PrepopulatedFields = new Dictionary<string, string>
                                   {
                                       ["TestField"] = "TestValue",
                                   };
                               }, false);
                    });
                }).Build();
                RunHostAndActivityAsync(host).ConfigureAwait(false);
            });
            Assert.Null(exception);
        }

        [Fact]
        public void TestODLTcpExporter_Dispose()
        {
            var host = new HostBuilder().ConfigureServices((context, service) =>
            {
                service.AddOpenTelemetry().WithTracing(builder =>
                {
                    builder.AddSource(sourceName)
                           .SetResourceBuilder(ResourceBuilder
                            .CreateDefault()
                            .AddService(serviceName: "ODLTestService", serviceVersion: "1.0.0"))
                           .AddODLTcpExporter(configure => configure.TCPPort = 9527, false);
                });
            }).Build();
            var options = host.Services.GetRequiredService<IOptions<ODLTcpTraceExporterOptions>>();
            var odlTcpTraceExporter = new ODLTcpTraceExporter(options);
            odlTcpTraceExporter.Dispose();
        }

        private static async Task RunHostAndActivityAsync(IHost host, string activityName = "Test")
        {
            await host.StartAsync().ConfigureAwait(true);
            var source = new ActivitySource(sourceName);
            var link = new ActivityLink(new ActivityContext(ActivityTraceId.CreateRandom(), ActivitySpanId.CreateRandom(), ActivityTraceFlags.Recorded));
            using (var activity = source.StartActivity("Foo", ActivityKind.Internal, null, null, new ActivityLink[] { link }))
            {
            }

            using (var activity = source.StartActivity("Bar"))
            {
                activity.SetStatus(ActivityStatusCode.Error, "bar error");
                using (var child = source.StartActivity("Bar2", ActivityKind.Client))
                {
                    child?.SetTag("http.method", "GET");
                }
            }

            using (var activity = source.StartActivity("Baz"))
            {
                activity.SetStatus(ActivityStatusCode.Ok);
            }

            await host.StopAsync().ConfigureAwait(true);
        }
    }
}
