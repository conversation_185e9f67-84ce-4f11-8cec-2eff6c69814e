<?xml version="1.0" encoding="utf-8"?>
<Project xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  
  <PropertyGroup>
    <MSBuildAllProjects>$(MSBuildAllProjects);$(MSBuildThisFileFullPath)</MSBuildAllProjects>

    <Build_IsSigned Condition="'$(ENABLE_CODESIGN)' == '1'">true</Build_IsSigned>
    <Build_IsPublic Condition="'$(QBUILD_DISTRIBUTED)' == '1'">true</Build_IsPublic>

    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>

    <!--
      QuickBuild uses $(BuildArchitecture) instead of $(Platform)
    -->
    <Platform Condition=" '$(BuildArchitecture)' != '' ">$(BuildArchitecture)</Platform>
    <Platform Condition=" '$(Platform)' == '' ">Amd64</Platform>
  </PropertyGroup>

</Project>