﻿// <copyright file="PostTraceBuildExtensions.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System;
using System.Diagnostics;
using System.Linq;
using System.Net.Http;
using OpenTelemetry.Trace;

namespace Microsoft.M365.Core.Telemetry.R9.Tracing.AdvancedSampling
{
    /// <summary>
    /// Builder Extension for Post Trace Sampling
    /// </summary>
    public static class PostTraceBuildExtensions
    {
        /// <summary>
        /// Enricher for Post Tracing
        /// </summary>
        /// <param name="builder"></param>
        /// <returns></returns>
        public static TracerProviderBuilder AddPostTracing(this TracerProviderBuilder builder)
        {
            if (builder == null)
            {
                throw new ArgumentNullException(nameof(builder));
            }

            return builder.AddHttpClientInstrumentation((options) => options.EnrichWithHttpResponseMessage = (activity, httpResponseMessage) =>
            {
                EnrichPostTraceBaggage(activity, httpResponseMessage);
            });
        }

        /// <summary>
        /// Enricher for Post-Trace Baggage when detecting spefici header in http response
        /// </summary>
        /// <param name="activity"></param>
        /// <param name="httpResponseMessage"></param>
        public static void EnrichPostTraceBaggage(Activity activity, HttpResponseMessage httpResponseMessage)
        {
            if (httpResponseMessage.Headers.TryGetValues(Constants.PostTraceBaggageName, out var values))
            {
                if (bool.TryParse(values.First(), out bool errorInTrace) && errorInTrace)
                {
                    var curr = activity;
                    while (curr != null)
                    {
                        curr.SetBaggage(Constants.PostTraceBaggageName, Constants.SampleBaggage);
                        curr = curr.Parent;
                    }
                }
            }
        }
    }
}
