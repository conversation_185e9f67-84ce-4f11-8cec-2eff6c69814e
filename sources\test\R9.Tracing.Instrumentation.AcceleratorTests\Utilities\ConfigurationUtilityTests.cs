﻿// <copyright file="ConfigurationUtilityTests.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.Diagnostics.CodeAnalysis;
using System.Runtime.InteropServices;
using Microsoft.M365.Core.Telemetry.ECSClient;
using Microsoft.M365.Core.Telemetry.R9.Tracing.Instrumentation.Accelerator.Options;
using Microsoft.Win32;
using Newtonsoft.Json.Linq;
using Xunit;
using static Microsoft.M365.Core.Telemetry.ECSClient.Constants;

namespace Microsoft.M365.Core.Telemetry.R9.Tracing.Instrumentation.Accelerator.Tests.Utilities
{
    [ExcludeFromCodeCoverage]
    public class ConfigurationUtilityTests
    {
        private readonly ServiceMetaDataOptions initialServiceMetaOptions = new ServiceMetaDataOptions
        {
            ServiceName = "ServiceA",
            RuntimeModel = "ModelA"
        };

        private readonly TracingAcceleratorOptions initialAcceleratorOptions = new TracingAcceleratorOptions
        {
            ActivitySources = new string[] { "Source1" },
            HttpClientTracing = new HttpClientTracingOptionsInherited
            {
                IsEnabled = true
            },
            HttpTracing = new HttpTracingOptionsInherited
            {
                IsEnabled = true,
                ExcludePathStartsWith = new HashSet<string> { "A" }
            },
            GenevaTraceExporter = new GenevaTraceExporterOptionsInherited
            {
                TableNameMappings = new Dictionary<string, string>
                {
                    { "ServiceA", "ServiceASpan" },
                    { "ServiceB", "ServiceBSpan" }
                }
            },
            TracingSamplerAndEnabled = new TracingSamplerAndEnableOptions[]
            {
                new TracingSamplerAndEnableOptions
                {
                    R9DTEnabled = true,
                    TraceSampleRate = 0.5f,
                    SamplerType = "ParentBased",
                    ParentRootSamplerType = "RatioBased",
                    DeployRing = "Unknown,sdfv2"
                },
                new TracingSamplerAndEnableOptions
                {
                    R9DTEnabled = true,
                    SamplerType = "AlwaysOn",
                    DeployRing = "msit"
                }
            }
        };

        [Fact]
        public void EtwExporterShouldBeEnabledByDefault()
        {
            Assert.True(initialAcceleratorOptions.ODLTraceExporter.IsEnabled);
        }

        [Fact]
        public void TcpExporterShouldBeDisabledByDefault()
        {
            Assert.False(initialAcceleratorOptions.ODLTcpTraceExporter.IsEnabled);
        }

        [Fact]
        public void ShouldAbleToDisableEtwExporter()
        {
            JToken ecsConfig = JToken.Parse(@"{
                'ODLTraceExporter': {
                    'IsEnabled': false
                }
            }");
            var actualResult = ConfigurationUtility.RefreshTracingAcceleratorOptions(initialAcceleratorOptions, ecsConfig);
            Assert.NotSame(initialAcceleratorOptions, actualResult);
            Assert.False(actualResult.ODLTraceExporter.IsEnabled);
            Assert.NotEqual(initialAcceleratorOptions.ODLTraceExporter.IsEnabled, actualResult.ODLTraceExporter.IsEnabled);
        }

        [Fact]
        public void ShouldAbleToEnableTcpExporter()
        {
            JToken ecsConfig = JToken.Parse(@"{
                'ODLTcpTraceExporter': {
                    'IsEnabled': true
                }
            }");
            var actualResult = ConfigurationUtility.RefreshTracingAcceleratorOptions(initialAcceleratorOptions, ecsConfig);
            Assert.NotSame(initialAcceleratorOptions, actualResult);
            Assert.True(actualResult.ODLTcpTraceExporter.IsEnabled);
            Assert.NotEqual(initialAcceleratorOptions.ODLTcpTraceExporter.IsEnabled, actualResult.ODLTcpTraceExporter.IsEnabled);
        }

        [Fact]
        public void ShouldNotMergeAcceleratorOptionsWhenEcsConfigIsNull()
        {
            JToken ecsConfig = null;
            var actualResult = ConfigurationUtility.RefreshTracingAcceleratorOptions(initialAcceleratorOptions, ecsConfig);
            Assert.Same(initialAcceleratorOptions, actualResult);
        }

        [Fact]
        public void ShouldMergeAcceleratorOptionsWithoutNullProperty()
        {
            JToken ecsConfig = JToken.Parse(@"{
                'ActivitySources': ['Source2'],
                'HttpTracing': {'IsEnabled': false, 'ExcludePathStartsWith': ['B']},
                'GenevaTraceExporter': {
                    'TableNameMappings': {
                        'ServiceA': 'ServiceASpan_New',
                        'ServiceC': 'ServiceCSpan'
                    }
                }
            }");
            var actualResult = ConfigurationUtility.RefreshTracingAcceleratorOptions(initialAcceleratorOptions, ecsConfig);
            Assert.NotSame(initialAcceleratorOptions, actualResult);
            Assert.Equal(initialAcceleratorOptions.HttpClientTracing.IsEnabled, actualResult.HttpClientTracing.IsEnabled);
            Assert.NotEqual(initialAcceleratorOptions.HttpTracing.IsEnabled, actualResult.HttpTracing.IsEnabled);

            Assert.DoesNotContain("Source1", actualResult.ActivitySources);
            Assert.Contains("Source2", actualResult.ActivitySources);
            Assert.DoesNotContain("A", actualResult.HttpTracing.ExcludePathStartsWith);
            Assert.Contains("B", actualResult.HttpTracing.ExcludePathStartsWith);
            Assert.Equal("ServiceASpan_New", actualResult.GenevaTraceExporter.TableNameMappings["ServiceA"]);
            Assert.Equal("ServiceBSpan", actualResult.GenevaTraceExporter.TableNameMappings["ServiceB"]);
            Assert.Equal("ServiceCSpan", actualResult.GenevaTraceExporter.TableNameMappings["ServiceC"]);
        }

        [Fact]
        public void ShouldMergeAcceleratorOptionsWithNullProperty()
        {
            JToken ecsConfig = JToken.Parse(@"{
                'ActivitySources': null,
                'HttpTracing': {'IsEnabled': false, 'ExcludePathStartsWith': null},
            }");
            var actualResult = ConfigurationUtility.RefreshTracingAcceleratorOptions(initialAcceleratorOptions, ecsConfig);
            Assert.NotSame(initialAcceleratorOptions, actualResult);
            Assert.Null(actualResult.ActivitySources);
            Assert.Null(actualResult.HttpTracing.ExcludePathStartsWith);
        }

        [Fact]
        public void ShouldMergeAcceleratorOptionsWithMissingProperty()
        {
            JToken ecsConfig = JToken.Parse(@"{
                'HttpTracing': {'IsEnabled': false},
            }");
            var actualResult = ConfigurationUtility.RefreshTracingAcceleratorOptions(initialAcceleratorOptions, ecsConfig);
            Assert.NotSame(initialAcceleratorOptions, actualResult);
            Assert.NotNull(actualResult.ActivitySources);
            Assert.Contains("Source1", actualResult.ActivitySources);
            Assert.DoesNotContain("Source2", actualResult.ActivitySources);

            Assert.NotNull(actualResult.HttpTracing.ExcludePathStartsWith);
            Assert.Contains("A", actualResult.HttpTracing.ExcludePathStartsWith);
            Assert.DoesNotContain("B", actualResult.HttpTracing.ExcludePathStartsWith);
        }

        [Fact]
        public void ShouldParseEnableAndSamplerOptionsCorrectly()
        {
            R9TracingConfig actualResult = ConfigurationUtility.ParseR9TracingConfig(initialServiceMetaOptions, initialAcceleratorOptions.TracingSamplerAndEnabled);
            Assert.True(actualResult.R9DTEnabled);
            Assert.Equal(0.5f, actualResult.TraceSampleRate);
            Assert.Equal(DynamicSamplerType.ParentBased, actualResult.SamplerType);
            Assert.Equal(DynamicSamplerType.RatioBased, actualResult.ParentRootSamplerType);
        }
    }
}
