<?xml version="1.0" encoding="utf-8"?>
<Project xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <MSBuildAllProjects>$(MSBuildAllProjects);$(MSBuildThisFileFullPath)</MSBuildAllProjects>
  </PropertyGroup>

<!-- Workaround that fixes sfproj for CloudBuild. Needs to be imported after Microsoft.VisualStudio.Azure.Fabric.Application.targets! -->
  <!-- Avoid ServiceFabric telemetry which greps for sln files, causing IMD errors in QuickBuild. -->
  <PropertyGroup>
    <AddCorrelationIds>false</AddCorrelationIds>
  </PropertyGroup>

  <ItemDefinitionGroup Condition=" '$(BuildingInsideVisualStudio)' != 'true' And '$(Platform)' != '' ">
    <ProjectReference>
      <Platform>$(Platform)</Platform>
    </ProjectReference>
  </ItemDefinitionGroup>

<!-- Packages each of the referenced service projects that supports 'DeployOnBuild'. -->
  <Target Name="PackageDeployableServices"
          Condition=" '@(DeployableServiceProjectReference)' != '' ">
    <!-- The only differences are calling DotNetPublish instead of the default target and passing NoBuild=true to avoid building the referenced project again -->
    <MSBuild Projects="%(DeployableServiceProjectReference.Identity)"
             Targets="DotNetPublish"
             Properties="%(DeployableServiceProjectReference.CommonBuildProperties);DeployOnBuild=true;PublishUrl=%(DeployableServiceProjectReference.PublishUrl);WebPublishMethod=FileSystem;NoBuild=true"
             Condition=" '%(DeployableServiceProjectReference.IncludeCodePackage)' == 'true' "/>
  </Target>

  <!-- Packages each of the referenced core service projects that supports 'Publish'. -->
  <Target Name="PackagePublishableServices"
          Condition=" '@(PublishableServiceProjectReference)' != '' ">
    <!-- The only difference is passing NoBuild=true, which will avoid the build and only do the Publish-related targets -->
    <MSBuild Projects="%(PublishableServiceProjectReference.Identity)"
             Targets="Publish"
             Properties="%(PublishableServiceProjectReference.CommonBuildProperties);PublishDir=%(PublishableServiceProjectReference.PublishDir);NoBuild=true"
             Condition=" '%(PublishableServiceProjectReference.IncludeCodePackage)' == 'true' "/>
  </Target>

</Project>