﻿// <copyright file="DyeBasedSamplingExtension.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System;
using System.Diagnostics;
using OpenTelemetry.Trace;

namespace Microsoft.M365.Core.Telemetry.R9.Tracing.AdvancedSampling.DyeBasedTraceSampling
{
    /// <summary>
    /// The extension for dye based sampling.
    /// </summary>
    public static class DyeBasedSamplingExtension
    {
        /// <summary>
        /// Add dye based sampling to the builder.
        /// </summary>
        /// <param name="builder"></param>
        /// <param name="option"></param>
        /// <returns></returns>
        public static TracerProviderBuilder AddDyeBasedSampling(this TracerProviderBuilder builder, DyeBasedSamplingOption option)
        {
            if (builder == null)
            {
                throw new ArgumentNullException(nameof(builder));
            }

            if (option == null)
            {
                throw new ArgumentNullException(nameof(option));
            }

            AddDyeBasedActivitySourceListener(option);

            return builder;
        }

        /// <summary>
        /// Add dye based activity source listener.
        /// </summary>
        /// <param name="option"></param>
        public static void AddDyeBasedActivitySourceListener(DyeBasedSamplingOption option)
        {
            ActivityListener listener = new ActivityListener
            {
                ShouldListenTo = (activitySource) => true,
                Sample = (ref ActivityCreationOptions<ActivityContext> _) => ActivitySamplingResult.AllData,
            };

            listener.ActivityStarted = (activity) =>
            {
                if (option.Tags != null && option.ActivitySource != null && option.ActivitySource.Contains(activity.Source.Name))
                {
                    DyeBasedTraceEnricher.EnrichDyeTraceBaggage(activity, "OnStartActivity", option.Tags);
                }
            };

            ActivitySource.AddActivityListener(listener);
        }
    }
}
