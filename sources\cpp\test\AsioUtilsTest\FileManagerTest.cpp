#include "FileManager.h"
#include "AsioContext.h"

#include <gtest/gtest.h>
#include <filesystem>
#include <fstream>
#include <thread>
#include <chrono>

using namespace Microsoft::M365::Exporters;

class FileManagerTest : public ::testing::Test {
public:
    FileManagerTest() : filename_("test_file.txt") {
        if (std::filesystem::exists(filename_)) {
            std::filesystem::remove(filename_);
        }
        file_manager_ = std::make_unique<FileManager>(AsioContext::GetIoContext(), filename_, std::chrono::seconds(1));
    }

    ~FileManagerTest() override { }

    std::string ReadFile() {
        std::ifstream file(filename_);
        std::string content((std::istreambuf_iterator<char>(file)), std::istreambuf_iterator<char>());
        return content;
    }

    void SetUp() override { }

    void TearDown() override {
        file_manager_->ShutDown();
        // Wait for all callbacks finish.
        std::this_thread::sleep_for(std::chrono::milliseconds(100));
        file_manager_.reset();
        if (std::filesystem::exists(filename_)) {
            std::filesystem::remove(filename_);
        }
    }

    std::unique_ptr<FileManager> file_manager_;
    std::string filename_;
};

// This test don't output the "pass"/"fail" result. Check for error output.
// Pass if no error output.
TEST_F(FileManagerTest, WriteAndReturn) {
    file_manager_->Write(std::make_shared<std::string>("Foo"));
    file_manager_->Write(std::make_shared<std::string>("Bar"));
    std::this_thread::sleep_for(std::chrono::milliseconds(100));  // 100ms
    EXPECT_EQ("FooBar", ReadFile());
    std::this_thread::sleep_for(std::chrono::milliseconds(1000));  // 1100ms, reset file by now.
    file_manager_->Write(std::make_shared<std::string>("AB"));
    EXPECT_EQ("ABoBar", ReadFile());
}