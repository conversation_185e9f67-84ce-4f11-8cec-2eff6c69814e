﻿// <copyright file="TestLogEnrichmentPropertyBag.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;

using Microsoft.R9.Extensions.Diagnostics;
using Microsoft.R9.Extensions.Enrichment;

namespace Microsoft.M365.Core.Telemetry.EnrichmentTest
{
    /// <summary>
    /// TestPropertyBag
    /// </summary>
    public class TestPropertyBag : IEnrichmentPropertyBag
    {
        private readonly Dictionary<string, object> properties = new Dictionary<string, object>();

        /// <summary>
        /// Properties
        /// </summary>
        public IReadOnlyDictionary<string, object> Properties => properties;

        /// <summary>
        /// Add
        /// </summary>
        /// <param name="key"></param>
        /// <param name="value"></param>
        public void Add(string key, object value)
        {
            _ = Throws.IfNullOrEmpty(key);
            _ = Throws.IfNull(value);

            properties.Add(key, value);
        }

        /// <summary>
        /// Add
        /// </summary>
        /// <param name="key"></param>
        /// <param name="value"></param>
        public void Add(string key, string value)
        {
            properties.Add(key, value);
        }

        /// <summary>
        /// Add
        /// </summary>
        /// <param name="properties"></param>
        public void Add(ReadOnlySpan<KeyValuePair<string, object>> properties)
        {
            foreach (var p in properties)
            {
                Add(p.Key, p.Value);
            }
        }

        /// <summary>
        /// Add
        /// </summary>
        /// <param name="properties"></param>
        public void Add(ReadOnlySpan<KeyValuePair<string, string>> properties)
        {
            foreach (var p in properties)
            {
                Add(p.Key, p.Value);
            }
        }
    }
}
