﻿// <copyright file="HttpClientEUIIRedactEnricher.netfx.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

#if NETFRAMEWORK
namespace Microsoft.M365.Core.Telemetry.R9.Tracing.Instrumentation.Accelerator
{
    using System;
    using System.Diagnostics;
    using System.Net;

    /// <summary>
    /// HttpClientEUIIRedactEnricher
    /// </summary>
    internal sealed class HttpClientEUIIRedactEnricher
    {
        /// <summary>
        /// EnrichWithHttpRequestMessage : onStartActivity
        /// </summary>
        /// <param name="activity"></param>
        /// <param name="httpWebRequest"></param>
        /// <param name="redactionStrategyType"></param>
        public static void EnrichWithHttpRequestMessage(Activity activity, HttpWebRequest httpWebRequest, RedactionStrategyType redactionStrategyType)
        {
            if (activity == null)
            {
                return;
            }
            var redactedValue = EUIIRedactor.RedactEgressPath(httpWebRequest.RequestUri?.AbsolutePath, redactionStrategyType);
            activity.SetTag(Constants.HttpUrl, Utility.DeriveHttpUrl(httpWebRequest, redactedValue));
        }
    }
}
#endif