<?xml version="1.0" encoding="utf-8"?>
<Project xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <MSBuildAllProjects>$(MSBuildAllProjects);$(MSBuildThisFileFullPath)</MSBuildAllProjects>
    <TargetFrameworkMonikerAssemblyAttributesPath>$(IntermediateOutputPath)$(TargetFrameworkMoniker).AssemblyAttributes$(DefaultLanguageSourceExtension)</TargetFrameworkMonikerAssemblyAttributesPath>
  </PropertyGroup>

  <!-- For PackageConfig.xml -->
  <Target Name="CopyPackagesToPackageConfigDrop" AfterTargets="GenerateNuspec">
    <ItemGroup>
      <PackageFiles Include="$(PackageOutputPath)\**\*.nupkg" />
    </ItemGroup>

    <Copy
      SourceFiles="@(PackageFiles)"
      DestinationFolder="$(PackageInfoXmlRootPath)"
      SkipUnchangedFiles="$(SkipCopyUnchangedFiles)"
      OverwriteReadOnlyFiles="$(OverwriteReadOnlyFiles)"
      Retries="$(CopyRetryCount)"
      RetryDelayMilliseconds="$(CopyRetryDelayMilliseconds)"
      UseHardlinksIfPossible="$(CreateHardLinksForCopyFilesToOutputDirectoryIfPossible)"
      UseSymboliclinksIfPossible="$(CreateSymbolicLinksForCopyFilesToOutputDirectoryIfPossible)">

      <Output TaskParameter="DestinationFiles" ItemName="PackageDistrib"/>
    </Copy>
  </Target>

  <!-- Code Signing -->
  <!-- If signed builds, then restore the CodeSign package. In case the project type does not support PackageReference,
       set property manually. The restore should happen via a Packages.Config checked into the project. -->
  <ItemGroup Condition="'$(Project_IsSigned)' == 'true' And !Exists('$(MSBuildProjectDirectory)\packages.config') And '$(MSBuildProjectExtension)' != '.vcxproj' And '$(MSBuildProjectExtension)' != '.ccproj' And '$(MSBuildProjectExtension)' != '.nuproj'">
    <PackageReference Include="Microsoft.Internal.CodeSign.CloudBuild" GeneratePathProperty="true" PrivateAssets="All"/>
  </ItemGroup>
  
  <PropertyGroup Condition="'$(Project_IsSigned)' == 'true'">
    <PkgMicrosoft_Internal_CodeSign_CloudBuild Condition=" '$(PkgMicrosoft_Internal_CodeSign_CloudBuild)' == '' ">$(EnlistmentRoot)\packages\Microsoft.Internal.CodeSign.CloudBuild.7.0.3</PkgMicrosoft_Internal_CodeSign_CloudBuild>
  </PropertyGroup>
  
  <!--  Explicitly import for non-SDK Style project files. -->
  <Import Project="$(PkgMicrosoft_Internal_CodeSign_CloudBuild)\CodeSign.targets" Condition="'$(Project_IsSigned)' == 'true' And '$(TargetFramework)' != '' AND '$(UsingMicrosoftNETSdk)' != 'true'" />

  <!-- For non-SDK projects, we need help converting properties into a cs file with all the assembly info attributes -->
  <Import Project="$(EnlistmentRoot)\Build\Extensions\Microsoft.NET.GenerateAssemblyInfo.targets" Condition=" '$(UsingMicrosoftNETSdk)' != 'true' " />

  
  <Target Name="AddNuProjPackagesConfigForRestore" BeforeTargets="RestorePackagesConfig">
    <ItemGroup>
      <!-- Add nuproj targets that have a packages.config -->
      <_RestoreProjectPathAdditionalProjects Include="$(EnlistmentRoot)\sources\dev\nuproj\**\*.nuproj"/>
      <_RestoreProjectPathItemsWithPackagesConfig Include="@(_RestoreProjectPathAdditionalProjects)"
                                                  Condition="Exists('%(RootDir)%(Directory)packages.config')" />
    </ItemGroup>
  </Target>
</Project>
