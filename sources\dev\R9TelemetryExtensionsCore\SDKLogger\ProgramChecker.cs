﻿// <copyright file="ProgramChecker.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.Diagnostics.CodeAnalysis;
using System.Linq;
using System.Reflection;

#if !NETFRAMEWORK
using System.Runtime.Loader;
#endif

namespace Microsoft.M365.Core.Telemetry.SDKLogger
{
    /// <summary>
    /// ProgramChecker
    /// </summary>
    // This class is used to gather info of the current process.
    // It's a lot of hacks. Once bond is upgraded, this should
    // be removed and we use normal monitors.
    internal static class ProgramChecker
    {
        /// <summary>
        /// GetMetricBaseType
        /// </summary>
        /// <param name="type"></param>
        /// <param name="targetType"></param>
        /// <returns></returns>
        internal static Type GetMetricBaseType(Type type, string targetType)
        {
            Type baseType = type;
            while (baseType != null)
            {
                if (baseType.IsGenericType && baseType.GetGenericTypeDefinition().FullName == targetType)
                {
                    break;
                }
                baseType = baseType.BaseType;
            }
            return baseType;
        }

        /// <summary>
        /// GetGenericTypes
        /// </summary>
        /// <param name="targetType"></param>
        /// <returns></returns>
        [ExcludeFromCodeCoverage]
        internal static List<Type> GetGenericTypes(string targetType)
        {
            _ = targetType;
            List<Type> res = new List<Type>();
#if !NETFRAMEWORK
            foreach (AssemblyLoadContext alc in AssemblyLoadContext.All)
            {
                foreach (Assembly assembly in alc.Assemblies)
                {
                    Type[] types;
                    try
                    {
                        types = assembly.GetTypes();
                    }
                    catch (ReflectionTypeLoadException e)
                    {
                        types = e.Types;
                    }

                    foreach (Type type in types.Where(t => t != null))
                    {
                        if (type.IsGenericTypeDefinition)
                        {
                            continue;
                        }
                        Type baseType = GetMetricBaseType(type, targetType);

                        if (baseType != null && baseType.IsGenericType && baseType.GetGenericTypeDefinition().FullName == targetType)
                        {
                            res.Add(type);
                            continue;
                        }

                        if (type.GetInterfaces().Length > 0)
                        {
                            foreach (Type intfc in type.GetInterfaces())
                            {
                                if (intfc.IsGenericType && intfc.GetGenericTypeDefinition().FullName == targetType)
                                {
                                    res.Add(type);
                                    break;
                                }
                            }
                        }
                    }
                }
            }
#endif
            return res;
        }
    }
}
