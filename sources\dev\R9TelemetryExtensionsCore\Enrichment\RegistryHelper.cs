﻿// <copyright file="RegistryHelper.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System;
using System.Diagnostics.CodeAnalysis;
using System.Runtime.ConstrainedExecution;
using System.Runtime.InteropServices;
using System.Security;
#if !NETFRAMEWORK
using Microsoft.M365.Core.Portable.Registry;
#else
using Microsoft.Win32;
#endif

namespace Microsoft.M365.Core.Telemetry.Enrichment
{
    /// <summary>
    /// RegisterHelper class
    /// </summary>
    [ExcludeFromCodeCoverage]
    internal static class RegistryHelper
    {
        // Win32 error codes

        /// <summary>
        /// Error Success code
        /// </summary>
        public const int ErrorSuccess = 0;

        /// <summary>
        /// Error Fuile Not Found Code
        /// </summary>
        public const int ErrorFileNotFound = 2;

        /// <summary>
        /// HKEY CLASSES ROOT
        /// </summary>
        private static IntPtr hkeyClassesRoot = new IntPtr(unchecked((int)0x80000000L));

        /// <summary>
        /// HKEY CURRENT USER
        /// </summary>
        private static IntPtr hkeyCurrentUser = new IntPtr(unchecked((int)0x80000001L));

        /// <summary>
        /// HKEY LOCAL MACHINE
        /// </summary>
        private static IntPtr hkeyLocalMachine = new IntPtr(unchecked((int)0x80000002L));

        /// <summary>
        /// HKEY Users
        /// </summary>
        private static IntPtr hkeyUsers = new IntPtr(unchecked((int)0x80000003L));

        /// <summary>
        ///  HKEY CURRENT CONFIG
        /// </summary>
        private static IntPtr hkeyCurrentConfig = new IntPtr(unchecked((int)0x80000005L));

        /// <summary>
        /// Opens a registry key on the current machine in the specified hive.
        /// </summary>
        /// <param name="hive">The Registry Hive</param>
        /// <param name="subKey">The subkey</param>
        /// <param name="accessMask">The access mask</param>
        /// <returns>Returns a safe handle to the registry</returns>
        public static SafeHandle OpenSubKey(
            RegistryHive hive,
            string subKey,
            RegistryAccessMask accessMask)
        {
            SafeRegistryHandle registryHandle;
            int error = RegOpenKeyEx(Convert(hive), subKey, 0, accessMask, out registryHandle);

            if (error == 0 && !registryHandle.IsInvalid)
            {
                return registryHandle;
            }
            else if (error == ErrorFileNotFound)
            {
                return null;
            }
            else
            {
                return null;
            }
        }

        /// <summary>
        /// Notifies the caller based on the change described in the filter for the registry key path supplied.
        /// </summary>
        /// <param name="hKey">The handle</param>
        /// <param name="watchSubtree">Should watch subtree</param>
        /// <param name="dwNotifyFilter">The notification filter</param>
        /// <param name="hEvent">A safe handle</param>
        /// <param name="fAsynchronous">Indicates synchronous access</param>
        public static void NotifyChangeKeyValue(
            SafeHandle hKey,
            bool watchSubtree,
            RegistryChangeNotificationFilter dwNotifyFilter,
            SafeHandle hEvent,
            bool fAsynchronous)
        {
            int result = RegNotifyChangeKeyValue(
                hKey,
                watchSubtree,
                dwNotifyFilter,
                hEvent,
                fAsynchronous);
        }

        /// <summary>
        /// Converts the hive to an IntPtr
        /// </summary>
        /// <param name="hive">The registry hive</param>
        /// <returns>An IntPtr struct</returns>
        private static IntPtr Convert(RegistryHive hive)
        {
            IntPtr baseKeyPtr = IntPtr.Zero;
            switch (hive)
            {
                case RegistryHive.ClassesRoot:
                    baseKeyPtr = hkeyClassesRoot;
                    break;

                case RegistryHive.CurrentConfig:
                    baseKeyPtr = hkeyCurrentConfig;
                    break;

                case RegistryHive.CurrentUser:
                    baseKeyPtr = hkeyCurrentUser;
                    break;

                case RegistryHive.LocalMachine:
                    baseKeyPtr = hkeyLocalMachine;
                    break;

                case RegistryHive.Users:
                    baseKeyPtr = hkeyUsers;
                    break;
            }

            return baseKeyPtr;
        }

        [DllImport("advapi32.dll", CharSet = CharSet.Auto)]
        private static extern int RegOpenKeyEx(
                                         IntPtr hKey,
                                         [MarshalAs(UnmanagedType.LPWStr)]
                                         string lpSubKey,
                                         int ulOptions,
                                         RegistryAccessMask samDesired,
                                         out SafeRegistryHandle hkResult);

        [DllImport("advapi32.dll", SetLastError = true)]
        private static extern int RegNotifyChangeKeyValue(
                                                             SafeHandle hKey,
                                                             bool watchSubtree,
                                                             [MarshalAs(UnmanagedType.U4)]
                                                             RegistryChangeNotificationFilter dwNotifyFilter,
                                                             SafeHandle hEvent,
                                                             bool fAsynchronous);

        /// <summary>
        /// This class was pulled from the .NET 4.0 public implementation. In .NET 2.0 the class is marked as internal
        /// and at the time of writing this assembly was linked against the 2.0 framework.
        /// </summary>
        private sealed class SafeRegistryHandle : SafeHandle
        {
            /// <summary>
            /// Custom Constructor
            /// </summary>
            /// <param name="preexistingHandle">A preexisting Handle</param>
            public SafeRegistryHandle(IntPtr preexistingHandle)
                : base(preexistingHandle, true)
            {
                SetHandle(preexistingHandle);
            }

            /// <summary>
            /// Internal Constructor
            /// </summary>
            internal SafeRegistryHandle()
                : base(IntPtr.Zero, true)
            {
            }

            /// <summary>
            /// Checks for handle validity
            /// </summary>
            public override bool IsInvalid
            {
                get
                {
                    if (this.handle != IntPtr.Zero)
                    {
                        return this.handle == new IntPtr(-1);
                    }

                    return true;
                }
            }

            /// <summary>
            /// Releases Handle
            /// </summary>
            /// <returns>A boolean indicating successful release</returns>
            [SecurityCritical]
            protected override bool ReleaseHandle()
            {
                return RegCloseKey(handle) == 0;
            }

            /// <summary>
            /// Closes Registry Key
            /// </summary>
            /// <param name="hKey">A handle</param>
            /// <returns>An integer</returns>
            [DllImport("advapi32.dll")]
            [SuppressUnmanagedCodeSecurity]
#if NETFRAMEWORK
            [ReliabilityContract(Consistency.WillNotCorruptState, Cer.Success)]
#endif
            private static extern int RegCloseKey(IntPtr hKey);
        }
    }
}
