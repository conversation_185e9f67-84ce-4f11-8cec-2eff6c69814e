﻿// <copyright file="Tags.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System.Collections.Generic;

namespace Microsoft.M365.Core.Telemetry.R9.Tracing.AdvancedSampling
{
    /// <inheritdoc/>
    public class Tags
    {
        /// <summary>
        /// constrcutor
        /// </summary>
        public Tags()
        {
        }

        /// <summary>
        /// constrcutor
        /// </summary>
        /// <param name="sample"></param>
        /// <param name="drop"></param>
        public Tags(ISet<string> sample = null, ISet<string> drop = null)
        {
            this.Sample = sample;
            this.Drop = drop;
        }

        /// <summary>
        /// Sample list, if current tags in which need sto sample
        /// </summary>
        public ISet<string> Sample { get; private set; } = new HashSet<string>(64);

        /// <summary>
        /// Drop list, if current tags in which need to drop
        /// </summary>
        public ISet<string> Drop { get; private set; } = new HashSet<string>(64);
    }
}
