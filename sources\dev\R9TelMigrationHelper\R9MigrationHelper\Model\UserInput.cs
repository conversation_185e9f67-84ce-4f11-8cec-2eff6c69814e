﻿// <copyright file="UserInput.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace R9MigrationHelper.Model
{
    /// <summary>
    /// UserInput.
    /// </summary>
    public class UserInput
    {
        static string appKey = string.Empty;
        static string independentXMLDropFilePath = "/sources/dev/monitoring/src/PassiveMonitoring/R9TelDepsDropHelper/XmlDrop.xml";
        static string independentCsProjFilePath = "/sources/dev/monitoring/src/PassiveMonitoring/R9TelDepsDropHelper/R9TelDepsDropHelper.csproj";
        static string binPlaceCsProjFilePath = "/sources/dev/common/src/BinPlaceForPackages/BinPlaceForPackages.csproj";
        static string sharedBindingRedirectPath = "/sources/dev/common/src/common/SharedBindingRedirects.config";

        List<AssemblyModel> dlls = new List<AssemblyModel>();
        string serviceName = string.Empty;
        string serviceFramework = string.Empty;
        string serviceConfigFilePath = string.Empty;
        string xMLDropFilePath = string.Empty;
        string binPlaceForPackageFilePath = string.Empty;
        string organization = "o365exchange";
        string project = "O365 Core";
        string repository = "Substrate";
        string serviceCsprojFilePath = string.Empty;
        string workingPullRequestUrl = string.Empty;
        List<string> selectedTopPackages = new List<string>();
        bool useBinplaceDrop = false;
        List<XmlFullDestination> selectedDestinations = new List<XmlFullDestination>();

        /// <summary>
        /// App key used for auth
        /// </summary>
        public static string AppKey
        {
            get { return appKey; }
            set { appKey = value; }
        }

        /// <summary>
        /// File path of centralized XML Drop 
        /// </summary>
        public static string IndependentXMLDropFilePath
        {
            get { return independentXMLDropFilePath; }
            set { independentXMLDropFilePath = value; }
        }

        /// <summary>
        /// File path of centralized csproj file 
        /// </summary>
        public static string IndependentCsProjFilePath
        {
            get { return independentCsProjFilePath; }
            set { independentCsProjFilePath = value; }
        }

        /// <summary>
        /// BinplaceCsProjFilePath
        /// </summary>
        public static string BinplaceCsProjFilePath
        {
            get { return binPlaceCsProjFilePath; }
            set { binPlaceCsProjFilePath = value; }
        }

        /// <summary>
        /// SharedBindingRedirectPath
        /// </summary>
        public static string SharedBindingRedirectPath
        {
            get { return sharedBindingRedirectPath; }
            set { sharedBindingRedirectPath = value; }
        }

        /// <summary>
        /// WorkingPullRequestUrl
        /// </summary>
        public string WorkingPullRequestUrl
        {
            get { return workingPullRequestUrl; }
            set { workingPullRequestUrl = value; }
        }

        /// <summary>
        /// DLL list.
        /// </summary>
        public List<AssemblyModel> DLLs
        {
            get { return dlls; }
            set { dlls = value; }
        }

        /// <summary>
        /// Service Name
        /// </summary>
        public string ServiceName
        {
            get { return serviceName; }
            set { serviceName = value; }
        }

        /// <summary>
        /// Service Framework.
        /// </summary>
        public string ServiceFramework
        {
            get { return serviceFramework; }
            set { serviceFramework = value; }
        }

        /// <summary>
        /// Service Config File Path.
        /// </summary>
        public string ServiceConfigFilePath
        {
            get { return serviceConfigFilePath; }
            set { serviceConfigFilePath = value; }
        }

        /// <summary>
        /// XMLDrop File Path.
        /// </summary>
        public string XMLDropFilePath
        {
            get { return xMLDropFilePath; }
            set { xMLDropFilePath = value; }
        }

        /// <summary>
        /// BinPlaceForPackage File Path.
        /// </summary>
        public string BinPlaceForPackageFilePath
        {
            get { return binPlaceForPackageFilePath; }
            set { binPlaceForPackageFilePath = value; }
        }

        /// <summary>
        /// UserInput.
        /// </summary>
        public string Organization
        {
            get { return organization; }
            set { organization = value; }
        }

        /// <summary>
        /// Project.
        /// </summary>
        public string Project
        {
            get { return project; }
            set { project = value; }
        }

        /// <summary>
        /// Repository.
        /// </summary>
        public string Repository
        {
            get { return repository; }
            set { repository = value; }
        }

        /// <summary>
        /// ServiceCsprojFilePath, for binding redirect
        /// </summary>
        public string ServiceCsprojFilePath
        {
            get { return serviceCsprojFilePath; }
            set { serviceCsprojFilePath = value; }
        }

        /// <summary>
        /// top Packages
        /// </summary>
        public List<string> SelectedTopPackages
        {
            get { return selectedTopPackages; }
            set { selectedTopPackages = value; }
        }

        /// <summary>
        /// UseBinplaceDrop
        /// </summary>
        public bool UseBinplaceDrop
        {
            get { return useBinplaceDrop; }
            set { useBinplaceDrop = value; }
        }

        /// <summary>
        /// SelectedDestinations
        /// </summary>
        public List<XmlFullDestination> SelectedDestinations
        {
            get { return selectedDestinations; }
            set { selectedDestinations = value; }
        }

        /// <summary>
        /// UserInput.
        /// </summary>
        public bool Validate()
        {
            if (string.IsNullOrEmpty(serviceFramework) || string.IsNullOrEmpty(serviceConfigFilePath) || (string.IsNullOrEmpty(xMLDropFilePath) && string.IsNullOrEmpty(binPlaceForPackageFilePath)))
            {
                Console.WriteLine("Missing inputs!");
                return false;
            }
            if (!serviceConfigFilePath.EndsWith(".config", StringComparison.OrdinalIgnoreCase))
            {
                Console.WriteLine("Service Config File path invalid!");
                return false;
            }
            if (!string.IsNullOrEmpty(xMLDropFilePath) && !xMLDropFilePath.EndsWith("XmlDrop.xml", StringComparison.OrdinalIgnoreCase))
            {
                Console.WriteLine("XMLDrop.xml file path invalid!");
                return false;
            }
            if (!string.IsNullOrEmpty(binPlaceForPackageFilePath) && !binPlaceForPackageFilePath.EndsWith("BinPlaceForPackages.csproj", StringComparison.OrdinalIgnoreCase))
            {
                Console.WriteLine("BinPlaceForPackage.csproj file path invalid!");
                return false;
            }
            if (!string.IsNullOrEmpty(independentXMLDropFilePath) && !independentXMLDropFilePath.EndsWith("XmlDrop.xml", StringComparison.OrdinalIgnoreCase))
            {
                Console.WriteLine("independent XMLDrop.xml file path invalid!");
                return false;
            }
            return true;
        }
    }
}
