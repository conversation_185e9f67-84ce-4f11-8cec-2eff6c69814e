﻿// <copyright file="EUIIRedactor.netfx.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

#if NETFRAMEWORK
using System.Collections.Specialized;

namespace Microsoft.M365.Core.Telemetry.R9.Tracing.Instrumentation.Accelerator
{
    /// <summary>
    /// EUIIRedactor
    /// </summary>
    internal class EUIIRedactor
    {
        /// <summary>
        /// RedactIngressPath
        /// </summary>
        /// <param name="path"></param>
        /// <param name="redactionStrategyType"></param>
        /// <returns></returns>
        internal static string RedactIngressPath(string path, RedactionStrategyType redactionStrategyType)
        {
            switch (redactionStrategyType)
            {
                default:
                    return RedactIngressPathDefault(path);
            }
        }

    /// <summary>
        /// RedactEgressPath
        /// </summary>
        /// <param name="path"></param>
        /// <param name="redactionStrategyType"></param>
        /// <returns></returns>
        internal static string RedactEgressPath(string path, RedactionStrategyType redactionStrategyType)
        {
            switch (redactionStrategyType)
            {
                default:
                    return RedactEgressPathDefault(path);
            }
        }
        
#pragma warning disable CA1801 // Remove unused parameter
        private static string RedactIngressPathDefault(string path)
#pragma warning restore CA1801 // Remove unused parameter
        {
            return Constants.RedactedPlacholder;
        }

#pragma warning disable CA1801 // Remove unused parameter
        private static string RedactEgressPathDefault(string path)
#pragma warning restore CA1801 // Remove unused parameter
        {
            return Constants.RedactedPlacholder;
        }
    }
}
#endif