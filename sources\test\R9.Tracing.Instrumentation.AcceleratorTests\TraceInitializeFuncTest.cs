﻿// <copyright file="TraceInitializeFuncTest.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>
using System;
using System.Diagnostics;
#if !NETFRAMEWORK
using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.Hosting;
using Microsoft.R9.Extensions.Tracing.Http;
#endif
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.M365.Core.Telemetry.ECSClient;
using Microsoft.R9.Extensions.Enrichment;
using Microsoft.R9.Extensions.HttpClient.Tracing;
using OpenTelemetry.Trace;
using Xunit;

namespace Microsoft.M365.Core.Telemetry.R9.Tracing.Instrumentation.Accelerator.Tests
{
    /// <summary>
    /// TraceInitializeFuncTest
    /// </summary>
    public class TraceInitializeFuncTest
    {
        private IConfiguration configuration;

        /// <summary>
        /// TraceInitializeFuncTest
        /// </summary>
        public TraceInitializeFuncTest()
        {
            this.configuration = new ConfigurationBuilder().AddJsonFile("appsettings.json").Build();

            R9TracingConfig mockedR9TracingConfig = new R9TracingConfig(configuration)
            {
                R9DTEnabled = true,
                SamplerType = ECSClient.Constants.DynamicSamplerType.AlwaysOn,
                TraceSampleRate = 1
            };
            TracerStartupExtensions.MockedR9TracingConfig = mockedR9TracingConfig;
        }

#if NETFRAMEWORK
        /// <summary>
        /// DtInitializeByServiceCollectionWithValidConfiguration
        /// </summary>
        [Fact]
        public void DtInitializeByServiceCollectionWithValidConfiguration()
        {
            var services = new ServiceCollection(); 
            HttpTracingOptionsInherited options = new HttpTracingOptionsInherited
            {
                IsEnabled = true,
            };

            Action<TracerProviderBuilder> action = (builder) =>
            {
                builder.AddHttpTracing(options);
            };
            services.AddDistributedTracingService(this.configuration.GetSection("Microsoft_m365_core_telemetry:ServiceMetadata"), this.configuration.GetSection("Microsoft_m365_core_telemetry:Tracing"), action);
            
            var serviceProvider = services.StartServices();

            ServiceValidation(serviceProvider);

            ListenerValidation();

            serviceProvider.StopServices();
        }

        /// <summary>
        /// InitDistributedTracingServiceWithValidConfiguration
        /// </summary>
        [Fact]
        public void InitDistributedTracingServiceWithValidConfiguration()
        {
            var serviceProvider = TracerSdk.InitDistributedTracingService(this.configuration.GetSection("Microsoft_m365_core_telemetry:ServiceMetadata"), this.configuration.GetSection("Microsoft_m365_core_telemetry:Tracing"));

            ServiceValidation(serviceProvider);

            ListenerValidation();

            serviceProvider.StopServices();
        }

#else
        /// <summary>
        /// DtInitializeByServiceCollectionWithValidConfiguration
        /// </summary>
        [Fact]
        public void DtInitializeByServiceCollectionWithValidConfiguration()
        {
            var host = Host.CreateDefaultBuilder(null)
                .ConfigureWebHostDefaults(webBuilder =>
                {
                    webBuilder.UseStartup<MockStartup>();
                }).Build();

            host.StartAsync().ConfigureAwait(true);

            ServiceValidation(host.Services);

            ListenerValidation();

            host.StopAsync().ConfigureAwait(true);
        }
#endif

        private void ServiceValidation(IServiceProvider services)
        {
            Assert.NotNull(services.GetService<TracerProvider>());

            Assert.NotNull(services.GetServices<ITraceEnricher>());

            Assert.NotNull(services.GetServices<IHttpClientTraceEnricher>());
#if !NETFRAMEWORK
            Assert.NotNull(services.GetServices<IHttpTraceEnricher>());
#endif
        }

        private void ListenerValidation()
        {
#if NETFRAMEWORK
            var clientSource = new ActivitySource("OpenTelemetry.Instrumentation.AspNet.Telemetry");
            Assert.True(clientSource.HasListeners());

            clientSource = new ActivitySource("OpenTelemetry.Instrumentation.Http.HttpWebRequest");
            Assert.True(clientSource.HasListeners());
#elif NET6_0
            var clientSource = new ActivitySource("OpenTelemetry.Instrumentation.AspNetCore");
            Assert.True(clientSource.HasListeners());

            clientSource = new ActivitySource("OpenTelemetry.Instrumentation.Http.HttpClient");
            Assert.True(clientSource.HasListeners());
#elif NET8_0_OR_GREATER
            var clientSource = new ActivitySource("Microsoft.AspNetCore");
            Assert.True(clientSource.HasListeners());

            clientSource = new ActivitySource("System.Net.Http");
            Assert.True(clientSource.HasListeners());
#endif
            clientSource = new ActivitySource("Source1");
            Assert.True(clientSource.HasListeners());

            clientSource = new ActivitySource("Source2");
            Assert.True(clientSource.HasListeners());

            clientSource.Dispose();
        }
    }
}
