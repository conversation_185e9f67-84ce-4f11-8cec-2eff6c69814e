// <copyright file="HashBasedRandomStrategyTest.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Text.Json;
using System.Text.Json.Nodes;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using OpenTelemetry.Logs;
using Xunit;

namespace Microsoft.M365.Core.Telemetry.R9.Logging.Substrate.Test.SamplingTest
{
    public class HashBasedRandomStrategyTest
    {
        private const string LoggerName = "HashBasedRandomStrategyTest";

        private const string ConfigTemplate = @"
{
    ""SubstrateLogging"": {
        ""RuleBasedSampler"": {
            ""HashBasedRandomStrategyTest"": [
                {
                    ""Constraints"": [],
                    ""Strategy"": {
                        ""Type"": ""HashBasedRandom"",
                        ""SampleRate"": ""SampleRatePlaceholder"",
                        ""HashKey"": ""HashKeyPlaceholder""
                    }
                }
            ]
        }
    }
}";

        private static string ReplaceConfigPlaceholder(double sampleRate, string? hashKey)
        {
            var config = ConfigTemplate
                .Replace("SampleRatePlaceholder", sampleRate.ToString());

            if (hashKey != null)
            {
                config = config.Replace("HashKeyPlaceholder", hashKey);
            }
            else
            {
                // Use JSON API to handle the null hash key case
                var jsonObject = JsonNode.Parse(config)?.AsObject();
                if (jsonObject == null)
                {
                    throw new InvalidOperationException("Failed to parse config template as JSON");
                }

                // Navigate to the Strategy node
                var strategyNode = jsonObject["SubstrateLogging"]?["RuleBasedSampler"]?["HashBasedRandomStrategyTest"]?[0]?["Strategy"]?.AsObject();
                if (strategyNode == null)
                {
                    throw new InvalidOperationException("Failed to find Strategy node in config template");
                }

                // Remove the HashKey property entirely if hashKey is null
                strategyNode.Remove("HashKey");

                // Convert back to JSON string with indentation for readability
                var options = new JsonSerializerOptions { WriteIndented = true };
                config = jsonObject.ToJsonString(options);
            }

            return config;
        }

        [Fact]
        public void HashKeyNotFound_ShouldSampleAllLogs()
        {
            double sampleRate = 0.5;
            string hashKey = "NonExistentKey";
            var exportedItems = new List<LogRecord>();
            var config = ReplaceConfigPlaceholder(sampleRate, hashKey);
            var logger = LoggerTestUtils.CreateLoggerWithConfig(config, exportedItems, LoggerName);

            // Log without the specified hash key
            int logCount = 100;
            for (int i = 0; i < logCount; i++)
            {
                logger.LogInformation("Test message with different key {DifferentKey}", $"value_{i}");
            }

            // All logs should be sampled when hash key is not found
            Assert.Equal(logCount, exportedItems.Count);
        }

        [Fact]
        public void HashKeyIsNullInConfig_ShouldSampleAllLogs()
        {
            double sampleRate = 0.0;
            string? hashKey = null; // Null hash key should be invalid
            var exportedItems = new List<LogRecord>();
            var config = ReplaceConfigPlaceholder(sampleRate, hashKey);
            var logger = LoggerTestUtils.CreateLoggerWithConfig(config, exportedItems, LoggerName);

            // Log multiple messages
            int logCount = 100;
            for (int i = 0; i < logCount; i++)
            {
                logger.LogInformation("Test message");
            }

            Assert.Equal(logCount, exportedItems.Count);
        }

        [Fact]
        public void HashValueIsNull_ShouldSampleAllLogs()
        {
            double sampleRate = 0.5;
            string hashKey = "UserId";
            var exportedItems = new List<LogRecord>();
            var config = ReplaceConfigPlaceholder(sampleRate, hashKey);
            var logger = LoggerTestUtils.CreateLoggerWithConfig(config, exportedItems, LoggerName);

            // Log with null value for the hash key
            int logCount = 100;
            for (int i = 0; i < logCount; i++)
            {
                logger.LogInformation("Test message {UserId}", null);
            }

            // All logs should be sampled when hash key value is null
            Assert.Equal(logCount, exportedItems.Count);
        }

        [Theory]
        [InlineData("user test")]
        [InlineData("")]
        public void SameHashValue_ShouldSampleAllOrNon(string hashValue)
        {
            double sampleRate = 0.5;
            string hashKey = "UserId";
            var exportedItems = new List<LogRecord>();
            var config = ReplaceConfigPlaceholder(sampleRate, hashKey);
            var logger = LoggerTestUtils.CreateLoggerWithConfig(config, exportedItems, LoggerName);

            // Log with empty string value for the hash key
            int logCount = 100;
            for (int i = 0; i < logCount; i++)
            {
                logger.LogInformation("Test message {UserId}", hashValue);
            }

            // Empty string should be used for sampling, so all logs should have the same sampling decision
            bool? firstResult = null;
            foreach (var item in exportedItems)
            {
                if (firstResult == null)
                {
                    firstResult = true;
                }
                else
                {
                    // All sampling decisions should be the same for the same empty string
                    Assert.True(firstResult);
                }
            }

            // But since we're using the same empty string for all logs, they should all have the same decision
            // So we'll either have 0 or logCount logs sampled
            Assert.True(exportedItems.Count == 0 || exportedItems.Count == logCount);
        }

        [Fact]
        public void SampleRateIsZero_ShouldNotSampleAnyLogs()
        {
            double sampleRate = 0.0;
            string hashKey = "UserId";
            var exportedItems = new List<LogRecord>();
            var config = ReplaceConfigPlaceholder(sampleRate, hashKey);
            var logger = LoggerTestUtils.CreateLoggerWithConfig(config, exportedItems, LoggerName);

            // Log with various user IDs
            int logCount = 100;
            for (int i = 0; i < logCount; i++)
            {
                logger.LogInformation("Test message {UserId}", $"user_{i}");
            }

            // No logs should be sampled when sample rate is 0
            Assert.Empty(exportedItems);
        }

        [Fact]
        public async Task MultiThread_SameHashValue_ShouldBeConsistentPerThread()
        {
            string hashKey = "UserId";
            int threadCount = 2;
            int logsPerThread = 1000;
            var threadResults = new ConcurrentDictionary<int, List<bool>>();
            var config = ReplaceConfigPlaceholder(0.5, hashKey);

            var tasks = Enumerable.Range(0, threadCount)
                .Select(threadId => Task.Run(() =>
                {
                    var threadExportedItems = new List<LogRecord>();
                    var threadLogger = LoggerTestUtils.CreateLoggerWithConfig(config, threadExportedItems, LoggerName);
                    var samplingResults = new List<bool>();

                    for (int i = 0; i < logsPerThread; i++)
                    {
                        var before = threadExportedItems.Count;
                        threadLogger.LogInformation("Test message {UserId}", $"userId_{i}");
                        samplingResults.Add(threadExportedItems.Count > before);
                    }
                    threadResults[threadId] = samplingResults;
                }));

            await Task.WhenAll(tasks);

            var threadPairs = threadResults.Keys
                .SelectMany(t1 => threadResults.Keys.Where(t2 => t2 > t1)
                    .Select(t2 => (t1, t2)));

            foreach (var (thread1, thread2) in threadPairs)
            {
                var sequence1 = threadResults[thread1];
                var sequence2 = threadResults[thread2];

                // Sequences should be exactly the same
                Assert.Equal(sequence1, sequence2);
            }
        }

        [Theory]
        [CombinatorialData]
        public async Task MultiThread_WithFullSampling_ShouldCaptureAllLogs(
            [CombinatorialValues(10, 100)] int threadCount,
            [CombinatorialValues(1000, 10000)] int logsPerThread)
        {
            double expectedRate = 1.0; // 100% sampling rate
            string hashKey = "UserId";
            int totalLogs = threadCount * logsPerThread;
            int expectedSampledCount = totalLogs; // No deviation allowed for 100% sampling rate

            var threadResults = new ConcurrentDictionary<int, List<LogRecord>>();
            var config = ReplaceConfigPlaceholder(expectedRate, hashKey);

            var tasks = Enumerable.Range(0, threadCount)
                .Select(threadId => Task.Run(() =>
                {
                    var threadExportedItems = new List<LogRecord>();
                    var threadLogger = LoggerTestUtils.CreateLoggerWithConfig(config, threadExportedItems, LoggerName);

                    for (int i = 0; i < logsPerThread; i++)
                    {
                        threadLogger.LogInformation("Test message {UserId}", $"user{threadId}_{i}");
                    }

                    threadResults[threadId] = threadExportedItems;
                }));

            await Task.WhenAll(tasks);

            int totalSampledCount = threadResults.Values.Sum(items => items.Count);
            Assert.Equal(expectedSampledCount, totalSampledCount);
        }

        [Fact]
        public void CustomStateObject_ShouldSampleAllLogs()
        {
            double sampleRate = 0;
            string hashKey = "UserId";
            var exportedItems = new List<LogRecord>();
            var config = ReplaceConfigPlaceholder(sampleRate, hashKey);
            var logger = LoggerTestUtils.CreateLoggerWithConfig(config, exportedItems, LoggerName);

            // Log with a custom state object that doesn't implement IReadOnlyList<KeyValuePair<string, object?>>
            int logCount = 100;
            for (int i = 0; i < logCount; i++)
            {
                // Using anonymous class that has the same property name as the hash key
                // but doesn't implement IReadOnlyList<KeyValuePair<string, object?>>
                var customState = new { UserId = $"user_{i}", Value = i };

                logger.Log(LogLevel.Information, new EventId(0), customState, null,
                    (state, ex) => $"Test message with custom state object: {state}");
            }

            // All logs should be sampled when state is not of expected type
            Assert.Equal(logCount, exportedItems.Count);
        }

        [Fact]
        public void PrimitiveStateType_ShouldSampleAllLogs() 
        {
            double sampleRate = 0;
            string hashKey = "UserId"; // This hash key won't be found in primitive state
            var exportedItems = new List<LogRecord>();
            var config = ReplaceConfigPlaceholder(sampleRate, hashKey);
            var logger = LoggerTestUtils.CreateLoggerWithConfig(config, exportedItems, LoggerName);

            // Log with primitive types as state
            int logCount = 100;
            for (int i = 0; i < logCount; i++)
            {
                // Using an integer as state - this doesn't have the hash key property
                logger.Log(LogLevel.Information, new EventId(0), i, null,
                    (state, ex) => $"Test message with int state: {state}");
            }

            // All logs should be sampled when state is not of expected type,
            Assert.Equal(logCount, exportedItems.Count);
        }

        [Fact]
        public void NullState_ShouldSampleAllLogs()
        {
            double sampleRate = 0;
            string hashKey = "UserId";
            var exportedItems = new List<LogRecord>();
            var config = ReplaceConfigPlaceholder(sampleRate, hashKey);
            var logger = LoggerTestUtils.CreateLoggerWithConfig(config, exportedItems, LoggerName);

            // Log with null state
            int logCount = 100;
            for (int i = 0; i < logCount; i++)
            {
                // Using null as state
                logger.Log<object>(LogLevel.Information, new EventId(0), null, null,
                    (state, ex) => "Test message with null state");
            }

            // All logs should be sampled when state is null
            Assert.Equal(logCount, exportedItems.Count);
        }

        [Fact]
        public void StringState_ShouldSampleAllLogs()
        {
            double sampleRate = 0;
            string hashKey = "UserId";
            var exportedItems = new List<LogRecord>();
            var config = ReplaceConfigPlaceholder(sampleRate, hashKey);
            var logger = LoggerTestUtils.CreateLoggerWithConfig(config, exportedItems, LoggerName);

            // Log with string as state
            int logCount = 100;
            for (int i = 0; i < logCount; i++)
            {
                // Using a string as state
                logger.Log(LogLevel.Information, new EventId(0), $"Simple string message {i}", null,
                    (state, ex) => state);
            }

            // All logs should be sampled when state is not of expected type
            Assert.Equal(logCount, exportedItems.Count);
        }
    }
}