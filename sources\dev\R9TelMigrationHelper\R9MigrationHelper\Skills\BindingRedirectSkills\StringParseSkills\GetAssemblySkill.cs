﻿// <copyright file="GetAssemblySkill.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System.Xml;
using System.Xml.Linq;
using R9MigrationHelper.Model;

namespace R9MigrationHelper.Skills.BindingRedirectSkills.StringParseSkills
{
    /// <summary>
    /// GetAssemblySkill.
    /// </summary>
    public class GetAssemblySkill
    {
        /// <summary>
        /// CommonPackages.
        /// </summary>
        private HashSet<string> commonPackages = new HashSet<string>()
        {
            "Microsoft.M365.Core.Telemetry.Enrichment",
            "Microsoft.M365.Core.Telemetry.R9",
            "Microsoft.Extensions.DependencyInjection",
            "Microsoft.R9.Extensions.Options.Validation",
            "OpenTelemetry.Exporter.Geneva",
            "Microsoft.M365.Core.PassiveMonitoring",
            "Microsoft.R9.Extensions.Httpclient.Tracing",
            "Microsoft.R9.Extensions.Redaction.Abstractions",
            "Microsoft.R9.Extensions.Redaction",
            "Microsoft.R9.Extensions.Tracing.Exporters.Geneva",
            "Microsoft.Extensions.Hosting"
        };

        /// <summary>
        /// GetPackageList.
        /// </summary>
        /// <param name="csProjContent">csProjContent.</param>
        public HashSet<string> GetPackageList(string csProjContent)
        {
            if (string.IsNullOrEmpty(csProjContent))
            {
                return new HashSet<string>();
            }
            XElement csProjContentXml = XElement.Parse(csProjContent);
            HashSet<string> packageReference = new HashSet<string>();
            foreach (XElement packageReferenceElement in csProjContentXml.Descendants("PackageReference"))
            {
                if (packageReferenceElement.Attribute("Include") != null)
                {
                    packageReference.Add(packageReferenceElement.Attribute("Include")!.Value);
                }
            }
            packageReference.UnionWith(commonPackages);
            return packageReference;
        }

        /// <summary>
        /// GetPackageList.
        /// </summary>
        /// <param name="dllList">dllList.</param>
        /// <param name="sharedBindingRedirectContent">sharedBindingRedirectContent.</param>
        public void GetAssemblyFromSharedBindingRedirect(List<AssemblyModel> dllList, string sharedBindingRedirectContent)
        {
            if (string.IsNullOrEmpty(sharedBindingRedirectContent))
            {
                return;
            }
            Dictionary<string, AssemblyModel> dict = new Dictionary<string, AssemblyModel>();
            foreach (AssemblyModel assemblyModel in dllList) { dict.Add(assemblyModel.Name.Split(".dll")[0], assemblyModel); }

            XmlDocument xelementServiceConfigFile = new XmlDocument();
            xelementServiceConfigFile.LoadXml(sharedBindingRedirectContent);

            string xmlns = "urn:schemas-microsoft-com:asm.v1";
            XmlNamespaceManager mgr = new XmlNamespaceManager(xelementServiceConfigFile.NameTable);
            mgr.AddNamespace("ns", xmlns);
            XmlNode assemblyBinding = xelementServiceConfigFile.SelectSingleNode("//ns:assemblyBinding", mgr)!;
            foreach (XmlNode dependentAssembly in assemblyBinding.ChildNodes)
            {
                XmlNode identity = dependentAssembly.SelectSingleNode("ns:assemblyIdentity", mgr)!;
                if (!dict.ContainsKey(identity.Attributes!["name"] !.Value)) { continue; }
                AssemblyModel assemblyModel = dict[identity.Attributes!["name"] !.Value];
                XmlNode bindingRedirect = dependentAssembly.SelectSingleNode("ns:bindingRedirect", mgr)!;
                assemblyModel.Version = bindingRedirect.Attributes!["newVersion"] !.Value;
                assemblyModel.PublicKeyToken = identity.Attributes!["publicKeyToken"] !.Value;
            }
        }
    }
}
