﻿// <copyright file="SampleLogMessages.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using Microsoft.Extensions.Logging;

namespace DISampleConsoleApp
{
    public static partial class SampleLogMessages
    {
        /// <summary>
        /// Test log with simple int
        /// </summary>
        /// <param name="logger"></param>
        /// <param name="count"></param>
        /// <param name="msg"></param>
        [LoggerMessage(
            EventId = 1,
            Level = LogLevel.Information,
            Message = "{Msg} Test Count: {Count}.")]
        public static partial void TestLogSimple(
            this ILogger logger, int count, string msg);

        /// <summary>
        /// Test log with object
        /// </summary>
        /// <param name="logger"></param>
        /// <param name="complex"></param>
        [LoggerMessage(
            EventId = 3, Level = LogLevel.Information, Message = "Test Object: {Complex}")]
        public static partial void TestLogObject(
            this ILogger logger,
    #pragma warning disable EXTEXP0003 // Type is for evaluation purposes only and is subject to change or removal in future updates. Suppress this diagnostic to proceed.
            [LogProperties(OmitReferenceName = true, SkipNullProperties = true, Transitive = true)] ComplexData complex);
    #pragma warning restore EXTEXP0003 // Type is for evaluation purposes only and is subject to change or removal in future updates. Suppress this diagnostic to proceed.
    }

    /// <summary>
    /// sample data for fast logging
    /// </summary>
    /// <param name="count"></param>
    /// <param name="msg"></param>
    public class BaseData(int count, string msg)
    {
        /// <summary>
        /// count
        /// </summary>
        public int Count { get; } = count;

        /// <summary>
        /// message
        /// </summary>
        public string Msg { get; } = msg;
    }

    /// <summary>
    /// Root
    /// </summary>
    /// <param name="msg"></param>
    public class RootData(string msg)
    {
        /// <summary>
        /// RootMsg
        /// </summary>
        public string RootMsg { get; } = msg;
    }

    /// <summary>
    /// sample data for nested fast logging
    /// </summary>
    /// <param name="msg"></param>
    public class SimpleData(string msg)
    {
        /// <summary>
        /// Root
        /// </summary>
        public RootData Root { get; } = new RootData("Msg from RootData");

        /// <summary>
        /// nested message
        /// </summary>
        public string NestedMsg { get; } = msg;
    }

    /// <summary>
    /// sample data for fast logging. With derive and nested object.
    /// </summary>
    /// <remarks>
    /// constructor
    /// </remarks>
    /// <param name="count"></param>
    /// <param name="msg"></param>
    /// <param name="extra"></param>
    public class ComplexData : BaseData
    {
        /// <summary>
        /// nested data
        /// </summary>
        public SimpleData Simple { get; }

        /// <summary>
        /// extra data
        /// </summary>
        public string Extra { get; }

        /// <summary>
        /// constructor
        /// </summary>
        /// <param name="count"></param>
        /// <param name="msg"></param>
        /// <param name="extra"></param>
        public ComplexData(int count, string msg, string extra) : base(count, msg)
        {
            Simple = new SimpleData("Msg from SimpleData");
            Extra = extra;
        }
    }
}

