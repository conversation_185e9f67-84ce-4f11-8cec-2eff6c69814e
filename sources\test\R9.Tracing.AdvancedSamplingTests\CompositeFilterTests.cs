﻿// <copyright file="CompositeFilterTests.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System.Diagnostics;
using Microsoft.M365.Core.Telemetry.R9.Tracing.AdvancedSampling;
using OpenTelemetry.Exporter.Filters;
using Xunit;

namespace R9.Tracing.AdvancedSamplingTests
{
    /// <summary>
    /// CompositeFilterTests
    /// </summary>
    public class CompositeFilterTests
    {
        /// <summary>
        /// Test PostTrace Filter
        /// </summary>
        [Fact]
        public void TestFilter()
        {
            BaggageFilter postTraceFilter = new BaggageFilter();
            Assert.False(postTraceFilter.ShouldFilter(null));

            var activity = new Activity("activity1");
            Assert.False(postTraceFilter.ShouldFilter(activity));

            var activityWithBaggage = new Activity("activity2");
            activityWithBaggage.SetBaggage(Constants.PostTraceBaggageName, "true");
            Assert.True(postTraceFilter.ShouldFilter(activityWithBaggage));

            SpecificTagFilter specificTagFilter = new SpecificTagFilter(new Dictionary<string, string> { { "FilterTag", "FilterValue" } });
            CompositeFilter compositeFilter = new CompositeFilter(new BaseFilter<Activity>[] { specificTagFilter,  postTraceFilter });

            var activityWithTag = new Activity("activity3");
            activityWithTag.SetTag("FilterTag", "FilterValue");

            Assert.False(compositeFilter.ShouldFilter(activity));
            Assert.True(compositeFilter.ShouldFilter(activityWithTag));
            Assert.True(compositeFilter.ShouldFilter(activityWithBaggage));
            Assert.NotEmpty(compositeFilter.GetDescription());
        }
    }
}