{
  "SubstrateLogging": {
    "UseRuleBasedSampler": true
    //  "RuleBasedSampler": { // Attention: Local configuration could be overridden by the ECS service.
    //    "SamplingDemoCategory": [
    //      {
    //        "Constraints": [
    //          {
    //            "Field": "Agent",
    //            "Type": "Enum",
    //            "Operator": "In",
    //            "Value": "Copilot,DeepSeek"
    //          }
    //        ],
    //        "Strategy": {
    //          "Type": "Random",
    //          "SampleRate": 0.7
    //        }
    //      },
    //      {
    //        "Constraints": [
    //          {
    //            "Field": "Severity",
    //            "Type": "Long",
    //            "Operator": "<=",
    //            "Value": "2"
    //          }
    //        ],
    //        "Strategy": {
    //          "Type": "HashBasedRandom",
    //          "SampleRate": 0.3,
    //          "HashKey": "UserId"
    //        }
    //      }
    //    ]
    //  }
    //},
  },
  "ECSParameters": { // This section is used in ConfigurationHelper.LoadConfiguration method.
    "ECSIdentifiers": {
      "ServiceName": "SamplingDemoService"
    },
    "EnvironmentType": "Integration",
    "Client": "UnifiedTelemetry",
    "Agents": [ "Log" ]
  }
}
