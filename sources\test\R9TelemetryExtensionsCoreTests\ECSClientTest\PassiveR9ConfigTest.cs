﻿// <copyright file="PassiveR9ConfigTest.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.IO;
using System.Reflection;
using System.Text;
using Microsoft.Extensions.Configuration;
using Microsoft.M365.Core.Telemetry.ECSClient;
using Microsoft.Skype.ECS.Client;
using Newtonsoft.Json.Linq;
using NSubstitute;
using Xunit;

namespace Microsoft.M365.Core.Telemetry.ECSClientTest
{
    /// <summary>
    /// Tests to validate PassiveR9Config code.
    /// </summary>
    [Collection("Non-Parallel Collection")]
    public class PassiveR9ConfigTest : IDisposable
    {
        private PassiveR9Config config;
        private Dictionary<string, string> ecsContext;
        private UnifiedTelemetryECSClient ecsClient;

        /// <summary>
        /// Setup.
        /// </summary>
        public PassiveR9ConfigTest()
        {
            var mockConfiguration = new Dictionary<string, string>
            {
                ["Microsoft_m365_core_telemetry:ServiceMetadata:ServiceName"] = "Pop3Test",
                ["Microsoft_m365_core_telemetry:ServiceMetadata:RuntimeModel"] = "ModelB2"
            };

            var configuration = new ConfigurationBuilder()
                .AddInMemoryCollection(mockConfiguration)
                .Build();
            config = new PassiveR9Config(configuration);
            ecsContext = TestUtils.GetPrivateField<Dictionary<string, string>>(config, "ecsContext");
            ecsClient = TestUtils.GetPrivateField<UnifiedTelemetryECSClient>(config, "ecsClient");
        }

        /// <summary>
        /// Cleanup.
        /// </summary>
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        /// <summary>
        /// Basic Dispose Pattern.
        /// </summary>
        /// <param name="disposing">disposing boolean.</param>
        protected virtual void Dispose(bool disposing)
        {
            if (disposing)
            {
                TestUtils.SetPrivateStaticField(typeof(UnifiedTelemetryECSClient), "ecsInstance", null);
            }
        }

        // Set up mock ecs.
        private void SetupMockECS(string appsettings_str)
        {
            JObject configRoot = JObject.Parse(appsettings_str);

            IECSConfigSettings mockConfigSettings = Substitute.For<IECSConfigSettings>();
            object unusedObjectArg = default(object);
            mockConfigSettings.TryGetRootValue("PassiveMon", out unusedObjectArg).Returns(r =>
            {
                r[1] = configRoot;
                return true;
            });

            IECSConfigurationRequester mockEcsRequester = Substitute.For<IECSConfigurationRequester>();
            SettingsETag settingsETag = new SettingsETag(mockConfigSettings, "FakeETag");
            mockEcsRequester.GetSettings(ecsContext).Returns(settingsETag);
            ecsClient.EcsRequester = mockEcsRequester;

            TestUtils.InvokePrivateMethod<object>(config, "UpdateConfigValue", new object[] { null, new HashSet<string>() { "PassiveMon" } });
        }

        // Get config object from private field.
        private IConfiguration GetConfigFromPrivateField(string field)
        {
            var bindingFlags = BindingFlags.Public | BindingFlags.NonPublic | BindingFlags.Instance;
            var builder = new ConfigurationBuilder();
            builder.AddJsonStream(
                new MemoryStream(
                    Encoding.ASCII.GetBytes(
                        (string)typeof(PassiveR9Config).GetField(field, bindingFlags).GetValue(config))));
            return builder.Build();
        }

        /// <summary>
        /// Test ECSContext.
        /// </summary>
        [Fact]
        public void TestECSContext()
        {
            Assert.True(ecsContext.Count == 10);
            Assert.True(ecsContext.TryGetValue("ServiceName", out string serviceName));
            Assert.Equal("Pop3Test", serviceName);
        }

        /// <summary>
        /// Test UpdateConfigValue with wrong agent.
        /// </summary>
        [Fact]
        public void TestUpdateConfigValueWithWrongAgent()
        {
            IECSConfigurationRequester ecsRequester = Substitute.For<IECSConfigurationRequester>();

            TestUtils.InvokePrivateMethod<object>(config, "UpdateConfigValue", new object[] { null, new HashSet<string>() { "testAgent" } });
        }

        /// <summary>
        /// Test UpdateConfigValue without config root.
        /// </summary>
        [Fact]
        public void TestUpdateConfigValueWithoutConfigRootJustPass()
        {
            IECSConfigSettings mockConfigSettings = Substitute.For<IECSConfigSettings>();
            mockConfigSettings.TryGetRootValue("PassiveMon", out _).Returns(r =>
            {
                r[1] = null;
                return true;
            });
            SettingsETag settingsETag = new SettingsETag(mockConfigSettings, "FakeETag");
            IECSConfigurationRequester ecsRequester = Substitute.For<IECSConfigurationRequester>();
            ecsRequester.GetSettings(ecsContext).Returns(settingsETag);
            ecsClient.EcsRequester = ecsRequester;
            TestUtils.InvokePrivateMethod<object>(config, "UpdateConfigValue", new object[] { null, new HashSet<string>() { "PassiveMon" } });
        }

        /// <summary>
        /// Tests UpdateConfigValue when fail to parse json and set with default val.
        /// </summary>
        [Fact]
        public void TestUpdateConfigValueFailToParseVerifyDefaultVal()
        {
            SetupMockECS(@"{""ConfigurationSettings"": ""NoValid""}");

            Assert.False(config.R9EventEnabled);
            Assert.False(config.R9MetricEnabled);
            Assert.True(config.IfxEventEnabled);
            Assert.True(config.IfxMetricEnabled);
            Assert.False(config.IsDebugInfoCollectionEnabled);
            Assert.False(config.MdsTraceEnabled);
            Assert.True(string.IsNullOrEmpty(config.IncludedTraceLog));
        }

        /// <summary>
        /// Tests enable flags are parsed.
        /// </summary>
        [Fact]
        public void TestUpdateConfigValueEnableFlags()
        {
            SetupMockECS(@"{
""R9EventEnabled"": ""true"",
""R9MetricEnabled"": ""true"",
""IfxMetricEnabled"": ""false"",
""IfxEventEnabled"": ""false""
}");
            Assert.True(config.R9EventEnabled);
            Assert.True(config.R9MetricEnabled);
            Assert.False(config.IfxEventEnabled);
            Assert.False(config.IfxMetricEnabled);
        }

        /// <summary>
        /// Tests selectively enable.
        /// </summary>
        [Fact]
        public void TestUpdateConfigValueSelectedTelemetry()
        {
            SetupMockECS(@"{
""DisabledIfxEvents"": [""FooEvent"", ""BarEvent""],
""EnabledR9Events"": [""BazEvent""],
""DisabledIfxMetrics"": [""FooMetric"", ""BarMetric""],
""EnabledR9Metrics"": [""BazMetric""],
}");
            Assert.True(config.EventDisabledForIfx("FooEvent"));
            Assert.False(config.EventDisabledForIfx("BazEvent"));
            Assert.True(config.EventEnabledForR9("BazEvent"));
            Assert.True(config.MetricDisabledForIfx("BarMetric"));
            Assert.True(config.MetricEnabledForR9("BazMetric"));
        }

        /// <summary>
        /// Tests debug flags are parsed.
        /// </summary>
        [Fact]
        public void TestUpdateConfigValueDebugFlags()
        {
            SetupMockECS(@"{
""Debug_MdsTraceEnabled"": ""true"",
""Debug_IsDebugInfoCollectionEnabled"": ""true"",
""Debug_IncludedTraceLog"": ""1001"", 
}");
            Assert.True(config.IsDebugInfoCollectionEnabled);
            Assert.True(config.MdsTraceEnabled);
            Assert.Equal("1001", config.IncludedTraceLog);
        }

        /// <summary>
        /// Tests library config is parsed.
        /// </summary>
        [Fact]
        public void TestUpdateConfigValueLibraryConfig()
        {
            SetupMockECS(@"{
""InternalR9Config"": {
  ""Logging"": {
    ""GenevaLogging"": {
      ""ConnectionString"": ""EtwSession=o365PassiveMonitoringSessionR9"",
      ""TableNameMappings"": {
        ""PassiveSamples.CustomExceptionEvent"": ""CustomEventR9""
      }
    }
  },
  ""Metering"": {
    ""GenevaMetering"": {
      ""Protocol"": ""Etw"",
      ""MonitoringAccount"": ""O365_Monitoring_SDK"",
      ""MonitoringNamespace"": ""R9EventCounter"",
      ""MonitoringNamespaceOverrides"": {
        ""Microsoft.Office.Datacenter.FakeSDK.FakeMetric"": ""FakeMetricR9Metric""
      }
    }
  }
}
}");
            IConfiguration libraryOption = GetConfigFromPrivateField("libraryR9Config");
            Assert.Equal("EtwSession=o365PassiveMonitoringSessionR9", libraryOption.GetSection("Logging:GenevaLogging:ConnectionString").Value);
            Assert.Equal("O365_Monitoring_SDK", libraryOption.GetSection("Metering:GenevaMetering:MonitoringAccount").Value);
        }

        /// <summary>
        /// Tests service config is parsed.
        /// </summary>
        [Fact]
        public void TestUpdateConfigValueServiceConfig()
        {
            SetupMockECS(@"{
""Metering"": {
  ""GenevaMetering"": {
    ""Protocol"": ""Etw"",
    ""MonitoringAccount"": ""O365_Monitoring_Pop"",
    ""MonitoringNamespace"": ""R9EventCounter"",
    ""MonitoringNamespaceOverrides"": {
    ""Microsoft.Exchange.PopImap.Core.Metrics.Directory.DirectoryLatencyMetric"": ""PassiveR9ECS"",
    ""Microsoft.Exchange.PopImap.Core.Metrics.Message.Pop3MessageApiLatencyMetric"": ""PassiveR9ECS""
    }
  }
},
""Logging"": {
  ""GenevaLogging"": {
    ""ConnectionString"": ""EtwSession=o365PassiveMonitoringSessionR9"",
    ""TableNameMappings"": {
      ""*"": ""ImapProtocolEventR9""
    }
  }
}
}");
            IConfiguration serviceLogOption = GetConfigFromPrivateField("serviceLogR9Config");
            Assert.Equal("ImapProtocolEventR9", serviceLogOption.GetSection("GenevaLogging:TableNameMappings:*").Value);

            IConfiguration serviceMetricOption = GetConfigFromPrivateField("serviceMetricR9Config");
            Assert.Equal("O365_Monitoring_Pop", serviceMetricOption.GetSection("GenevaMetering:MonitoringAccount").Value);
        }
    }
}
