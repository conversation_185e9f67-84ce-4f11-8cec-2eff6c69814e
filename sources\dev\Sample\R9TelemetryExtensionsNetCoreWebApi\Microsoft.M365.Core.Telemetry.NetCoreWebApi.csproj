﻿<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <RootNamespace>Microsoft.M365.Core.Telemetry.NetCoreWebApi</RootNamespace>
    <AssemblyName>Microsoft.M365.Core.Telemetry.NetCoreWebApi</AssemblyName>
    <OutputType>Exe</OutputType>
    <TargetFrameworks>net6.0;net8.0</TargetFrameworks>
    <Description>This project can be used to test if accelerator can generate tracing data.</Description>
  </PropertyGroup>

  <ItemGroup> 
    <PackageReference Include="Newtonsoft.Json" />
    <PackageReference Include="Microsoft.Extensions.Hosting" />
    <PackageReference Include="OpenTelemetry.Exporter.Geneva" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\R9.Tracing.Instrumentation.Accelerator\R9.Tracing.Instrumentation.Accelerator.csproj" />
  </ItemGroup>

  <ItemGroup>
    <None Update="appsettings.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
  </ItemGroup>

</Project>