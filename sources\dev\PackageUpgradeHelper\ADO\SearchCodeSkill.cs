﻿// <copyright file="SearchCodeSkill.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http.Headers;
using System.Text;
using System.Text.Json.Serialization;
using System.Threading.Tasks;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;

namespace PackageUpgradeHelper.ADO
{
    /// <summary>
    /// Search code skill
    /// </summary>
    class SearchCodeSkill
    {
        private readonly string org = Globals.ORGANIZATION;
        private readonly string proj = Globals.PROJECT;
        private readonly string repo = Globals.REPOSITORY;
        private readonly string apiVersion = Globals.ADO_API_VERSION;
        private readonly string pat = Globals.PAT;

        private const int TOP_LIMIT = 1000;

        /// <summary>
        /// Search code 
        /// </summary>
        /// <param name="searchText"></param>
        /// <param name="basePath"></param>
        /// <returns></returns>
        public async Task<List<ADOFileInfo>> SearchCodeAsync(string searchText, string basePath = "/")
        {
            using HttpClient client = new HttpClient();
            client.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));
            client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Basic", Convert.ToBase64String(Encoding.ASCII.GetBytes(string.Format("{0}:{1}", string.Empty, pat))));
            var url = $"https://almsearch.dev.azure.com/{org}/_apis/search/codesearchresults?api-version={apiVersion}";
            // Attention: In Substrate repo, it is only allowed to search in master branch
            var filter = new Filter(new List<string> { proj }, new List<string> { repo }, new List<string> { "master" }, new List<string> { basePath });
            int searchTop = TOP_LIMIT;
            int searchSkip = 0;

            // Search first time to get the count of the result
            var requestBody = new RequestBody(searchText, filter, searchTop, searchSkip, null);
            var httpContent = new StringContent(JsonConvert.SerializeObject(requestBody), Encoding.UTF8, "application/json");
            var response = await client.PostAsync(url, httpContent).ConfigureAwait(false);
            var message = await response.Content.ReadAsStringAsync().ConfigureAwait(false);
            var json = JObject.Parse(message);
            var fileCount = int.Parse(json["count"]!.ToString());

            var ret = new List<ADOFileInfo>();
            while (fileCount > 0)
            {
                requestBody = new RequestBody(searchText, filter, searchTop, searchSkip, null);
                httpContent = new StringContent(JsonConvert.SerializeObject(requestBody), Encoding.UTF8, "application/json");
                response = await client.PostAsync(url, httpContent).ConfigureAwait(false);
                message = await response.Content.ReadAsStringAsync().ConfigureAwait(false);
                json = JObject.Parse(message);
                foreach (var file in json["results"]!)
                {
                    var fileName = $"{file["fileName"]}";
                    var path = $"{file["path"]}";
                    var project = $"{file["project"]}";
                    var projectName = $"{file["project"]!["name"]}";
                    var projectId = $"{file["project"]!["id"]}";
                    var repoName = $"{file["repository"]!["name"]}";
                    var repoId = $"{file["repository"]!["id"]}";
                    ret.Add(new ADOFileInfo(fileName, path, projectName, projectId, repoName, repoId));
                }
                searchSkip += searchTop;
                fileCount -= searchTop;
            }
            return ret;
        }
    }

    /// <summary>
    /// Requset body required by code search api
    /// </summary>
    internal class RequestBody
    {
        /// <summary>
        /// Search Text
        /// </summary>
        [JsonPropertyName("searchText")]
        public string SearchText { get; set; }

        /// <summary>
        /// Filter
        /// </summary>
        [JsonPropertyName("filters")]
        public Filter Filters { get; set; }

        /// <summary>
        /// Number of results to return, limit 1000
        /// </summary>
        [JsonProperty(PropertyName = "$top")]
        public int Top { get; set; }

        /// <summary>
        /// Number of results to skip, limit 5000
        /// </summary>
        [JsonProperty(PropertyName = "$skip")]
        public int Skip { get; set; }

        /// <summary>
        /// Order by
        /// Valid value = {"ASC", "DESC"}
        /// </summary>
        [JsonProperty(PropertyName = "$orderBy")]
        public List<Dictionary<string, string>>? OrderBy { get; set; }

        /// <summary>
        /// Public constructor for user input
        /// </summary>
        /// <param name="searchText"></param>
        /// <param name="filter"></param>
        /// <param name="top"></param>
        /// <param name="skip"></param>
        /// <param name="orderBy"></param>
        public RequestBody(string searchText, Filter filter, int top, int skip, List<Dictionary<string, string>>? orderBy)
        {
            SearchText = searchText;
            Filters = filter;
            Top = top;
            Skip = skip;
            OrderBy = orderBy;
        }
    }

    /// <summary>
    /// Filter
    /// </summary>
    internal class Filter
    {
        /// <summary>
        /// Project
        /// </summary>
        public List<string> Project { get; set; }

        /// <summary>
        /// Repository
        /// </summary>
        public List<string> Repository { get; set; }

        /// <summary>
        /// Path
        /// </summary>
        public List<string> Path { get; set; }

        /// <summary>
        /// Branch
        /// </summary>
        public List<string> Branch { get; set; }

        // We don't use CodeElement, it is designed for single word in serach text
        // Instead, write code element in search text directly, e.g. "ext:csproj ..."
        // public List<string> CodeElement { get; set; }

        /// <summary>
        /// Constructor with user input
        /// </summary>
        /// <param name="project"></param>
        /// <param name="repository"></param>
        /// <param name="path"></param>
        /// <param name="branch"></param>
        public Filter(List<string> project, List<string> repository, List<string> branch, List<string> path)
        {
            Project = project;
            Repository = repository;
            Branch = branch;
            Path = path;
        }
    }

    /// <summary>
    /// File info returned by code search result
    /// </summary>
    public class ADOFileInfo
    {
        /// <summary>
        /// File name
        /// </summary>
        public string FileName { get; set; }

        /// <summary>
        /// Path
        /// </summary>
        public string Path { get; set; }

        /// <summary>
        /// Project Name
        /// </summary>
        public string ProjectName { get; set; }

        /// <summary>
        /// Project Id
        /// </summary>
        public string ProjectId { get; set; }

        /// <summary>
        /// Repository name
        /// </summary>
        public string RepositoryName { get; set; }

        /// <summary>
        /// Repository Id
        /// </summary>
        public string RepositoryId { get; set; }

        /// <summary>
        /// Public constructor
        /// </summary>
        /// <param name="fileName"></param>
        /// <param name="path"></param>
        /// <param name="projectName"></param>
        /// <param name="projectId"></param>
        /// <param name="repositoryName"></param>
        /// <param name="repositoryId"></param>
        public ADOFileInfo(string fileName, string path, string projectName, string projectId, string repositoryName, string repositoryId)
        {
            FileName = fileName;
            Path = path;
            ProjectName = projectName;
            ProjectId = projectId;
            RepositoryName = repositoryName;
            RepositoryId = repositoryId;
        }
    }
}
