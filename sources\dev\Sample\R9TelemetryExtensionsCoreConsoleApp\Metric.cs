﻿// <copyright file="Metric.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using Microsoft.R9.Extensions.Metering;

namespace Microsoft.M365.Core.Telemetry.SampleConsoleApp
{
    internal static partial class Metric
    {
        [Counter("Status")]
        public static partial Requests CreateRequests(IMeter meter);

        [Gauge("Service")]
        public static partial Responses CreateResponses(IMeter meter);

        [Histogram("Service")]
        public static partial Latency CreateLatency(IMeter meter);
    }
}
