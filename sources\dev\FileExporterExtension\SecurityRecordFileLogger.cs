// <copyright file="SecurityRecordFileLogger.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System.Collections.Generic;
using System.Text;
using Microsoft.R9.Extensions.SecurityTelemetry;

namespace Microsoft.M365.Core.Telemetry.FileExporter
{
    /// <summary>
    /// Implementation of <see cref="AbstractFileLogger<SecurityRecord>"/>.
    /// </summary>
    internal sealed class SecurityRecordFileLogger : AbstractFileLogger<SecurityRecord>
    {
        private new readonly FileExporterOptions options;

        private readonly SecurityTelemetryOptions securityTelemetryOptions;

        private Dictionary<string, string> staticProperties = new Dictionary<string, string>();

        private List<string> fileLogEntries = new List<string>();

        /// <summary>
        /// Initializes a new instance of the <see cref="LogRecordFileLogger{T}"/> class.
        /// </summary>
        /// <param name="securityTelemetryOptions"><see cref="SecurityTelemetryOptions"/>.</param>
        /// <param name="fileExporterOptions"><see cref="FileExporterOptions"/>.</param>
        internal SecurityRecordFileLogger(SecurityTelemetryOptions securityTelemetryOptions, FileExporterOptions fileExporterOptions)
            : base(fileExporterOptions, SystemFileManager.Instance)
        {
            this.securityTelemetryOptions = securityTelemetryOptions;
            this.options = fileExporterOptions;
        }

        /// <summary>
        /// Log Batch of SecurityRecord to disk.
        /// </summary>
        /// <param name="batch"></param>
        public override void Log(in Batch<SecurityRecord> batch)
        {
            foreach (var securityRecord in batch)
            {
                var staticString = GetCachedStaticString(securityRecord);                
                fileLogEntries.Add(options.SecurityRecordFormatter(securityRecord, staticString));
            }

            LogToFile(fileLogEntries);
            fileLogEntries.Clear();
        }

        /// <summary>
        /// Log single SecurityRecord to disk.
        /// </summary>
        /// <param name="securityRecord"></param>
        public override void Log(SecurityRecord securityRecord)
        {
            var staticString = GetCachedStaticString(securityRecord);
            fileLogEntries.Add(options.SecurityRecordFormatter(securityRecord, staticString));

            LogToFile(fileLogEntries);
            fileLogEntries.Clear();
        }

        /// <summary>
        /// Add or Get the specific static property string according to SchemaName of SecurityRecord
        /// </summary>
        /// <param name="securityRecord"></param>
        /// <returns></returns>
        private string GetCachedStaticString(SecurityRecord securityRecord)
        {
            var cachedString = string.Empty;
            if (!staticProperties.TryGetValue(securityRecord.SchemaName, out string? staticString))
            {
                cachedString = GetFormattedStaticString(securityRecord.StaticProperties);
                staticProperties.Add(securityRecord.SchemaName, cachedString);
            }
            else
            {
                cachedString = staticString;
            }
            return cachedString;
        }

        /// <summary>
        /// Create a string for static property pairs.
        /// </summary>
        /// <param name="staticPropertiesKeys"></param>
        /// <returns></returns>
        private string GetFormattedStaticString(IReadOnlyList<string> staticPropertiesKeys)
        {
            StringBuilder sb = new StringBuilder();
            foreach (var key in staticPropertiesKeys)
            {
                if (securityTelemetryOptions.PrepopulatedFields.TryGetValue(key, out object? value))
                {
                    if (value is not null)
                    {
                        sb = sb.Append(key).Append(':').Append(value).Append(',');
                    }                    
                }
            }
            return sb.ToString();
        }
    }
}
