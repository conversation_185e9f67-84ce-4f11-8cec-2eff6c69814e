﻿// <copyright file="UrlPatterns.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.Linq;

namespace Microsoft.M365.Core.Telemetry.R9.Tracing.AdvancedSampling
{
    /// <inheritdoc/>
    public class UrlPatterns
    {
        /// <summary>
        /// prefixes from which to sample or drop
        /// </summary>
        public List<string> Prefixes { get; internal set; } = new List<string>(1024);

        /// <summary>
        /// keywords to sample or drop
        /// </summary>
        public List<string> Keywords { get; internal set; } = new List<string>(1024);

        /// <summary>
        /// check if the url matches the prefixes or keywords
        /// </summary>
        /// <param name="url"></param>
        /// <returns></returns>
        public bool FindPatternInUrl(string url)
        {
            return Prefixes.Any(prefix => url.StartsWith(prefix, StringComparison.OrdinalIgnoreCase))
            || Keywords.Any(keyword => url.Contains(keyword, StringComparison.OrdinalIgnoreCase));
        }
    }
}
