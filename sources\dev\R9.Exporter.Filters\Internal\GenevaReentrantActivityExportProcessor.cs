﻿// <copyright file="GenevaReentrantActivityExportProcessor.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System.Diagnostics;
using System.Diagnostics.CodeAnalysis;
using Microsoft.Extensions.Options;
using Microsoft.R9.Extensions.Tracing.Exporters;
using OpenTelemetry.Exporter.Geneva;

namespace Microsoft.R9.Extensions.Telemetry.Exporter.Filters
{
    /// <summary>
    /// Geneva Exporter with batch paramaters
    /// </summary>
    /// <remarks>will remove in one of the conditions
    /// - R9 make the class visible to this project and make this class not sealed
    /// - the geneva extension with filter will be added to Tracing.Exporters.Geneva 
    /// </remarks>
    [ExcludeFromCodeCoverage]
    internal class GenevaReentrantActivityExportProcessor : ReentrantExportProcessor<Activity>
    {
        public GenevaReentrantActivityExportProcessor(
           IOptions<GenevaTraceExporterOptions> options)
#pragma warning disable CA2000 // This object shouldn't be disposed
                : base(new GenevaTraceExporter(options.Value))
#pragma warning restore CA2000 // This object shouldn't be disposed
        {
        }
    }
}