﻿// <copyright file="R9TracingConfigTest.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using Microsoft.Extensions.Configuration;
using Microsoft.M365.Core.Telemetry.ECSClient;
using Microsoft.Skype.ECS.Client;
using Newtonsoft.Json.Linq;
using NSubstitute;
using Xunit;

namespace Microsoft.M365.Core.Telemetry.ECSClientTest
{
    /// <summary>
    /// Tests to validate R9TracingConfig code.
    /// </summary>
    [Collection("Non-Parallel Collection")]
    public class R9TracingConfigTest : IDisposable
    {
        private R9TracingConfig config;
        private Dictionary<string, string> ecsContext;
        private UnifiedTelemetryECSClient ecsClient;
        private Dictionary<string, string> ecsContextCosmic;

        /// <summary>
        /// Setup.
        /// </summary>
        public R9TracingConfigTest()
        {
            var mockConfiguration = new Dictionary<string, string>
            {
                { "Microsoft_m365_core_telemetry:ServiceMetadata:ServiceName", "Pop3TracingTest" },
                { "Microsoft_m365_core_telemetry:ServiceMetadata:RuntimeModel", "ModelB2" }
            };

            var configuration = new ConfigurationBuilder()
                .AddInMemoryCollection(mockConfiguration)
                .Build();
            config = new R9TracingConfig(configuration);
            ecsContext = TestUtils.GetPrivateField<Dictionary<string, string>>(config, "ecsContext");
            ecsClient = TestUtils.GetPrivateField<UnifiedTelemetryECSClient>(config, "ecsClient");

            // Cosmic ecsContext
            Environment.SetEnvironmentVariable("COSMIC_PODNAME", "FooPod");
            var mockConfigurationCosmic = new Dictionary<string, string>
            {
                { "Microsoft_m365_core_telemetry:ServiceMetadata:ServiceName", "Pop3TracingTest" },
            };
            var configurationCosmic = new ConfigurationBuilder()
                .AddInMemoryCollection(mockConfigurationCosmic)
                .Build();
            var configCosmic = new R9TracingConfig(configurationCosmic);
            ecsContextCosmic = TestUtils.GetPrivateField<Dictionary<string, string>>(configCosmic, "ecsContext");
            Environment.SetEnvironmentVariable("COSMIC_PODNAME", null);
        }

        /// <summary>
        /// Cleanup.
        /// </summary>
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        /// <summary>
        /// Basic Dispose Pattern.
        /// </summary>
        /// <param name="disposing">disposing boolean.</param>
        protected virtual void Dispose(bool disposing)
        {
            if (disposing)
            {
                TestUtils.SetPrivateStaticField(typeof(UnifiedTelemetryECSClient), "ecsInstance", null);
            }
        }

        // Set up mock ecs.
        private void SetupMockECS(string appsettings_str)
        {
            JObject configRoot = JObject.Parse(appsettings_str);

            IECSConfigSettings mockConfigSettings = Substitute.For<IECSConfigSettings>();
            object unusedObjectArg = default(object);
            mockConfigSettings.TryGetRootValue("SOTELSTracing", out unusedObjectArg).Returns(r =>
            {
                r[1] = configRoot;
                return true;
            });

            IECSConfigurationRequester mockEcsRequester = Substitute.For<IECSConfigurationRequester>();
            SettingsETag settingsETag = new SettingsETag(mockConfigSettings, "FakeETag");
            mockEcsRequester.GetSettings(ecsContext).Returns(settingsETag);
            ecsClient.EcsRequester = mockEcsRequester;
            TestUtils.InvokePrivateMethod<object>(config, "UpdateConfigValue", new object[] { null, new HashSet<string>() { "SOTELSTracing" } });
        }

        /// <summary>
        /// Test R9TracingConfig with invalid configuration.
        /// </summary>
        [Fact]
        public void TestConstructorInvalidConfig()
        {
            var invalidConfiguration = new Dictionary<string, string>
            {
                { "ServiceMetadata:ServiceName", "fakeServiceName" },
                { "ServiceMetadata:RuntimeModel", "fakeRuntimeModel" }
            };
            var configuration = new ConfigurationBuilder()
                .AddInMemoryCollection(invalidConfiguration)
                .Build();
            R9TracingConfig invalidConfig = new R9TracingConfig(configuration);
        }

        /// <summary>
        /// Test ECSContext.
        /// </summary>
        [Fact]
        public void TestECSContext()
        {
            Assert.Equal(10, ecsContext.Count);
            Assert.True(ecsContext.TryGetValue("ServiceName", out string serviceName));
            Assert.Equal("Pop3TracingTest", serviceName);
        }

        /// <summary>
        /// Test ECSContext.
        /// </summary>
        [Fact]
        public void TestECSContextCosmic()
        {
            Assert.Equal(11, ecsContextCosmic.Count);
            Assert.True(ecsContextCosmic.TryGetValue("Model", out string model));
            Assert.Equal("Cosmic", model);
            Assert.True(ecsContextCosmic.TryGetValue("COSMIC_PODNAME", out string pod));
            Assert.Equal("FooPod", pod);
        }

        /// <summary>
        /// Test UpdateConfigValue with wrong agent.
        /// </summary>
        [Fact]
        public void TestUpdateConfigValueWithWrongAgent()
        {
            IECSConfigurationRequester ecsRequester = Substitute.For<IECSConfigurationRequester>();

            TestUtils.InvokePrivateMethod<object>(config, "UpdateConfigValue", new object[] { null, new HashSet<string>() { "testAgent" } });

            Assert.False(config.R9DTEnabled);
            Assert.Equal(0, config.TraceSampleRate);
            Assert.Equal(Constants.DynamicSamplerType.AlwaysOff, config.SamplerType);
            Assert.Equal(Constants.DynamicSamplerType.AlwaysOff, config.ParentRootSamplerType);
        }

        /// <summary>
        /// Test UpdateConfigValue without config root.
        /// </summary>
        [Fact]
        public void TestUpdateConfigValueWithoutConfigRoot()
        {
            IECSConfigSettings mockedConfigSettings = Substitute.For<IECSConfigSettings>();
            mockedConfigSettings.TryGetRootValue("SOTELSTracing", out _).Returns(r =>
            {
                r[1] = null;
                return true;
            });
            SettingsETag settingsETag = new SettingsETag(mockedConfigSettings, "FakeETag");
            IECSConfigurationRequester ecsRequester = Substitute.For<IECSConfigurationRequester>();
            ecsRequester.GetSettings(ecsContext).Returns(settingsETag);
            ecsClient.EcsRequester = ecsRequester;
            TestUtils.InvokePrivateMethod<object>(config, "UpdateConfigValue", new object[] { null, new HashSet<string>() { "SOTELSTracing" } });

            Assert.False(config.R9DTEnabled);
            Assert.Equal(0, config.TraceSampleRate);
            Assert.Equal(Constants.DynamicSamplerType.AlwaysOff, config.SamplerType);
            Assert.Equal(Constants.DynamicSamplerType.AlwaysOff, config.ParentRootSamplerType);
        }

        /// <summary>
        /// Tests UpdateConfigValue when fail to parse json and set with default val.
        /// </summary>
        [Fact]
        public void TestUpdateConfigValueFailToParseVerifyDefaultVal()
        {
            SetupMockECS(@"{""ConfigurationSettings"": ""NoValid""}");

            Assert.False(config.R9DTEnabled);
            Assert.Equal(0, config.TraceSampleRate);
            Assert.Equal(Constants.DynamicSamplerType.AlwaysOff, config.SamplerType);
            Assert.Equal(Constants.DynamicSamplerType.AlwaysOff, config.ParentRootSamplerType);
        }

        /// <summary>
        /// Tests UpdateConfigValue.
        /// </summary>
        [Fact]
        public void TestUpdateConfigValue()
        {
            SetupMockECS(@"{""R9DTEnabled"": ""true"", ""TraceSampleRate"": ""1"", ""SamplerType"": ""AlwaysOn"", ""ParentRootSamplerType"": ""AlwaysOn""}");

            Assert.True(config.R9DTEnabled);
            Assert.Equal(1, config.TraceSampleRate);
            Assert.Equal(Constants.DynamicSamplerType.AlwaysOn, config.ParentRootSamplerType);
            Assert.Equal(Constants.DynamicSamplerType.AlwaysOn, config.SamplerType);
            Assert.True(config.ConfigChanged);
        }

        /// <summary>
        /// Test ParseSamplerType.
        /// </summary>
        [Fact]
        public void TestParseSamplerType()
        {
            var type = TestUtils.InvokePrivateStaticMethod<Constants.DynamicSamplerType>(typeof(R9TracingConfig), "ParseSamplerType", new object[] { "ratioBased" });
            Assert.Equal(Constants.DynamicSamplerType.RatioBased, type);

            type = TestUtils.InvokePrivateStaticMethod<Constants.DynamicSamplerType>(typeof(R9TracingConfig), "ParseSamplerType", new object[] { "notValid" });
            Assert.Equal(Constants.DynamicSamplerType.AlwaysOff, type);

            type = TestUtils.InvokePrivateStaticMethod<Constants.DynamicSamplerType>(typeof(R9TracingConfig), "ParseSamplerType", new object[] { "ratioBased" });
            Assert.Equal(Constants.DynamicSamplerType.RatioBased, type);

            type = TestUtils.InvokePrivateStaticMethod<Constants.DynamicSamplerType>(typeof(R9TracingConfig), "ParseSamplerType", new object[] { "notValid" });
            Assert.Equal(Constants.DynamicSamplerType.AlwaysOff, type);
        }
    }
}
