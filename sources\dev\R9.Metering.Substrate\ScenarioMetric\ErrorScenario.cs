﻿// <copyright file="ErrorScenario.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

namespace Microsoft.M365.Core.Telemetry.R9.Metering.Substrate
{
    /// <summary>
    /// Represents an error scenario with a specific error type.
    /// </summary>
    public class ErrorScenario : ScenarioDimension
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="ErrorScenario"/> class.
        /// </summary>
        /// <param name="component">The component of the scenario.</param>
        /// <param name="scenario">The scenario name.</param>
        /// <param name="subScenario">The sub-scenario name.</param>
        /// <param name="type"></param>
        public ErrorScenario(string component, string scenario, string subScenario, string type) : base(component, scenario, subScenario)
        {
            this.Type = type;
        }

        /// <summary>
        /// Gets or sets the type of the error.
        /// </summary>
        public string Type { get; }
    }
}
