﻿<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<TargetFrameworks>net472;net6.0</TargetFrameworks>
		<ImplicitUsings>enable</ImplicitUsings>
		<Nullable>enable</Nullable>
		<LangVersion>10.0</LangVersion>
		<Platforms>AnyCPU;x64</Platforms>
	</PropertyGroup>
	<ItemGroup>
		<PackageReference Include="Microsoft.Extensions.Hosting" />
		<PackageReference Include="Microsoft.NET.Test.Sdk" />
		<PackageReference Include="Moq" />
		<PackageReference Include="xunit" />
		<PackageReference Include="xunit.runner.visualstudio" />
	</ItemGroup>
	<ItemGroup>
	  <ProjectReference Include="..\..\..\dev\ODLNrtTcpClient\Microsoft.M365.ODL.NrtTcpClient\Microsoft.M365.ODL.NrtTcpClient.csproj" />
	  <ProjectReference Include="..\..\..\dev\SecurityTelemetryTcpExporter\SecurityTelemetryTcpExporter\SecurityTelemetryTcpExporter.csproj" />
	</ItemGroup>
</Project>
