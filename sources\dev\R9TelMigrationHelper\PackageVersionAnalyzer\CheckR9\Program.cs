﻿// <copyright file="Program.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using NuGet.Common;
using NuGet.Configuration;
using NuGet.Packaging.Core;
using NuGet.Protocol;
using NuGet.Protocol.Core.Types;
using NuGet.Versioning;

/// <summary>
/// The main program class.
/// </summary>
public class Program
{
    private static SortedDictionary<string, string> dependentPackages = new SortedDictionary<string, string>();
    private static SourceCacheContext cache = new SourceCacheContext();

    /// <summary>
    /// The main entry point.
    /// </summary>
    /// <param name="args">The arguments.</param>
    public static void Main()
    {
        var exoURL = "https://o365exchange.pkgs.visualstudio.com/DefaultCollection/_packaging/Common/nuget/v3/index.json";

        // navigate to https://o365exchange.visualstudio.com/_usersSettings/tokens for personalAccessToken
        // the needed permission is Packaging(Read)
        var personalAccessToken = "your PAT";
        var alias = "your alias";
        var exoCredential = new PackageSourceCredential(exoURL, alias, personalAccessToken, true, string.Empty);

        var exoResource = GetResource<PackageMetadataResource>(exoCredential);

        SortedDictionary<string, string> packageList = ReadProjectFile("Q:\\src\\SOTELS\\sources\\dev\\PackageVersionAnalyzer\\CheckR9\\upgradingPackages.xml");
        SortedDictionary<string, string> packageProps = ReadProjectFile("Q:\\src\\Substrate\\Packages.props");

        foreach (string packageName in packageList.Keys)
        {
            GetDependencyTree(exoResource, packageName, packageList[packageName]);
        }

        Console.WriteLine("==================== packageNames to be updated =====================");

        foreach (string packageName in dependentPackages.Keys)
        {
            // if packageName not in packageList, print it out
            if (!packageProps.ContainsKey(packageName) || string.Compare(packageProps[packageName], dependentPackages[packageName], StringComparison.Ordinal) > 0)
            {
                Console.WriteLine(packageName + " " + dependentPackages[packageName]);
            }
        }
    }

    /// <summary>
    /// Extracts the package information from a line.
    /// </summary>
    /// <param name="line">The line containing package information.</param>
    /// <returns>A key-value pair of package name and version.</returns>
    public static KeyValuePair<string, string> ExtractPackage(string line)
    {
        var start = line.IndexOf("Include=\"", StringComparison.Ordinal) + "Include=\"".Length;
        var end = line.IndexOf("\"", start, StringComparison.Ordinal);
        var packageName = line.Substring(start, end - start);

        start = line.IndexOf("Version=\"", StringComparison.Ordinal) + "Version=\"".Length;
        end = line.IndexOf("\"", start, StringComparison.Ordinal);
        var version = line.Substring(start, end - start);

        return new KeyValuePair<string, string>(packageName, version);
    }

    /// <summary>
    /// Reads the project file and extracts all the package references.
    /// </summary>
    /// <param name="path">The path to the project file.</param>
    /// <returns>A sorted dictionary of package names and versions.</returns>
    public static SortedDictionary<string, string> ReadProjectFile(string path)
    {
        var lines = System.IO.File.ReadAllLines(path);
        var packages = new SortedDictionary<string, string>();
        foreach (var line in lines)
        {
            if (line.Contains("PackageReference", StringComparison.Ordinal) || line.Contains("PackageVersion", StringComparison.Ordinal))
            {
                var package = ExtractPackage(line);
                packages.Add(package.Key, package.Value);
            }
        }

        return packages;
    }

    /// <summary>
    /// Gets the resource of the specified type.
    /// </summary>
    /// <typeparam name="T">The type of the resource.</typeparam>
    /// <param name="credential">The package source credential.</param>
    /// <returns>The resource of the specified type.</returns>
    private static T GetResource<T>(PackageSourceCredential credential) where T : class, INuGetResource
    {
        var source = new PackageSource(credential.Source)
        {
            Credentials = credential,
        };
        SourceRepository repo = Repository.Factory.GetCoreV3(source);
        var resource = repo.GetResourceAsync<T>().Result;
        return resource;
    }

    /// <summary>
    /// Gets the dependency tree of the specified package.
    /// </summary>
    /// <param name="resource">The package metadata resource.</param>
    /// <param name="dependencies">The dictionary to store dependencies.</param>
    /// <param name="name">The package name.</param>
    /// <param name="version">The package version.</param>
    /// <param name="depth">The depth of the dependency tree.</param>
    /// <param name="framework">The target framework.</param>
    public static void InternalGetDependencyTree(PackageMetadataResource resource, Dictionary<string, VersionRange> dependencies, string name, string version, int depth, string? framework = null)
    {
        if (dependencies.ContainsKey(name) && String.Compare(new VersionRange(new NuGetVersion(version)).ToString(), dependencies[name].ToString(), StringComparison.Ordinal) != 0)
        {
            dependencies[name] = VersionRange.CommonSubSet(new List<VersionRange> { dependencies[name], new VersionRange(new NuGetVersion(version)) });
        }
        else
        {
            dependencies[name] = new VersionRange(new NuGetVersion(version));
        }

        for (var i = 0; i < depth; i++)
        {
            Console.Write("->");
        }
        Console.WriteLine(name + version + framework ?? string.Empty);
        dependentPackages.TryAdd(name, version);
        ILogger logger = NullLogger.Instance;

        var packageID = new PackageIdentity(name, new NuGetVersion(version));
        var meta = resource.GetMetadataAsync(packageID, cache, logger, CancellationToken.None).Result;

        if (meta == null)
        {
            return;
        }

        var frameworks = meta.DependencySets;

        if (!String.IsNullOrEmpty(framework))
        {
            frameworks = frameworks.Where(x => string.Compare(x.TargetFramework.ToString(), framework, StringComparison.Ordinal) == 0);
        }

        foreach (var f in frameworks)
        {
            foreach (var package in f.Packages)
            {
#pragma warning disable CS8602 // Dereference of a possibly null reference.
                InternalGetDependencyTree(resource, dependencies, package.Id, package.VersionRange.MinVersion.ToString(), depth + 1, f.TargetFramework.ToString());
#pragma warning restore CS8602 // Dereference of a possibly null reference.
            }
        }
    }

    /// <summary>
    /// Gets the dependency tree of the specified package.
    /// </summary>
    /// <param name="resource">The package metadata resource.</param>
    /// <param name="name">The package name.</param>
    /// <param name="version">The package version.</param>
    /// <param name="framework">The target framework.</param>
    /// <returns>A dictionary of dependencies.</returns>
    public static Dictionary<string, VersionRange> GetDependencyTree(PackageMetadataResource resource, string name, string version, string? framework = null)
    {
        var dependencies = new Dictionary<string, VersionRange>();
        InternalGetDependencyTree(resource, dependencies, name, version, 0, framework);

        return dependencies;
    }
}
