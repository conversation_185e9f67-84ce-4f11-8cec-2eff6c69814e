{"ODLTraceExporterWithParam": {"LogTypeMappings": {"*": "TestEVR"}}, "ODLTraceExporterEmpty": {}, "ODLTraceExporterInvalid": {"LogTypeMappings": "user"}, "ODLTcpTraceExporterWithParam": {"LogTypeMappings": {"*": "TestEVR"}}, "ODLTcpTraceExporterEmpty": {}, "ODLTcpTraceExporterInvalid": {"LogTypeMappings": "user"}, "GenevaExporterWindows": {"ConnectionString": "EtwSession=OpenTelemetryLocal"}, "GenevaExporterLinux": {"ConnectionString": "Endpoint=unix:/var/run/mdsd/default_fluent.socket"}, "GenevaExporterEmpty": {}, "GenevaExporterExtended": {"ConnectionString": "EtwSession=OpenTelemetryLocal", "MaxQueueSize": 20, "MaxExportBatchSize": 10, "ExportInterval": "00:00:10"}}