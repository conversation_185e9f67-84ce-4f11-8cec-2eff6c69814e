﻿# Configuration Worker API user guidance

- [Configuration Worker API user guidance](#configuration-worker-api-user-guidance)
  - [Populate appsettings.json about service side configuration](#populate-appsettingsjson-about-service-side-configuration)
  - [Populate embedded defaults about ECS side configurations](#populate-embedded-defaults-about-ecs-side-configurations)
  - [User scenarios](#user-scenarios)
    - [DI](#di)
    - [Non-DI](#non-di)

## Populate appsettings.json about service side configuration
Reference to this [link](https://o365exchange.visualstudio.com/O365%20Core/_git/TelemetryCore?path=%2Fsources%2Fdev%2FR9.Logging.Substrate%2FReadMe.md&version=GBmaster&_a=preview) to learn about how to populate appsettings.json.

## Populate embedded defaults about ECS side configurations
Customer could use following API to get an example of embedded defaults:
https://s2s.config.skype.com/config/v1/{ClientName}/1.0?agents={AgentName}&EcsCanary=1. The returned result is a full set of all customer services' configuration. Customer should pick the configuration items they want, and put them into **ECSDefaultConfig.json**.

Take ClientName=UnifiedTelemetry, AgentName=UnifiedTelemetry as an example, the result is as follows:

```json
{
    "ECS": {
        "c72ea287-ed77-4fa6-a480-3712406c367e": "aka.ms/EcsCanary"
    },
    "UnifiedTelemetry": {
        "SubstrateLogging": {
            "R9Logging": {},
            "Exporters": {           
                "Geneva": {
                    "ConnectionString": "EtwSession=test"
                },
                "OdlTcp": {
                    "ConnectionString": "tcp://localhost:1234"
                },
                "VirtualTableMappings": {
                    "Test.TestEvent": "TestEventTable",
                    "Test.TestEvent3": "TestEventTable",
                    "TestMetric.MyService": "MyServiceTable"
                },                
                "VirtualTableExports": {
                    "TestEventTable": [
                        {
                            "ExportPlatform": "Odl",
                            "ExportTableName": "TestLogType"
                        }
                    ],
                    "MyServiceTable": [
                        {
                            "ExportPlatform": "Geneva",
                            "ExportTableName": "TestGeneva"
                        },
                        {
                            "ExportPlatform": "Odl",
                            "ExportTableName": "TestOdl"
                        }
                    ]
                }
            }
        }
    }        
}        
```

## User scenarios
### DI
In this case, we provide an extension method on **IHostBuilder**. It receives 2 parameters:
- defaultConfigPath(**required**): Set the path to ECS embedded defaults file.
- useDefaultOnly: If set to true, no runtime dependencies on ECS service will be introduced and only the embedded default configuration will be used.
```csharp
        public static IHostBuilder RegisterConfiguration(
            this IHostBuilder hostBuilder,
            string defaultConfigPath,
            bool useDefaultOnly = false)
        {
            var ecsConfiguration = GenerateECSClientConfiguration(defaultConfigPath, useDefaultOnly);
            hostBuilder.AddEcsConfigurationProvider(ecsConfiguration);
            return hostBuilder;
        }
```
Following is a sample to use this API. For more detailed samples, please refer to sample.
```csharp
var host = Host.CreateDefaultBuilder(args)
            .RegisterConfiguration(Path.Combine(Directory.GetCurrentDirectory(), "ECSDefaultConfig", "ECSDefaultConfig.json"))
```

### Non-DI
In this case, we provide an static method to merge the service side configuration from appsettings.json and the ECS side configuration. It receives 3 parameters:
- defaultConfigPath(**required**): Set the path to ECS embedded defaults file.
- appsettingsPath(**required**): Set the path to apsettings.json.
- useDefaultOnly: If set to true, no runtime dependencies on ECS service will be introduced and only the embedded default configuration will be used.
```csharp
public static IConfiguration LoadConfiguration(
            string defaultConfigPath,
            string appsettingsPath,
            bool useDefaultOnly = false)
        {
            if (!File.Exists(appsettingsPath))
            {
                throw new FileNotFoundException($"The appsettings.json file is not found at the path: {appsettingsPath}. Please make sure the file exists.");
            }

            var ecsConfiguration = GenerateECSClientConfiguration(defaultConfigPath, useDefaultOnly);
            
            var builder = new ConfigurationBuilder()
                .AddJsonFile(appsettingsPath, optional: false, reloadOnChange: true)
                .AddEcs(source =>
                {
                    source.Configuration = ecsConfiguration;
                });

            return builder.Build();
        }
```
Following is a sample to use this API. For more detailed samples, please refer to sample.
```csharp
            IConfiguration configuration = ConfigurationHelper.LoadConfiguration(
                Path.Combine(Directory.GetCurrentDirectory(), "ECSDefaultConfig", "ECSDefaultConfig.json"), 
                Path.Combine(Directory.GetCurrentDirectory(), "appsettings.json"));
```