﻿// <copyright file="R9ExtensionsTest.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>
using System;
using System.Collections.Generic;
using System.Linq;
using FluentAssertions;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Options;
using Microsoft.M365.Core.Telemetry.ECSClient;
using Microsoft.M365.Core.Telemetry.Enrichment;
using Microsoft.M365.Core.Telemetry.R9;
using Microsoft.M365.Core.Telemetry.TestCommon;
using Microsoft.R9.Extensions.ClusterMetadata.Cosmic;
using Microsoft.R9.Extensions.Enrichment;
using NSubstitute;
using Xunit;
using Xunit.Abstractions;

namespace Microsoft.M365.Core.Telemetry.R9Test
{
    /// <summary>
    /// R9ExtensionsTest
    /// </summary>
    public class R9ExtensionsTest : BaseTest
    {
        private IConfiguration configuration;

        /// <summary>
        /// R9ExtensionsTest
        /// </summary>
        /// <param name="output"></param>
        public R9ExtensionsTest(ITestOutputHelper output) : base(output)
        {
            configuration = Substitute.For<IConfiguration>();
        }

        /// <summary>
        /// InitTelemetryGivenAnyNullArgumentThrows
        /// </summary>
        [Fact]
        public void InitTelemetryGivenAnyNullArgumentThrows()
        {
            Assert.Throws<ArgumentNullException>(() =>
                ((IServiceCollection)null!).InitTelemetry(configuration));
        }

        /// <summary>
        /// InitTelemetryCorrectlyRegister
        /// </summary>
        [Fact]
        public void InitTelemetryCorrectlyRegister()
        {
            using (var serviceProvider = new ServiceCollection().InitTelemetry(configuration).BuildServiceProvider())
            {
                serviceProvider.GetRequiredService<IBlockList>().Should().NotBeNull();
                serviceProvider.GetRequiredService<IPassiveR9Config>().Should().NotBeNull();
            }
        }

        /// <summary>
        /// InitTelemetryB2Enrichers
        /// </summary>
        [Fact]
        public void InitTelemetryB2Enrichers()
        {
            using (var serviceProvider = new ServiceCollection().InitTelemetry(configuration).BuildServiceProvider())
            {
                serviceProvider.GetRequiredService<IBlockList>().Should().NotBeNull();
                serviceProvider.GetRequiredService<IPassiveR9Config>().Should().NotBeNull();
                var logEnrichers = serviceProvider.GetRequiredService<IEnumerable<ILogEnricher>>();
                logEnrichers.Should().ContainSingle();
                logEnrichers.Single().Should().BeOfType<B2PassiveLogEnricher>();
                var meterEnrichers = serviceProvider.GetRequiredService<IEnumerable<IMetricEnricher>>();
                meterEnrichers.Should().ContainSingle();
                meterEnrichers.Single().Should().BeOfType<B2PassiveMetricEnricher>();

                var clusterMetadata = serviceProvider.GetRequiredService<IEnumerable<IOptions<CosmicClusterMetadata>>>();
                clusterMetadata.Should().BeEmpty();
            }
        }

        /// <summary>
        /// InitTelemetryCosmicEnrichers
        /// </summary>
        [Fact]
        public void InitTelemetryCosmicEnrichers()
        {
            // We can't test existance of CosmicLogEnricher and CosmicMetricEnricher because no visibility.
            Environment.SetEnvironmentVariable("COSMIC_PODNAME", "FooPod");
            using (var serviceProvider = new ServiceCollection().InitTelemetry(configuration).BuildServiceProvider())
            {
                serviceProvider.GetRequiredService<IBlockList>().Should().NotBeNull();
                serviceProvider.GetRequiredService<IPassiveR9Config>().Should().NotBeNull();
                var logEnrichers = serviceProvider.GetRequiredService<IEnumerable<ILogEnricher>>();
                logEnrichers.Should().ContainSingle();
                logEnrichers.Single().Should().NotBeOfType<B2PassiveLogEnricher>();

                // Metric enricher not added.
                var meterEnrichers = serviceProvider.GetRequiredService<IEnumerable<IMetricEnricher>>();
                meterEnrichers.Should().ContainSingle();
                meterEnrichers.Single().Should().NotBeOfType<B2PassiveMetricEnricher>();

                // CosmicClusterMetadata added.
                var clusterMetadata = serviceProvider.GetRequiredService<IEnumerable<IOptions<CosmicClusterMetadata>>>();
                clusterMetadata.Should().ContainSingle();
            }
            Environment.SetEnvironmentVariable("COSMIC_PODNAME", string.Empty);
        }

        /// <summary>
        /// InitTelemetryAlwaysEnableR9
        /// </summary>
        [Fact]
        public void InitTelemetryAlwaysEnableR9()
        {
            _ = new ServiceCollection().InitTelemetry(configuration, true).BuildServiceProvider();
            Assert.True(R9Services.Initialized);
            Assert.True(R9Services.AlwaysEnableR9);
        }
    }
}
