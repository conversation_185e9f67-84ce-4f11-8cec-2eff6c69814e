function global:SelectTds {
  CleanUp
  $adalModule = Get-InstalledModule Microsoft.ADAL.PowerShell -ErrorAction SilentlyContinue;
  
  if ($null -eq $adalModule)
  {
      Write-Host -ForegroundColor Red "Microsoft.ADAL.PowerShell not installed! Please install it with:"
      Write-Host -ForegroundColor Red "Install-Module Microsoft.ADAL.PowerShell -Scope AllUsers"
      Exit
  }
  
  # Import ADAL library to acquire access token  
  $adalLocation = $adalModule.InstalledLocation -replace "`n",", " -replace "`r",", "
  Add-Type -Path "${adalLocation}\Microsoft.IdentityModel.Clients.ActiveDirectory.dll"; 
  
  # Create AAD Uri  
  $adAuthority  = "https://login.microsoftonline.com/72f988bf-86f1-41af-91ab-2d7cd011db47"; 
  
  # Create AuthenticationContext for acquiring token  
  $authContext = [Microsoft.IdentityModel.Clients.ActiveDirectory.AuthenticationContext]::new($adAuthority, $false); 

  # Clear any pre-existing tokens 
  $authContext.TokenCache.Clear(); 
  $audience = "b51e4b8d-44f8-4beb-b426-d57ed395cb57"; 
  $audienceCloud = "39d3fd7b-b60a-4c13-9a57-879d0ac4da3f"; 
  
  $clientId = "2c883697-cfd4-46a5-9962-cdc74ba55125"; 
  $nativeRedirectUri = "urn:ietf:wg:oauth:2.0:oob"; 
  
  # Acquire new token 
  $authToken = $authContext.AcquireToken($audience, $clientId, $nativeRedirectUri, "Auto"); 
  $authTokenCloud = $authContext.AcquireToken($audienceCloud, $clientId, $nativeRedirectUri, "Auto"); 
  
  # Set HTTP request headers to include Authorization header 
  $authHeader = @{ 
     'Content-Type'='application/json' 
     'Authorization'=$authToken.CreateAuthorizationHeader() 
  }; 
  
  $authHeaderCloud = @{ 
     'Content-Type'='application/json' 
     'Authorization'=$authTokenCloud.CreateAuthorizationHeader() 
  }; 
  
  $topologyOwner = $authToken.UserInfo.DisplayableId; 
  
  $cloudBaseUri = "https://tdsprdapi.o365Infra.internal.office.net"; 
  $classicBaseUri = "https://tdswebuistackb.azurewebsites.net"; 
  $uriTopologyList = "api/jobs/byownerwithpasswords?owner=$topologyOwner"; 
  
  $machineList = @();
  $counter = 0;
  
  Write-Host "Getting topologies From Cloud"; 
  $uri = [string]::Join("/", $cloudBaseUri, $uriTopologyList); 
  $topos = ''; 
  $topos = (Invoke-RestMethod -Method:Get -Headers:$authHeaderCloud -Uri:$uri); 
  foreach ($item in $topos) {
      $topologyName = $item.Name;
      $topologyId = $item.TopologyId;
      foreach ($machine in $item.MachinesExt) {
          $machineItemObj = [PSCustomObject]@{
              Id           =$counter;
              Type         ="Cloud";
              IPAddress    =$machine.IPAddress;
              MachineName  =$machine.Name;
              BuildNumber  =$machine.BuildNumber;
              Password     =$machine.VmAdmin;
              TopologyId   =$topologyId;
              TopologyName =$topologyName;
          }
          $counter += 1;
          $machineList += $machineItemObj;
      }
  }
  
  Write-Host "Getting topologies From Classic"; 
  $uri = $uri = [string]::Join("/", $classicBaseUri, $uriTopologyList); 
  $topos = ''; 
  $topos = (Invoke-RestMethod -Method:Get -Headers:$authHeader -Uri:$uri); 
  foreach ($item in $topos) {
      $topologyName = $item.Name;
      $topologyId = $item.TopologyId;
      foreach ($machine in $item.MachinesExt) {
          $machineItemObj = [PSCustomObject]@{
              Id           =$counter;
              Type         ="Classic";
              IPAddress    =$machine.IPAddress;
              MachineName  =$machine.Name;
              BuildNumber  =$machine.BuildNumber;
              Password     =$machine.VmAdmin;
              TopologyId   =$topologyId;
              TopologyName =$topologyName;
          }
          $counter += 1;
          $machineList += $machineItemObj;
      }
  }
  
  $data = $machineList | Format-Table -AutoSize Id, Type, MachineName, IPAddress, BuildNumber, TopologyId, TopologyName | Out-String;
  Write-Host $data;
  
  $chosen = -1;
  while ($chosen -lt 0 -or $chosen -ge $machineList.Count) {
      $n = Read-Host "Which Machine do you want to install?"
      try {
          $chosen = [int]$n;
      }
      catch {
          $chosen = -1;
      }
  }
  
  $chosenMachine = $machineList[$chosen]
  
  if ($chosenMachine.Type -eq "Cloud")
  {
      Write-Host -ForegroundColor Red "Please connect to TDSVPN before continue" -WarningAction Inquire
  }

  # Check WSMan:\localhost\Client\TrustedHosts
  $trustedHosts = Get-Item WSMan:\localhost\Client\TrustedHosts
  $trustedHosts = $trustedHosts.Value
  if ($trustedHosts -ne "*") {
      Write-Warning "TrustedHosts not set, Please set up using"
      Write-Warning "winrm s winrm/config/client '@{TrustedHosts=`"*`"}'"
      Exit
  }
  
  # Start PS-Session and remote drive.
  Write-Host "Connecting"
  $User = "Administrator"
  $PWord = ConvertTo-SecureString -String $chosenMachine.Password -AsPlainText -Force
  $Credential = New-Object -TypeName System.Management.Automation.PSCredential -ArgumentList $User, $PWord
  $global:session = New-PSSession -ComputerName $chosenMachine.IPAddress -Credential $Credential
  $rootPath = "\\"+$chosenMachine.IPAddress+"\d$"
  $global:drive = New-PSDrive -Name "TopologyD" -PSProvider "FileSystem" -Root $rootPath -Credential $Credential -Scope Global

  Invoke-Command -Session $global:session -ScriptBlock {
      if (-not (Test-Path "D:\workspace")) {
        New-Item -Path "D:\workspace" -ItemType "directory"
      }
  }

  if (-not (Test-Path "C:\workspace")) {
    New-Item -Path "C:\workspace" -ItemType Directory -Force
  }
}

function global:Cleanup {
  Remove-PSSession $global:session.Id -ErrorAction SilentlyContinue
  Remove-PSDrive -Name "TopologyD" -ErrorAction SilentlyContinue
}

function global:ToTds {
  param (
    [string]$source,
    [string]$dest
  )
  Copy-Item $source -Destination "Topology${dest}" -Force:$true -Recurse:$true
}

function global:FromTds {
  param (
    [string]$source,
    [string]$dest
  )
  Copy-Item "Topology${source}" -Destination $dest -Force:$true -Recurse:$true
}

function global:InstallMatds {
  $copied = Invoke-Command -Session $session -ScriptBlock { Test-Path "D:\workspace\matds\init.ps1" }
  if (!$copied) {
    Copy-Item "C:\workspace\matds.zip" -Destination "TopologyD:\workspace\matds.zip" -Force:$true
    Invoke-Command -Session $session -ScriptBlock {
      Expand-Archive "D:\workspace\matds.zip" -DestinationPath "D:\workspace" -Force:$true
    }
  }
  Invoke-Command -Session $session -ScriptBlock {
    . "D:\workspace\matds\install.ps1"
    . "D:\workspace\matds\init.ps1"
  }
}

function global:MaStart {
  param (
    [string]$maconfigVer
  )

  Invoke-Command -Session $session -ScriptBlock {
    . "D:\workspace\matds\init.ps1"
    MaStart $Using:maconfigVer
  }
}

function global:MaStop {
  Invoke-Command -Session $session -ScriptBlock {
    . "D:\workspace\matds\init.ps1"
    MaStop
  }
  Invoke-Command -Session $session -ScriptBlock {
    Compress-Archive -Path "D:\workspace\matds\csv" -DestinationPath "D:\workspace\csv.zip" -Force
  }
  Remove-Item "C:\workspace\csv.zip"
  Remove-Item "C:\workspace\csv" -Recurse
  Copy-Item "TopologyD:\workspace\csv.zip" -Destination "C:\workspace\csv.zip" -Force:$true
  Expand-Archive -Path "C:\workspace\csv.zip" -DestinationPath "C:\workspace" -Force:$true
}

function global:InstallOdl {
  $copied = Invoke-Command -Session $session -ScriptBlock { 
    Test-Path "D:\workspace\ODL\OfficeDataLoader.nupkg" 
  }
  if (!$copied) {
    Invoke-Command -Session $session -ScriptBlock {
      New-Item -Path "D:\workspace\ODL" -ItemType Directory -Force
      New-Item -Path "D:\workspace\ODL\DebugLog" -ItemType Directory -Force
    }
    Copy-Item "C:\workspace\OfficeDataLoader.nupkg" -Destination "TopologyD:\workspace\ODL\OfficeDataLoader.nupkg" -Force:$true
    Copy-Item "C:\workspace\SetupODL.ps1" -Destination "TopologyD:\workspace\ODL\SetupODL.ps1" -Force:$true
  }

  $installed = Invoke-Command -Session $session -ScriptBlock { 
    Test-Path "D:\workspace\ODL\Uploader.xml" 
  }
  if (!$installed) {
    Invoke-Command -Session $session -ScriptBlock {
      . "D:\workspace\ODL\SetupODL.ps1" -ODLPackageLocation "D:\workspace\ODL" -Destination "D:\workspace\ODL"
    }
  }
}

# TBD
function global:ODLStart {
  Invoke-Command -Session $session -ScriptBlock {
    $processOptions = @{
      FilePath               = "D:\workspace\ODL\Microsoft.Office.BigData.DataLoader.exe"
      WorkingDirectory       = "D:\workspace\ODL"
      #ArgumentList           = "www.example.com"
      RedirectStandardOutput = "D:\workspace\ODL\DebugLog\output.txt"
      RedirectStandardError  = "D:\workspace\ODL\DebugLog\error.txt"
      #UseNewEnvironment      = $true
    }
    Start-Process @processOptions
  }
}

# TBD
function global:ODLStop {
  Invoke-Command -Session $session -ScriptBlock {
    Stop-Process -Name "Microsoft.Office.BigData.DataLoader" -Force
  }
  FromTds -source "D:\workspace\ODL\DebugLog" -dest "C:\workspace\ODL"
}

function global:InitNanoTest {
  Invoke-Command -Session $global:session -ScriptBlock {
    Set-Service -Name "GrpcRemoting" -StartupType Disabled;
    Get-Service -Name "MSExchangeNanoProxy*" | Set-Service -StartupType Manual;

    Write-Host "Stopping NanoProxy"
    $nanoproxy = Get-Service -Name "MSExchangeNanoProxy*";
    $nanoproxy | Stop-Service -Force;
    Write-Host "Stopping Variantconfig"
    $variantconfig = Get-Service -Name "MSExchangeVariantConfigSyncService";
    $variantconfig | Stop-Service -Force;
    Write-Host "Stopping GrpcRemoting"
    $gprcremoting = Get-Service -Name "GrpcRemoting";
    $gprcremoting | Stop-Service -Force;
    Write-Host "Stopping ODL"
    $odl = Get-Service -Name "MSOfficeDataLoader";
    $odl | Stop-Service -Force;

    $stuck = Get-WmiObject -Class win32_service | Where-Object {$_.state -eq 'stop pending'} | %{Get-Process -Id $_.ProcessId};
    $stuck | Stop-Process -Force;
    Write-Host "Stopping GenExt"
    $genext = Get-Process | Where-Object { $_.Name -like "*GenExtHealthMonitor*" };
    $genext | Stop-Process -Force;  
    Write-Host "Stopping MonAgent"
    $monagent = Get-Process | Where-Object { $_.Name -like "*MonAgent*" };
    $monagent | Stop-Process -Force;
  }

  Write-Host "Creating test accounts..."
  Invoke-Command -Session $global:session -ScriptBlock {
    # Local test config.
    $path = "HKLM:SOFTWARE\Microsoft\MSExchangeNanoProxy\DefaultInstance"
    Set-ItemProperty -Path $path -Name ListenPrefixes -Value https://+:443/API/
    Set-ItemProperty -Path $path -Name EnableOpenTelemetryProcessor -Value 1

    New-ItemProperty -Path $path -Name MetricExportIntervalMillis -Value 10000 -PropertyType QWord -Force
    New-ItemProperty -Path $path -Name MetricExportTimeoutMillis -Value 5000 -PropertyType QWord -Force
    New-ItemProperty -Path $path -Name GenevaLogTableNameMapping -Value "NanoProxyLogger:NanoProxyEvent,NanoProxyTest:NanoProxyTestEvent" -PropertyType String -Force
    New-ItemProperty -Path $path -Name OdlTraceBatchScheduleDelayMillis -Value 10000 -PropertyType QWord -Force
    New-ItemProperty -Path $path -Name OdlTraceBatchMaxQueueSize -Value 150 -PropertyType QWord -Force
    New-ItemProperty -Path $path -Name OdlTraceBatchMaxExportBatchSize -Value 50 -PropertyType QWord -Force
    New-ItemProperty -Path $path -Name OdlTraceSamplerType -Value "UnkownSampler" -PropertyType String -Force

    Set-Location D:\WinCoreQuickLauncher\NanoProxy
    . .\Bootstrap.ps1
    Pop-Location
  }
}

function global:NanoUpdate {
  param (
    [string]$NanoRoot
  )
  Invoke-Command -Session $global:session -ScriptBlock {
    Get-Service -Name "*NanoProxy*" | Stop-Service
    Get-Process -Name "*NanoProxy*" | Stop-Process -Force
  }
  $files = @(
    "${NanoRoot}\target\distrib\Release\x64\Microsoft.M365.RoutingPlane.NanoProxyService.Native\Microsoft.M365.RoutingPlane.SharedCacheAsyncRpcClient.pdb",
    "${NanoRoot}\target\distrib\Release\x64\Microsoft.M365.RoutingPlane.NanoProxyService.Native\Microsoft.M365.RoutingPlane.RpcLocalBaseClient.pdb",
    "${NanoRoot}\target\distrib\Release\x64\Microsoft.M365.RoutingPlane.NanoProxyService.Native\Microsoft.M365.RoutingPlane.RoutingAsyncRpcClient.pdb",
    "${NanoRoot}\target\distrib\Release\x64\Microsoft.M365.RoutingPlane.NanoProxyService.Native\Microsoft.M365.RoutingPlane.NanoProxyService.pdb",
    "${NanoRoot}\target\distrib\Release\x64\Microsoft.M365.RoutingPlane.NanoProxyService.Native\Microsoft.M365.RoutingPlane.NanoProxyRouteSelector.pdb",
    "${NanoRoot}\target\distrib\Release\x64\Microsoft.M365.RoutingPlane.NanoProxyService.Native\Microsoft.M365.RoutingPlane.NanoProxyConfigManager.pdb",
    "${NanoRoot}\target\distrib\Release\x64\Microsoft.M365.RoutingPlane.NanoProxyService.Native\Microsoft.M365.RoutingPlane.NanoProxyCommon.pdb",
    "${NanoRoot}\target\distrib\Release\x64\Microsoft.M365.RoutingPlane.NanoProxyService.Native\Microsoft.M365.RoutingPlane.NanoProxy.pdb",
    "${NanoRoot}\target\distrib\Release\x64\Microsoft.M365.RoutingPlane.NanoProxyService.Native\Tfx.dll",
    "${NanoRoot}\target\distrib\Release\x64\Microsoft.M365.RoutingPlane.NanoProxyService.Native\Microsoft.M365.RoutingPlane.SharedCacheAsyncRpcClient.dll",
    "${NanoRoot}\target\distrib\Release\x64\Microsoft.M365.RoutingPlane.NanoProxyService.Native\Microsoft.M365.RoutingPlane.RpcLocalBaseClient.dll",
    "${NanoRoot}\target\distrib\Release\x64\Microsoft.M365.RoutingPlane.NanoProxyService.Native\Microsoft.M365.RoutingPlane.RoutingAsyncRpcClient.dll",
    "${NanoRoot}\target\distrib\Release\x64\Microsoft.M365.RoutingPlane.NanoProxyService.Native\Microsoft.M365.RoutingPlane.NanoProxyRouteSelector.dll",
    "${NanoRoot}\target\distrib\Release\x64\Microsoft.M365.RoutingPlane.NanoProxyService.Native\Microsoft.M365.RoutingPlane.NanoProxyConfigManager.dll",
    "${NanoRoot}\target\distrib\Release\x64\Microsoft.M365.RoutingPlane.NanoProxyService.Native\Microsoft.M365.RoutingPlane.NanoProxyCommon.dll",
    "${NanoRoot}\target\distrib\Release\x64\Microsoft.M365.RoutingPlane.NanoProxyService.Native\Microsoft.M365.RoutingPlane.NanoProxy.dll",
    "${NanoRoot}\target\distrib\Release\x64\Microsoft.M365.RoutingPlane.NanoProxyService.Native\IfxMetrics.dll",
    "${NanoRoot}\target\distrib\Release\x64\Microsoft.M365.RoutingPlane.NanoProxyService.Native\IfxEvents.dll",
    "${NanoRoot}\target\distrib\Release\x64\Microsoft.M365.RoutingPlane.NanoProxyService.Native\Microsoft.M365.RoutingPlane.NanoProxyService.exe"
  )
  $zipPath = "c:\workspace\nanobits.zip"
  Compress-Archive -Path $files -DestinationPath $zipPath -Force
  Write-Host "Copying ${zipPath} to tds..."
  ToTds $zipPath "D:\workspace\nanobits.zip"
  Write-Host "Decompress and replace..."
  Invoke-Command -Session $global:session -ScriptBlock {
    Expand-Archive -Path "D:\workspace\nanobits.zip" -DestinationPath "D:\workspace\nanobits" -Force
    $files = Get-ChildItem -Path "D:\workspace\nanobits" -File
    foreach ($file in $files) {
      Copy-Item -Path $file.FullName -Destination "C:\Program Files\Microsoft\Exchange Server\V15\RoutingPlane" -Force
    }
  }
}

function global:NanoStop {
  Invoke-Command -Session $global:session -ScriptBlock {
    Get-Service -Name "MSExchangeNanoProxy*" | ForEach-Object { Stop-Service -Name $_.Name }
  }
}

function global:NanoStart {
  Invoke-Command -Session $global:session -ScriptBlock {
    Start-Service -Name "MSExchangeNanoProxyDefaultInstance"
  }
}

function global:TestRequest {
  $a = Invoke-Command -Session $session -ScriptBlock {
    Push-Location
    Set-Location D:\WinCoreQuickLauncher\NanoProxy
    . .\WinHttpSendRestRequest.ps1
    Pop-Location
  }
  Write-Host $a[1]
}

function global:WEREnable {
  param (
    [string]$app
  )
  Invoke-Command -Session $session -ScriptBlock { 
    $app = $Using:app
    if (-not (Test-Path "D:\workspace\memorydump")) {
      New-Item -Path "D:\workspace\memorydump" -ItemType "directory"
    }
    Remove-Item "D:\workspace\memorydump\*" -Recurse -Force
    $basePath = "HKLM:\Software\Microsoft\Windows\Windows Error Reporting\LocalDumps"
    if (-not (Test-Path $basePath)) {
        New-Item -Path "HKLM:\Software\Microsoft\Windows\Windows Error Reporting" -Name "LocalDumps"
    }
    $registryPath = "${basePath}\${app}"
    if (-not (Test-Path $registryPath)) {
        New-Item -Path $basePath -Name $app
    }
    Set-ItemProperty -Path $registryPath -Name "DumpFolder" -Value "D:\workspace\memorydump" -Type "ExpandString"
    Set-ItemProperty -Path $registryPath -Name "DumpCount" -Value 10 -Type "DWord"
    Set-ItemProperty -Path $registryPath -Name "DumpType" -Value 2 -Type "DWord"
    appverif -enable Exceptions -for $app
  }
}

function global:WERDisable {
  param (
    [string]$app
  )
  Invoke-Command -Session $session -ScriptBlock { 
    $app = $Using:app
    $registryPath = "HKLM:\Software\Microsoft\Windows\Windows Error Reporting\LocalDumps\${app}"
    Remove-Item -Recurse -Force -Path $registryPath
    appverif -disable * -for *
  }
}

function global:WERWaitForDumpAndDisable {
  param (
    [string]$app
  )
  Invoke-Command -Session $session -ScriptBlock { 
    $app = $Using:app
    while ((Get-ChildItem -Path "D:\workspace\memorydump").Count -eq 0) {
      Start-Sleep -Seconds 5 
    }
    $registryPath = "HKLM:\Software\Microsoft\Windows\Windows Error Reporting\LocalDumps\${app}"
    Remove-Item -Recurse -Force -Path $registryPath
    appverif -disable * -for *

    Compress-Archive -Path "D:\workspace\memorydump" -DestinationPath "D:\workspace\memorydump.zip" -Force
    Remove-Item "D:\workspace\memorydump\*" -Force
  }
    Remove-Item "C:\workspace\memorydump.zip"
    Remove-Item "C:\workspace\memorydump" -Recurse
    Copy-Item "TopologyD:\workspace\memorydump.zip" -Destination "C:\workspace\memorydump.zip" -Force:$true
    Expand-Archive -Path "C:\workspace\memorydump.zip" -DestinationPath "C:\workspace" -Force:$true
}

#Microsoft.M365.RoutingPlane.NanoProxyService.exe

