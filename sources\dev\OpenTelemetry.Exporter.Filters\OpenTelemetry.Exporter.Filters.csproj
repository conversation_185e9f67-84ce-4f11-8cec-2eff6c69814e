<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <AssemblyName>Microsoft.M365.Core.Telemetry.OpenTelemetry.Exporter.Filters</AssemblyName>
    <RootNamespace>$(AssemblyName)</RootNamespace>
    <Description>OpenTelemetry based .NET Processors with Filter for Exporters</Description>
    <TargetFrameworks>net6.0;net8.0;net472</TargetFrameworks>
    <LangVersion>latest</LangVersion>
    <Nullable>disable</Nullable>
    <Workstream>Telemetry</Workstream>
    <Category>Telemetry</Category>
    <UseR9Generators>true</UseR9Generators>
    <GeneratePackageOnBuild>true</GeneratePackageOnBuild>
    <PlatformTarget>anycpu</PlatformTarget>
    <PackageId>Microsoft.M365.Core.Telemetry.OpenTelemetry.Exporter.Filters</PackageId>
    <PackageVersion>9.0.2</PackageVersion>
    <PackageReleaseNotes>Update R9 to 8.11.1 and OpenTelemtry.Api and Geneva Exporter to 1.9.0</PackageReleaseNotes>
    <NoWarn>SA1600</NoWarn>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="OpenTelemetry.Api" />
    <PackageReference Include="OpenTelemetry.Exporter.Console" />
    <PackageReference Include="OpenTelemetry.Exporter.Geneva" />
  </ItemGroup>

  <ItemGroup>
    <InternalsVisibleToTest Include="$(AssemblyName).Test" />
  </ItemGroup>
</Project>