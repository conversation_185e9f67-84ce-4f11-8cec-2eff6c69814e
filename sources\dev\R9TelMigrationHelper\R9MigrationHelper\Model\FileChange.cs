﻿// <copyright file="FileChange.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace R9MigrationHelper.Model
{
    /// <summary>
    /// FileChange
    /// </summary>
    public class FileChange
    {
        private string updatedFileContent = string.Empty;
        private string filePath = string.Empty;

        /// <summary>
        /// UpdatedFileContent
        /// </summary>
        public string UpdatedFileContent { get => updatedFileContent; set => updatedFileContent = value; }

        /// <summary>
        /// FilePath
        /// </summary>
        public string FilePath { get => filePath; set => filePath = value; }

        /// <summary>
        /// Public constructor
        /// </summary>
        /// <param name="updatedFileContent"></param>
        /// <param name="filePath"></param>
        public FileChange(string updatedFileContent, string filePath)
        {
            UpdatedFileContent = updatedFileContent;
            FilePath = filePath;
        }
    }
}
