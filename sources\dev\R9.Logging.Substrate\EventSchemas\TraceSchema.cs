﻿// <copyright file="TraceSchema.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>
namespace Microsoft.M365.Core.Telemetry.R9.Logging.Substrate.EventSchemas
{
    using System.Diagnostics.Tracing;

    /// <summary>
    /// Represents the schema for trace events in the telemetry system.
    /// Inherits from <see cref="ScenarioSchema"/> to provide additional context for trace scenarios.
    /// </summary>
    public class TraceSchema : ScenarioSchema
    {
        /// <summary>
        /// Gets or sets the eventLevel for the trace event.
        /// </summary>
        public uint EventLevel { get; set; }

        /// <summary>
        /// Gets or sets the message for the trace event.
        /// </summary>
        public string Message { get; set; }

        /// <summary>
        /// Initializes a new instance of the <see cref="TraceSchema"/> class.
        /// </summary>
        public TraceSchema() : base()
        {
            this.Message = string.Empty;
        }

        /// <summary>
        /// Custom Constructor
        /// </summary>
        /// <param name="message">The message</param>
        /// <param name="eventLevel">The event level</param>
        public TraceSchema(string message, EventLevel eventLevel = System.Diagnostics.Tracing.EventLevel.Informational) : base()
        {
            this.EventLevel = (uint)eventLevel;
            this.Message = message;
        }
    }
}
