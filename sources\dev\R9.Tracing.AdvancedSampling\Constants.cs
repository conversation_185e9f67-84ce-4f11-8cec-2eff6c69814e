﻿// <copyright file="Constants.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System;

namespace Microsoft.M365.Core.Telemetry.R9.Tracing.AdvancedSampling
{
    /// <summary>
    /// Costants
    /// </summary>
    public class Constants
    {
        /// <summary>
        /// Name for Post-Trace Baggage
        /// </summary>
        public static readonly string PostTraceBaggageName = "ErrorInTrace";

        /// <inheritdoc />
        public static readonly string SampleBaggageName = "KeepSpan";

        /// <inheritdoc />
        public static readonly string SampleBaggage = Boolean.TrueString;

        /// <inheritdoc />
        public static readonly string DropBaggage = Boolean.FalseString;
    }
}
