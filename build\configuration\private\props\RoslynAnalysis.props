<?xml version="1.0" encoding="utf-8"?>
<Project>
  <PropertyGroup>
    <MSBuildAllProjects>$(MSBuildAllProjects);$(MSBuildThisFileFullPath)</MSBuildAllProjects>
  </PropertyGroup>

  <PropertyGroup>
    <UseRoslynAnalyzers Condition="'$(UseRoslynAnalyzers)'==''">true</UseRoslynAnalyzers>
    <RoslynBaseline Condition="'$(UseRoslynAnalyzers)'!='true'">false</RoslynBaseline>
  </PropertyGroup>

  <PropertyGroup Condition="'$(UseRoslynAnalyzers)'=='true'">
    <CodeAnalysisRuleSet>$(EnlistmentRoot)\build\configuration\private\xml\RoslynAnalyzers.ruleset</CodeAnalysisRuleSet>
    <TestCodeAnalysisRuleSet>$(EnlistmentRoot)\build\configuration\private\xml\RoslynAnalyzersTest.ruleset</TestCodeAnalysisRuleSet>
    <CodeAnalysisRuleSet Condition="Exists('$(TestCodeAnalysisRuleSet)') AND ('$([System.Text.RegularExpressions.Regex]::IsMatch($(MSBuildProjectFullPath.ToLower()), `$(InetrootRegex.ToLower())\\sources\\test\\.*`))' OR '$([System.Text.RegularExpressions.Regex]::IsMatch($(MSBuildProjectFullPath.ToLower()), `$(InetrootRegex.ToLower())\\sources\\cpp\\test\\.*`))')">$(TestCodeAnalysisRuleSet)</CodeAnalysisRuleSet>
    <TreatWarningsAsErrors Condition="'$(RoslynBaseline)'=='true'">false</TreatWarningsAsErrors>
  </PropertyGroup>

  <ItemGroup Condition="'$(UseRoslynAnalyzers)'=='true' and !Exists('$(MSBuildProjectDirectory)\packages.config')">
    <AdditionalFiles Include="$(EnlistmentRoot)\build\configuration\private\json\stylecop.json" />  
    <PackageReference Include="Microsoft.CodeAnalysis.FxCopAnalyzers" PrivateAssets="All" />
    <PackageReference Include="Microsoft.Internal.Analyzers" PrivateAssets="All" />
    <PackageReference Include="StyleCop.Analyzers" PrivateAssets="All" />
  </ItemGroup>

  <PropertyGroup>
    <Inetroot Condition="'$(Inetroot)'==''">$(EnlistmentRoot.TrimEnd('\\'))</Inetroot>
    <TargetPathDir Condition="'$(TargetPathDir)'==''">$(TargetRoot)\</TargetPathDir>
  </PropertyGroup>

  <ItemGroup Condition="!Exists('$(MSBuildProjectDirectory)\packages.config')">
    <PackageReference Include="OSS.RoslynAnalysis" PrivateAssets="All" ExcludeAssets="Compile;Runtime" />
  </ItemGroup>

  <Import Project="RoslynBaseline.props" Condition="'$(RoslynBaseline)'=='true' AND '$(NCrunch)'!='1'" />
</Project>