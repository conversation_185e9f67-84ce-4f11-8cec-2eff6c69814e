﻿// <copyright file="ConfigurationHelper.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System;
using System.IO;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Hosting;

namespace Microsoft.M365.Core.Telemetry.R9.Logging.Substrate.Configuration
{
    /// <summary>
    /// The helper class which provides methods to load and merge configurations.
    /// </summary>
    public static class ConfigurationHelper
    {
        /// <summary>
        /// Extension method on IHostBuilder to register configuration from ECS. Adapt to DI scenario.
        /// </summary>
        /// <param name="hostBuilder">On which the extension method is defined.</param>
        /// <param name="defaultConfigPath">The path to ECS embedded default configuration files.</param>
        /// <param name="useDefaultOnly">Whether to use embedded default configuration only. If set to true, no connection to ECS service will be established.</param>
        /// <returns>An <see cref="IConfiguration"/> object that contains the merged configuration.</returns>
        public static IHostBuilder RegisterConfiguration(
            this IHostBuilder hostBuilder,
            string defaultConfigPath,
            bool useDefaultOnly = false)
        {
            return hostBuilder.ConfigureAppConfiguration((context, config) =>
            {
                var appSettingsConfig = config.Build();

                config.AddValidatedEcsConfiguration(
                    validationSource: appSettingsConfig,
                    defaultConfigPath: defaultConfigPath,
                    useDefaultOnly: useDefaultOnly);
            });
        }

        /// <summary>
        /// Extension method on IHostBuilder to register configuration from ECS. Adapt to DI scenario.
        /// </summary>
        /// <param name="hostBuilder">On which the extension method is defined.</param>
        /// <returns>An <see cref="IConfiguration"/> object that contains the merged configuration.</returns>
        public static IHostBuilder RegisterConfiguration(this IHostBuilder hostBuilder)
        {
            return RegisterConfiguration(hostBuilder: hostBuilder, defaultConfigPath: string.Empty, useDefaultOnly: false);
        }

        /// <summary>
        /// Load configuration from both appsettings.json and ECS service. Merge them into a single IConfiguration object. Adapt to Non-DI scenario.
        /// </summary>
        /// <param name="defaultConfigPath">The path to ECS embedded default configuration files.</param>
        /// <param name="appsettingsPath">The path to the appsettings.json file.</param>
        /// <param name="useDefaultOnly">Whether to use embedded default configuration only. If set to true, no connection to ECS service will be established.</param>
        public static IConfiguration LoadConfiguration(
            string defaultConfigPath,
            string appsettingsPath,
            bool useDefaultOnly = false)
        {
            if (!File.Exists(appsettingsPath))
            {
                throw new FileNotFoundException($"The appsettings.json file is not found at the path: {appsettingsPath}. Please make sure the file exists.");
            }

            var appSettingsConfig = new ConfigurationBuilder()
                .AddJsonFile(appsettingsPath, optional: false, reloadOnChange: true)
                .Build();

            // Create the main builder, add appsettings, then add validated ECS config
            var builder = new ConfigurationBuilder()
                .AddJsonFile(appsettingsPath, optional: false, reloadOnChange: true)
                .AddValidatedEcsConfiguration(
                    validationSource: appSettingsConfig,
                    defaultConfigPath: defaultConfigPath,
                    useDefaultOnly: useDefaultOnly);

            return builder.Build();
        }

        /// <summary>
        /// Load configuration from both appsettings.json and ECS service. Merge them into a single IConfiguration object. Adapt to Non-DI scenario.
        /// </summary>
        /// <param name="appsettingsPath">The path to the appsettings.json file.</param>
        public static IConfiguration LoadConfiguration(string appsettingsPath)
        {
            return LoadConfiguration(defaultConfigPath: string.Empty, appsettingsPath: appsettingsPath, useDefaultOnly: false);
        }
    }
}
