﻿// <copyright file="PullRequestModel.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace R9MigrationHelper.Model
{
    /// <summary>
    /// UserInput.
    /// </summary>
    public class PullRequestModel
    {
        string pullRequestUrl = string.Empty;
        string branchName = string.Empty;

        /// <summary>
        /// Pull Request Url.
        /// </summary>
        public string PullRequestUrl
        {
            get { return pullRequestUrl; }
            set { pullRequestUrl = value; }
        }

        /// <summary>
        /// Branch Name.
        /// </summary>
        public string BranchName
        {
            get { return branchName; }
            set { branchName = value; }
        }
    }
}
