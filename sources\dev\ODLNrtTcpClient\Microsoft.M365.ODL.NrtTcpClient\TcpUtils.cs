﻿// <copyright file="TcpUtils.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System.Diagnostics.CodeAnalysis;
using System.Net.Sockets;
using Microsoft.Office.BigData.DataLoader;

namespace Microsoft.M365.ODL.NrtTcpClient
{
    /// <summary>
    /// Tcp util class
    /// </summary>
    [ExcludeFromCodeCoverage]
    public sealed class TcpUtils
    {
        /// <summary>
        /// NeverTimeout
        /// </summary>
        public const int NeverTimeout = Timeout.Infinite;

        private static IOdlLogger logger = OdlLogger.Instance;

        /// <summary>
        /// Send NRT data synchronously with timeout and wait for response, not thread-safe
        /// </summary>
        /// <param name="commandType">the type of request</param>
        /// <param name="currentStream">tcp stream</param>
        /// <param name="dataBuffer">the data to be sent</param>
        /// <param name="len">the len of the pb message</param>
        /// <param name="seq">the seq</param>
        /// <param name="memoryStream">the reusable memory stream for network I/O</param>
        /// <param name="timeoutMilliSeconds">the timeout for sending/receving, default is never timeout</param>
        /// <returns>true if send success</returns>
        public static bool Send(ODLNRTCommandType commandType, Stream currentStream, byte[] dataBuffer, int len, long seq, MemoryStream memoryStream, int timeoutMilliSeconds = NeverTimeout)
        {
            if (timeoutMilliSeconds != NeverTimeout && timeoutMilliSeconds < 0)
            {
                throw new ArgumentOutOfRangeException($"Invalid timeout millis {timeoutMilliSeconds}");
            }

            if (null == currentStream || null == dataBuffer || dataBuffer.Length == 0)
            {
                return false;
            }

            try
            {
                currentStream.WriteTimeout = timeoutMilliSeconds;
                currentStream.ReadTimeout = timeoutMilliSeconds;

                // send a magic byte to indicate the start of a message
                currentStream.WriteByte(0x01);
#if NET472
                // send length of message as byte[]
                byte[] lengthBytes = BitConverter.GetBytes(len);
                currentStream.Write(lengthBytes, 0, lengthBytes.Length);
#elif NET6_0_OR_GREATER
                Span<byte> lengthBytes = stackalloc byte[4];
                BitConverter.TryWriteBytes(lengthBytes, len);
                currentStream.Write(lengthBytes);
#endif

                currentStream.Write(dataBuffer, 0, len);

                return ReceiveAck(currentStream, dataBuffer, seq, memoryStream, commandType, timeoutMilliSeconds);
            }
            catch (Exception e)
            {
                if (TcpUtils.IsThreadStopException(e))
                {
                    throw;
                }

                logger.Log(LogLevel.Error, LogEventId.TcpProtocolError, $"Error while sending data of type {commandType}:{e}");

                return false;
            }
        }

        /// <summary>
        /// Receive ack from server
        /// </summary>
        /// <param name="currentStream">network stream</param>
        /// <param name="dataBuffer">the data buffer to be reused</param>
        /// <param name="seq">the seq of the correpsonding request</param>
        /// <param name="memoryStream">the memory stream to reuse</param>
        /// <param name="commandType">the command type</param>
        /// <param name="timeoutMilliSeconds">time out to receive</param>
        /// <returns>true if success</returns>
        public static bool ReceiveAck(Stream currentStream, byte[] dataBuffer, long seq, MemoryStream memoryStream, ODLNRTCommandType commandType, int timeoutMilliSeconds)
        {
            bool result = true;
            long endPos = -1;
            int bytesRead = -1;

            try
            {
                try
                {
                    bytesRead = currentStream.Read(dataBuffer, 0, 4);
                }
                catch (IOException ex) when (ex.InnerException is SocketException socketEx && socketEx.SocketErrorCode == SocketError.IOPending)
                {
                    // The only reason of this should be reading immediately after writing on the same currentStream in the same thread
                    logger.Log(LogLevel.Error, LogEventId.TcpProtocolError, $"{commandType}: Retry to read due to {ex}");
                    Thread.Sleep(timeoutMilliSeconds);
                    bytesRead = currentStream.Read(dataBuffer, 0, 4);
                }

                if (bytesRead == 4)
                {
                    int ackMessageLen = BitConverter.ToInt32(dataBuffer, 0);

                    // May remove this in the future since this is for debugging purpose
                    string firstFourBytes = ConvertBytesToHexStr(dataBuffer, 0, 4);

                    if (!ReadToStream(currentStream, dataBuffer, ackMessageLen, memoryStream))
                    {
                        logger.Log(LogLevel.Error, LogEventId.TcpProtocolError, $"{commandType}: The bytes read {ackMessageLen} from response head is not align with the real readable length: " +
                            $"{endPos = memoryStream.Position}, the first four bytes is {firstFourBytes}");

                        result = false;
                        return false;
                    }

                    endPos = memoryStream.Position;
                    memoryStream.Position = 0;
                    var reponse = ODLNRTResponse.Parser.ParseFrom(memoryStream);
                    if (reponse == null || reponse.Head?.Sequence != seq)
                    {
                        logger.Log(LogLevel.Error, LogEventId.TcpProtocolError, $"{commandType}: The response is not as expected seq {seq}:{reponse?.Head?.Sequence}");
                        result = false;
                        return false;
                    }
                }
                else
                {
                    logger.Log(LogLevel.Error, LogEventId.TcpProtocolError, $"{commandType}: The response should start with 4 bytes of length, actual readable length is {bytesRead}");

                    return false;
                }

                return true;
            }
            catch (IOException ioEx) when (ioEx.InnerException is SocketException socketEx)
            {
                if (socketEx.SocketErrorCode != SocketError.TimedOut &&
                    socketEx.SocketErrorCode != SocketError.ConnectionReset)
                {
                    logger.Log(LogLevel.Error, LogEventId.TcpProtocolError, $"{commandType}: Error when receving response:{ioEx}");
                }
                result = false;
                return false;
            }
            catch (Exception e)
            {
                logger.Log(LogLevel.Error, LogEventId.TcpProtocolError, $"{commandType}: Error when receving response:{e}");
                result = false;
                return false;
            }
            finally
            {
                if (!result)
                {
                    logger.Log(LogLevel.Error, LogEventId.TcpProtocolError, $"{commandType}: The received data bytes that result in ReceiveAck failure is: {ConvertMemoryStreamToHexStr(memoryStream, endPos)}");
                }

                // Reset to reuse
                memoryStream?.SetLength(0);
            }
        }

        /// <summary>
        /// convert memory stream to its Hexidecimal representation
        /// </summary>
        /// <param name="stream"></param>
        /// <param name="len"></param>
        /// <returns></returns>
        public static string ConvertMemoryStreamToHexStr(MemoryStream stream, long len)
        {
            if (len <= 0)
            {
                return string.Empty;
            }

            try
            {
                stream.Position = 0;
                byte[] bytes = new byte[(int)len];
                stream.Read(bytes, 0, (int)len);
                return ConvertBytesToHexStr(bytes, 0, (int)len);
            }
            catch (Exception e)
            {
                logger.Log(LogLevel.Error, LogEventId.TcpProtocolError, $"Fail to perform ConvertMemoryStreamToHexStr:{e}");
                return string.Empty;
            }
        }

        /// <summary>
        /// Convert byte array to its Hexidecimal representation
        /// </summary>
        /// <param name="data"></param>
        /// <param name="startOffset"></param>
        /// <param name="length"></param>
        /// <returns></returns>
        public static string ConvertBytesToHexStr(byte[] data, int startOffset, int length)
        {
            try
            {
                return BitConverter.ToString(data, startOffset, length);
            }
            catch (Exception e)
            {
                logger.Log(LogLevel.Error, LogEventId.TcpProtocolError, $"Fail to perform ConvertBytesToHexStr:{e}");
                return string.Empty;
            }
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="currentStream"></param>
        /// <param name="readBuffer"></param>
        /// <param name="lenToRead">len to read</param>
        /// <param name="stream"></param>
        /// <returns></returns>
        public static bool ReadToStream(Stream currentStream, byte[] readBuffer, int lenToRead, MemoryStream stream)
        {
            if (readBuffer == null || stream == null)
            {
                return false;
            }

            // read message from stream
            int totalReadLen = 0;
            while (totalReadLen < lenToRead)
            {
                int needReadLen = Math.Min(lenToRead - totalReadLen, readBuffer.Length);
                int readLen = currentStream.Read(readBuffer, 0, needReadLen);
                if (readLen <= 0)
                {
                    return false;
                }

                stream.Write(readBuffer, 0, readLen);
                totalReadLen += readLen;
            }

            return true;
        }

        /// <summary>
        /// Whether current exception indicates current thread need to be stopped
        /// </summary>
        /// <param name="e">the exception</param>
        /// <returns>true if needs to be stopped</returns>
        public static bool IsThreadStopException(Exception e)
        {
            return e is ThreadInterruptedException || e is ThreadAbortException;
        }
    }
}
