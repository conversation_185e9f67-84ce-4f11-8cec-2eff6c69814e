#include "TcpClient.h"
#include "TcpServer.h"
#include "AsioContext.h"

#include <gtest/gtest.h>
#include <opentelemetry/exporters/ostream/log_record_exporter.h>
#include <opentelemetry/exporters/ostream/span_exporter_factory.h>
#include <opentelemetry/logs/log_record.h>
#include <opentelemetry/logs/logger_provider.h>
#include <opentelemetry/logs/provider.h>
#include <opentelemetry/sdk/logs/exporter.h>
#include <opentelemetry/sdk/logs/logger_provider.h>
#include <opentelemetry/sdk/logs/logger_provider_factory.h>
#include <opentelemetry/sdk/logs/recordable.h>
#include <opentelemetry/sdk/logs/simple_log_record_processor_factory.h>

#include <chrono>
#include <thread>

using namespace Microsoft::M365::Exporters;

namespace logs_api      = opentelemetry::logs;
namespace logs_sdk      = opentelemetry::sdk::logs;
namespace logs_exporter = opentelemetry::exporter::logs;

std::string sizeTTo4ByteString(unsigned int value) {
    std::string result(4, '\0'); // Create a string of 4 null characters
    for (int i = 0; i < 4; ++i) {
        result[i] = static_cast<unsigned char>((value >> ((3 - i) * 8)) & 0xFF);
    }
    return result;
}

class TcpClientTest : public ::testing::Test {
public:
    TcpClientTest() {
        //InitConsoleLogger();
    }

    ~TcpClientTest() override {
    }

    // Upon creation, client is able to connect to the server, even if the server doesn't call start_accept.
    void CreateServer() {
        server_ = std::make_unique<TcpServer>(12345);
    }

    // Upon creation, attempts to connect to the server.
    void CreateClient() {
        client_ = std::make_unique<TcpClient>(
            TcpClientOptions{"127.0.0.1", "12345", 10 /*ms*/, 1 /*s*/},
            AsioContext::GetIoContext(),
            opentelemetry::logs::Provider::GetLoggerProvider()->GetLogger("OdlTraceExporterLogger"));
    }

    // Only turn on for debug. It mess with the gtest console output.
    void InitConsoleLogger() {
        auto exporter = std::unique_ptr<logs_sdk::LogRecordExporter>(new logs_exporter::OStreamLogRecordExporter);
        auto processor = logs_sdk::SimpleLogRecordProcessorFactory::Create(std::move(exporter));
        std::shared_ptr<opentelemetry::sdk::logs::LoggerProvider> sdk_provider(
            logs_sdk::LoggerProviderFactory::Create(std::move(processor)));
        const std::shared_ptr<logs_api::LoggerProvider> &api_provider = sdk_provider;
        logs_api::Provider::SetLoggerProvider(api_provider);
    }

    void SetUp() override { }

    void TearDown() override {
        server_->ShutDown();
        client_->ShutDown();
        // Wait for all callbacks finish.
        std::this_thread::sleep_for(std::chrono::milliseconds(100));
        server_.reset();
        client_.reset();
    }

    std::unique_ptr<TcpServer> server_;
    std::unique_ptr<TcpClient> client_;
};

TEST_F(TcpClientTest, OneMessage) {
    CreateServer();
    CreateClient();
    auto message = std::make_shared<std::string>(
        std::string(1, '\x01') + sizeTTo4ByteString(5) + "Hello");
    // wait for server start and client connect. Empty the callback queue. ~500ms.
    std::this_thread::sleep_for(std::chrono::milliseconds(1000));

    client_->Send(message);
    // Wait for handle_write to be called and finish.
    std::this_thread::sleep_for(std::chrono::milliseconds(10));
    EXPECT_EQ(0, client_->failure_count());
    EXPECT_EQ(1, client_->success_count());
    EXPECT_EQ("Hello", server_->get_last_message());
}

TEST_F(TcpClientTest, StartupConnectionSlow) {
    CreateClient();
    auto message = std::make_shared<std::string>(
        std::string(1, '\x01') + sizeTTo4ByteString(5) + "Hello");
    // Empty the callback queue. ~XXms.
    std::this_thread::sleep_for(std::chrono::milliseconds(100));

    client_->Send(message);
    // Wait for handle_write to be called and finish.
    std::this_thread::sleep_for(std::chrono::milliseconds(10));
    // Server not started, no message received.
    EXPECT_EQ(1, client_->failure_count());
    EXPECT_EQ(0, client_->success_count());

    CreateServer();
    // wait for server start and client connect. Empty the callback queue. ~500ms.
    std::this_thread::sleep_for(std::chrono::milliseconds(1000));
    client_->Send(message);
    // Wait for handle_write to be called and finish.
    std::this_thread::sleep_for(std::chrono::milliseconds(10));
    EXPECT_EQ(1, client_->failure_count());
    EXPECT_EQ(1, client_->success_count());
    EXPECT_EQ("Hello", server_->get_last_message());
}

TEST_F(TcpClientTest, ServerDownAndBackUp) {
    CreateServer();
    CreateClient();
    auto message = std::make_shared<std::string>(
        std::string(1, '\x01') + sizeTTo4ByteString(5) + "Hello");
    // wait for server start and client connect. Empty the callback queue. ~500ms.
    std::this_thread::sleep_for(std::chrono::milliseconds(1000));

    client_->Send(message);
    // Wait for handle_write to be called and finish.
    std::this_thread::sleep_for(std::chrono::milliseconds(10));
    EXPECT_EQ(0, client_->failure_count());
    EXPECT_EQ(1, client_->success_count());
    EXPECT_EQ("Hello", server_->get_last_message());

    server_->ShutDown();
    // Empty the callback queue. ~XXms.
    std::this_thread::sleep_for(std::chrono::milliseconds(100));
    client_->Send(message);
    // Wait for handle_write to be called and finish.
    std::this_thread::sleep_for(std::chrono::milliseconds(10));
    EXPECT_EQ(1, client_->failure_count());
    EXPECT_EQ(1, client_->success_count());

    server_->Start();
    // wait for server start and client connect. Empty the callback queue. ~500ms.
    std::this_thread::sleep_for(std::chrono::milliseconds(1000));
    message = std::make_shared<std::string>(
        std::string(1, '\x01') + sizeTTo4ByteString(11) + "Hello again");
    client_->Send(message);
    // Wait for handle_write to be called and finish.
    std::this_thread::sleep_for(std::chrono::milliseconds(10));
    EXPECT_EQ(1, client_->failure_count());
    EXPECT_EQ(2, client_->success_count());
    EXPECT_EQ("Hello again", server_->get_last_message());
}

TEST_F(TcpClientTest, ClientDownAndBackUp) {
    CreateServer();
    CreateClient();
    auto message = std::make_shared<std::string>(
        std::string(1, '\x01') + sizeTTo4ByteString(5) + "Hello");
    // wait for server start and client connect. Empty the callback queue. ~500ms.
    std::this_thread::sleep_for(std::chrono::milliseconds(1000));

    client_->Send(message);
    // Wait for handle_write to be called and finish.
    std::this_thread::sleep_for(std::chrono::milliseconds(10));
    EXPECT_EQ(0, client_->failure_count());
    EXPECT_EQ(1, client_->success_count());
    EXPECT_EQ("Hello", server_->get_last_message());

    client_.reset();
    CreateClient();
    // wait for server start and client connect. Empty the callback queue. ~500ms.
    std::this_thread::sleep_for(std::chrono::milliseconds(1000));
    message = std::make_shared<std::string>(
        std::string(1, '\x01') + sizeTTo4ByteString(11) + "Hello again");
    client_->Send(message);
    // Wait for handle_write to be called and finish.
    std::this_thread::sleep_for(std::chrono::milliseconds(10));
    EXPECT_EQ(0, client_->failure_count());
    EXPECT_EQ(1, client_->success_count());
    EXPECT_EQ("Hello again", server_->get_last_message());
}