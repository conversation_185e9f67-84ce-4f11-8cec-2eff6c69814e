﻿// <copyright file="ServiceDependentLogger.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.ServiceProcess;
using System.Threading;
using Microsoft.Extensions.Logging;

namespace Microsoft.R9.Extensions.Telemetry.Exporters.Base
{
    /// <summary>
    /// DBA service dependent logger
    /// </summary>
    public class ServiceDependentLogger : IInternalLogger, IDisposable
    {
        /// <summary>
        /// limitation set for whole event package
        /// </summary>
        private const int MaxSize = 31 * 1024;

        /// <summary>
        /// Event trace logger
        /// </summary>
        private readonly Lazy<IInternalLogger> etwLogger;

        /// <summary>
        /// file logger
        /// </summary>
        private readonly Lazy<IInternalLogger> fileLogger;

        /// <summary>
        /// Service status
        /// </summary>
        private ServiceControllerStatus currentStatus;

        /// <summary>
        /// Flag if should write to etw
        /// </summary>
        private bool writeToEtw;

        /// <summary>
        /// To detect redundant calls
        /// </summary>
        private bool isDisposed;

        /// <summary>
        /// determine whether to fall back
        /// </summary>
        private bool enableFallBack;

        /// <summary>
        /// Initializes a new instance of the <see cref="ServiceDependentLogger" /> class.
        /// </summary>
        /// <param name="enableFallBack">determine whether to fall back</param>
        public ServiceDependentLogger(bool enableFallBack)
        {
            etwLogger = new Lazy<IInternalLogger>(() => new ETWLogger());
            fileLogger = new Lazy<IInternalLogger>(() => new FileLogger());
            this.enableFallBack = enableFallBack;
            if (enableFallBack)
            {
                // Initialize service state. Consider service not exist as not running (as we only care about running/!running)
                currentStatus = ServiceHelper.GetServiceStatus(ODLExporterCommonSettings.OdlServiceName);
                writeToEtw = currentStatus == ServiceControllerStatus.Running;
            }
            else
            {
                writeToEtw = true;
            }

            // TODO: will add a thread or serive to monitor state of the ODL instance. Init / Start it in the constructor here.
        }

        /// <summary>
        /// determine if current message need fall back to use disk logger
        /// </summary>
        /// <param name="logType">log type</param>
        /// <param name="message">serialized payload</param>
        /// <returns>If need to fall back </returns>
        internal static bool HitETWSizeLimit(string logType, string message)
        {
            return message.Length > MaxSize;
        }

        /// <summary>
        /// This code added to correctly implement the disposable pattern.
        /// </summary>
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        /// <summary>
        /// Log implement
        /// </summary>
        /// <param name="logLevel">Log level</param>
        /// <param name="logtype">Log type</param>
        /// <param name="atguid">Audit trail guid</param>
        /// <param name="message">Log message</param>
        /// <param name="dimensionPairs">Customer fields and values </param>
        public void Log(LogLevel logLevel, string logtype, string atguid, string message, Dictionary<string, string> dimensionPairs)
        {
            GetActiveLogger(logtype, message).Log(logLevel, logtype, atguid, message, dimensionPairs);
        }

        /// <summary>
        /// Get current active logger
        /// </summary>
        /// <param name="logtype">ODL logtype</param>
        /// <param name="message">message</param>
        /// <returns>Current active logger</returns>
        internal IInternalLogger GetActiveLogger(string logtype, string message)
        {
            if (!enableFallBack || (writeToEtw && !HitETWSizeLimit(logtype, message)))
            {
                try
                {
                    return etwLogger.Value;
                }
                catch (Exception e)
                {
#pragma warning disable SYSLIB0006 // https://o365exchange.visualstudio.com/O365%20Core/_workitems/edit/3818406
                    // ThreadAbortException is a special exception that can be caught by application code,
                    // but is rethrown at the end of the catch block unless ResetAbort is called.
                    // ResetAbort cancels the request to abort, and prevents the ThreadAbortException from terminating the thread.
                    if (e is ThreadAbortException)
                    {
                        // Add try-catch to catch ThreadStateException
                        try
                        {
                            Thread.ResetAbort();
                        }
                        catch (ThreadStateException e2)
                        {
                            ExporterLogger.Log.LogWriteEventError(logtype, e2.ToString());
                        }
                    }
#pragma warning restore SYSLIB0006 // https://o365exchange.visualstudio.com/O365%20Core/_workitems/edit/3818406
                }
            }

            return fileLogger.Value;
        }

        /// <summary>
        /// Dispose instance
        /// </summary>
        /// <param name="disposing">Dispose or finalize</param>
        protected virtual void Dispose(bool disposing)
        {
            if (!isDisposed)
            {
                if (disposing)
                {
                    if (etwLogger.IsValueCreated)
                    {
                        ((IDisposable)etwLogger.Value).Dispose();
                    }
                }

                isDisposed = true;
            }
        }

        /// <summary>
        /// trace implement
        /// </summary>
        /// <param name="logtype">Log type</param>
        /// <param name="atguid">Audit trail guid</param>
        /// <param name="message">Log message</param>
        public void Trace(string logtype, string atguid, string message)
        {
            GetActiveLogger(logtype, message).Trace(logtype, atguid, message);
        }

        // TODO: need to add a callback function, when status changed to running or stop, call this function to change the value of 'writeToETW'.
    }
}