<?xml version="1.0" encoding="utf-8" ?>
<Project xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  
  <ItemGroup>
    <QCustomOutput Include="$(QVersionPropertyFile)" />
  </ItemGroup>

  <PropertyGroup>
    <CoreBuildDependsOn></CoreBuildDependsOn>
  </PropertyGroup>

  <Target Name="Build" DependsOnTargets="$(BuildDependsOn)">

    <GenerateVersionPropertyFile
      FilePath="$(QVersionPropertyFile)"
      AssemblyVersion="$(AssemblyVersion)"
      FileVersion="$(FileVersion)"
      InformationalVersion="$(InformationalVersion)"
    />

  </Target>

  <UsingTask
    TaskName="GenerateVersionPropertyFile"
    TaskFactory="CodeTaskFactory"
    AssemblyFile="$(MSBuildToolsPath)\Microsoft.Build.Tasks.Core.dll">
    <ParameterGroup>
      <FilePath Required="true" />
      <AssemblyVersion Required="true" />
      <FileVersion Required="true" />
      <InformationalVersion Required="true" />
    </ParameterGroup>
    <Task>
      <Using Namespace="Microsoft.Build.Construction" />
      <Reference Include="Microsoft.Build" />
      <Reference Include="System.Xml" />
      <Code Type="Fragment" Language="cs">
        <![CDATA[
        
        Log.LogMessage("Creating version property file '{0}'", FilePath);
        
        Directory.CreateDirectory(Path.GetDirectoryName(FilePath));
        
        ProjectRootElement projectRootElement = ProjectRootElement.Create(FilePath);
        
        ProjectPropertyGroupElement propertyGroup = projectRootElement.AddPropertyGroup();
        
        propertyGroup.AddProperty("AssemblyVersion", AssemblyVersion);
        propertyGroup.AddProperty("FileVersion", FileVersion);
        propertyGroup.AddProperty("InformationalVersion", InformationalVersion);
        
        projectRootElement.Save();
        
        ]]>
      </Code>
    </Task>
  </UsingTask>
</Project>