﻿[*.cs]

# CS8618: Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
dotnet_diagnostic.CS8618.severity = none

# CS8601: Possible null reference assignment.
dotnet_diagnostic.CS8601.severity = none

# CS8625: Cannot convert null literal to non-nullable reference type.
dotnet_diagnostic.CS8625.severity = none

# CS8604: Possible null reference argument.
dotnet_diagnostic.CS8604.severity = none

# IDE0003: Remove qualification
dotnet_diagnostic.IDE0003.severity = none

# CS8622: Nullability of reference types in type of parameter doesn't match the target delegate (possibly because of nullability attributes).
dotnet_diagnostic.CS8622.severity = none

# R9EXP0008: Type is for evaluation purposes only and is subject to change or removal in future updates. Suppress this diagnostic to proceed.
dotnet_diagnostic.R9EXP0008.severity = none

# R9EXPDEV: Type is for evaluation purposes only and is subject to change or removal in future updates. Suppress this diagnostic to proceed.
dotnet_diagnostic.R9EXPDEV.severity = none
