﻿// <copyright file="Rule.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.Linq;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Logging.Abstractions;

namespace Microsoft.M365.Core.Telemetry.R9.Logging.Substrate.Sampler
{
    /// <summary>
    /// Represents a concrete rule used to match and sample the log entries.
    /// </summary>
    internal class Rule
    {
        private SamplingStrategy? Strategy { get; set; }
        
        private Matcher? Matcher { get; set; }
        
        private List<Constraint> Constraints { get; set; }

        private StrategyParameter Parameter { get; set; }

        private bool IsInvalid { get; set; }

        /// <summary>
        /// Initializes a new instance of the <see cref="Rule"/> class with the specified constraints and strategy parameter.
        /// </summary>
        /// <param name="constraints">Constraints</param>
        /// <param name="strategyParameter">StrategyParameter</param>
        public Rule(List<Constraint> constraints, StrategyParameter strategyParameter)
        {
            Constraints = constraints;
            Parameter = strategyParameter;
            IsInvalid = !RuleValidator.IsValid(Constraints, Parameter);

            if (!IsInvalid)
            {
                Matcher = new Matcher(Constraints);
                Strategy = Parameter.Type switch
                {
                    StrategyType.Random => new RandomStrategy(Parameter.SampleRate),
                    StrategyType.HashBasedRandom when Parameter.HashKey != null => new HashBasedRandomStrategy(Parameter.SampleRate, Parameter.HashKey),
                    _ => throw new NotImplementedException() // Should never happen, we should have validated the strategy parameter before.
                };
            }
        }

        /// <summary>
        /// Determines whether the rule should be skipped based on its validity.
        /// </summary>
        /// <returns><see langword="true" /> if the rule should be skipped; otherwise, <see langword="false" />.</returns>
        public bool ShouldSkip() => IsInvalid;

        /// <summary>
        /// Tries to match the given log entry against the constraints.
        /// </summary>
        /// <typeparam name="TState">The type of the state associated with the log entry.</typeparam>
        /// <param name="logEntry">The log entry to match.</param>
        /// <returns><see langword="true" /> if the log entry matches the constraints; otherwise, <see langword="false" />.</returns>
        public bool TryMatch<TState>(in LogEntry<TState> logEntry)
        {
            return !IsInvalid && Matcher!.TryMatch(logEntry);
        }

        /// <summary>
        /// Makes a sampling decision for the provided <paramref name="logEntry"/>.
        /// If sampled, keep the log. If not, discard the log.
        /// </summary>
        /// <param name="logEntry">The log entry used to make the sampling decision for.</param>
        /// <typeparam name="TState">The type of the log entry state.</typeparam>
        /// <returns><see langword="true" /> if the log record should be sampled; otherwise, <see langword="false" />.</returns>
        public bool ShouldSample<TState>(in LogEntry<TState> logEntry)
        {   
            return !IsInvalid && Strategy!.ShouldSample(logEntry);
        }
    }
}
