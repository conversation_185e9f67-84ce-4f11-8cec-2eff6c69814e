﻿// <copyright file="NrtTcpClientMonitor.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System.Collections.Concurrent;
using System.Diagnostics.CodeAnalysis;
using Microsoft.Office.BigData.DataLoader;

namespace Microsoft.M365.ODL.NrtTcpClient
{
    /// <summary>
    /// Class for monitor the aliveness of the TCP connection of NrtTcpClient
    /// </summary>
    [ExcludeFromCodeCoverage]
    public sealed class NrtTcpClientMonitor
    {
        private readonly object lockForCheckLiveness = new object();

        private Timer timer;

        private int runIntervalMilliSec;

        private readonly IEnumerable<NrtTcpClientThread> threads;

        private int timeoutForLiveness;

        private ODLNRTRequest heartbeatMessage = new ODLNRTRequest();

        private long seq = 0;

        private IOdlLogger logger = OdlLogger.Instance;

        private bool isRunning = false;

        private bool isCosmic = false;

        /// <summary>
        /// The ctor
        /// </summary>
        /// <param name="runIntervalMilliSec">health check running interval in milli sec</param>
        /// <param name="threads">the threads to be monitored</param>
        /// <param name="timeoutForLiveness">the timeout for determing tcp connection healthy</param>
        /// <param name="isCosmic">if current env is cosmic</param>
        public NrtTcpClientMonitor(IEnumerable<NrtTcpClientThread> threads, int runIntervalMilliSec = 30000, int timeoutForLiveness = 1000, bool isCosmic = false)
        {
            if (null == threads || !threads.Any())
            {
                throw new ArgumentNullException($"the TCP connections to be monitored is either null or empty:{threads}");
            }

            this.runIntervalMilliSec = runIntervalMilliSec;
            this.threads = threads;
            this.timeoutForLiveness = timeoutForLiveness;
            this.isCosmic = isCosmic;

            // Reusable heartbeat request
            this.heartbeatMessage = this.GetHeartBeatMessage();
        }

        private ODLNRTRequest GetHeartBeatMessage()
        {
            ODLNRTRequest heartbeat = new ODLNRTRequest();
            heartbeat.Head = new ODLNRTCommonHead();
            heartbeat.Head.CommandType = ODLNRTCommandType.OdlnrtcmdHeartbeat;
            heartbeat.Head.Timestamp = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds();
            heartbeat.Head.Sequence = Interlocked.Increment(ref this.seq);
            heartbeat.HeartbeatReq = new ODLNRTHeartbeatReq();

            return heartbeat;
        }

        /// <summary>
        /// start work
        /// </summary>
        /// <param name="delayInterval">
        /// The delay interval to wait before starting the first iteration.
        /// </param>
        public void StartWorkAfterDelay(int delayInterval)
        {
            // Try connect at the very beginning
            this.Work(null);

            this.timer = new Timer(
                this.Work,
                null,                   // state
                delayInterval,          // dueTime (the delay before starting)
                runIntervalMilliSec);      // period (interval between invocations)
        }

        private bool NeedUpdatePort(NrtTcpClientThread thread, int realPort)
        {
            if (isCosmic)
            {
                return false;
            }
            return (thread.Port != realPort) && RegistryReader.IsValidPort(realPort);
        }

        /// <summary>
        /// worker task to check the liveness of the threads and try to reconnect them if possible
        /// </summary>
        /// <param name="state">
        /// The state object containing application-specific context.  This is not used by this method.
        /// </param>
        private void Work(object state)
        {
            lock (lockForCheckLiveness)
            {
                if (isRunning)
                {
                    return;
                }

                isRunning = true;
            }

            try
            {
                long totalCnt = 0, fallbackCnt = 0;
                Dictionary<string, long> totalCntPerSdksource = new ();
                Dictionary<string, long> fallBackCntPerSdksource = new ();

                var registryPort = isCosmic ? -1 : RegistryReader.GetTcpPort();
                foreach (var thread in threads)
                {
                    bool needUpdatePort = false;
                    try
                    {
                        if (!thread.IsDisposing && ((needUpdatePort = NeedUpdatePort(thread, registryPort)) || !CheckAliveness(thread)))
                        {
                            if (needUpdatePort)
                            {
                                thread.Port = registryPort;
                            }

                            thread.TryConnect(timeoutForLiveness);
                        }
                    }
                    catch (Exception e)
                    {
                        if (TcpUtils.IsThreadStopException(e))
                        {
                            continue;
                        }

                        logger.Log(LogLevel.Error, LogEventId.TcpClientMonitorError, $"Error when checking healthness of thread:{e}");
                        thread.IsAlive = false;
                    }
                    finally
                    {
                        this.GetStatisticalData(thread, false, out long threadTotalCnt, out long threadFallbackCnt, out ConcurrentDictionary<string, long> threadTotalCntPerSdksource, out ConcurrentDictionary<string, long> threadFallbackCntPerSdksource);
                        totalCnt += threadTotalCnt;
                        fallbackCnt += threadFallbackCnt;
                        totalCntPerSdksource = totalCntPerSdksource.Concat(threadTotalCntPerSdksource).GroupBy(kvp => kvp.Key).ToDictionary(g => g.Key, g => g.Sum(kvp => kvp.Value));
                        fallBackCntPerSdksource = fallBackCntPerSdksource.Concat(threadFallbackCntPerSdksource).GroupBy(kvp => kvp.Key).ToDictionary(g => g.Key, g => g.Sum(kvp => kvp.Value));
                    }
                }

                this.SendStatisticalData(totalCnt, fallbackCnt, totalCntPerSdksource, fallBackCntPerSdksource);
            }
            finally
            {
                lock (lockForCheckLiveness)
                {
                    isRunning = false;
                }
            }
        }

        /// <summary>
        /// Get StatisticalData
        /// </summary>
        /// <param name="thread"></param>
        /// <param name="force">force to get statistic data</param>
        /// <param name="threadTotalCnt"></param>
        /// <param name="threadFallbackCnt"></param>
        private void GetStatisticalData(NrtTcpClientThread thread, bool force, out long threadTotalCnt, out long threadFallbackCnt, out ConcurrentDictionary<string, long> threadTotalCntPerSdksource, out ConcurrentDictionary<string, long> threadFallbackCntPerSdksource)
        {
            threadTotalCnt = 0;
            threadFallbackCnt = 0;
            threadTotalCntPerSdksource = new ();
            threadFallbackCntPerSdksource = new ();

            try
            {
                if (force || DateTime.UtcNow - thread.LastSendStatisticTime >= thread.SendStatisticsInterval
                    || thread.TotalRecordCount.CurrentValue >= thread.MaxRecordCountForStatic
                    || thread.FallbackRecordCount.CurrentValue >= thread.MaxRecordCountForStatic)
                {
                    thread.TotalRecordCount.GetAndReset(out threadTotalCnt);
                    thread.TotalRecordCount.GetAndResetKvp(out threadTotalCntPerSdksource);
                    thread.FallbackRecordCount.GetAndReset(out threadFallbackCnt);
                    thread.FallbackRecordCount.GetAndResetKvp(out threadFallbackCntPerSdksource);

                    thread.LastSendStatisticTime = DateTime.UtcNow;
                }
            }
            catch { }
        }

        private void SendStatisticalData(long totalCnt, long fallbackCnt, Dictionary<string, long> totalCntPerSdksource, Dictionary<string, long> fallbackCntPerSdksource)
        {
            try
            {
                if (totalCnt > 0 || fallbackCnt > 0)
                {
                    logger.TraceLog(LogLevel.Information, LogEventId.TcpClientStatisticalTraceInfo,
$"Total records cnt:{totalCnt}, fallback to disk records cnt:{fallbackCnt}");
                }

                if (totalCntPerSdksource != null && totalCntPerSdksource.Count > 0)
                {
                    foreach (var kvp in totalCntPerSdksource)
                    {
                        logger.TraceLog(LogLevel.Information, LogEventId.TcpClientStatisticalTraceInfo, $"Total records per SDKSouce {kvp.Key} cnt:{kvp.Value}");
                    }
                }

                if (fallbackCntPerSdksource != null && fallbackCntPerSdksource.Count > 0)
                {
                    foreach (var kvp in fallbackCntPerSdksource)
                    {
                        logger.TraceLog(LogLevel.Information, LogEventId.TcpClientStatisticalTraceInfo, $"Fallback to disk records per SDKSouce {kvp.Key} cnt:{kvp.Value}");
                    }
                }
            }
            catch { }
        }

        /// <summary>
        /// CheckAliveness
        /// </summary>
        /// <param name="tcpClient">the tcp client to be checked for</param>
        /// <returns>true if the connection is healthy</returns>
        private bool CheckAliveness(NrtTcpClientThread tcpClient)
        {
            if (tcpClient == null || !tcpClient.IsInit())
            {
                return false;
            }

            try
            {
                // Avoid the new round starts even if the previous one is not ended
                lock (this.lockForCheckLiveness)
                {
                    this.heartbeatMessage.Head.Sequence = Interlocked.Increment(ref this.seq);
                    this.heartbeatMessage.Head.Timestamp = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds();

                    tcpClient.IsAlive = tcpClient.SendData(this.heartbeatMessage);
                }
            }
            catch (Exception e)
            {
                if (TcpUtils.IsThreadStopException(e))
                {
                    throw;
                }

                logger.Log(LogLevel.Error, LogEventId.TcpClientMonitorError, $"Error when checking aliveness:{e}");
                tcpClient.IsAlive = false;
            }

            return tcpClient.IsAlive;
        }

        /// <summary>
        /// Disposes this instance
        /// </summary>
        public void Dispose()
        {
            try
            {
                if (this.timer != null)
                {
                    this.timer.Dispose();
                    this.timer = null;
                }

                long totalCnt = 0, fallbackCnt = 0;
                Dictionary<string, long> totalCntPerSdksouce = new Dictionary<string, long>();
                Dictionary<string, long> fallBackCntPerSdksource = new Dictionary<string, long>();

                foreach (var thread in this.threads)
                {
                    GetStatisticalData(thread, true, out long threadTotalCnt, out long threadFallbackCnt, out ConcurrentDictionary<string, long> threadTotalCntPerSdksource, out ConcurrentDictionary<string, long> threadFallbackCntPerSdksource);
                    totalCnt += threadTotalCnt;
                    fallbackCnt += threadFallbackCnt;
                    totalCntPerSdksouce = totalCntPerSdksouce.Concat(threadTotalCntPerSdksource).GroupBy(kvp => kvp.Key).ToDictionary(g => g.Key, g => g.Sum(kvp => kvp.Value));
                    fallBackCntPerSdksource = fallBackCntPerSdksource.Concat(threadFallbackCntPerSdksource).GroupBy(kvp => kvp.Key).ToDictionary(g => g.Key, g => g.Sum(kvp => kvp.Value));
                }

                this.SendStatisticalData(totalCnt, fallbackCnt, totalCntPerSdksouce, fallBackCntPerSdksource);
            }
            catch (Exception e)
            {
                logger.Log(LogLevel.Error, LogEventId.TcpClientMonitorError, $"Error when disposing client monitor:{e}");
            }
        }
    }
}
