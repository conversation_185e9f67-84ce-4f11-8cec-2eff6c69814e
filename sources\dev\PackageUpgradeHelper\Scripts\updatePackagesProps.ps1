param(
    [Parameter(Mandatory=$false)]
    [string]$OutputFilePath = "./OutputFiles/UpgradeAddList.txt",
    
    [Parameter(Mandatory=$false)]
    [string]$PackagesFilePath = "q:/src/Substrate/Packages.props",
    
    [Parameter(Mandatory=$false)]
    [string]$CorextFilePath = "q:/src/Substrate/build/corext/corext.config",
    
    [Parameter(Mandatory=$false)]
    [string]$AdditionalPackagesFilePath = "./OutputFiles/targetPackages.txt"
)

# Prevent XML output from being displayed
$ProgressPreference = 'SilentlyContinue'
$global:xmlOutput = $null

# Load the output from analyzeOutput.ps1
$outputLines = Get-Content -Path $OutputFilePath

# Load the additional packages from packages.txt
$additionalPackagesLines = Get-Content -Path $AdditionalPackagesFilePath

# Load the XML content from Packages.props
[xml]$packagesXmlContent = Get-Content -Path $PackagesFilePath

# Load the XML content from corext.config
[xml]$corextXmlContent = Get-Content -Path $CorextFilePath

# Get the namespace from the document (if any)
$packagesNamespace = $packagesXmlContent.DocumentElement.NamespaceURI
$corextNamespace = $corextXmlContent.DocumentElement.NamespaceURI

# Initialize a dictionary to store the updates
$updatesDict = @{}

# Parse the output and populate the updates dictionary
$currentSection = ""
foreach ($line in $outputLines) {
    if ($line -match "Packages requiring upgrade:") {
        $currentSection = "upgrade"
    } elseif ($line -match "Packages to be added:") {
        $currentSection = "noDependency"
    } elseif ($line -match "^\s*(.+): (.+)$") {
        $packageName = $matches[1]
        $packageVersion = $matches[2]
        $updatesDict[$packageName] = $packageVersion
    }
}

# Parse the additional packages and populate the updates dictionary
foreach ($line in $additionalPackagesLines) {
    if ($line -match "^\s*(.+)\s+(.+)$") {
        $packageName = $matches[1]
        $packageVersion = $matches[2]
        $updatesDict[$packageName] = $packageVersion
    }
}

# Update the Packages.props file
$packageDict = @{}
$packageVersions = $packagesXmlContent.Project.ItemGroup.PackageVersion
foreach ($packageVersion in $packageVersions) {
    $packageName = $packageVersion.Include
    $version = $packageVersion.Version
    if ($updatesDict.ContainsKey($packageName)) {
        $version = $updatesDict[$packageName]
    }
    $packageDict[$packageName] = $version
}

# Add new packages from updates
foreach ($packageName in $updatesDict.Keys) {
    if (-not $packageDict.ContainsKey($packageName)) {
        $packageDict[$packageName] = $updatesDict[$packageName]
    }
}

# Create a new ItemGroup with packages in alphabetical order
$itemGroup = $packagesXmlContent.Project.ItemGroup | Select-Object -First 1
$newItemGroup = $packagesXmlContent.CreateElement("ItemGroup", $packagesNamespace)

# Add packages in sorted order to the new ItemGroup
$packageDict.GetEnumerator() | Sort-Object Name | ForEach-Object {
    $packageName = $_.Key
    $version = $_.Value
        
    # Add comment after package
    $comment = $packagesXmlContent.CreateComment("This comment exists to prevent Git merge conflicts.  Do not delete it when editing this file.")
    $null = $newItemGroup.AppendChild($comment)

    # Create new package element
    $newPackageVersion = $packagesXmlContent.CreateElement("PackageVersion", $packagesNamespace)
    $newPackageVersion.SetAttribute("Include", $packageName)
    $newPackageVersion.SetAttribute("Version", $version)
    $null = $newItemGroup.AppendChild($newPackageVersion)
}

# Replace the existing ItemGroup with the new sorted one
$itemGroup.ParentNode.ReplaceChild($newItemGroup, $itemGroup)

# Save the updated Packages.props file
if ($PSCmdlet.ShouldProcess($PackagesFilePath, "Update package versions")) {
    $packagesXmlContent.Save($PackagesFilePath)
}

# Update the corext.config file
$packagesNode = $corextXmlContent.SelectSingleNode("//packages")
$corextPackages = $packagesNode.SelectNodes("package")

# Update existing packages in corext.config
foreach ($package in $corextPackages) {
    $packageName = $package.id
    if ($updatesDict.ContainsKey($packageName)) {
        $package.version = $updatesDict[$packageName]
        $updatesDict.Remove($packageName)
    }
}

# Get all existing package names and sort them
$existingCorextPackages = @()
foreach ($package in $corextPackages) {
    $existingCorextPackages += $package.id
}
$existingCorextPackages = $existingCorextPackages | Sort-Object

# Add new packages to corext.config in alphabetical order
foreach ($packageName in ($updatesDict.Keys | Sort-Object)) {
    $exists = $false
    foreach ($package in $corextPackages) {
        if ($package.id -eq $packageName) {
            $exists = $true
            break
        }
    }
    
    if (-not $exists) {
        # Find the correct position to insert based on alphabetical order
        $insertBefore = $null
        $allPackageNames = ($existingCorextPackages + @($packageName)) | Sort-Object
        $index = $allPackageNames.IndexOf($packageName)
        
        if ($index -lt $allPackageNames.Length - 1) {
            # Find the next package in alphabetical order
            for ($i = $index + 1; $i -lt $allPackageNames.Length; $i++) {
                $nextName = $allPackageNames[$i]
                foreach ($package in $corextPackages) {
                    if ($package.id -eq $nextName) {
                        # Find comment before this package
                        $currentNode = $package.PreviousSibling
                        while ($currentNode -and $currentNode.NodeType -ne [System.Xml.XmlNodeType]::Comment) {
                            $currentNode = $currentNode.PreviousSibling
                        }
                        if ($currentNode) {
                            $insertBefore = $currentNode
                        } else {
                            $insertBefore = $package
                        }
                        break
                    }
                }
                if ($insertBefore -ne $null) {
                    break
                }
            }
        }
        
        # Create comment
        $comment = $corextXmlContent.CreateComment("This comment exists to prevent Git merge conflicts.  Do not delete it when editing this file.")
        
        # Create new package element
        $newPackage = $corextXmlContent.CreateElement("package")
        $newPackage.SetAttribute("id", $packageName)
        $newPackage.SetAttribute("version", $updatesDict[$packageName])
        
        # Insert before existing package/comment
        $null = $packagesNode.InsertBefore($comment, $insertBefore)
        $null = $packagesNode.InsertBefore($newPackage, $insertBefore)
        
        # Update existing packages list
        $existingCorextPackages += $packageName
    }
}

# Save the updated corext.config file
if ($PSCmdlet.ShouldProcess($CorextFilePath, "Update package versions")) {
    $corextXmlContent.Save($CorextFilePath)
}

Write-Output "Packages.props and corext.config have been updated successfully."