﻿// ---------------------------------------------------------------------------
// <copyright file="ODLLoggerUtils.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>
// ---------------------------------------------------------------------------
using System;
using System.Collections.Generic;
using Newtonsoft.Json;

namespace Microsoft.R9.Extensions.Telemetry.Exporters.Base
{
    /// <summary>
    /// ODL logger utility functions.
    /// </summary>
    internal static class ODLLoggerUtils
    {
        /// <summary>
        /// default dimension value for customer dimensions.
        /// </summary>
        private const string DefaultDimensionValue = "Missing";

        /// <summary>
        /// construct dimension names and values to JSON string
        /// </summary>
        /// <param name="logtype">log type used to log error</param>
        /// <param name="dimensionPairs">customer dimension names and values</param>
        /// <returns>constructed dimensions</returns>
        internal static string ConstructDimensions(string logtype, Dictionary<string, string> dimensionPairs)
        {
            try
            {
                var customerDimensions = new List<KeyValuePair<string, string>>(dimensionPairs.Count);
                foreach (var pair in dimensionPairs)
                {
                    string value = string.IsNullOrEmpty(pair.Value) ? DefaultDimensionValue : pair.Value;
                   
                    customerDimensions.Add(new KeyValuePair<string, string>(pair.Key, value));
                }
                return JsonConvert.SerializeObject(customerDimensions);
            }
            catch (Exception e)
            {
                ExporterLogger.Log.LogWriteEventError(logtype, e.ToString());
                return string.Empty;
            }
        }
    }
}