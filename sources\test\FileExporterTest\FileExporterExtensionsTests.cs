// <copyright file="FileExporterExtensionsTests.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System.Linq;
using Microsoft.R9.Extensions.SecurityTelemetry;
using Xunit;

namespace Microsoft.M365.Core.Telemetry.FileExporter.Tests
{
#pragma warning disable R9EXPDEV
#pragma warning disable R9EXP0008
    /// <summary>
    /// FileExporterExtensionsTests
    /// </summary>
    public class FileExporterExtensionsTests
    {
        /// <summary>
        /// Test AddFileExporter
        /// </summary>
        [Fact]
        public void AddNRTFileExporter()
        {
            var exporter = FileExporterExtensions.CreatNRTFileExporter();
            Assert.NotNull(exporter);

            string[] weekDays = new string[] { "Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat" };
            var batch = new Batch<string>(weekDays, 7);

            exporter.BatchExport(batch);
            exporter.BatchExport(weekDays.ToList<string>());

            exporter.Dispose();
        }

        /// <summary>
        /// Test AddFileExporterWithOption
        /// </summary>
        [Fact]
        public void AddNRTFileExporterWithOption()
        {
            TextFileLoggerOptions options = new TextFileLoggerOptions { Directory = "logtesttofile", FileName = "app.log", EnableCleanUpArchivedFiles = true };

            var exporter = FileExporterExtensions.CreatNRTFileExporter(options);
            Assert.NotNull(exporter);

            exporter.Export("testline");

            exporter.Dispose();
        }
    }
}
