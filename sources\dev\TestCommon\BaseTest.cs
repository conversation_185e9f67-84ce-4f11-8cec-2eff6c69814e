﻿// <copyright file="BaseTest.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System;
using Microsoft.M365.Core.Telemetry.Enrichment;
using Microsoft.M365.Core.Telemetry.Hacks;
using Xunit;
using Xunit.Abstractions;

namespace Microsoft.M365.Core.Telemetry.TestCommon
{
    [Collection("Do not parallel")]
#pragma warning disable CA1063 // Implement IDisposable Correctly
    public class BaseTest : IDisposable
#pragma warning restore CA1063 // Implement IDisposable Correctly
    {
        internal FakeRegistryKey TestRegistryKey;

        public BaseTest(ITestOutputHelper output)
        {
            XUnitBugs.Output = output;
            FakeRegistryKey.ResetRegistries();
            TestRegistryKey = new FakeRegistryKey();
            DimensionValues.InternalSetRegistryKey(TestRegistryKey);
        }

#pragma warning disable CA1063 // Implement IDisposable Correctly
#pragma warning disable CA1816 // Dispose methods should call SuppressFinalize
        public void Dispose()
#pragma warning restore CA1816 // Dispose methods should call SuppressFinalize
#pragma warning restore CA1063 // Implement IDisposable Correctly
        {
        }
    }
}
