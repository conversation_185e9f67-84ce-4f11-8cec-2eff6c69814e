{"hydrated": false, "properties": {"helpUri": "https://eng.ms/docs/microsoft-security/security/azure-security/cloudai-security-fundamentals-engineering/security-integration/guardian-wiki/microsoft-guardian/general/baselines", "hydrationStatus": "This file does not contain identifying data. It is safe to check into your repo. To hydrate this file with identifying data, run `guardian hydrate --help` and follow the guidance."}, "version": "1.0.0", "baselines": {"cloudbuild": {"name": "cloudbuild", "createdDate": "2023-10-27 06:10:32Z", "lastUpdatedDate": "2023-10-27 06:10:32Z"}}, "results": {"76a31d03bb5bb69128767cb7a3dfbb42bfd50c9b4239025ee05f16b3a62ec655": {"signature": "76a31d03bb5bb69128767cb7a3dfbb42bfd50c9b4239025ee05f16b3a62ec655", "alternativeSignatures": ["eaa64271d9af7866531378ffaa264b54c08355ec6deeb9a00f2b0b803c36dc7a"], "memberOf": ["cloudbuild"], "createdDate": "2023-10-27 06:36:08Z", "expirationDate": "2024-04-14 07:01:50Z", "justification": "This error is baselined with an expiration date of 180 days from 2023-10-27 07:01:50Z"}, "fc54adb60576129af0eabda382d18fc7bd778e43f4ef46f0651b17893055748c": {"signature": "fc54adb60576129af0eabda382d18fc7bd778e43f4ef46f0651b17893055748c", "alternativeSignatures": ["c144e09081bc199b24a7fa27dd5f604e916486dab15e6f9c0faec6039d547896"], "memberOf": ["cloudbuild"], "createdDate": "2023-10-27 06:36:08Z", "expirationDate": "2024-04-14 07:01:50Z", "justification": "This error is baselined with an expiration date of 180 days from 2023-10-27 07:01:50Z"}}}