﻿// <copyright file="SecurityTelemetryTcpExporterOptions.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.Diagnostics.CodeAnalysis;
using Microsoft.R9.Extensions.SecurityTelemetry;

namespace Microsoft.M365.Core.Telemetry.ODL.SecurityTelemetryTcpExporter
{
    /// <summary>
    /// The option class for SecurityTelemetryTcpExporter
    /// </summary>
    [ExcludeFromCodeCoverage]
    public class SecurityTelemetryTcpExporterOptions
    {
        /// <summary>
        /// Count of records per TCP request
        /// </summary>
        /// <remarks>Default to 20.</remarks>
        public int RecordCountPerRequest { get; set; } = 20;

        /// <summary>
        /// Gets or sets the max queue size.
        /// </summary>
        /// <remarks>Default to 2048.</remarks>
        public int MaxQueueSize { get; set; } = 2048;

        /// <summary>
        /// Gets or sets the scheduled delay.
        /// </summary>
        /// <remarks>Default to 5 seconds.</remarks>
        public int ScheduledDelayMilliseconds { get; set; } = 5000;

        /// <summary>
        /// Gets or sets the exporter timeout.
        /// </summary>
        /// <remarks>Default to 30 seconds.</remarks>
        public int ExporterTimeoutMilliseconds { get; set; } = 30000;

        /// <summary>
        /// Gets or sets the max exporter batch size.
        /// </summary>
        /// <remarks>Default to 512.</remarks>
        public int MaxExportBatchSize { get; set; } = 512;

        /// <summary>
        /// The interval to check connection healthy in milli seconds
        /// </summary>
        /// <remarks>Default to 30s</remarks>
        public int ConnectionHealthCheckInterval { get; set; } = 30000;

        /// <summary>
        /// Thread number for async data sending
        /// </summary>
        /// <remarks>Default to 5.</remarks>
        public int ThreadNumAsyncSend { get; set; } = 5;

        /// <summary>
        /// The queue size for async data sending
        /// </summary>
        /// <remarks>Default to 1000.</remarks>
        public int QueueSizeAsnycSend { get; set; } = 1000;

        /// <summary>
        /// Timeout for data sending in milli seconds
        /// </summary>
        /// <remarks>Default to 5000 ms.</remarks>
        public int SendTimeout { get; set; } = 3000;

        /// <summary>
        /// Timeout for tcp connection in milli seconds
        /// </summary>
        /// <remarks>Default to 5000 ms.</remarks>
        public int ConnectTimeout { get; set; } = 5000;

        /// <summary>
        /// The BufferSize of data buffer for carrying serilized tcp request
        /// </summary>
        /// <remarks>Default to 128KB.</remarks>
        public int BufferSize { get; set; } = 32 * 4 * 1024;

        /// <summary>
        /// the batch size for sending metric of TCP client
        /// </summary>
        /// <remarks>Default to 500.</remarks>
        public int BatchSizeForSendingMetric { get; set; } = 500;

        /// <summary>
        /// Time interval for sending metric
        /// </summary>
        /// <remarks>Default to 5 mins.</remarks>
        public TimeSpan SendMetricInterval { get; set; } = TimeSpan.FromMinutes(5);

        /// <summary>
        /// Whether using adhoc logger(Event log)
        /// </summary>
        /// <remarks>Default to true</remarks>
        public bool UseAdhocLogger { get; set; } = true;

        /// <summary>
        /// Gets or sets a value indicating whether to enable archived files clean up or not.
        /// </summary>
        /// <remarks>Default to true.</remarks>
        public bool EnableCleanUpArchivedFiles { get; set; } = true;

        /// <summary>
        /// Gets or sets the max retention <see cref="TimeSpan"/>.
        /// </summary>
        /// <remarks>Default to 2 days.</remarks>
        public TimeSpan RetentionInterval { get; set; } = TimeSpan.FromDays(2);

        /// <summary>
        /// Gets or sets the max retention count.
        /// </summary>
        /// <remarks>Default to 99 files.</remarks>
        public long RetentionCount { get; set; } = 99;

        /// <summary>
        /// Gets or sets the TCP NRT server ip.
        /// By default, it is set to localhost.
        /// </summary>
        public string ServerIP { get; set; } = @"127.0.0.1";

        /// <summary>
        /// Gets or sets the TCP NRT server port.
        /// </summary>
        public int TCPPort { get; set; } = 9527;

        /// <summary>
        /// If current environment is cosmic
        /// </summary>
        public bool IsCosmic { get; set; } = false;

        /// <summary>
        /// Switch of if need to enableTls
        /// </summary>
        public bool EnableTls { get; set; } = false;

        /// <summary>
        /// Gets or sets the Tls cert Path.
        /// e.g. "tls.pfx"
        /// </summary>
        public string TlsCertFilePath { get; set; } = string.Empty;

        /// <summary>
        /// Gets or sets the Tls pfx password.
        /// </summary>
        [System.Text.Json.Serialization.JsonIgnore]
        public string TlsPfxPassword { get; set; } = string.Empty;

        /// <summary>
        /// Gets or sets the Tls CA cert path.
        /// </summary>
        public string TlsCACertFilePath { get; set; } = string.Empty;

        /// <summary>
        /// Gets or sets the Tls certs folder path.
        /// </summary>
        public string TlsCertsFolderPath { get; set; } = string.Empty;

        /// <summary>
        /// Gets or sets the Tls cert subject name.
        /// </summary>
        public string TlsCertSubjectName { get; set; } = string.Empty;

        /// <summary>
        /// Time interval for sending security telemetry heartbeat
        /// </summary>
        /// <remarks>Default to 5 mins.</remarks>
        public TimeSpan HeartbeatIntervalMilliseconds { get; set; } = TimeSpan.FromMinutes(5);

        /// <summary>
        /// Gets or sets the formatter for <see cref="SecurityRecord"/>.
        /// </summary>
        /// <remarks>Default output format:
        /// SchemaName:SchemaName
        /// StaticPropertyKey1:StaticPropertyValue1
        /// StaticPropertyKey2:StaticPropertyValue2
        /// DynamicPropertyKey1:DynamicPropertyValue1
        /// DynamicPropertyKey2:DynamicPropertyValue2
        /// ...
        /// </remarks>
        public Func<SecurityRecord, IReadOnlyDictionary<string, object>, string> SecurityRecordFormatter { get; set; } = (securityRecord, staticProperties) => DefaultSecurityRecordFormatter
        .FormatSecurityRecord(securityRecord, staticProperties);
    }
}
