<?xml version="1.0" encoding="utf-8"?>
<Project Sdk="Microsoft.Build.NoTargets">
  <PropertyGroup>
    <TargetFramework>net462</TargetFramework>
    <QTestType></QTestType>
    <QTestDirToDeploy></QTestDirToDeploy>
    <Project_IsSigned>false</Project_IsSigned>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="OSS.Build.ExchangeTestStudio" GeneratePathProperty="true" PrivateAsset="All"/>
    <PackageReference Include="Exchange.Focus.BuildTasks" GeneratePathProperty="true" PrivateAsset="All"/>
    <Content Include="$(PkgOSS_Build_ExchangeTestStudio)\ExchangeTestStudio\**\*.*"/>
    <Content Include="$(MSBuildProjectDirectory)\DependencyConfiguration.xml" />
    <Content Include="$(MSBuildProjectDirectory)\SetupScripts\PreInstallationCustomScript.ps1" />
    <Content Include="$(MSBuildProjectDirectory)\SetupScripts\PostInstallationCustomScript.ps1" />
  </ItemGroup>

  <!-- BuildType -->
  <PropertyGroup>
    <!-- Map standard Visual Studio configuration names to CoreXT build types -->
    <BuildType Condition="'$(Configuration)' == 'Release'">retail</BuildType>
    <BuildType Condition="'$(Configuration)' == 'Debug'">debug</BuildType>

    <!-- Fallback to Configuration -->
    <BuildType Condition="'$(BuildType)' == ''">$(Configuration)</BuildType>

    <!-- Use the default -->
    <BuildType Condition="'$(BuildType)' == ''">debug</BuildType>
  </PropertyGroup>

  <Target Name="CopyConfigFiles" BeforeTargets="PrepareForRun">
    <Copy SourceFiles="@(Content)" DestinationFiles="@(Content-> '$(INETROOT)\TARGET\DISTRIB\test\all\$(BuildType)\Amd64\%(RecursiveDir)%(Filename)%(Extension)')" />
  </Target>
  
  <Import Project="$(PkgExchange_Focus_BuildTasks)\build\DependencyConfiguration.targets" />
</Project>