#pragma once
#include <boost/asio.hpp>

#include <mutex>

namespace Microsoft {
namespace M365 {
namespace Exporters {

// Class is thread safe. No exception is thrown.
class AsioContext {
  public:
    static boost::asio::io_context& GetIoContext() noexcept;

    // Drops unfinished tasks and stops the io_context.
    static void SetThreadCount(int count) noexcept;

  private:
    AsioContext();
    AsioContext(const AsioContext &) = delete;
    void start_timer();

    static AsioContext& Get();

    std::mutex mutex_;
    boost::asio::io_context io_context_;
    std::vector<std::thread> threads_;
    boost::asio::steady_timer timer_; // Timer to keep io_context_ alive.
};

} // namespace Exporters
} // namespace M365
} // namespace Microsoft
