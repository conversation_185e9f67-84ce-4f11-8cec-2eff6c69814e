﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
	  <AssemblyName>Microsoft.M365.Core.Telemetry.R9.Tracing.AdvancedSampling</AssemblyName>
	  <RootNamespace>$(AssemblyName)</RootNamespace>
	  <TargetFrameworks>net8.0;net6.0</TargetFrameworks>
	  <GeneratePackageOnBuild>true</GeneratePackageOnBuild>
	  <PackageId>Microsoft.M365.Core.Telemetry.R9.Tracing.AdvancedSampling</PackageId>
	  <PackageVersion>$(R9TelemetryExtensionsCorePackageVersion)</PackageVersion>
	  <PackageReleaseNotes>$(R9TelemetryExtensionsCoreReleaseNotes)</PackageReleaseNotes>
	  <Authors>SOTELS</Authors>
	  <Product>Microsoft.M365.Core.Telemetry</Product>
	  <Copyright>Copyright 2023 Microsoft Corp. All rights reserved.</Copyright>
	  <Description>M365 SOTELS R9 based tracing sampling policy</Description>
	  <PlatformTarget>anycpu</PlatformTarget>
	  <LangVersion>9</LangVersion>
	  <AssemblyVersion>*******</AssemblyVersion>
  </PropertyGroup>

  <ItemGroup>
    <Folder Include="DyeBasedTraceSampling\" />
    <Folder Include="PostTraceSampling\" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Newtonsoft.Json" />
    <PackageReference Include="Microsoft.R9.Extensions.HttpClient.Tracing" />
    <PackageReference Include="Microsoft.R9.Extensions.Tracing" />
  </ItemGroup>

  <ItemGroup>
		<ProjectReference Include="..\OpenTelemetry.Exporter.Filters\OpenTelemetry.Exporter.Filters.csproj"/>
  </ItemGroup>

  <ItemGroup>
	<FrameworkReference Include="Microsoft.AspNetCore.App" />
  </ItemGroup>

</Project>
