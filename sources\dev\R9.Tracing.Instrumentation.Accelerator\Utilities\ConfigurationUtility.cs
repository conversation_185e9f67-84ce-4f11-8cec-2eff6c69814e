﻿// <copyright file="ConfigurationUtility.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

namespace Microsoft.M365.Core.Telemetry.R9.Tracing.Instrumentation.Accelerator
{
    using System;
    using System.Collections.Generic;
    using System.Globalization;
    using Microsoft.Cloud.InstrumentationFramework.Metrics.Extensions;
    using Microsoft.Extensions.Configuration;
    using Microsoft.Extensions.DependencyInjection;
    using Microsoft.M365.Core.Telemetry.ECSClient;
    using Microsoft.M365.Core.Telemetry.R9.Tracing.Instrumentation.Accelerator.Options;
    using Microsoft.R9.Extensions.ClusterMetadata.Cosmic;
    using Microsoft.R9.Extensions.Diagnostics;
    using Newtonsoft.Json.Linq;

    /// <summary>
    /// ConfigurationUtility
    /// </summary>
    internal class ConfigurationUtility
    {
        /// <summary>
        /// ParseAndValidateTracingAcceleratorOptions
        /// </summary>
        /// <param name="services"></param>
        /// <param name="acceleratorConfiguration"></param>
        /// <returns></returns>
        internal static TracingAcceleratorOptions ParseAndValidateTracingAcceleratorOptions(IServiceCollection services, IConfiguration acceleratorConfiguration)
        {
            if (acceleratorConfiguration == default)
            {
                return new TracingAcceleratorOptions();
            }

            var schemaVersion = acceleratorConfiguration.GetValue<ConfigSchemaSupportedVersions>(nameof(TracingAcceleratorOptions.SchemaVersion));
            switch (schemaVersion)
            {
                default:
                    return ParseAndValidateTracingAcceleratorOptionsV1(services, acceleratorConfiguration);
            }
        }

        /// <summary>
        /// ParseAndValidateServiceMetaDataOptions
        /// </summary>
        /// <param name="services"></param>
        /// <param name="serviceMetaConfiguration"></param>
        /// <returns></returns>
        internal static ServiceMetaDataOptions ParseAndValidateServiceMetaDataOptions(IServiceCollection services, IConfiguration serviceMetaConfiguration)
        {
            Throws.IfNull(serviceMetaConfiguration, nameof(serviceMetaConfiguration));

            var schemaVersion = serviceMetaConfiguration.GetValue<ConfigSchemaSupportedVersions>(nameof(ServiceMetaDataOptions.SchemaVersion));
            switch (schemaVersion)
            {
                default:
                    return ParseAndValidateServiceMetaDataOptionsV1(services, serviceMetaConfiguration);
            }
        }

        /// <summary>
        /// ParseR9TracingConfig
        /// </summary>
        /// <param name="serviceMetaDataOptions"></param>
        /// <param name="tracingSamplerAndEnableOptions"></param>
        /// <returns></returns>
        internal static R9TracingConfig ParseR9TracingConfig(ServiceMetaDataOptions serviceMetaDataOptions, TracingSamplerAndEnableOptions[] tracingSamplerAndEnableOptions = null)
        {
            Throws.IfNull(serviceMetaDataOptions, nameof(serviceMetaDataOptions));
            var configBuilder = new ConfigurationBuilder().AddInMemoryCollection(new Dictionary<string, string>
            {
                { Constants.ServiceName, serviceMetaDataOptions.ServiceName },
                { Constants.RuntimeModel, serviceMetaDataOptions.RuntimeModel },
            });

            // Only set default value for non-cosmic services
            if (!IsCosmicService(serviceMetaDataOptions.RuntimeModel) && tracingSamplerAndEnableOptions != null)
            {
                foreach (TracingSamplerAndEnableOptions option in tracingSamplerAndEnableOptions)
                {
                    string deployRings = option.DeployRing.ToUpper(CultureInfo.InvariantCulture);
                    foreach (string deployRing in deployRings.Split(new char[] { ',' }, StringSplitOptions.RemoveEmptyEntries))
                    {
                        var deployRingTrim = deployRing.Trim();
                        configBuilder.AddInMemoryCollection(new Dictionary<string, string>
                        {
                            { $"{deployRingTrim}:{nameof(option.R9DTEnabled)}", option.R9DTEnabled.ToString(CultureInfo.InvariantCulture) },
                            { $"{deployRingTrim}:{nameof(option.TraceSampleRate)}", option.TraceSampleRate.ToString(CultureInfo.InvariantCulture) },
                            { $"{deployRingTrim}:{nameof(option.SamplerType)}", option.SamplerType },
                            { $"{deployRingTrim}:{nameof(option.ParentRootSamplerType)}", option.ParentRootSamplerType }
                        });
                    }
                }
            }

            return new R9TracingConfig(configBuilder.Build());
        }

        /// <summary>
        /// Refresh Accelerator configs by ECS configs
        /// </summary>
        /// <param name="current"></param>
        /// <param name="ecsConfig"></param>
        /// <returns></returns>
        internal static TracingAcceleratorOptions RefreshTracingAcceleratorOptions(TracingAcceleratorOptions current, JToken ecsConfig)
        {
            if (ecsConfig is null || !ecsConfig.HasValues)
            {
                return current;
            }

            try
            {
                var acceleratorJson = JObject.FromObject(current, new Newtonsoft.Json.JsonSerializer
                {
                    ReferenceLoopHandling = Newtonsoft.Json.ReferenceLoopHandling.Ignore
                });
                acceleratorJson.Merge(ecsConfig, new JsonMergeSettings
                {
                    MergeArrayHandling = MergeArrayHandling.Replace,
                    MergeNullValueHandling = MergeNullValueHandling.Merge
                });

                return acceleratorJson.ToObject<TracingAcceleratorOptions>();
            }
            catch { return current; }
        }

        /// <summary>
        /// IsCosmicService
        /// </summary>
        /// <param name="runtimeModel"></param>
        /// <returns></returns>
        internal static bool IsCosmicService(string runtimeModel)
        {
            return !string.IsNullOrEmpty(Environment.GetEnvironmentVariable("COSMIC_PODNAME"))
                || (runtimeModel ?? string.Empty).Equals(Constants.Cosmic, StringComparison.OrdinalIgnoreCase);
        }

        /// <summary>
        /// ConfigureCosmicMetaData
        /// </summary>
        // Dimensions list is copied from https://domoreexp.visualstudio.com/R9/_git/SDK?path=/src/Extensions/ClusterMetadata.Cosmic/CosmicClusterMetadataSource.cs&version=GBmain&line=106&lineEnd=136&lineStartColumn=1&lineEndColumn=6&lineStyle=plain&_a=contents
        internal static Action<CosmicClusterMetadata> ConfigureCosmicMetaData = meta =>
        {
            meta.PodName = Environment.GetEnvironmentVariable("COSMIC_PODNAME") ?? Constants.Missing;
            meta.Deployment = Utility.ExtractDeploymentFromPodName(meta.PodName) ?? Constants.Missing;
            meta.Ring = Environment.GetEnvironmentVariable("COSMIC_RING") ?? Constants.Missing;
            meta.Geo = Environment.GetEnvironmentVariable("COSMIC_REGION") ?? Constants.Missing;
            meta.Region = Environment.GetEnvironmentVariable("COSMIC_LOCATION") ?? Constants.Missing;
            meta.NodeName = Environment.GetEnvironmentVariable("COSMIC_NODENAME") ?? Constants.Missing;
            meta.Version = Environment.GetEnvironmentVariable("COSMIC_VERSION") ?? Constants.Missing;
            meta.SecretCache = Environment.GetEnvironmentVariable("COSMIC_SECRET_CACHE") ?? Constants.Missing;
            meta.Partition = Environment.GetEnvironmentVariable("COSMIC_PARTITION") ?? Constants.Missing;
            meta.Namespace = Environment.GetEnvironmentVariable("POD_NAMESPACE") ?? Environment.GetEnvironmentVariable("COSMIC_NAMESPACE") ?? Constants.Missing;
            meta.NamespaceInstanceId = Environment.GetEnvironmentVariable("COSMIC_NS_INSTANCE_ID") ?? Constants.Missing;
            meta.Cloud = Environment.GetEnvironmentVariable("COSMIC_AZURECLOUD") ?? Constants.Missing;
        };

        private static TracingAcceleratorOptions ParseAndValidateTracingAcceleratorOptionsV1(IServiceCollection services, IConfiguration acceleratorConfiguration)
        {
            var acceleratorOptions = new TracingAcceleratorOptions();
            acceleratorConfiguration.Bind(acceleratorOptions);
            services.Configure<TracingAcceleratorOptions>(acceleratorConfiguration).AddOptionsWithValidateOnStart<TracingAcceleratorOptions>();

            return acceleratorOptions;
        }

        private static ServiceMetaDataOptions ParseAndValidateServiceMetaDataOptionsV1(IServiceCollection services, IConfiguration serviceMetaConfiguration)
        {
            var serviceMetaDataOptions = new ServiceMetaDataOptions();
            serviceMetaConfiguration.Bind(serviceMetaDataOptions);
            services.Configure<ServiceMetaDataOptions>(serviceMetaConfiguration).AddOptionsWithValidateOnStart<ServiceMetaDataOptions>();

            // TODO： @yangyzh: shorterm solution: because validate data annotation does not work at service start up, we need to validate it here.
            Throws.IfNullOrEmpty(serviceMetaDataOptions.ServiceName, $"ServiceMetaData:{nameof(serviceMetaDataOptions.ServiceName)} is a required configuration, it will be used as filter value to obtain the DistributedTracing switch from the ECS portal, please set the correct data");
            Throws.IfNullOrEmpty(serviceMetaDataOptions.RuntimeModel, $"ServiceMetaData:{nameof(serviceMetaDataOptions.RuntimeModel)} is a required configuration, it will be used as filter value to obtain the DistributedTracing switch from the ECS portal, please set the correct data");

            return serviceMetaDataOptions;
        }
    }
}
