﻿// <copyright file="ConfigSchemaSupportedVersions.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System.Diagnostics.CodeAnalysis;

namespace Microsoft.M365.Core.Telemetry.R9.Tracing.Instrumentation.Accelerator
{
    /// <summary>
    /// ConfigSchemaSupportedVersions
    /// </summary>
    [SuppressMessage("Naming", "CA1717:Only FlagsAttribute enums should have plural names", Justification = "TBD")]
    public enum ConfigSchemaSupportedVersions
    {
        V1 = 0,
    }
}
