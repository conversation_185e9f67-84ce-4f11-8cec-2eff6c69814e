﻿// <copyright file="IRegistryWatcher.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System;

namespace Microsoft.M365.Core.Telemetry.Enrichment
{
    /// <summary>
    /// IRegistryWatcher
    /// </summary>
    internal interface IRegistryWatcher
    {
        /// <summary>
        /// to be deleted
        /// Raised each time a registry change is detected according to the parameters of the watcher
        /// </summary>
        Action KeyChanged { get; set; }

        /// <summary>
        /// Begins monitoring of the registry key according to the supplied parameters.
        /// </summary>
        void Start();
    }
}
