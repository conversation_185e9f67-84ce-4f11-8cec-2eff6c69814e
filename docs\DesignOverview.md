# Micro Repo Migration
## 1. Purpose
To provide a solution for SOTELS R9 extensions package upgrade which is blocked in Substrate and Root repo.
## 2. Goal
To migrate SOTELS R9 extension projects from [Root repo](https://o365exchange.visualstudio.com/O365%20Core/_git/Root?path=/sources/Core/Telemetry/dev) (Exclude Logging.Exporters.NLog, OpenTelemetry.Exporter.Filters, and R9.Tracing.Instrumentation.Accelerator) to TelemetryCore repo, merge them as one NuGet package Microsoft.M365.Core.R9TelemetryExtensionsCore, and publish to [Common Feed](https://o365exchange.visualstudio.com/O365%20Core/_artifacts/feed/Common).

We will try our best to wrap the methods we use in R9 Telemetry libraries into our APIs to handle breaking changes in R9 Telemetry, and try our best not to make breaking changes in our APIs.

The goal does not include to provide solution for breaking changes in R9 Telemetry libraries directly: if a substrate service has direct R9 reference of higher versions, and reference our Microsoft.M365.Core.R9TelemetryExtensionsCore package which reference lower R9 versions, it might result in build or runtime error due to R9 breaking changes. We will rely on the service themselves to deal with this kind of issues.
## 3. Main Points
### 3.1 R9 telemetry versions to support
The NuGet will have multiple major versions to support different R9 telemetry versions, currently we will have the following two major versions:
| Our Major Version | Support R9 Version | Branch                     |   ECS Version |
| ----------------- | ------------------ | ---------------------------|---------------|
|   10-ecs20        |       8.11.0       |release/TelemetryCore_1.0.130|20.3.0.1509   | 
|     10            |       8.11.0       |      master                | 16.1.0.602    |
|     9             |       8.6.0        |release/TelemetryCore_1.0.142| 16.1.0.600   |
|     7             |       1.28.3       |release/TelemetryCore_1.0.53|  16.1.0.600   |
| 6(out of use)     |       1.18.0       |release/TelemetryCore_1.0.18|  16.1.0.600   |
      
- lower major versions will be based on R9 telemetry 1.18.0, for services with R9 references from 1.18 to 1.27, targeting Substrate repo
- higher major versions will be based on R9 telemetry 1.28.2, for services with R9 references higher or equal to 1.28, targeting coral repo and others

If R9 telemetry libararies or OpenTelemetry make breaking changes in the future, we will increment the major versions to base the NuGet on that R9 version
### 3.2 NuGet version control
Official version will be [Major].[Minor].[Patch], and prerelease build will generate [Major Version].[Minor Version].[Patch-Suffix[Number]] versions.

Major version 1 will support R9 version 1.18.0, targeting Substrate repo, and major version 2 will support R9 version 1.28.2, targeting other coral repos. Major versions shall only be updated if we decide to support higher versions of R9 libraries, or we need to deal with really crucial breaking changes.

Minor versions will be updated in each official release. If same features need to be apply to both Substrate and Coral repo, their minor versions shall be the same. If the update is not for both repo, the minor version shall increment for the targeting repo, but the version targeting the other repo shall be preserved. (For instance, if we have 1.13.0 and 2.13.0 right now, but a feature update only targeting substrate need to to published officially, 1.13.0 will go to 1.14.0, but the next official release will be 1.15.0 and 2.15.0).

Patch will be done on the release branches. Depending on the situation, we might also need to cherry pick it to the master branch.
### 3.3 Different develop branches vs projects in different folders
Different develop branches: our library code targeting Sbustrate and Coral repo will have different develop branches. For each feature shared by two branches, merge or cherry-pick between two branches shall be executed regularly.

Projects in different folders: our library code will have different projects to pack the NuGet for Substrate and Coral repo, respectively.

Since our feature work will mostly be done on higher major versions, and lower major versions will adopt bugfix more frequently and feature less frequently, prefer different develop branches over projects in different folders.
### 3.4 NuGet release process
We will set up two release pipelines, one for releasing official NuGet versions, and the other for prerelease ones.

The prerelease pipeline will be triggered by Official daily build on any branches, and is for adhoc testing purposes.

The pipeline to release official versions will starts from Official Dogfood build on master or major develop branches: the dogfood build will create a release branch, then the official incremental build will be triggered on the release branch, and then the auto publish NuGet task in the pipeline will release the official NuGet to Common feed. After the release process, we need to add a tag "[NuGetName]_[Version]" on the branch for potential patch in the future.
### 3.5 Branches
Currently there will be a master branch for R9 version 1.28, and a develop branch for R9 version 1.18. When we decide to support higher R9 versions, we will fork a new develop branch for R9 1.28, and upgrade R9 versions on the master branch.

Feature changes will still be made on the master branch, and when we are ready to release an official version, we will fork a release branch from the master branch (by Official Dogfood build) to release the higher major versions.

If this change is also needed for lower major versions, we will cherry pick the commit to the corresponding develop branch, and fork another release branch from the develop branch.

All release branches will not be deleted.
### 3.6 csproj vs nuspec
csproj to create NuGet packages will be prefered, as long as csproj could fulfill our requirements. nuspec will result in much more complexity in coding.
### 3.7 Interface and Breaking changes handling
OpenTelemetry put their APIs in [PublicAPI.Shipped|UnShipped.txt](https://github.com/open-telemetry/opentelemetry-dotnet/tree/main/src/OpenTelemetry/.publicApi/Stable/netstandard2.0), we could consider follow these practice and only expose these APIs as public while make others internal.

Although we claim that we will try our best to handle breaking changes, we also need to discuss the plan if we do need a breaking change: considering that if we make breaking changes in 1.[minor].[patch] will inevitably break scenarios when users would like to upgrade their packages to 2.[minor].[patch], we might need to upgrade the major versions in such cases.

If we add APIs in 1.[minor].[patch], these will not appear in lower versions of 2.[minor].[patch], which is counter intuitive in such cases. Currently we could only give guidances to customers that when upgrading to higher major versions, minor versions shall be the same or higher.
## 4. Future Plans
- Review APIs for our NuGet.
- Design for repo running|processing strategies.