﻿// <copyright file="WindowsRegistryKey.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System;
using System.Diagnostics.CodeAnalysis;
#if !NETFRAMEWORK
using Microsoft.M365.Core.Portable.Registry;
#else
using Microsoft.Win32;
#endif

namespace Microsoft.M365.Core.Telemetry.Enrichment
{
    /// <summary>
    /// Windows registry key
    /// TODO(jiayiwang): Add back Dispose and UpenSubkey when adding the RegistryWatcher back.
    /// </summary>
    internal class WindowsRegistryKey : IRegistryKey
    {
        /// <summary>
        /// The windows registry key
        /// </summary>
        private readonly RegistryKey registryKey;

        /// <summary>
        /// Custom Constructor
        /// Open a registry key root
        /// </summary>
        public WindowsRegistryKey()
            : this(Registry.LocalMachine)
        {
        }

        /// <summary>
        /// Custom Constructor
        /// </summary>
        /// <param name="registryKey">The registry key</param>
        private WindowsRegistryKey(RegistryKey registryKey)
        {
            this.registryKey = registryKey;
        }

        /// <inheritDoc/>
        /// <summary>
        /// Gets a registry value given a name
        /// </summary>
        /// <param name="name"> Name to lookup in registry</param>
        /// <param name="defaultValue"> The default value to set</param>
        /// <returns> Returns a registry value</returns>
        public object GetValue(string name, object defaultValue)
        {
            return this.registryKey.GetValue(name, defaultValue);
        }

        /// <inheritDoc/>
        /// <summary> 
        /// Opens the registry subkey
        /// </summary>
        /// <param name="path">The path to open in registry</param>
        /// <returns> Returns an Iregistry Key </returns>
        public IRegistryKey OpenSubKey(string path)
        {
            var subkey = this.registryKey.OpenSubKey(path, false);
            if (subkey == null)
            {
                return null;
            }

            return new WindowsRegistryKey(subkey);
        }
    }
}
