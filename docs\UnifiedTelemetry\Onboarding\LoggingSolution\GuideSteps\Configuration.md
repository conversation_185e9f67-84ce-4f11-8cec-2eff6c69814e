# Step 1 - Set up Configuration

You can put the configuration of R9 logging in appsettings.json file or other existing configuration files. When R9 is initialized, the configuration should be loaded.

## [Optional] Add ECS Source

[!include[](../../../include/ECSConfig.md)]

After populating the ECS parameter sections with your expected values, you are ready to put the configurations onto [ESC Portal](https://ecs.skype.com/UnifiedTelemetry/Log/configurations?view=shared). It's **suggested** that only put the configurations which have dynamic values and need hot reloading onto ECS.

In following example, the filter `ServiceName` should be same as defined in `ECSParameters:ECSIdentifiers:ServiceName`. 
![alt text](../../../.images/Logging/StepByStep/ecs-example.png)

### Fallback
In case disconnectivity with ECS, it's **highly suggested** you set the stable fallback behavior in `appsettings.json`. otherwise the ECS-managed configurations would fall back to default values, which may not meet your expectations.

**Note**: Once the ECS configuration is fetched, it will **override** the entries with same configuration keys in `appsettings.json`.

**Example**:
```jsx
{
    "SubstrateLogging": {
        "UseCompositeExporter": true,
        "CompositeExporter": {
            "VirtualTableMappings": {
                "Test.MyService": "MyServiceTable"
            },
            "Geneva": {
                "ConnectionString": "EtwSession=test"
            },
            "OdlTcp": {
                "ConnectionString": "tcp://localhost:1234"
            },
            // This section is the fallback section for ECS.
            // It should represent the stable behavior you want when there's no hot-reloading support.
            "VirtualTableExports": {
                "MyServiceTable": [
                    {
                        "ExporterType": "Geneva",
                        "ExportTable": "ServiceEvent"
                    },
                    {
                        "ExporterType": "OdlTcp",
                        "ExportTable": "TestLogType"
                    }
                ]
            }
        }
    }
}
```

## Configure Exporter

Currently we support configuring a Geneva Exporter or a Composite Exporter.
Geneva Exporter simply export log data to Geneva, while Composite Exporter supports multiple Exporters.
We can route logs to two different exporters, or switch the exporter to change destination on runtime.
It can also be configured to only export to Geneva.

### Use Geneva Exporter
[!include[](../../../include/GenevaExporterConfig.md)]

### [Recommended] Use Composite Exporter
[!include[](../../../include/CompositeExporterConfig.md)]

## Summary

Now we have the essential configuration.
These configurations will be loaded at next step to configure logging.

**Next Step**: [Initialize R9 Telemetry](./InitR9.md)