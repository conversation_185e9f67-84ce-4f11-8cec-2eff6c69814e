#include "LogFileExporter.h"

#include "AsioContext.h"
#include "LogFileExporterRecordable.h"
#include <opentelemetry/sdk/common/exporter_utils.h>

#include <iostream>
#include <memory>

namespace Microsoft {
namespace M365 {
namespace Exporters {

LogFileExporter::LogFileExporter(LogFileExporterOptions options) noexcept
    : file_manager_(std::make_unique<FileManager>(AsioContext::GetIoContext(), options.file_path, options.reset_duration)) { }

std::unique_ptr<opentelemetry::sdk::logs::Recordable> LogFileExporter::MakeRecordable() noexcept {
    return std::unique_ptr<opentelemetry::sdk::logs::Recordable>(new LogFileExporterRecordable());
}

opentelemetry::sdk::common::ExportResult LogFileExporter::Export(
    const opentelemetry::nostd::span<std::unique_ptr<opentelemetry::sdk::logs::Recordable>> &records) noexcept {
    std::string body;
    for (auto &record : records) {
        auto json_record = std::unique_ptr<LogFileExporterRecordable>(
            static_cast<LogFileExporterRecordable *>(record.release()));
        body += json_record->GetJSON().dump() + "\n";
    }
    file_manager_->Write(std::make_shared<std::string>(body));

    return opentelemetry::sdk::common::ExportResult::kSuccess;
}

bool LogFileExporter::Shutdown(std::chrono::microseconds timeout) noexcept {
    // Just push a callback. Ignore the timeout.
    file_manager_->ShutDown();
    return true;
}

bool LogFileExporter::ForceFlush(std::chrono::microseconds timeout) noexcept {
    // Nothing to do here.
    return true;
}

} // namespace Exporters
} // namespace M365
} // namespace Microsoft
