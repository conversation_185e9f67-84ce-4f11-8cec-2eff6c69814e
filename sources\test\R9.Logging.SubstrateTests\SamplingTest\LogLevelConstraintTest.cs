// <copyright file="LogLevelConstraintTest.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>
using System;
using System.Collections.Generic;
using System.IO;
using System.Text;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.M365.Core.Telemetry.R9.Logging.Substrate.Sampler;
using OpenTelemetry.Logs;
using Xunit;

namespace Microsoft.M365.Core.Telemetry.R9.Logging.Substrate.Test.SamplingTest
{
    public class LogLevelConstraintTest
    {
        private const string LoggerName = "LogLevelConstraintTest";

        private const string LogLevelConstraintsConfig = @"
{
    ""SubstrateLogging"": {
        ""RuleBasedSampler"": {
            ""LogLevelConstraintTest"": [
                {
                    ""Constraints"": [
                        {
                            ""Field"": ""LogLevel"",
                            ""Type"": ""TypePlaceholder"",
                            ""Operator"": ""OperatorPlaceholder"",
                            ""Value"": ""ValuePlaceholder""
                        }
                    ],
                    ""Strategy"": {
                        ""Type"": ""Random"",
                        ""SampleRate"": 1.0
                    }
                },
                {
                    ""Constraints"": [],
                    ""Strategy"": {
                        ""Type"": ""Random"",
                        ""SampleRate"": 0.0
                    }
                }
            ]
        }
    }
}";

        private static string ReplaceConfigPlaceholders(string type, string op, string value)
        {
            return LogLevelConstraintsConfig
                .Replace("TypePlaceholder", type)
                .Replace("OperatorPlaceholder", op)
                .Replace("ValuePlaceholder", value);
        }

        [Fact]
        public void NumericEquals_ShouldMatchAndSample()
        {
            var config = ReplaceConfigPlaceholders(ConstraintType.LogLevel, OperatorType.NumericEquals, "Information");
            var exportedItems = new List<LogRecord>();
            var logger = LoggerTestUtils.CreateLoggerWithConfig(config, exportedItems, LoggerName);

            logger.LogInformation("Test message");

            Assert.Single(exportedItems);
        }

        [Fact]
        public void NumericNotEquals_ShouldMatchAndSample()
        {
            var config = ReplaceConfigPlaceholders(ConstraintType.LogLevel, OperatorType.NumericNotEquals, "Information");
            var exportedItems = new List<LogRecord>();
            var logger = LoggerTestUtils.CreateLoggerWithConfig(config, exportedItems, LoggerName);

            logger.LogWarning("Test message");

            Assert.Single(exportedItems);
        }

        [Fact]
        public void GreaterThan_ShouldMatchAndSample()
        {
            var config = ReplaceConfigPlaceholders(ConstraintType.LogLevel, OperatorType.GreaterThan, "Information");
            var exportedItems = new List<LogRecord>();
            var logger = LoggerTestUtils.CreateLoggerWithConfig(config, exportedItems, LoggerName);

            logger.LogWarning("Test message");

            Assert.Single(exportedItems);
        }

        [Fact]
        public void LessThan_ShouldMatchAndSample()
        {
            var config = ReplaceConfigPlaceholders(ConstraintType.LogLevel, OperatorType.LessThan, "Information");
            var exportedItems = new List<LogRecord>();
            var logger = LoggerTestUtils.CreateLoggerWithConfig(config, exportedItems, LoggerName);

            logger.LogDebug("Test message");

            Assert.Single(exportedItems);
        }

        [Fact]
        public void GreaterThanOrEqual_ShouldMatchAndSample()
        {
            var config = ReplaceConfigPlaceholders(ConstraintType.LogLevel, OperatorType.GreaterThanOrEqual, "Information");
            var exportedItems = new List<LogRecord>();
            var logger = LoggerTestUtils.CreateLoggerWithConfig(config, exportedItems, LoggerName);

            logger.LogInformation("Test message");

            Assert.Single(exportedItems);
        }

        [Fact]
        public void LessThanOrEqual_ShouldMatchAndSample()
        {
            var config = ReplaceConfigPlaceholders(ConstraintType.LogLevel, OperatorType.LessThanOrEqual, "Information");
            var exportedItems = new List<LogRecord>();
            var logger = LoggerTestUtils.CreateLoggerWithConfig(config, exportedItems, LoggerName);

            logger.LogInformation("Test message");

            Assert.Single(exportedItems);
        }

        [Fact]
        public void InvalidType_ShouldNotMatch()
        {
            var config = ReplaceConfigPlaceholders("InvalidLogLevel", OperatorType.NumericEquals, "Information");
            var exportedItems = new List<LogRecord>();
            var logger = LoggerTestUtils.CreateLoggerWithConfig(config, exportedItems, LoggerName);

            logger.LogInformation("Test message");

            Assert.Empty(exportedItems);
        }

        [Fact]
        public void InvalidOperator_ShouldNotMatch()
        {
            var config = ReplaceConfigPlaceholders(ConstraintType.LogLevel, "InvalidOperator", "Information");
            var exportedItems = new List<LogRecord>();
            var logger = LoggerTestUtils.CreateLoggerWithConfig(config, exportedItems, LoggerName);

            logger.LogInformation("Test message");

            Assert.Empty(exportedItems);
        }

        [Fact]
        public void OperatorWithWhitespace_ShouldMatchAndSample()
        {
            var config = ReplaceConfigPlaceholders(ConstraintType.LogLevel, "  " + OperatorType.NumericEquals + "  ", "Information");
            var exportedItems = new List<LogRecord>();
            var logger = LoggerTestUtils.CreateLoggerWithConfig(config, exportedItems, LoggerName);

            logger.LogInformation("Test message");

            Assert.Single(exportedItems);
        }

        [Fact]
        public void TypeWithWhitespace_ShouldMatchAndSample()
        {
            var config = ReplaceConfigPlaceholders("  " + ConstraintType.LogLevel + "  ", OperatorType.NumericEquals, "Information");
            var exportedItems = new List<LogRecord>();
            var logger = LoggerTestUtils.CreateLoggerWithConfig(config, exportedItems, LoggerName);

            logger.LogInformation("Test message");

            Assert.Single(exportedItems);
        }

        [Fact]
        public void CaseInsensitive_ShouldMatchAndSample()
        {
            var config = ReplaceConfigPlaceholders(ConstraintType.LogLevel, OperatorType.NumericEquals, "information");
            var exportedItems = new List<LogRecord>();
            var logger = LoggerTestUtils.CreateLoggerWithConfig(config, exportedItems, LoggerName);

            logger.LogInformation("Test message");

            Assert.Single(exportedItems);
        }

        [Fact]
        public void NumericEquals_WithMixedCase_ShouldMatchAndSample()
        {
            var config = ReplaceConfigPlaceholders(ConstraintType.LogLevel, OperatorType.NumericEquals, "InFoRmAtIoN");
            var exportedItems = new List<LogRecord>();
            var logger = LoggerTestUtils.CreateLoggerWithConfig(config, exportedItems, LoggerName);

            logger.LogInformation("Test message");

            Assert.Single(exportedItems);
        }

        [Fact]
        public void TraceLevel_ShouldMatchAndSample()
        {
            var config = ReplaceConfigPlaceholders(ConstraintType.LogLevel, OperatorType.NumericEquals, "Trace");
            var exportedItems = new List<LogRecord>();
            var logger = LoggerTestUtils.CreateLoggerWithConfig(config, exportedItems, LoggerName);

            logger.LogTrace("Test message");

            Assert.Single(exportedItems);
        }

        [Fact]
        public void CriticalLevel_ShouldMatchAndSample()
        {
            var config = ReplaceConfigPlaceholders(ConstraintType.LogLevel, OperatorType.NumericEquals, "Critical");
            var exportedItems = new List<LogRecord>();
            var logger = LoggerTestUtils.CreateLoggerWithConfig(config, exportedItems, LoggerName);

            logger.LogCritical("Test message");

            Assert.Single(exportedItems);
        }

        [Fact]
        public void NumericEquals_WithNoneLevel_ShouldMatchButNotSample()
        {
            var config = ReplaceConfigPlaceholders(ConstraintType.LogLevel, OperatorType.NumericEquals, "None");
            var exportedItems = new List<LogRecord>();
            var logger = LoggerTestUtils.CreateLoggerWithConfig(config, exportedItems, LoggerName);

            logger.Log(LogLevel.None, "Test message");

            Assert.Empty(exportedItems);
        }

        [Fact]
        public void NumericEquals_WithInvalidLevel_ShouldNotMatch()
        {
            var config = ReplaceConfigPlaceholders(ConstraintType.LogLevel, OperatorType.NumericEquals, "InvalidLevel");
            var exportedItems = new List<LogRecord>();
            var logger = LoggerTestUtils.CreateLoggerWithConfig(config, exportedItems, LoggerName);

            logger.LogInformation("Test message");

            Assert.Empty(exportedItems);
        }

        [Fact]
        public void NumericEquals_WithNumericValue_ShouldMatchAndSample()
        {
            var config = ReplaceConfigPlaceholders(ConstraintType.LogLevel, OperatorType.NumericEquals, "3");
            var exportedItems = new List<LogRecord>();
            var logger = LoggerTestUtils.CreateLoggerWithConfig(config, exportedItems, LoggerName);

            logger.LogError("Test message");

            Assert.Empty(exportedItems);
        }

        [Fact]
        public void NumericEquals_WithNullLevel_ShouldNotMatch()
        {
            var config = ReplaceConfigPlaceholders(ConstraintType.LogLevel, OperatorType.NumericEquals, "null");
            var exportedItems = new List<LogRecord>();
            var logger = LoggerTestUtils.CreateLoggerWithConfig(config, exportedItems, LoggerName);

            logger.LogInformation("Test message");

            Assert.Empty(exportedItems);
        }

        [Fact]
        public void NullState_ShouldStillMatchLogLevel()
        {
            var config = ReplaceConfigPlaceholders(ConstraintType.LogLevel, OperatorType.NumericEquals, "Information");
            var exportedItems = new List<LogRecord>();
            var logger = LoggerTestUtils.CreateLoggerWithConfig(config, exportedItems, LoggerName);

            // Using null as state
            logger.Log<object>(LogLevel.Information, new EventId(0), null, null,
                (state, ex) => "Test message with null state");

            // Should still match because LogLevel constraint doesn't depend on state
            Assert.Single(exportedItems);
        }

        [Fact]
        public void CustomStateObject_ShouldStillMatchLogLevel()
        {
            var config = ReplaceConfigPlaceholders(ConstraintType.LogLevel, OperatorType.NumericEquals, "Information");
            var exportedItems = new List<LogRecord>();
            var logger = LoggerTestUtils.CreateLoggerWithConfig(config, exportedItems, LoggerName);

            // Using a custom object as state that doesn't implement IReadOnlyList<KeyValuePair<string, object?>>
            var customState = new { UserId = "user_123", Value = 42 };

            logger.Log(LogLevel.Information, new EventId(0), customState, null,
                (state, ex) => $"Test message with custom state object: {state}");

            // Should still match because LogLevel constraint doesn't depend on state
            Assert.Single(exportedItems);
        }

        [Fact]
        public void PrimitiveStateType_ShouldStillMatchLogLevel()
        {
            var config = ReplaceConfigPlaceholders(ConstraintType.LogLevel, OperatorType.NumericEquals, "Information");
            var exportedItems = new List<LogRecord>();
            var logger = LoggerTestUtils.CreateLoggerWithConfig(config, exportedItems, LoggerName);

            // Using an integer as state
            logger.Log(LogLevel.Information, new EventId(0), 123, null,
                (state, ex) => $"Test message with int state: {state}");

            // Should still match because LogLevel constraint doesn't depend on state
            Assert.Single(exportedItems);
        }

        [Fact]
        public void StringState_ShouldStillMatchLogLevel()
        {
            var config = ReplaceConfigPlaceholders(ConstraintType.LogLevel, OperatorType.NumericEquals, "Information");
            var exportedItems = new List<LogRecord>();
            var logger = LoggerTestUtils.CreateLoggerWithConfig(config, exportedItems, LoggerName);

            // Using a string as state
            logger.Log(LogLevel.Information, new EventId(0), "Simple string message", null,
                (state, ex) => state);

            // Should still match because LogLevel constraint doesn't depend on state
            Assert.Single(exportedItems);
        }
    }
}