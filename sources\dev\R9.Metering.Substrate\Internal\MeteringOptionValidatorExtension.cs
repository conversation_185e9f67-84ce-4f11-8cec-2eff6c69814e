﻿// <copyright file="MeteringOptionValidatorExtension.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using Microsoft.Extensions.Configuration;
using Microsoft.R9.Extensions.Metering;
using Microsoft.R9.Extensions.Metering.Exporters;

namespace Microsoft.M365.Core.Telemetry.R9.Metering.Substrate
{
    /// <summary>
    /// constants for Metering options
    /// </summary>
    internal static class ValidatorHelper
    {
        /// <summary>
        /// Mapping between option type and configuration section name
        /// </summary>
        private static readonly Dictionary<Type, string> configSectionName = new ()
        {
            [typeof(MeteringOptions)] = "SubstrateMetering:R9Metering",
            [typeof(GenevaMeteringExporterOptions)] = "SubstrateMetering:GenevaExporter"
        };

        /// <summary>
        /// Update options from configuration (general for all options)
        /// </summary>
        /// <typeparam name="T">option type</typeparam>
        /// <param name="options"></param>
        /// <param name="configuration"></param>
        /// <returns></returns>
        internal static void UpdateOption<T>(this T options, IConfiguration configuration)
        {
            if (configSectionName.TryGetValue(typeof(T), out var sectionName))
            {
                var section = configuration.GetSection(sectionName);
                section.Bind(options);
            }
            else
            {
                throw new ArgumentException($"Loading configurations, but Option Type [{typeof(T)}] is not registered.");
            }
        }
    }

    /// <summary>
    /// validate Metering options for substrate
    /// </summary>
    public static class MeteringOptionValidatorExtension
    {
        /// <summary>
        /// load values from configuration section "Metering", update and validate Metering options
        /// </summary>
        /// <param name="options"></param>
        /// <param name="configuration"></param>
        /// <returns></returns>
        public static MeteringOptions UpdateAndValidateOptions(this MeteringOptions options, IConfiguration configuration)
        {
            options.UpdateOption(configuration);

            options.MeterState = MeteringState.Disabled;
            if (options.MeterStateOverrides.Count == 0)
            {
                throw new ArgumentException("*MeterStateOverrides* is required for Geneva Metrics. Set 'Enabled' for recording metrics.");
            }

            return options;
        }
    }

    /// <summary>
    /// validate geneva log exporter options
    /// </summary>
    public static class GenevaMeteringOptionvalidatorExtension
    {
        /// <summary>
        /// load values from configuration "GenevaMeteringExporter", update and validate Metering options
        /// </summary>
        /// <param name="options"></param>
        /// <param name="configuration"></param>
        /// <returns></returns>
        public static GenevaMeteringExporterOptions UpdateAndValidateOptions(this GenevaMeteringExporterOptions options, IConfiguration configuration)
        {
            options.MetricExportIntervalMilliseconds = 1000;
            options.UpdateOption(configuration);

            if (string.IsNullOrEmpty(options.MonitoringAccount))
            {
                throw new ArgumentException("*MonitoringAccount* is required for Geneva Metrics.");
            }
            if (string.IsNullOrEmpty(options.MonitoringNamespace))
            {
                throw new ArgumentException("*MonitoringNamespace* is required for Geneva Metrics.");
            }

            return options;
        }
    }
}
