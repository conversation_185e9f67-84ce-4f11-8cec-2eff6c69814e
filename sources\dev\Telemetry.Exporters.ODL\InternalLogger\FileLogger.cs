﻿// ---------------------------------------------------------------------------
// <copyright file="FileLogger.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>
// ---------------------------------------------------------------------------
using System;
using System.Collections.Generic;
using System.Diagnostics.Tracing;
using Microsoft.Extensions.Logging;

namespace Microsoft.R9.Extensions.Telemetry.Exporters.Base
{
    /// <summary>
    /// DBA file logger
    /// </summary>
    internal sealed class FileLogger : IInternalLogger
    {
        /// <summary>
        /// Log info with customer dimensions
        /// </summary>
        /// <param name="logLevel">Log level</param>
        /// <param name="logtype">Log type</param>
        /// <param name="atguid">Audit trail guid</param>
        /// <param name="message">formatted message</param>
        /// <param name="dimensionPairs">customer dimension names</param>
        public void Log(LogLevel logLevel, string logtype, string atguid, string message, Dictionary<string, string> dimensionPairs)
        {
            // TODO:replace with file logger implements
            throw new NotImplementedException();
        }

        /// <summary>
        /// Log trace using file logger
        /// </summary>
        /// <param name="logtype">Log type</param>
        /// <param name="atguid">Audit trail guid</param>
        /// <param name="message">formatted message</param>
        public void Trace(string logtype, string atguid, string message)
        {
            throw new NotImplementedException();
        }
    }
}