﻿// <copyright file="SDKDebugInfoCollector.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System;
using System.Collections.Concurrent;
using System.Diagnostics;
using System.Threading;

using Microsoft.Extensions.Logging;

using Newtonsoft.Json;

namespace Microsoft.M365.Core.Telemetry.SDKLogger
{
    /// <summary>
    /// The debug info collector
    /// </summary>
    internal class SDKDebugInfoCollector
    {
        /// <summary>
        ///  The default value for sending records in seconds
        /// </summary>
        private const uint DefaultSendRecordsTimeIntevalInSec = 30;

        /// <summary>
        /// The instance of DebugInfoCollector
        /// </summary>
        private static SDKDebugInfoCollector instance;

        /// <summary>
        /// Instance lock for muti-thread
        /// </summary>
        private static readonly object lockHelper = new object();

        /// <summary>
        /// Dictionary lock for muti-thread
        /// </summary>
        private static readonly object dictionaryLockHelper = new object();

        /// <summary>
        /// The dictionary for passive metric records. <Metric Name, <Dimension Name, Value>>
        /// </summary>
        private static ConcurrentDictionary<string, ConcurrentDictionary<string, long>> passiveMetricsRecord = new ConcurrentDictionary<string, ConcurrentDictionary<string, long>>();

        /// <summary>
        /// The dictionary for r9 metric records. <Metric Name, <Dimension Name, Value>>
        /// </summary>
        private static ConcurrentDictionary<string, ConcurrentDictionary<string, long>> r9MetricsRecord = new ConcurrentDictionary<string, ConcurrentDictionary<string, long>>();

        /// <summary>
        /// The timer for send records
        /// </summary>
        private Timer sendRecordsTimer;

        /// <summary>
        /// The time span interal
        /// </summary>
        private TimeSpan sendRecordsTimeInterval = TimeSpan.FromSeconds(DefaultSendRecordsTimeIntevalInSec);

        /// <summary>
        /// The stopwathc to record the debug time consuming.
        /// </summary>
        private Stopwatch stopwatch = new Stopwatch();

        /// <summary>
        /// The flag to indicate whether the debug info collector been enabled or not
        /// </summary>
        private bool isDebugInfoCollectorEnabled;

        /// <summary>
        /// Property to access singleton instance
        /// </summary>
        public static SDKDebugInfoCollector Instance
        {
            get
            {
                if (instance == null)
                {
                    lock (lockHelper)
                    {
                        if (instance == null)
                        {
                            instance = new SDKDebugInfoCollector();
                        }
                    }
                }

                return instance;
            }
        }

        /// <summary>
        /// Debug info collector
        /// </summary>
        private SDKDebugInfoCollector()
        {
            try
            {
                this.isDebugInfoCollectorEnabled = true;

                if (this.sendRecordsTimer == null)
                {
                    this.sendRecordsTimer = new Timer(new TimerCallback(this.SendRecords), null, this.sendRecordsTimeInterval, this.sendRecordsTimeInterval);
                }
            }
            catch (Exception e)
            {
                SDKLog.Error($"Error happens in SDKDebugInfoCollector:{e}");
            }
        }

        /// <summary>
        /// Send records
        /// </summary>
        /// <param name="state">the state</param>
        private void SendRecords(object state)
        {
            try
            {
                stopwatch.Start();

                string logMessage = string.Empty;
                lock (dictionaryLockHelper)
                {
                    logMessage = $"DateTime:{DateTime.UtcNow}. R9:{JsonConvert.SerializeObject(r9MetricsRecord)}. Passive:{JsonConvert.SerializeObject(passiveMetricsRecord)}";

                    r9MetricsRecord = new ConcurrentDictionary<string, ConcurrentDictionary<string, long>>();
                    passiveMetricsRecord = new ConcurrentDictionary<string, ConcurrentDictionary<string, long>>();
                }

                stopwatch.Stop();
                SDKMeter.RecordSDKDebugPerfCounter(stopwatch.ElapsedTicks);
                stopwatch.Reset();

                MDSLogger.TraceLog(LogLevel.Information, LogEventID.PassiveSDKMetricDebugInfomation, logMessage);
            }
            catch (Exception e)
            {
                SDKLog.Error($"Error happens in SendRecords:{e}");
            }
        }

        /// <summary>
        /// Add metric values to the R9 dictionary
        /// </summary>
        /// <param name="metricName">The metric name</param>
        /// <param name="data">The value</param>
        /// <param name="listDimensionValues">The dimension list</param>
        public void AppendRecordsToR9Dictionary(string metricName, long data, params string[] listDimensionValues)
        {
            if (this.isDebugInfoCollectorEnabled)
            {
                try
                {
                    string dimensionsKey = "null";
                    if (listDimensionValues != null)
                    {
                        dimensionsKey = string.Join("_", listDimensionValues);
                    }

                    ConcurrentDictionary<string, long> dimensionDictionary;
                    if (r9MetricsRecord.TryGetValue(metricName, out dimensionDictionary))
                    {
                        if (dimensionDictionary.ContainsKey(dimensionsKey))
                        {
                            dimensionDictionary[dimensionsKey] += data;
                        }
                        else
                        {
                            dimensionDictionary.TryAdd(dimensionsKey, data);
                        }
                    }
                    else
                    {
                        dimensionDictionary = new ConcurrentDictionary<string, long>();
                        dimensionDictionary.TryAdd(dimensionsKey, data);
                        r9MetricsRecord.TryAdd(metricName, dimensionDictionary);
                    }
                }
                catch (Exception e)
                {
                    SDKLog.Error($"Error happens in AppendRecordsToR9Dictionary:{e}");
                }
            }
        }

        /// <summary>
        /// Add metric values to the passive dictionary
        /// </summary>
        /// <param name="metricName">The metric name</param>
        /// <param name="data">The value</param>
        /// <param name="listDimensionValues">The dimension list</param>
        public void AppendRecordsToPassiveSDKDictionary(string metricName, long data, params string[] listDimensionValues)
        {
            if (this.isDebugInfoCollectorEnabled)
            {
                try
                {
                    string dimensionsKey = "null";
                    if (listDimensionValues != null)
                    {
                        dimensionsKey = string.Join("_", listDimensionValues);
                    }

                    ConcurrentDictionary<string, long> dimensionDictionary;
                    if (passiveMetricsRecord.TryGetValue(metricName, out dimensionDictionary))
                    {
                        if (dimensionDictionary.ContainsKey(dimensionsKey))
                        {
                            dimensionDictionary[dimensionsKey] += data;
                        }
                        else
                        {
                            dimensionDictionary.TryAdd(dimensionsKey, data);
                        }
                    }
                    else
                    {
                        dimensionDictionary = new ConcurrentDictionary<string, long>();
                        dimensionDictionary.TryAdd(dimensionsKey, data);
                        passiveMetricsRecord.TryAdd(metricName, dimensionDictionary);
                    }
                }
                catch (Exception e)
                {
                    SDKLog.Error($"Error happens in AppendRecordsToPassiveSDKDictionary:{e}");
                }
            }
        }
    }
}
