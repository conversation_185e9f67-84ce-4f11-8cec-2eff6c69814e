﻿// <copyright file="ODLTcpTraceExporterExtensions.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Options;
using Microsoft.R9.Extensions.Diagnostics;
using OpenTelemetry;
using OpenTelemetry.Trace;

namespace Microsoft.M365.Core.Telemetry.Exporters.ODLTCP.Trace
{
    /// <summary>
    /// ODL Tcp NRT exporter extension.
    /// </summary>
    public static class ODLTcpTraceExporterExtensions
    {
        /// <summary>
        /// Adds ODL NRT exporter as a configuration to the OpenTelemetry ILoggingBuilder.
        /// </summary>
        /// <param name="builder">Logging builder where the exporter will be added.</param>
        /// <param name="configure">ODL exporter extended options to be configured.</param>
        /// <param name="batchExport">determine whether to use batchLogRecordExportProcessor</param>
        /// <returns>The instance of <see cref="ILoggingBuilder"/> to chain the calls.</returns>
        public static TracerProviderBuilder AddODLTcpExporter(this TracerProviderBuilder builder, Action<ODLTcpTraceExporterOptions> configure, bool batchExport)
        {
            _ = Throws.IfNull(builder);
            _ = Throws.IfNull(configure);
            _ = builder.ConfigureServices(services => services.Configure(configure));
            return builder.AddProcessor(batchExport);
        }

        /// <summary>
        /// Extension method to add Geneva exporter.
        /// </summary>
        /// <param name="builder">Logging builder where the exporter will be added.</param>
        /// <param name="section">Configuration section that contains <see cref="ODLLogExporterOptions"/>.</param>
        /// <param name="batchExport">determine whether to use batchLogRecordExportProcessor</param>
        /// <returns>The instance of <see cref="ILoggingBuilder"/>.</returns>
        /// <exception cref="ArgumentNullException">When <paramref name="builder"/> or <paramref name="section"/> is <see langword="null" />.</exception>
        public static TracerProviderBuilder AddODLTcpExporter(this TracerProviderBuilder builder, IConfigurationSection section, bool batchExport)
        {
            _ = Throws.IfNull(builder);
            _ = Throws.IfNull(section);
            _ = builder.ConfigureServices(services => services.Configure<ODLTcpTraceExporterOptions>(section));
            return builder.AddProcessor(batchExport);
        }

        /// <summary>
        /// Extension method to add proessor.
        /// </summary>
        /// <param name="builder"></param>
        /// <param name="batchExport"></param>
        /// <returns>The instance of <see cref=">TracerProviderBuilder"/></returns>
        internal static TracerProviderBuilder AddProcessor(this TracerProviderBuilder builder, bool batchExport)
        {
            _ = builder.AddProcessor(sp =>
            {
                var options = sp.GetRequiredService<IOptions<ODLTcpTraceExporterOptions>>();
                if (batchExport)
                {
                    return new BatchActivityExportProcessor(new ODLTcpTraceExporter(options), options.Value.MaxQueueSize, options.Value.ScheduledDelayMilliseconds, options.Value.ExporterTimeoutMilliseconds, options.Value.MaxExportBatchSize);
                }
                else
                {
                    return new SimpleActivityExportProcessor(new ODLTcpTraceExporter(options));
                }
            });
            return builder;
        }
    }
}
