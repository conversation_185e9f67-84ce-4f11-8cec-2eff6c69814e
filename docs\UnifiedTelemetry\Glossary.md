# Glossary

## C

### Category

## I

### ILogger

This is the primary interface for logging in .NET. It provides methods to log messages at different severity levels (e.g., LogInformation, LogWarning, LogError). ILogger is typically used with dependency injection, allowing it to be injected into classes and services.

## L

### Logger

This is an implementation of the ILogger interface. It is responsible for writing log messages to the configured logging providers. The Logger class is usually not directly instantiated by developers; instead, it is created by the LoggerFactory.

### Logger Factory

This is a factory class that creates instances of ILogger. It stores all the configuration that determines where log messages are sent (e.g., console, file, or external systems). The LoggerFactory can be configured with different logging providers to direct log messages to various outputs.

## T

### TableNameMappings

An optiong of Geneva Log Exporter.
It defines a routing rule from Logger to Geneva Event.
The key is the log category (or logger name), the value is Geneva event name.
[How to set TableNameMappings for Geneva Log Exporter](./FAQs/TableNameMappings.md) explains more with some samples.