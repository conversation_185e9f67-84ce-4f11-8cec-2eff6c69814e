﻿// <copyright file="TracingSamplerAndEnableOptions.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

namespace Microsoft.M365.Core.Telemetry.R9.Tracing.Instrumentation.Accelerator.Options
{
    /// <summary>
    /// TracingSamplerAndEnableOptions to control the tracing sampler and enable options in local configuration
    /// </summary>
    public class TracingSamplerAndEnableOptions
    {
        /// <summary>
        /// Gets or sets the deploy ring
        /// </summary>
        public string DeployRing { get; set; }

        /// <summary>
        /// Gets or sets the R9 distributed tracing enable flag
        /// </summary>
        public bool R9DTEnabled { get; set; }
        
        /// <summary>
        /// Gets or sets the trace sample rate in DynamicRatioSampler
        /// </summary>
        public float TraceSampleRate { get; set; }
        
        /// <summary>
        /// Gets or sets the sampler type: RatioBased, ParentBased
        /// </summary>
        public string SamplerType { get; set; } = "AlwaysOff";
        
        /// <summary>
        /// Gets or sets the parent root sampler type: <PERSON>ioBase<PERSON>, AlwaysOff
        /// </summary>
        public string ParentRootSamplerType { get; set; } = "AlwaysOff";
    }
}
