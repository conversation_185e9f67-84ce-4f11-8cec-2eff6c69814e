// <copyright file="DynamicSampler.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using Microsoft.M365.Core.Telemetry.ECSClient;
using OpenTelemetry.Trace;

namespace Microsoft.M365.Core.Telemetry.ECS.DynamicComponent.Samplers
{
    /// <summary>
    /// DynamicSampler
    /// </summary>
    public class DynamicSampler : Sampler
    {
        private readonly R9TracingConfig config;
        private Sampler sampler;

        /// <summary>
        /// DynamicSampler
        /// </summary>
        /// <param name="config"></param>
        public DynamicSampler(R9TracingConfig config)
        {
            this.config = config;

            // set default sampler from config
            SetSampler();
        }

        /// <summary>
        /// ShouldSample
        /// </summary>
        /// <param name="samplingParameters"></param>
        /// <returns></returns>
        // TODO(yanling): inject the samplers and hold references
        public override SamplingResult ShouldSample(in SamplingParameters samplingParameters)
        {
            if (!config.R9DTEnabled)
            {
                return new SamplingResult(SamplingDecision.Drop);
            }
            if (!config.ConfigChanged)
            {
                return sampler.ShouldSample(samplingParameters);
            }

            SetSampler();

            config.ConfigChanged = false;
            return sampler.ShouldSample(samplingParameters);
        }

        private void SetSampler()
        {
            switch (config.SamplerType)
            {
                case Constants.DynamicSamplerType.AlwaysOn:
                    sampler = new AlwaysOnSampler();
                    break;
                case Constants.DynamicSamplerType.AlwaysOff:
                    sampler = new AlwaysOffSampler();
                    break;
                case Constants.DynamicSamplerType.RatioBased:
                    sampler = new DynamicRatioSampler(config);
                    break;
                case Constants.DynamicSamplerType.ParentBased:
                    {
                        switch (config.ParentRootSamplerType)
                        {
                            case Constants.DynamicSamplerType.AlwaysOn:
                                sampler = new ParentBasedSampler(new AlwaysOnSampler());
                                break;
                            case Constants.DynamicSamplerType.RatioBased:
                                sampler = new ParentBasedSampler(new DynamicRatioSampler(config));
                                break;
                            case Constants.DynamicSamplerType.AlwaysOff:
                            default:
                                sampler = new ParentBasedSampler(new AlwaysOffSampler());
                                break;
                        }
                    }
                    break;
                default: // default to always off
                    sampler = new AlwaysOffSampler();
                    break;
            }
        }
    }
}
