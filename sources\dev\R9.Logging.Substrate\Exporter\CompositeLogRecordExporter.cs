﻿// <copyright file="CompositeLogRecordExporter.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Diagnostics.CodeAnalysis;
using System.Runtime.InteropServices;
using Microsoft.Extensions.Options;
using Microsoft.M365.Core.Telemetry.Exporters.ODLTCP.Log;
using Microsoft.M365.Core.Telemetry.R9.Logging.Substrate.Exporter;
using Microsoft.R9.Extensions.Logging.Exporters;
using OpenTelemetry;
using OpenTelemetry.Exporter.Geneva;
using OpenTelemetry.Logs;

namespace Microsoft.M365.Core.Telemetry.R9.Logging.Substrate
{
    /// <summary>
    /// Processes log records and routes them to the appropriate exporters based on configuration.
    /// </summary>
    internal class CompositeLogRecordExportProcessor : BaseProcessor<LogRecord>
    {
        private readonly bool isWindows = RuntimeInformation.IsOSPlatform(OSPlatform.Windows);

        private GenevaLogExporterOptions genevaLogExporterOptions;

        private ODLTcpLogExporterOptions odlTcpLogExporterOptions;

        /// <summary>
        /// Gets or sets the event routing mappings.
        /// </summary>
        internal ConcurrentDictionary<string, LogExportType> EventRoutingMappings;

        /// <summary>
        /// actual Exporters
        /// </summary>
        internal ConcurrentDictionary<LogExportType, BaseExportProcessor<LogRecord>> Exporters;

        /// <summary>
        /// Initializes a new instance of the <see cref="CompositeLogRecordExportProcessor"/> class.
        /// </summary>
        /// <param name="genevaExporterOptions"></param>
        /// <param name="odlTcpExporterOptions"></param>
        /// <param name="substrateExporterOptions"></param>
        public CompositeLogRecordExportProcessor(
            IOptions<GenevaLogExporterOptions> genevaExporterOptions,
            IOptions<ODLTcpLogExporterOptions> odlTcpExporterOptions,
            IOptionsMonitor<CompositeLogRecordExporterOptions> substrateExporterOptions)
        {
            // Build routing maps
            // from central config
            (var eventRoutingMappings, var genevaTableNameMappings, var odlTcpTableNameMappings) = substrateExporterOptions.CurrentValue.BuildRoutingMaps();
            this.EventRoutingMappings = eventRoutingMappings;
            substrateExporterOptions.OnChange(RefreshRoutingMap);
            Exporters = new ConcurrentDictionary<LogExportType, BaseExportProcessor<LogRecord>>();

            // Update Table mappings for Exporters (category -> eventStorageTable)
            genevaLogExporterOptions = genevaExporterOptions.Value;
            odlTcpLogExporterOptions = odlTcpExporterOptions.Value;
            genevaLogExporterOptions.TableNameMappings = genevaTableNameMappings;
            odlTcpLogExporterOptions.LogTypeMappings = odlTcpTableNameMappings;

            BuildExporters();
            RegisterHandlers();
        }

        private static void ShutdownExporters(IEnumerable<BaseExportProcessor<LogRecord>> exporters)
        {
            foreach (var exporter in exporters)
            {
                exporter.Shutdown();
            }
        }

        [ExcludeFromCodeCoverage]
        private void RegisterHandlers()
        {
            // Shutdown to send remaining logs on process exit or unhandled exception
            AppDomain.CurrentDomain.ProcessExit += (s, e) => ShutdownExporters(Exporters.Values);
            AppDomain.CurrentDomain.UnhandledException += (s, e) => ShutdownExporters(Exporters.Values);
        }

        /// <summary>
        /// Update routing maps
        /// </summary>
        /// <param name="options"></param>
        /// <param name="str"></param>
        public void RefreshRoutingMap(CompositeLogRecordExporterOptions options, string? str)
        {
            (var newRoutingMappings, var genevaMapping, var odlMapping) = options.BuildRoutingMaps();
            genevaLogExporterOptions.TableNameMappings = genevaMapping;
            odlTcpLogExporterOptions.LogTypeMappings = odlMapping;

            var oldExporters = Exporters;
            BuildExporters();
            this.EventRoutingMappings = newRoutingMappings;

            // using new Exporters now, flush data in the old Exporters
            ShutdownExporters(oldExporters.Values);
        }

        /// <summary>
        /// Processes the log record and routes it to the appropriate exporter.
        /// </summary>
        /// <param name="record"></param>
        public override void OnEnd(LogRecord record)
        {
            if (record.CategoryName != null && EventRoutingMappings.TryMatchValue(record.CategoryName, out LogExportType destination))
            {
                if ((destination & LogExportType.Geneva) == LogExportType.Geneva)
                {
                    if (Exporters.TryGetValue(LogExportType.Geneva, out var genevaExporter))
                    {
                        genevaExporter.OnEnd(record);
                    }
                    else
                    {
                        throw new KeyNotFoundException("Geneva exporter not correctly initialized");
                    }
                }

                if ((destination & LogExportType.OdlTcp) == LogExportType.OdlTcp)
                {
                    if (Exporters.TryGetValue(LogExportType.OdlTcp, out var odlTcpExporter))
                    {
                        odlTcpExporter.OnEnd(record);
                    }
                    else
                    {
                        throw new KeyNotFoundException("ODLTcp exporter not correctly initialized");
                    }
                }
            }
        }

        private void BuildExporters()
        {
            var newExporters = new ConcurrentDictionary<LogExportType, BaseExportProcessor<LogRecord>>();

            // Copied from R9 GenevaBatchLogRecordExportProcessor as it's internal and not exposed to this lib.
            if (!isWindows)
            {
                newExporters[LogExportType.Geneva] = new BatchLogRecordExportProcessor(
                    new GenevaLogExporter(genevaLogExporterOptions),
                    maxQueueSize: genevaLogExporterOptions.MaxQueueSize,
                    maxExportBatchSize: genevaLogExporterOptions.MaxExportBatchSize,
                    scheduledDelayMilliseconds: Convert.ToInt32(genevaLogExporterOptions.ScheduledDelay.TotalMilliseconds),
                    exporterTimeoutMilliseconds: Convert.ToInt32(genevaLogExporterOptions.ExporterTimeout.TotalMilliseconds));
            }
            else
            {
                newExporters[LogExportType.Geneva] = new ReentrantExportProcessor<LogRecord>(new GenevaLogExporter(genevaLogExporterOptions));
            }

            newExporters[LogExportType.OdlTcp] = new BatchLogRecordExportProcessor(
                new ODLTcpLogExporter(Options.Create(odlTcpLogExporterOptions)),
                maxQueueSize: odlTcpLogExporterOptions.MaxQueueSize,
                maxExportBatchSize: odlTcpLogExporterOptions.MaxExportBatchSize,
                scheduledDelayMilliseconds: odlTcpLogExporterOptions.ScheduledDelayMilliseconds,
                exporterTimeoutMilliseconds: odlTcpLogExporterOptions.ExporterTimeoutMilliseconds);

            Exporters = newExporters;
        }
    }
}
