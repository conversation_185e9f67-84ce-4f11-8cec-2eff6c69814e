﻿// <copyright file="ODLTraceExporterOptions.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using Microsoft.R9.Extensions.Diagnostics;

namespace Microsoft.R9.Extensions.Tracing.Exporters
{
    /// <summary>
    /// Options for configuring the ODLNRTExporter.
    /// </summary>
    public class ODLTraceExporterOptions
    {
        /// <summary>
        /// fields used by the ODL Exporter.
        /// </summary>
        private IReadOnlyDictionary<string, string> fields = new Dictionary<string, string>();

        /// <summary>
        /// logTypeMapping used by the ODL Exporter.
        /// </summary>
        /// <remarks>
        /// The logType is required and corresponds to the ODL agent config.
        /// </remarks>
        private IReadOnlyDictionary<string, string> logTypeMappings = new Dictionary<string, string>();

        /// <summary>
        /// Gets or sets for logTypeMapping.
        /// </summary>
        public IReadOnlyDictionary<string, string> LogTypeMappings
        {
            get => logTypeMappings;
        }

        /// <summary>
        /// Gets or sets a value indicating whether to enable fall back logging
        /// </summary>
        public bool EnableFallBack { get; set; }

        /// <summary>
        /// prepopulated fields used by the ODL Exporter.
        /// </summary>
        public IReadOnlyDictionary<string, string> PrepopulatedFields
        {
            get => this.fields;
            set
            {
                _ = Throws.IfNull(value);

                var copy = new Dictionary<string, string>(value.Count);

                foreach (var entry in value)
                {
                    if (entry.Value is null)
                    {
                        throw new ArgumentNullException(entry.Key, $"{nameof(this.PrepopulatedFields)} must not contain null values.");
                    }

                    copy[entry.Key] = entry.Value;
                }

                this.fields = copy;
            }
        }
    }
}