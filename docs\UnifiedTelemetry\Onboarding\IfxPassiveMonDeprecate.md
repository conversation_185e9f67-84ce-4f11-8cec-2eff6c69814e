The **IFx SDK** is scheduled for deprecation on **September 30, 2026**. For more details, refer to the [IFx SDK Retirement Announcement](https://eng.ms/docs/products/geneva/collect/instrument/ifx/ifx-retirement).

Similarly, the **PassiveMon SDK**, which is built on top of the IFx SDK, will also be deprecated. With Microsoft's strategic decision to adopt OpenTelemetry as the company-wide standard for telemetry collection and the migration of most services to the latest .NET, the PassiveMon SDK has become outdated. It no longer aligns with the modern infrastructure required to leverage cutting-edge technologies effectively.


## Nuget Packages to be Retired
- [Microsoft.M365.Core.PassiveMonitoring](https://o365exchange.visualstudio.com/O365%20Core/_artifacts/feed/M365Common/NuGet/Microsoft.M365.Core.PassiveMonitoring/overview/5.0.16)
- [Microsoft.Office.Datacenter.PassiveMonitoring](https://o365exchange.visualstudio.com/O365%20Core/_artifacts/feed/M365Common/NuGet/Microsoft.Office.Datacenter.PassiveMonitoring/overview/16.1.5493)

## Retirement Timeline
The retirement will happen in two phases.

### Phase 1 - No new onboarding after January 1st, 2026
New customers must not onboard to the PassiveMon packages. 
Any violation of this will not be supported. 
All feature work and bug fixes will stop. 
Security/compliance issues will still be addressed in a timely manner.


### Phase 2 - PassiveMon SDK end of life on September 30, 2026
PassiveMon SDK will no longer be supported. 
Security/compliance fixes will no longer be addressed.
Customers must complete the migration to R9 Telemetry before this.

## Why Moving to R9 Telemetry
**R9 Telemetry**, built on top of OpenTelemetry, provides robust infrastructure to simplify the adoption of telemetry solutions for Microsoft services while ensuring compliance. As the north star for telemetry collection in Substrate services, R9 Telemetry offers numerous benefits, including:

- **Linux Support**: Seamless integration with Linux environments.
- **Cross-Model Support**: A unified codebase that supports Model A, B2, and COSMIC.
- **Performance Improvements**: Optimized for high performance with the `ILogger` API.
- **Advanced Features**: Includes log sampling to reduce log volume and routing between exporters.
- **Built-in Distributed Tracing**: Eliminates the need for manual stitching, enabling features like Execution Graph.
- **Automatic Instrumentation**: Popular libraries and frameworks are instrumented out of the box, unlike IFx, which requires manual telemetry creation.
- **Enhanced Performance**: Outperforms the IFx SDK in various scenarios.

For more details, refer to the [R9 SDK](https://eng.ms/docs/experiences-devices/r9-sdk) and [OpenTelemetry](https://opentelemetry.io/).

## How to R9 Telemetry
1. **Onboarding**: Follow the **R9 Logging/Metering Onboarding guide** to complete the initial setup.
2. **Migration**: Follow this section to mesure the risk and complete the transition.  
	- Step 1: Migrate Instrumentaion
	- Step 2: Check Potential Risks
	- Step 3: Switch Data Seamlessly