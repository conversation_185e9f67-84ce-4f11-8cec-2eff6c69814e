<?xml version="1.0" encoding="utf-8"?>
<Project xmlns="http://schemas.microsoft.com/developer/msbuild/2003">

  <Import Project="$([MSBuild]::GetDirectoryNameOfFileAbove('$(MSBuildThisFileDirectory)..', 'Directory.Build.targets'))\Directory.Build.targets" />

  <PropertyGroup>
    <VCDebugCrtDir_170>$(VCInstallDir_170)\redist\debug_nonredist\$(Platform)\Microsoft.VC143.DebugCRT</VCDebugCrtDir_170>
  </PropertyGroup>
  <ItemGroup>
    <VCDebugCrtDll_170 Include="$(VCDebugCrtDir_170)\*.dll"/>
  </ItemGroup>
  <Target Name="CopyDebugCrt"
          Condition="'$(Configuration)|$(Platform)'=='Debug|x64' And '$(Keyword)'=='Win32Proj'"
          AfterTargets="PostBuildEvent">
    <Copy
        SourceFiles="@(VCDebugCrtDll_170)"
        DestinationFolder="$(OutputPath)"
    />
  </Target>

</Project>