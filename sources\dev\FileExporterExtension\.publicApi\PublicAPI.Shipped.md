﻿```csharp

namespace Microsoft.M365.Core.Telemetry.FileExporter
{
    public static class FileExporterExtensions
    {
        public static ISecurityTelemetryPipelineBuilder AddODLFileExporter(this ISecurityTelemetryPipelineBuilder builder);
        public static NRTFileExporter CreatNRTFileExporter();
        public static NRTFileExporter CreatNRTFileExporter(TextFileLoggerOptions options)
    }

    public sealed class NRTFileExporter : IDisposable
    {
        public void BatchExport(in Batch<string> batch);
        public void Export(string logline);
    }

     public class FileExporterOptions : BaseFileLoggerOptions
     {
        public int MaxQueueSize { get; set; } = 2048;
        public int ScheduledDelayMilliseconds { get; set; } = 5000;
        public int ExporterTimeoutMilliseconds { get; set; } = 30000;
        public int MaxExportBatchSize { get; set; } = 512;
        public Func<SecurityRecord, string, string> SecurityRecordFormatter { get; set; }
     }

     public class TextFileLoggerOptions : BaseFileLoggerOptions
     {
        public Func<string, string> StringFormatter { get; set; }
     }

     public class BaseFileLoggerOptions
     {
        public string Directory { get; set; } = string.Empty;
        public string FileName { get; set; } = "application.log";
        public bool EnableRotation { get; set; }
        public long MaxSizeBeforeRotation { get; set; } = 16_777_216;
        public string RotationFileDateTimePattern { get; set; } = "yyyy-MM-ddTHH-mm-ssZ";
        public TimeSpan RetentionInterval { get; set; } = TimeSpan.FromDays(30);
        public long RetentionCount { get; set; } = 99;
        public bool EnableBatchProcessing { get; set; } = true;
     }
}

```
