﻿// <copyright file="SDKDebugInfoCollectorTest.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System;
using System.Collections.Concurrent;
using System.Reflection;
using Microsoft.M365.Core.Telemetry.SDKLogger;
using Xunit;

namespace Microsoft.M365.Core.Telemetry.SDKLoggerTest
{
    /// <summary>
    /// SDKDebugInfoCollectorTest
    /// </summary>
    public class SDKDebugInfoCollectorTest
    {
        /// <summary>
        /// SDKDebugInfoCollectorAttributesTest
        /// </summary>
        [Fact]
        public void SDKDebugInfoCollectorAttributesTest()
        {
            SDKDebugInfoCollector instance = SDKDebugInfoCollector.Instance;
            uint timeslotInSec = SDKDebugInfoCollectorTest.GetPrivateStaticField<uint>(instance, "DefaultSendRecordsTimeIntevalInSec");
            bool isDebugInfoCollectorEnabled = SDKDebugInfoCollectorTest.GetPrivateField<bool>(instance, "isDebugInfoCollectorEnabled");
            ConcurrentDictionary<string, ConcurrentDictionary<string, long>> passiveMetricsRecord = SDKDebugInfoCollectorTest.GetPrivateStaticField<ConcurrentDictionary<string, ConcurrentDictionary<string, long>>>(instance, "passiveMetricsRecord");
            ConcurrentDictionary<string, ConcurrentDictionary<string, long>> r9MetricsRecord = SDKDebugInfoCollectorTest.GetPrivateStaticField<ConcurrentDictionary<string, ConcurrentDictionary<string, long>>>(instance, "r9MetricsRecord");

            Xunit.Assert.Equal(30, (int)timeslotInSec);
            Xunit.Assert.True(isDebugInfoCollectorEnabled);
            Xunit.Assert.Empty(passiveMetricsRecord);
            Xunit.Assert.Empty(r9MetricsRecord);
        }

        /// <summary>
        /// SendRecordsTest
        /// </summary>
        [Fact]
        public void SendRecordsTest()
        {
            SDKDebugInfoCollector instance = SDKDebugInfoCollector.Instance;
            ConcurrentDictionary<string, ConcurrentDictionary<string, long>> passiveMetricsRecord = SDKDebugInfoCollectorTest.GetPrivateStaticField<ConcurrentDictionary<string, ConcurrentDictionary<string, long>>>(instance, "passiveMetricsRecord");
            ConcurrentDictionary<string, long> keyValuePairs = new ConcurrentDictionary<string, long>();
            passiveMetricsRecord.TryAdd("test", keyValuePairs);

            Xunit.Assert.True(passiveMetricsRecord.Count == 1);
            
            object state = new object();
            SDKDebugInfoCollectorTest.InvokePrivateMethod<object>(instance, "SendRecords", state);
            passiveMetricsRecord = SDKDebugInfoCollectorTest.GetPrivateStaticField<ConcurrentDictionary<string, ConcurrentDictionary<string, long>>>(instance, "passiveMetricsRecord");
            
            Xunit.Assert.True(passiveMetricsRecord.IsEmpty);
        }

        /// <summary>
        /// AppendDataTest
        /// </summary>
        [Fact]
        public void AppendDataTest()
        {
            SDKDebugInfoCollector instance = SDKDebugInfoCollector.Instance;

            SetPrivateField(instance, "isDebugInfoCollectorEnabled", true);

            ConcurrentDictionary<string, ConcurrentDictionary<string, long>> passiveMetricsRecord = SDKDebugInfoCollectorTest.GetPrivateStaticField<ConcurrentDictionary<string, ConcurrentDictionary<string, long>>>(instance, "passiveMetricsRecord");
            ConcurrentDictionary<string, ConcurrentDictionary<string, long>> r9MetricsRecord = SDKDebugInfoCollectorTest.GetPrivateStaticField<ConcurrentDictionary<string, ConcurrentDictionary<string, long>>>(instance, "r9MetricsRecord");

            Xunit.Assert.Empty(passiveMetricsRecord);
            Xunit.Assert.Empty(r9MetricsRecord);

            string r9Metric = "r9Metric";
            string passiveMetric = "passiveMetric";

            long data = 100;

            string dimention1 = "dimention1";
            string dimention2 = "dimention2";
            string dimention3 = "dimention3";
            string dimension4 = "dimension4";

            instance.AppendRecordsToR9Dictionary(r9Metric, data, dimention1, dimention2, dimention3);
            instance.AppendRecordsToR9Dictionary(r9Metric, data, dimention1, dimention2, dimention3);
            instance.AppendRecordsToR9Dictionary(r9Metric, data, dimention1, dimention2, dimention3, dimension4);

            instance.AppendRecordsToPassiveSDKDictionary(passiveMetric, data, string.Empty, string.Empty, string.Empty);
            instance.AppendRecordsToPassiveSDKDictionary(passiveMetric, data, dimention1, dimention2, dimention3);
            instance.AppendRecordsToPassiveSDKDictionary(passiveMetric, data, dimention1, dimention2, dimention3);

            Xunit.Assert.Equal(200, r9MetricsRecord[r9Metric][dimention1 + "_" + dimention2 + "_" + dimention3]);
            Xunit.Assert.Equal(100, r9MetricsRecord[r9Metric][dimention1 + "_" + dimention2 + "_" + dimention3 + "_" + dimension4]);
            Xunit.Assert.Equal(100, passiveMetricsRecord[passiveMetric]["__"]);
            Xunit.Assert.Equal(200, passiveMetricsRecord[passiveMetric][dimention1 + "_" + dimention2 + "_" + dimention3]);
        }

        /// <summary>
        /// GetPrivateStaticField
        /// </summary>
        /// <typeparam name="T">T</typeparam>
        /// <param name="o"></param>
        /// <param name="name"></param>
        /// <returns></returns>
        protected static T GetPrivateStaticField<T>(object o, string name)
        {
            BindingFlags flags = BindingFlags.Static | BindingFlags.NonPublic;
            FieldInfo fiInfo = o.GetType().GetField(name, flags);
            return (T)fiInfo.GetValue(o);
        }

        /// <summary>
        /// GetPrivateField
        /// </summary>
        /// <typeparam name="T">T</typeparam>
        /// <param name="o"></param>
        /// <param name="name"></param>
        /// <returns></returns>
        protected static T GetPrivateField<T>(object o, string name)
        {
            FieldInfo fiInfo = o.GetType().GetField(name, BindingFlags.NonPublic | BindingFlags.Instance);
            return (T)fiInfo.GetValue(o);
        }

        /// <summary>
        /// SetPrivateField
        /// </summary>
        /// <param name="o"></param>
        /// <param name="name"></param>
        /// <param name="value"></param>
        protected static void SetPrivateField(object o, string name, object value)
        {
            FieldInfo fiInfo = o.GetType().GetField(name, BindingFlags.Instance | BindingFlags.NonPublic);
            fiInfo.SetValue(o, value);
        }

        /// <summary>
        /// InvokePrivateMethod
        /// </summary>
        /// <typeparam name="T">T</typeparam>
        /// <param name="o"></param>
        /// <param name="methodName"></param>
        /// <param name="methodParams"></param>
        /// <returns></returns>
        protected static T InvokePrivateMethod<T>(object o, string methodName, params object[] methodParams)
        {
            MethodInfo dynMethod = SDKDebugInfoCollectorTest.GetPrivateMethod(o.GetType(), methodName);
            return (T)dynMethod.Invoke(o, methodParams);
        }

        private static MethodInfo GetPrivateMethod(object o, string name)
        {
            Type t = o is Type ? o as Type : o.GetType();

            return t.GetMethod(name, BindingFlags.Instance | BindingFlags.NonPublic);
        }
    }
}
