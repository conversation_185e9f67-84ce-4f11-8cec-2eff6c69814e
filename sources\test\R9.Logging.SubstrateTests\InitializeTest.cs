// <copyright file="InitializeTest.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System;
using System.IO;
using System.Text;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.M365.Core.Telemetry.R9.Logging.Substrate;
using Microsoft.R9.Extensions.Logging;
using Microsoft.R9.Extensions.Logging.Exporters;
using Xunit;

namespace R9.Logging.SubstrateTests
{
    /// <summary>
    /// Test Extension for Substrate Logging Initialization
    /// </summary>
    public class InitializeTest
    {
        private const string BasicConfig = @"
{
    ""Logging"": {
        ""LogLevel"": {
            ""Default"": ""Information""
        }
    },
    ""SubstrateLogging"": {
        ""R9Logging"": {
            ""MaxStackTraceLength"": 3000
        },
        ""GenevaExporter"": {
            ""ConnectionString"": ""EtwSession=TestSession1"",
            ""TableNameMappings"": {
                ""Microsoft.Namespace.EventName"": ""EventName""
            }
        }
    }
}";

        /// <summary>
        /// call to initialize normally
        /// </summary>
        [Fact]
        public void InitializeSubstrateFullProcess()
        {
            IConfiguration configuration = new ConfigurationBuilder()
                .AddJsonStream(new MemoryStream(Encoding.UTF8.GetBytes(BasicConfig)))
                .Build();
            var services = new ServiceCollection();

            var exception = Record.Exception(() =>
            {
                services.AddSubstrateLogging(configuration, loggingBuilder =>
                {
                    loggingBuilder.AddGenevaExporter(opt => opt.ConnectionString = "EtwSession=TestSession2");
                });
                services.BuildServiceProvider();
            });
            Assert.Null(exception);

            var serviceProvider = services.BuildServiceProvider();
            var logger = serviceProvider.GetService<ILogger<InitializeTest>>();
            Assert.NotNull(logger);
        }

        /// <summary>
        /// Examine the case where some optional sections are missing
        /// </summary>
        /// <param name="section"></param>
        /// <param name="newValue"></param>
        [Theory]
        [InlineData("SubstrateLogging:R9Logging")]
        [InlineData("SubstrateLogging:R9Logging:MaxStackTraceLength")]
        public void InitializeSubstrateMinimalConfig(string section, string newValue = null)
        {
            var trimedConfig = BasicConfig.UpdateJsonSection(section, newValue);
            IConfiguration configuration = new ConfigurationBuilder()
                .AddJsonStream(new MemoryStream(Encoding.UTF8.GetBytes(trimedConfig)))
                .Build();
            var services = new ServiceCollection();

            var exception = Record.Exception(() =>
            {
                services.AddSubstrateLogging(configuration);
                services.BuildServiceProvider();
            });
            Assert.Null(exception);

            var serviceProvider = services.BuildServiceProvider();
            var logger = serviceProvider.GetService<ILogger<InitializeTest>>();
            Assert.NotNull(logger);
        }

        /// A theory test for the following scenario:
        /// - Section "GenevaLoggingExporter" is missing/has invalid value of the "ConnectionString" property.
        /// - Section "GenevaLoggingExporter" is missing/has invalid value of the "TableNameMappings" property.
        /// <summary>
        /// A theory test for missing configuration sections
        /// </summary>
        /// <param name="section">The section with wrong value</param>
        /// <param name="newValue">Wrong value to be validated</param>
        [Theory]
        [InlineData("SubstrateLogging:GenevaExporter:ConnectionString")]
        [InlineData("SubstrateLogging:GenevaExporter:ConnectionString", "")]
        [InlineData("SubstrateLogging:GenevaExporter:TableNameMappings")]
        [InlineData("SubstrateLogging:GenevaExporter:TableNameMappings", "")]
        public void OptionsInvalidConfigSections(string section, string newValue = null)
        {
            var editedConfig = BasicConfig.UpdateJsonSection(section, newValue);

            IConfiguration configuration = new ConfigurationBuilder()
                .AddJsonStream(new MemoryStream(Encoding.UTF8.GetBytes(editedConfig)))
                .Build();
            var services = new ServiceCollection();
            var exception = Record.Exception(() =>
            {
                new LoggingOptions().UpdateAndValidateOptions(configuration);
                new GenevaLogExporterOptions().UpdateAndValidateOptions(configuration);
            });
            
            Assert.IsType<ArgumentException>(exception);
#if NET472
            var sections = section.Split(':');
            Assert.Contains(sections[sections.Length - 1], exception.Message, StringComparison.CurrentCultureIgnoreCase);
#else
            Assert.Contains(section.Split(':')[^1], exception.Message, StringComparison.CurrentCultureIgnoreCase);
#endif
        }

        /// <summary>
        /// Examine the case where an option is not registered with "configSectionNames"
        /// </summary>
        [Fact]
        public void OptionNotRegistered()
        {
            IConfiguration configuration = new ConfigurationBuilder()
                .AddJsonStream(new MemoryStream(Encoding.UTF8.GetBytes(BasicConfig)))
                .Build();
            var unRegistered = new { Id = 1, Name = "name" };

            var exception = Record.Exception(() =>
            {
                unRegistered.UpdateOption(configuration);
            });

            Assert.IsType<ArgumentException>(exception);
        }
    }
}