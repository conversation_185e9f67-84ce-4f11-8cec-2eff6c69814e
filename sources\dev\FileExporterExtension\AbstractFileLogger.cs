﻿// <copyright file="AbstractFileLogger.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Threading;
using Microsoft.R9.Extensions.SecurityTelemetry;

namespace Microsoft.M365.Core.Telemetry.FileExporter
{
    /// <summary>
    /// Abstract FileLogger.
    /// </summary>
    /// <typeparam name="T">The type T : Class</typeparam>
    internal abstract class AbstractFileLogger<T> : IFileLogger<T>
        where T : class
    {
        /// <param name="options"><see cref="BaseFileLoggerOptions"/>.</param>
        protected readonly BaseFileLoggerOptions options;

        /// <param name="FileManager"><see cref="IFileManager"/>.</param>
        protected readonly IFileManager fileManager;

        /// <param name="FileOpLock"><see cref="SemaphoreSlim"/>.</param>
        protected readonly SemaphoreSlim fileOpLock = new (1);

        /// <summary>
        /// Initializes a new instance of the <see cref="AbstractFileLogger"/> class.
        /// </summary>
        /// <param name="options"><see cref="FileExporterOptions"/>.</param>
        /// <param name="fileManager"><see cref="IFileManager"/>.</param>
        protected AbstractFileLogger(BaseFileLoggerOptions options, IFileManager fileManager)
        {
            this.options = options;
            this.fileManager = fileManager;

            if (string.IsNullOrEmpty(this.options.Directory))
            {
                this.options.Directory = Directory.GetCurrentDirectory();
            }
            else
            {
                this.options.Directory = Path.Combine(Directory.GetCurrentDirectory(), this.options.Directory);
            }
        }

        /// <summary>
        /// Dispose
        /// </summary>
        public void Dispose()
        {
            fileOpLock.Dispose();
        }

        /// <summary>
        /// Log batch of records to disk
        /// </summary>
        /// <param name="batch"></param>
        public abstract void Log(in Batch<T> batch);

        /// <summary>
        /// Log single records to disk.
        /// </summary>
        /// <param name="t"></param>
        public abstract void Log(T t);

        /// <summary>
        /// Log file to disk.
        /// </summary>
        /// <param name="logEntries"></param>
        internal void LogToFile(ICollection<string> logEntries)
        {
            if (logEntries.Count == 0)
            {
                return;
            }

            try
            {
                fileOpLock.Wait();

                if (!fileManager.DirectoryExists(options.Directory))
                {
                    fileManager.CreateDirectory(options.Directory);
                }

                var fullFileName = Path.Combine(options.Directory, GetFormattedFileName(options.FileName));
                
                if (options.EnableCleanUpArchivedFiles)
                {
                    try
                    {
                        var archivedLogFiles = new List<string>(fileManager.GetFiles(options.Directory))
                    .Select(fileName => fileManager.CreateLogFile(fileName));
                        CleanUpArchivedFiles(archivedLogFiles);
                    }
                    catch (Exception ex)
                    {
                        FileExporterEventSource.Instance.FileIOFailed(ex.Message, options.FileName);
                    }
                }

                fileManager.WriteAllLines(fullFileName, logEntries);
            }
            finally
            {
                _ = fileOpLock.Release();
            }
        }

        /// <summary>
        /// Format file name with timeslot.
        /// </summary>
        /// <param name="fileName"></param>
        /// <returns></returns>
        internal string GetFormattedFileName(string fileName)
        {
            return $"{fileName}_{Process.GetCurrentProcess().Id}_{Thread.CurrentThread.ManagedThreadId}_{DateTimeOffset.UtcNow.ToString(options.FileDateTimePattern, DateTimeFormatInfo.InvariantInfo)}{options.FileExtension}";
        }

        /// <summary>
        /// CleanUpArchivedFiles
        /// </summary>
        /// <param name="archivedLogFiles"></param>
        internal void CleanUpArchivedFiles(IEnumerable<LogFile> archivedLogFiles)
        {
            var remainingLogFiles = new List<LogFile>();

            foreach (var logFile in archivedLogFiles)
            {
                if (logFile.Name.StartsWith(options.FileName, StringComparison.Ordinal))
                {
                    var currentTimeUtc = DateTimeOffset.UtcNow;

                    if (currentTimeUtc > logFile.LastWriteTimeOffset && DateTimeOffset.UtcNow.Subtract(logFile.LastWriteTimeOffset) > options.RetentionInterval)
                    {
                        FileExporterEventSource.Instance.CleanUpArchivedLogFiles("over retention interval", logFile.FullName);
                        fileManager.DeleteFile(logFile.FullName);
                    }
                    else
                    {
                        remainingLogFiles.Add(logFile);
                    }
                }
            }

            if (remainingLogFiles.Count > options.RetentionCount)
            {
                remainingLogFiles.Sort((f1, f2) => f1.LastWriteTimeOffset.CompareTo(f2.LastWriteTimeOffset));

                var remainingDeletionCount = (int)(remainingLogFiles.Count - options.RetentionCount);

                while (remainingDeletionCount > 0)
                {
                    var deletingFileName = remainingLogFiles[remainingDeletionCount - 1].FullName;

                    remainingDeletionCount--;

                    FileExporterEventSource.Instance.CleanUpArchivedLogFiles("over retention count", deletingFileName);
                    fileManager.DeleteFile(deletingFileName);
                }
            }
        }
    }
}
