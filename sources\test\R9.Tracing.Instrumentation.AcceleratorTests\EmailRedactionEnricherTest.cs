﻿// <copyright file="EmailRedactionEnricherTest.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System.Diagnostics;
using System.Net;
using System.Net.Http;
using Microsoft.Extensions.Http.Diagnostics;
using Microsoft.M365.Core.Telemetry.R9.Tracing.Instrumentation.Accelerator.EUIIRedaction;
using Microsoft.R9.Extensions.Telemetry;
using Xunit;
using RequestMetadata = Microsoft.R9.Extensions.Telemetry.RequestMetadata;

namespace Microsoft.M365.Core.Telemetry.R9.Tracing.Instrumentation.Accelerator.Tests
{
    /// <summary>
    /// EmailRedactionEnricherTest
    /// </summary>
    public class EmailRedactionEnricherTest
    {
        HttpClientEmailRedaction enricher = new HttpClientEmailRedaction();

        /// <summary>
        /// Enricher Test
        /// </summary>
#if NETFRAMEWORK
        [Fact]
        public void EnrichEmailRedactionTest()
        {
            var activity = new Activity("test");
            HttpWebRequest httpRequest = WebRequest.CreateHttp("http://localhost:8080/api/values?name=foo");
            enricher.Enrich(activity, httpRequest, null);
#pragma warning disable R9EXP0008 // Type is for evaluation purposes only and is subject to change or removal in future updates. Suppress this diagnostic to proceed.
            Assert.NotNull(httpRequest.GetRequestMetadata());
#pragma warning restore R9EXP0008 // Type is for evaluation purposes only and is subject to change or removal in future updates. Suppress this diagnostic to proceed.
        }

        /// <summary>
        /// WithInvalidRequest_MetaDataNotSet
        /// </summary>
        [Fact]
        public void TestInvalidRequest()
        {
            var activity = new Activity("test");
            HttpWebRequest httpRequest = WebRequest.CreateHttp("http://localhost:8080/api/values?name=foo");

            // request is null, not throw exception
            Assert.Null(Record.Exception(() => enricher.Enrich(activity, null, null)));

            // activity is null, not throw exception
            Assert.Null(Record.Exception(() => enricher.Enrich(null, httpRequest, null)));

            //metadata is not null, not throw exception
#pragma warning disable R9EXP0008 // Type is for evaluation purposes only and is subject to change or removal in future updates. Suppress this diagnostic to proceed.
            httpRequest.SetRequestMetadata(new RequestMetadata());
#pragma warning restore R9EXP0008 // Type is for evaluation purposes only and is subject to change or removal in future updates. Suppress this diagnostic to proceed.
            Assert.Null(Record.Exception(() => enricher.Enrich(activity, httpRequest, null)));
        }
#else
        /// <summary>
        /// Enricher Test
        /// </summary>
        [Fact]
        public void EnrichEmailRedactionTest()
        {
            HttpClientEmailRedaction enricher = new HttpClientEmailRedaction();
            var activity = new Activity("test");
            HttpRequestMessage httpRequestMessage = new HttpRequestMessage(HttpMethod.Get, "https://www.example.com/api/v1/users/12345");
            HttpResponseMessage httpResponseMessage = new HttpResponseMessage();
            enricher.Enrich(activity, httpRequestMessage, httpResponseMessage);
            Assert.NotNull(httpRequestMessage.GetRequestMetadata());
        }

        /// <summary>
        /// WithInvalidRequest_MetaDataNotSet
        /// </summary>
        [Fact]
        public void TestInvalidRequest()
        {
            var activity = new Activity("test");
            HttpRequestMessage httpRequestMessage = new HttpRequestMessage();
            httpRequestMessage.RequestUri = null;
            
            // uri is null, return
            enricher.Enrich(activity, httpRequestMessage, null);
            Assert.Null(Record.Exception(() => enricher.Enrich(activity, httpRequestMessage, null)));

            // request is null, return
            enricher.Enrich(activity, null, null);
            Assert.Null(Record.Exception(() => enricher.Enrich(activity, null, null)));

            // activity is null, return
            httpRequestMessage = new HttpRequestMessage(HttpMethod.Get, "https://www.example.com/api/v1/users/12345");
            Assert.Null(Record.Exception(() => enricher.Enrich(null, httpRequestMessage, null)));

            //metadata is not null, return
            httpRequestMessage.SetRequestMetadata(new RequestMetadata());
            Assert.Null(Record.Exception(() => enricher.Enrich(activity, httpRequestMessage, null)));
        }

#endif
    }
}