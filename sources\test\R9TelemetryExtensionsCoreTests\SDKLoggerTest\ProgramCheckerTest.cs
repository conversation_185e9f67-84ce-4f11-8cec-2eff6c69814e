﻿// <copyright file="ProgramCheckerTest.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System.Collections.Generic;
using FluentAssertions;
using Microsoft.M365.Core.Telemetry.SDKLogger;
using Microsoft.M365.Core.Telemetry.TestCommon;
using Xunit;
using Xunit.Abstractions;

namespace Microsoft.M365.Core.Telemetry.SDKLoggerTest
{
    /// <summary>
    /// ProgramCheckerTest
    /// </summary>
    public class ProgramCheckerTest : BaseTest
    {
        /// <summary>
        /// ProgramCheckerTest
        /// </summary>
        /// <param name="output"></param>
        public ProgramCheckerTest(ITestOutputHelper output) : base(output)
        {
        }

        /// <summary>
        /// TestGetMetricBaseType
        /// </summary>
        [Fact]
        public void TestGetMetricBaseType()
        {
            var obj = new List<string>();
            var paramType = obj.GetType();
            var type1 = ProgramChecker.GetMetricBaseType(paramType, "System.Object");
            Assert.Null(type1);
            var type2 = ProgramChecker.GetMetricBaseType(paramType, "System.Collections.Generic.List`1");
            Assert.Equal(paramType, type2);
        }
    }
}
