// Use abseil variant in opentelemetry to avoid ambiguous symbol error.
// Define the flag in code here. The flag defined in vcxproj file is not
// populated to the included header files.
#define HAVE_ABSEIL

#include "OdlTraceExporter.h"
#include <ODLNRTMessage.pb.h>
#include "AsioContext.h"

#include <opentelemetry/exporters/etw/etw_tracer_exporter.h>
#include <opentelemetry/exporters/geneva/metrics/exporter.h>
#include <opentelemetry/exporters/geneva/geneva_logger_exporter.h>
#include <opentelemetry/exporters/ostream/log_record_exporter.h>
#include <opentelemetry/logs/logger_provider.h>
#include <opentelemetry/logs/provider.h>
#include <opentelemetry/metrics/meter_provider.h>
#include <opentelemetry/metrics/provider.h>
#include <opentelemetry/sdk/logs/logger_provider.h>
#include <opentelemetry/sdk/logs/logger_provider_factory.h>
#include <opentelemetry/sdk/logs/simple_log_record_processor_factory.h>
#include <opentelemetry/sdk/metrics/export/periodic_exporting_metric_reader_factory.h>
#include <opentelemetry/sdk/metrics/export/periodic_exporting_metric_reader_options.h>
#include <opentelemetry/sdk/metrics/meter.h>
#include <opentelemetry/sdk/metrics/meter_provider.h>
#include <opentelemetry/sdk/metrics/meter_provider_factory.h>

#include <string>
#include <iostream>
#include <objbase.h>

using namespace Microsoft::M365::ODLExporter;

namespace {
std::string SizeTTo4ByteString(unsigned int value) {
    std::string result(4, '\0'); // Create a string of 4 null characters
    for (int i = 0; i < 4; ++i) {
        result[i] = static_cast<unsigned char>((value >> (i * 8)) & 0xFF);
    }
    return result;
}

void DebugPrintHex(const std::string& str) {
    for (unsigned char c : str) {
        std::cout << std::hex << std::setw(2) << std::setfill('0') << static_cast<int>(c) << " ";
    }
    std::cout << std::endl;
}

std::string GetUniqueId()
{
    GUID guid;
    if (CoCreateGuid(&guid) != S_OK)
    {
        // TODO(jiayiwang): Just log, don't throw.
        throw std::runtime_error("Failed to create GUID");
    }

    // The buffer needs to be larger than the actually size 36.
    CHAR szBuffer[40] = {0};
    sprintf_s(szBuffer, "%08lX-%04hX-%04hX-%02hhX%02hhX-%02hhX%02hhX%02hhX%02hhX%02hhX%02hhX", 
        guid.Data1, guid.Data2, guid.Data3, 
        guid.Data4[0], guid.Data4[1], guid.Data4[2], guid.Data4[3],
        guid.Data4[4], guid.Data4[5], guid.Data4[6], guid.Data4[7]);

    return std::string(szBuffer);
}

// TODO(jiayiwang): Centrolize the config logic and enable unit test.
std::shared_ptr<opentelemetry::metrics::MeterProvider> CreateGenevaMeterProvider() {
    std::string conn_string = "Account=O365_Account;Namespace=M365TelemetryCpp";
    opentelemetry::exporter::geneva::metrics::ExporterOptions geneva_metric_options{conn_string};
    std::unique_ptr<opentelemetry::sdk::metrics::PushMetricExporter> exporter{
        new opentelemetry::exporter::geneva::metrics::Exporter(geneva_metric_options)};

    opentelemetry::sdk::metrics::PeriodicExportingMetricReaderOptions metric_reader_options;
    // Config: export interval and timeout. Timeout < interval.
    metric_reader_options.export_interval_millis = std::chrono::milliseconds(60000);
    metric_reader_options.export_timeout_millis  = std::chrono::milliseconds(10000);
    auto reader = opentelemetry::sdk::metrics::PeriodicExportingMetricReaderFactory::Create(
        std::move(exporter), metric_reader_options);
    auto provider = opentelemetry::sdk::metrics::MeterProviderFactory::Create();
    provider->AddMetricReader(std::move(reader));
    std::shared_ptr<opentelemetry::metrics::MeterProvider> metric_api_provider(std::move(provider));
    return metric_api_provider;
}

std::shared_ptr<opentelemetry::logs::LoggerProvider> CreateGenevaLoggerProvider() {
    std::map<std::string, std::string> tableNameMappings = {
        {"OdlTraceExporterCpp", "MTCOdlTraceExporterCpp"}};
    opentelemetry::exporter::etw::TelemetryProviderOptions geneva_log_options = {
        {"enableTableNameMappings", true},
        {"tableNameMappings", tableNameMappings}};
    return std::make_shared<opentelemetry::exporter::etw::LoggerProvider>(geneva_log_options);
}

// Used in local debug to keep time order with other std::cout.
std::shared_ptr<opentelemetry::logs::LoggerProvider> CreateConsoleLoggerProvider() {
    auto exporter = std::unique_ptr<opentelemetry::sdk::logs::LogRecordExporter>(
        new opentelemetry::exporter::logs::OStreamLogRecordExporter);
    auto processor = opentelemetry::sdk::logs::SimpleLogRecordProcessorFactory::Create(std::move(exporter));
    auto provider = opentelemetry::sdk::logs::LoggerProviderFactory::Create(std::move(processor));

    return std::move(provider);
}

} // namespace

namespace Microsoft::M365::Exporters
{
OdlTraceExporter::OdlTraceExporter(OdlTraceExporterOptions options) noexcept
    : options_(options) {
        logger_provider_ = CreateGenevaLoggerProvider();
        logger_ = logger_provider_->GetLogger("M365TelemetryCppSession", "OdlTraceExporterCpp");
        meter_provider_ = CreateGenevaMeterProvider();
        meter_ = meter_provider_->GetMeter("OdlTraceExporterCpp");
        span_number_counter_ = meter_->CreateUInt64Counter("OdlTraceExporterCppSpanNumber");

        TcpClientOptions tcp_options = {
            options.host,
            options.port,
            options.tcpclient_reconnect_interval_ms,
            options.tcpclient_monitor_interval_s
        };
        tcp_client_ = std::make_unique<TcpClient>(tcp_options, AsioContext::GetIoContext(), logger_);
    }

std::unique_ptr<opentelemetry::sdk::trace::Recordable> OdlTraceExporter::MakeRecordable() noexcept
{
    return std::unique_ptr<opentelemetry::sdk::trace::Recordable>(new OdlTraceExporterRecordable());
}

opentelemetry::sdk::common::ExportResult OdlTraceExporter::Export(
    const opentelemetry::nostd::span<std::unique_ptr<opentelemetry::sdk::trace::Recordable>> &spans) noexcept
{
    span_number_counter_->Add(spans.size(), {{"LogType", options_.source}});
    if (is_shutdown_)
    {
        logger_->Error("OdlTraceExporter is shutdown when calling Export.");
        return opentelemetry::sdk::common::ExportResult::kFailure;
    }
    OdlSpan json_spans = {};
    try
    {
        auto message = std::make_shared<std::string>(serialize_spans(spans, options_, logger_));
        tcp_client_->Send(message);
    }
    catch (const std::exception &e)
    {
        logger_->Error("Error during JSON serialization or message sending: {}", e.what());
        return opentelemetry::sdk::common::ExportResult::kFailure;
    }
    return opentelemetry::sdk::common::ExportResult::kSuccess;
}

bool OdlTraceExporter::ForceFlush(std::chrono::microseconds /* timeout */) noexcept
{
    // TODO(jiayiwang): Handle leftovers.
    return true;
}

bool OdlTraceExporter::Shutdown(std::chrono::microseconds /* timeout */) noexcept
{
    // TODO(jiayiwang): Handle leftovers.
    is_shutdown_ = true;
    tcp_client_->ShutDown();
    return true;
}

int64_t OdlTraceExporter::get_next_sequance()
{
    static std::atomic<int64_t> seq(0);
    return ++seq;
}

std::string OdlTraceExporter::serialize_spans(
    const opentelemetry::nostd::span<std::unique_ptr<opentelemetry::sdk::trace::Recordable>> &spans,
    const OdlTraceExporterOptions &options, std::shared_ptr<opentelemetry::logs::Logger> logger)
{
    // TODO(jiayiwang): Do some memory swap to save a copy.
    // TODO(jiayiwang): One batch can generate multiple messages < 128kb.
    int64_t timestamp = std::chrono::duration_cast<std::chrono::milliseconds>(
        std::chrono::system_clock::now().time_since_epoch()).count();
    ODLNRTRequest request;
    request.mutable_head()->set_timestamp(timestamp);
    request.mutable_head()->set_sequence(get_next_sequance());
    request.mutable_head()->set_commandtype(ODLNRTCommandType::ODLNRTCmd_CommonMessageBatch);
    request.mutable_commonmessagebatchreq()->mutable_head()->set_timestamp(timestamp);

    std::string value_string;
    for (auto &recordable : spans)
    {
        auto rec = std::unique_ptr<OdlTraceExporterRecordable>(static_cast<OdlTraceExporterRecordable *>(recordable.release()));
        if (rec != nullptr)
        {
            auto message = request.mutable_commonmessagebatchreq()->add_messages();
            message->set_type(ODLNRTCommonMessageType::ODLNRTMessageType_String);
            message->set_id(GetUniqueId());
            message->set_eventid(1);
            // TODO(jiayiwang): Implement logtype map and set multiple ODLNRTCommonMessage. For now we have only Nanoproxy.
            message->set_source(options.source);
            message->set_valuestring(rec->Serialize(options.common_dimensions, logger));
        }
    }
    return (std::string(1, kMagicByte) +
            SizeTTo4ByteString(static_cast<unsigned int>(request.ByteSizeLong())) +
            request.SerializeAsString());
}

} // namespace Microsoft::M365::Exporters
