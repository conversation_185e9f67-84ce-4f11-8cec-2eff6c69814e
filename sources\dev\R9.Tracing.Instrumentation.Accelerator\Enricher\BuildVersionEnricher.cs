﻿// <copyright file="BuildVersionEnricher.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System;
using System.Diagnostics;
using System.Diagnostics.CodeAnalysis;
using System.Globalization;
using System.Text;
using Microsoft.Extensions.Options;
using Microsoft.M365.Core.Telemetry.R9.Tracing.Instrumentation.Accelerator.Utilities;
using Microsoft.R9.Extensions.Enrichment;
using Microsoft.Win32;

namespace Microsoft.M365.Core.Telemetry.R9.Tracing.Instrumentation.Accelerator
{
    /// <summary>
    /// Enrich build version
    /// </summary>
    public class BuildVersionEnricher : ITraceEnricher
    {
        /// <summary>
        /// Get build version
        /// </summary>
        /// <param name="registryKey"> Registry key </param>
        public static string GetBuildVersion(IRegistryKey registryKey)
        {
            // Normally build version change is a normal deployment and everything restarts, cleaning the cache.
            string buildVersionCache = TracerStartupExtensions.BuildVersionCache;
            if (!string.IsNullOrEmpty(buildVersionCache))
            {
                return buildVersionCache;
            }

            IRegistryKey variantConfigKey = registryKey.OpenSubKey(@"SOFTWARE\Microsoft\ExchangeServer\v15\Setup");

            int? productMajor = GetSanitizedRegistryIntValue(variantConfigKey, @"MsiProductMajor");
            int? productMinor = GetSanitizedRegistryIntValue(variantConfigKey, @"MsiProductMinor");
            int? buildMajor = GetSanitizedRegistryIntValue(variantConfigKey, @"MsiBuildMajor");
            int? buildMinor = GetSanitizedRegistryIntValue(variantConfigKey, @"MsiBuildMinor");
            string fastTrainVersion = GetSanitizedRegistryValue(variantConfigKey, @"FastTrainVersion");
            string buildVersion = "0.00.0000.000";

            if (productMajor != null && productMinor != null && buildMajor != null && buildMinor != null)
            {
                buildVersion = String.Format(CultureInfo.InvariantCulture, "{0}.{1}.{2}.{3}", productMajor, productMinor.Value.ToString("D2", CultureInfo.InvariantCulture), buildMajor.Value.ToString("D4", CultureInfo.InvariantCulture), buildMinor.Value.ToString("D3", CultureInfo.InvariantCulture));
            }

            if (string.IsNullOrWhiteSpace(fastTrainVersion) || fastTrainVersion == "Unknown" || string.Compare(fastTrainVersion, buildVersion, true, CultureInfo.InvariantCulture) < 0)
            {
                TracerStartupExtensions.BuildVersionCache = buildVersion;
                return buildVersion;
            }

            TracerStartupExtensions.BuildVersionCache = fastTrainVersion;
            return fastTrainVersion;
        }

        /// <inheritdoc/>
        public void Enrich(Activity activity)
        {
            Enrich(activity, new WindowsRegistryKey());
        }

        /// <summary>
        /// Erich with given registryKey
        /// </summary>
        /// <param name="activity"> Activity to enrich </param>
        /// <param name="registryKey"> Registry key </param>
        internal void Enrich(Activity activity, IRegistryKey registryKey)
        {
            string buildVersion = GetBuildVersion(registryKey);
            activity?.SetTag(Constants.BuildVersion, buildVersion);
        }

        private static int? GetSanitizedRegistryIntValue(IRegistryKey registryKey, string keyName)
        {
            int? value = registryKey?.GetValue(keyName) as int?;
            return value;
        }

        /// <summary>
        /// Get the value from <paramref name="variantConfigKey"/>. If missing or empty, return <see cref="_unknown"/>
        /// </summary>
        /// <param name="registryKey"> Registry key </param>
        /// <param name="keyName"> Registry key name </param>
        /// <returns> Sanitized registry key </returns>
        private static string GetSanitizedRegistryValue(IRegistryKey registryKey, string keyName)
        {
            string value = registryKey?.GetValue(keyName) as string;
            return string.IsNullOrWhiteSpace(value) ? "Unknown" : value;
        }
    }
}
