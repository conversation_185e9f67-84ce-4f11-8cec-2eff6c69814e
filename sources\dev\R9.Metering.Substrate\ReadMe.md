
# Substrate R9 Telemetry Metering Extension

This extension package is designed for Substrate users to facilitate the utilization of R9 Metering.
It aims to streamline the setup and configuration process for Metering scenarios, providing users with the flexibility to easily configure data sampling and the destination to store.


## Usage

### Add configurations
The package control the telemetry behavior with configurations.
It loads specific sections of an `IConfiguration` (which should be input when calling the method) and takes essential settings.
A top-level seciton `SubstrateMetering` is introduced to store our settings.


### Must-have configurations
**MeterStateOverrides**

Some of Substrate common libraries collect their own metrics.
To avoid catching them, we set MeterState to `Disabled` by default.
Users need to set `MeterStateOverrides` for listening metrics.

**MonitoringAccount**

Account for Geneva MDM

**MonitoringNamespace**

Namespace for Geneva MDM
```json
{
    "SubstrateMetering": {
        "R9Metering": {
            "MeterStateOverrides": {
                "Microsoft.SomeDomain": "Enabled"
            }
        },
        "GenevaExporter": {
            "MonitoringAccount": "fooAccount",
            "MonitoringNamepsace": "fooNamespace"
        }
    }
}
```

Users may store them at local file(like `appsettings.json`) or remote config service (e.g. ECS).

### Initialize R9 Metering in code
Before calling the initialization method, there **must be** a `IConfiguration configuration` that has loaded the configurations above.
There are 3 ways to initialize R9 metering. Please choose appropriate one.

#### Dependency injection (DI) model with an existing service collection
If you are building an application/library with DI, call `AddSubstrateMetering()` to setup Metering. DI container manage the whole lifecycle of its services.
```C#
serviceCollection.AddSubstrateMetering(configuration)
```

#### Non-DI model
If your application/library doesn't use DI, please call `ConfigureSubstrateMetering()` to setup Metering. The lifetime of the meter provider is managed by the extention method automatically. You don't need to worry about the dispose thing. 
```c#
SubstrateMeteringExtension.ConfigureSubstrateMetering(configuration)
```

#### Non-DI model with an existing MeterProviderBuilder
If you already have a `MeterProviderBuilder` in your code, call `ConfigureSubstrateMetering()` to setup Metering. Please remember to build a MeterProvider and dispose it when application shuts down.
```c#
meterProviderBuilder.ConfigureSubstrateMetering(configuration)
```

### Advanced - Customize MeterProviderBuilder
It supports customize the meter provider by passing a `Action<MeterProviderBuilder>` to config `MeterProviderBuilder`:
```C#
serviceCollection.AddSubstrateMetering(configuration, meterProviderBuilder =>
{
    meterProviderBuilder.AddView(...);
})
or
SubstrateMeteringExtension.ConfigureSubstrateMetering(configuration, meterBuilder =>
{
    meterBuilder.AddConsoleExporter();
});
```

## Configuration Example and Reference
Here is an example of user config with document reference link in the comments:
```jsx
{
    // We introduce a new section
    "SubstrateMetering": {
        // // config of R9 MeteringOptions, see:
        // // https://eng.ms/docs/experiences-devices/r9-sdk/refdocs/microsoftr9extensionsMeteringMeteringextensions
        // "R9Metering": {
        //     "MaxStackTraceLength": 3000
        // },
        // // config of GenevaLogExporterOptions, see:
        // // https://eng.ms/docs/experiences-devices/r9-sdk/docs/telemetry/Metering/geneva-log-export#logger-configuration
        // "GenevaExporter": {
        //     "ConnectionString": "EtwSession=TestSession"
        // }
    },
    //...
}
```

## Use Scenario metric
If you use Scenario based metric provided by Passive SDK before, we recommend you migrate to `R9ScenarioMetric`. 
For scenario based metrics, we enforce the following 3 dimensions.
|Dimension Name|Description|Example value|
|--|--|--|
|ComponentName|Component name|OWS|
|ScenarioName|Name of the scenario|PopulateCalendarWithFlights|
|SubScenarioName|Name of the sub scenario|SouthWestFlights|

It is very easy to use R9ScenarioMetric directly.

### Availability Metric & Latency Metric
Availability Metric and Latency Metric have all the scenario based metric dimensions. The usage of Availability Metric and Latency Metric are same. The differece is that Availability Metric records 0 (Failed) or 1 (Succeed). Latency Metric records latency value in milliseconds.
```C#
var meter = new Meter("test");
var latencyMetric = R9ScenarioMetric.CreateLatencyMetric(meter);
latencyMetric.Record(123, new ScenarioDimension("component", "scenario", "subScenario"));
```

### Error Metric
Error Metric has all the scenario based metric dimensions. It has an additional dimension called `Type`. Error Metric is a counter which counts errors happeded.
```c#
var meter = new Meter("test");
var errorMetric = R9ScenarioMetric.CreateErrorMetric(meter);
errorMetric.Add(1, new ErrorScenario("component", "scenario", "subScenario", "test"));
```

### Extend scenario based metric
You can extend the scenario based metric with custom dimensions.
First define the custom scenario class with the custom properties for custom dimensions. For example `ErrorScenario`.
```c#
    public class ErrorScenario : ScenarioDimension
    {
        public ErrorScenario(string component, string name, string subName) : base(component, name, subName)
        {
        }
        public string? Type { get; set; }
    }
```
Then you can define your own scenario based metric by fast metering.
```c#
    public static partial class YourScenarioMetric
    {
        [Counter(typeof(ErrorScenario))]
        public static partial ErrorMetric CreateErrorMetric(this Meter meter);
    }
```
Then your can create your metric and use it like above examples.