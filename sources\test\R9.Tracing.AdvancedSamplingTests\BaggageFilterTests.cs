﻿// <copyright file="BaggageFilterTests.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System.Diagnostics;
using Microsoft.M365.Core.Telemetry.R9.Tracing.AdvancedSampling;
using Xunit;

namespace R9.Tracing.AdvancedSamplingTests
{
    /// <summary>
    /// PostTraceSamplingTests
    /// </summary>
    public class BaggageFilterTests
    {
        /// <summary>
        /// Test BaggageFilter
        /// </summary>
        [Fact]
        public void TestBaggageFilter()
        {
            BaggageFilter filter = new BaggageFilter();
            Assert.False(filter.ShouldFilter(null));

            var activity = new Activity("activity1");
            Assert.False(filter.ShouldFilter(activity));

            var activityWithPostBaggage = new Activity("activity2");
            activityWithPostBaggage.SetBaggage(Constants.PostTraceBaggageName, Constants.SampleBaggage);
            Assert.True(filter.ShouldFilter(activityWithPostBaggage));

            var activityWithSampleBaggage = new Activity("activity3");
            activityWithSampleBaggage.SetBaggage(Constants.SampleBaggageName, Constants.SampleBaggage);
            Assert.True(filter.ShouldFilter(activityWithSampleBaggage));

            BaggageFilter customFilter = new BaggageFilter(new Dictionary<string, string> { { "TestBaggage", "TestVal" } });

            var activityWithCustomBaggage = new Activity("activity4");
            activityWithCustomBaggage.SetBaggage(Constants.SampleBaggageName, Constants.SampleBaggage);
            Assert.False(customFilter.ShouldFilter(activityWithCustomBaggage));

            activityWithCustomBaggage.SetBaggage("TestBaggage", "OtherVal");
            Assert.False(customFilter.ShouldFilter(activityWithCustomBaggage));

            activityWithCustomBaggage.SetBaggage("TestBaggage", "TestVal");
            Assert.True(customFilter.ShouldFilter(activityWithCustomBaggage));

            Assert.NotEmpty(filter.GetDescription());
        }
    }
}