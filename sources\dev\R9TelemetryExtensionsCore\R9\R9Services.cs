﻿// <copyright file="R9Services.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System;
using System.Diagnostics.CodeAnalysis;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.M365.Core.Telemetry.ECSClient;
using Microsoft.M365.Core.Telemetry.SDKLogger;
using Microsoft.R9.Extensions.Metering;

namespace Microsoft.M365.Core.Telemetry.R9
{
    /// <summary>
    /// Export R9 telemetry objects from IServiceCollection.
    /// </summary>
    [ExcludeFromCodeCoverage]
    public static class R9Services
    {
        /// <summary>
        /// AlwaysEnableR9
        /// </summary>
        // TODO(jiayiwang): LoggerFactory.Create is only for > .net extensions 3.1.5.
        // Hosting is only for > .net extensions 5.0.0.
        internal static bool AlwaysEnableR9 { get; private set; }

        private static IServiceProvider serviceProvider;

        /// <summary>
        /// Get IPassiveR9Config
        /// </summary>
        /// <returns>IPassiveR9config.</returns>
        public static IPassiveR9Config GetPassiveR9Config()
        {
            return serviceProvider.GetRequiredService<IPassiveR9Config>();
        }

        /// <summary>
        /// Get ITelemetryEmitConfig
        /// </summary>
        /// <returns>ITelemetryEmitConfig</returns>
        public static ITelemetryEmitConfig GetTelemetryEmitConfig()
        {
            return serviceProvider.GetRequiredService<ITelemetryEmitConfig>();
        }

        /// <summary>
        /// Writes to Geneva in .net core 3.1, writes to console in .net framework and netstandard.
        /// </summary>
        public static bool Initialized
        {
            get;
            set;
        }

        /// <summary>
        /// Receives IServiceCollection from service. R9 extensions should be
        /// already injected.
        /// </summary>
        /// <param name="externalServiceCollection">Service's IServiceCollection.</param>
        /// <param name="alwaysEnableR9">Flag to indicate if R9 is always enabled.</param>
        internal static void InitR9Services(IServiceCollection externalServiceCollection, bool alwaysEnableR9 = false)
        {
            // TODO(jiayiwang): remove alwaysEnableR9 flag and do something else for 
            // https://o365exchange.visualstudio.com/O365%20Core/_git/Root/pullrequest/2768618
            // Generally R9 should work to some degree without ECS.
            serviceProvider = externalServiceCollection.BuildServiceProvider();
            ECSManagedServiceCollection.Instance.SetService(serviceProvider);
            Initialized = true;
            AlwaysEnableR9 = alwaysEnableR9;
        }

        /// <summary>
        /// Get an ILogger, used to export Log data to designated Geneva table.
        /// </summary>
        /// <typeparam name="T">Log category. Map from category to Geneva table is defined in
        /// TableNamesMapping.</typeparam>
        /// <returns></returns>
        // TODO(jiayiwang): Decide what error to return if InitR9Services is not called.
        internal static ILogger<T> CreateLogger<T>()
        {
            if (!OperatingSystemHelper.IsLinux)
            {
                // During migration, the configs should be controlled by ECS. 
                // After migration, the configs should be controlled by local config. (IfxDisabled && R9Enabled)
                if (ECSManagedServiceCollection.Instance.ServiceProviderV2 != null && ShouldSendR9Event(typeof(T).FullName))
                {
                    return ECSManagedServiceCollection.Instance.GetLoggerV2<T>();
                }

                // SDK case:
                if (LibraryManagedServiceCollection.Instance.AcquireR9Option.GetSection($"Logging:GenevaLogging:TableNameMappings:{typeof(T).FullName}").Exists())
                {
                    SDKMeter.RecordSDKCounter("CreateLoggerFromLibraryECS", typeof(T).FullName);
                    return LibraryManagedServiceCollection.Instance.GetLogger<T>();
                }

                if (GetPassiveR9Config().IfxEventEnabled && GetPassiveR9Config().R9EventEnabled)
                {
                    return ECSManagedServiceCollection.Instance.GetLogger<T>();
                }
            }
            SDKMeter.RecordSDKCounter("CreateLoggerFromServiceECS", typeof(T).FullName);

            // Service case:
            // The service provider of ECSManagedServiceCollection is from the service at first when InitTelemetry was called in service side.
            // So we can use it directly.
            return serviceProvider.GetRequiredService<ILoggerFactory>().CreateLogger<T>();
        }

        /// <summary>
        /// Get an IMeter, used to export Metric data to designated Geneva monitoring
        /// account and namespace..
        /// </summary>
        /// <typeparam name="T">Log category. Map from category to Geneva table is defined in
        /// TableNamesMapping.</typeparam>
        /// <returns></returns>
        internal static IMeter<T> GetMeter<T>()
        {
            if (!OperatingSystemHelper.IsLinux)
            {
                // During migration, the configs should be controlled by ECS. 
                // After migration, the configs should be controlled by local config. (IfxDisabled && R9Enabled)
                if (ECSManagedServiceCollection.Instance.ServiceProviderV2 != null && ShouldSendR9Metric(typeof(T).FullName))
                {
                    return ECSManagedServiceCollection.Instance.GetMeterV2<T>();
                }

                // SDK case:
                if (LibraryManagedServiceCollection.Instance.AcquireR9Option.GetSection($"Metering:GenevaMetering:MonitoringNamespaceOverrides:{typeof(T).FullName}").Exists())
                {
                    SDKMeter.RecordSDKCounter("CreateMeterFromLibraryECS", typeof(T).FullName);
                    return LibraryManagedServiceCollection.Instance.GetMeter<T>();
                }

                if (GetPassiveR9Config().IfxMetricEnabled && GetPassiveR9Config().R9MetricEnabled)
                {
                    return ECSManagedServiceCollection.Instance.GetMeter<T>();
                }
            }
            SDKMeter.RecordSDKCounter("CreateMeterFromServiceECS", typeof(T).FullName);

            // Service case:
            // The service provider of ECSManagedServiceCollection is from the service at first when InitTelemetry was called in service side.
            // So we can use it directly.
            return serviceProvider.GetRequiredService<IMeter<T>>();
        }

        /// <summary>
        /// Check whether the R9 metric should be blocked due to sending multiple metrics to the same name.
        /// </summary>
        /// <param name="name">Metric name.</param>
        /// <param name="category">Metric category.</param>
        /// <returns>Whether the R9 metric should be blocked.</returns>
        internal static bool ShouldBlockMetric(string name, string category)
        {
            return serviceProvider.GetRequiredService<IBlockList>().ShouldBlockMetric(name, category);
        }

        /// <summary>
        /// ShouldSendR9Event
        /// </summary>
        /// <param name="eventName"></param>
        /// <returns></returns>
        public static bool ShouldSendR9Event(string eventName)
        {
            return GetTelemetryEmitConfig().QueryEventConfig<bool>(eventName, Constants.R9Enabled);
        }

        /// <summary>
        /// Judge whether should send event.
        /// </summary>
        /// <param name="eventName">Event type.</param>
        /// <returns></returns>
        public static bool ShouldSendIfxEvent(string eventName)
        {
            return !GetTelemetryEmitConfig().QueryEventConfig<bool>(eventName, Constants.IfxDisabled);
        }

        /// <summary>
        /// Judge whether should send metric.
        /// </summary>
        /// <param name="metricName">Metric type.</param>
        /// <returns></returns>
        public static bool ShouldSendR9Metric(string metricName)
        {
            return GetTelemetryEmitConfig().QueryMetricConfig<bool>(metricName, Constants.R9Enabled);
        }

        /// <summary>
        /// Judge whether should send metric.
        /// </summary>
        /// <param name="metricName">Metric type.</param>
        /// <returns></returns>
        public static bool ShouldSendIfxMetric(string metricName)
        {
            return !GetTelemetryEmitConfig().QueryMetricConfig<bool>(metricName, Constants.IfxDisabled);
        }
    }
}
