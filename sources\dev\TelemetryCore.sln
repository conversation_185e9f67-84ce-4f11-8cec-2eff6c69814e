﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.8.34408.163
MinimumVisualStudioVersion = 10.0.40219.1
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "dev", "dev", "{4E0A3C88-470E-4CCD-8193-2D4F49C29C41}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "test", "test", "{D81254CC-76BE-48CA-A3F2-EC319566978E}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "R9.Tracing.Instrumentation.Accelerator", "R9.Tracing.Instrumentation.Accelerator\R9.Tracing.Instrumentation.Accelerator.csproj", "{884A2634-8904-45A4-B59A-CE5498E5695D}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "R9.Tracing.Instrumentation.AcceleratorTests", "..\test\R9.Tracing.Instrumentation.AcceleratorTests\R9.Tracing.Instrumentation.AcceleratorTests.csproj", "{3DE48113-17FA-4774-8235-CFC636315474}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "R9TelemetryExtensionsCore", "R9TelemetryExtensionsCore", "{567D2C46-CC4B-42A7-AF2D-A75EBEEE3CA8}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ECS.DynamicComponent", "R9TelemetryExtensionsCore\ECS.DynamicComponent\ECS.DynamicComponent.csproj", "{81B9D2B8-212B-4F8E-97C3-13F2FEA261C3}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ECSClient", "R9TelemetryExtensionsCore\ECSClient\ECSClient.csproj", "{E80F52EE-6F3F-45D9-956E-96657169213A}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Enrichment", "R9TelemetryExtensionsCore\Enrichment\Enrichment.csproj", "{059C568F-B335-4F32-A705-C8189F98D678}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "R9", "R9TelemetryExtensionsCore\R9\R9.csproj", "{5B7A9961-0117-4D73-9056-B6F34F1E95FC}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "SDKLogger", "R9TelemetryExtensionsCore\SDKLogger\SDKLogger.csproj", "{D3A8B248-4EEA-4C0E-AF6C-2A37AC0CAD22}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Sample", "Sample", "{B2D0DCD8-A766-4B73-9768-C37A478E9F6E}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "R9TelemetryExtensionsCoreConsoleApp", "Sample\R9TelemetryExtensionsCoreConsoleApp\R9TelemetryExtensionsCoreConsoleApp.csproj", "{3944770A-AD16-40E3-8E78-03CA9278B545}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Microsoft.M365.Core.Telemetry.NetCoreWebApi", "Sample\R9TelemetryExtensionsNetCoreWebApi\Microsoft.M365.Core.Telemetry.NetCoreWebApi.csproj", "{E61ED45B-E955-4B2B-8AE9-E2114E43463C}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "R9TelemetryExtensionsCoreTests", "R9TelemetryExtensionsCoreTests", "{7DFD98A4-4FE1-4E85-957A-D7C7F109D638}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ECS.DynamicComponentTest", "..\test\R9TelemetryExtensionsCoreTests\ECS.DynamicComponentTest\ECS.DynamicComponentTest.csproj", "{55C7FFE2-EB66-4C13-AB91-D0C778FFB754}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ECSClientTest", "..\test\R9TelemetryExtensionsCoreTests\ECSClientTest\ECSClientTest.csproj", "{72905EB1-CD5C-46F0-A77B-A7419B52A3D3}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "EnrichmentTest", "..\test\R9TelemetryExtensionsCoreTests\EnrichmentTest\EnrichmentTest.csproj", "{CA3D9162-DC2B-4C42-AA5D-B350288CC6CF}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "R9Test", "..\test\R9TelemetryExtensionsCoreTests\R9Test\R9Test.csproj", "{34A335E1-3F06-4292-8CAA-BA63FEAA53BA}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "SDKLoggerTest", "..\test\R9TelemetryExtensionsCoreTests\SDKLoggerTest\SDKLoggerTest.csproj", "{3B4EF92E-A183-4B74-A751-6936D294BFDB}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "FileExporter", "FileExporterExtension\FileExporter.csproj", "{CA778DF7-AF98-4FC6-B34D-99BDDB2413E2}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "FileExporterTest", "..\test\FileExporterTest\FileExporterTest.csproj", "{8567CB5F-6C48-44CE-9A04-733A2AF7C1D6}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "TestCommon", "TestCommon\TestCommon.csproj", "{F612CBF7-530D-4590-802C-88B5CB40DA47}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "R9.Tracing.AdvancedSampling", "R9.Tracing.AdvancedSampling\R9.Tracing.AdvancedSampling.csproj", "{76A0340C-3900-4966-8D93-FCDE04F958D3}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "R9.Tracing.AdvancedSamplingTests", "..\test\R9.Tracing.AdvancedSamplingTests\R9.Tracing.AdvancedSamplingTests.csproj", "{2F674468-630C-4EF6-AA5D-2C2FFC4934BC}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Telemetry.Exporters.ODL", "Telemetry.Exporters.ODL\Telemetry.Exporters.ODL.csproj", "{CA5FE769-4F0F-4D5A-AA7F-5B439A48155F}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "OpenTelemetry.Exporter.Filters", "OpenTelemetry.Exporter.Filters\OpenTelemetry.Exporter.Filters.csproj", "{E368106D-D2DE-4768-B4DE-611601FAFE76}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "R9.Exporter.Filters", "R9.Exporter.Filters\R9.Exporter.Filters.csproj", "{********-DE91-4EF2-AB52-9FF69C65D811}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "R9.Exporter.FiltersTests", "..\test\R9.Exporter.FiltersTests\R9.Exporter.FiltersTests.csproj", "{487E6848-0A8D-4FC6-B0ED-6ED73E758568}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "OpenTelemtry.Exporter.FiltersTests", "..\test\OpenTelemtry.Exporter.FiltersTests\OpenTelemtry.Exporter.FiltersTests.csproj", "{6E594F05-6830-4370-8E36-E231BAE86E0A}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Telemetry.Exporters.ODLTests", "..\test\Telemetry.Exporters.ODLTests\Telemetry.Exporters.ODLTests.csproj", "{C40B3C78-EDE6-446E-9AF2-A8B23D6192F3}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Telemetry.Exporters.ODLBenchmark", "..\test\Telemetry.Exporters.ODLBenchmark\Telemetry.Exporters.ODLBenchmark.csproj", "{20CC6BC4-7D56-4AC1-93C9-975E52558FC9}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "R9.Logging.Substrate", "R9.Logging.Substrate\R9.Logging.Substrate.csproj", "{FFA54FD5-63B7-4A5D-82E7-2AD563CA0349}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "R9.Logging.SubstrateTests", "..\test\R9.Logging.SubstrateTests\R9.Logging.SubstrateTests.csproj", "{DA994D28-3F87-430E-9845-9DD3F6D7726C}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Telemetry.Exporters.ODLTcp", "Telemetry.Exporters.ODLTcp\Telemetry.Exporters.ODLTcp.csproj", "{DBE7DDCC-4996-4311-A46F-B553431AE3DA}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Microsoft.M365.ODL.NrtTcpClient", "ODLNrtTcpClient\Microsoft.M365.ODL.NrtTcpClient\Microsoft.M365.ODL.NrtTcpClient.csproj", "{C41EF9A2-F649-4464-BB66-16EA6656CC2D}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "R9.Metering.Substrate", "R9.Metering.Substrate\R9.Metering.Substrate.csproj", "{15DD5A7C-F673-4A8C-8296-807E487D5ED7}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "R9.Metering.SubstrateTest", "..\test\R9.Metering.SubstrateTest\R9.Metering.SubstrateTest.csproj", "{17042439-CAAE-4AF6-B420-46E5BEF28DCA}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "sample", "sample", "{F35795DB-B566-4A92-949A-83F95BA5116A}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "R9.Logging.Substrate", "R9.Logging.Substrate", "{906BD338-9E48-47F3-A18A-A05EDA93DAAF}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "DISampleConsoleApp", "..\sample\R9.Logging.Substrate\DISampleApp\DISampleConsoleApp.csproj", "{34FDBBBE-A582-42D0-8984-6247F3AB7A51}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "NonDISampleConsoleApp", "..\sample\R9.Logging.Substrate\NonDISampleApp\NonDISampleConsoleApp.csproj", "{CB6904F6-5A30-42AA-B30F-C8A916F37C5A}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Telemetry.Exporters.ODLTcpTests", "..\test\Telemetry.Exporters.ODLTcpTests\Telemetry.Exporters.ODLTcpTests.csproj", "{87495E7D-F708-4D40-A0C2-CCEC7D5128AE}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "LogSamplingSampleConsoleApp", "..\sample\R9.Logging.Substrate\LogSamplingSampleConsoleApp\LogSamplingSampleConsoleApp.csproj", "{DC712BF4-8910-05F8-8C64-2D1B3A945BED}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{884A2634-8904-45A4-B59A-CE5498E5695D}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{884A2634-8904-45A4-B59A-CE5498E5695D}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{884A2634-8904-45A4-B59A-CE5498E5695D}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{884A2634-8904-45A4-B59A-CE5498E5695D}.Release|Any CPU.Build.0 = Release|Any CPU
		{3DE48113-17FA-4774-8235-CFC636315474}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{3DE48113-17FA-4774-8235-CFC636315474}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{3DE48113-17FA-4774-8235-CFC636315474}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{3DE48113-17FA-4774-8235-CFC636315474}.Release|Any CPU.Build.0 = Release|Any CPU
		{81B9D2B8-212B-4F8E-97C3-13F2FEA261C3}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{81B9D2B8-212B-4F8E-97C3-13F2FEA261C3}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{81B9D2B8-212B-4F8E-97C3-13F2FEA261C3}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{81B9D2B8-212B-4F8E-97C3-13F2FEA261C3}.Release|Any CPU.Build.0 = Release|Any CPU
		{E80F52EE-6F3F-45D9-956E-96657169213A}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{E80F52EE-6F3F-45D9-956E-96657169213A}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{E80F52EE-6F3F-45D9-956E-96657169213A}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{E80F52EE-6F3F-45D9-956E-96657169213A}.Release|Any CPU.Build.0 = Release|Any CPU
		{059C568F-B335-4F32-A705-C8189F98D678}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{059C568F-B335-4F32-A705-C8189F98D678}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{059C568F-B335-4F32-A705-C8189F98D678}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{059C568F-B335-4F32-A705-C8189F98D678}.Release|Any CPU.Build.0 = Release|Any CPU
		{5B7A9961-0117-4D73-9056-B6F34F1E95FC}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{5B7A9961-0117-4D73-9056-B6F34F1E95FC}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{5B7A9961-0117-4D73-9056-B6F34F1E95FC}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{5B7A9961-0117-4D73-9056-B6F34F1E95FC}.Release|Any CPU.Build.0 = Release|Any CPU
		{D3A8B248-4EEA-4C0E-AF6C-2A37AC0CAD22}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{D3A8B248-4EEA-4C0E-AF6C-2A37AC0CAD22}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{D3A8B248-4EEA-4C0E-AF6C-2A37AC0CAD22}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{D3A8B248-4EEA-4C0E-AF6C-2A37AC0CAD22}.Release|Any CPU.Build.0 = Release|Any CPU
		{3944770A-AD16-40E3-8E78-03CA9278B545}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{3944770A-AD16-40E3-8E78-03CA9278B545}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{3944770A-AD16-40E3-8E78-03CA9278B545}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{3944770A-AD16-40E3-8E78-03CA9278B545}.Release|Any CPU.Build.0 = Release|Any CPU
		{E61ED45B-E955-4B2B-8AE9-E2114E43463C}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{E61ED45B-E955-4B2B-8AE9-E2114E43463C}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{E61ED45B-E955-4B2B-8AE9-E2114E43463C}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{E61ED45B-E955-4B2B-8AE9-E2114E43463C}.Release|Any CPU.Build.0 = Release|Any CPU
		{55C7FFE2-EB66-4C13-AB91-D0C778FFB754}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{55C7FFE2-EB66-4C13-AB91-D0C778FFB754}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{55C7FFE2-EB66-4C13-AB91-D0C778FFB754}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{55C7FFE2-EB66-4C13-AB91-D0C778FFB754}.Release|Any CPU.Build.0 = Release|Any CPU
		{72905EB1-CD5C-46F0-A77B-A7419B52A3D3}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{72905EB1-CD5C-46F0-A77B-A7419B52A3D3}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{72905EB1-CD5C-46F0-A77B-A7419B52A3D3}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{72905EB1-CD5C-46F0-A77B-A7419B52A3D3}.Release|Any CPU.Build.0 = Release|Any CPU
		{CA3D9162-DC2B-4C42-AA5D-B350288CC6CF}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{CA3D9162-DC2B-4C42-AA5D-B350288CC6CF}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{CA3D9162-DC2B-4C42-AA5D-B350288CC6CF}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{CA3D9162-DC2B-4C42-AA5D-B350288CC6CF}.Release|Any CPU.Build.0 = Release|Any CPU
		{34A335E1-3F06-4292-8CAA-BA63FEAA53BA}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{34A335E1-3F06-4292-8CAA-BA63FEAA53BA}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{34A335E1-3F06-4292-8CAA-BA63FEAA53BA}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{34A335E1-3F06-4292-8CAA-BA63FEAA53BA}.Release|Any CPU.Build.0 = Release|Any CPU
		{3B4EF92E-A183-4B74-A751-6936D294BFDB}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{3B4EF92E-A183-4B74-A751-6936D294BFDB}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{3B4EF92E-A183-4B74-A751-6936D294BFDB}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{3B4EF92E-A183-4B74-A751-6936D294BFDB}.Release|Any CPU.Build.0 = Release|Any CPU
		{CA778DF7-AF98-4FC6-B34D-99BDDB2413E2}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{CA778DF7-AF98-4FC6-B34D-99BDDB2413E2}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{CA778DF7-AF98-4FC6-B34D-99BDDB2413E2}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{CA778DF7-AF98-4FC6-B34D-99BDDB2413E2}.Release|Any CPU.Build.0 = Release|Any CPU
		{8567CB5F-6C48-44CE-9A04-733A2AF7C1D6}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{8567CB5F-6C48-44CE-9A04-733A2AF7C1D6}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{8567CB5F-6C48-44CE-9A04-733A2AF7C1D6}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{8567CB5F-6C48-44CE-9A04-733A2AF7C1D6}.Release|Any CPU.Build.0 = Release|Any CPU
		{F612CBF7-530D-4590-802C-88B5CB40DA47}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{F612CBF7-530D-4590-802C-88B5CB40DA47}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{F612CBF7-530D-4590-802C-88B5CB40DA47}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{F612CBF7-530D-4590-802C-88B5CB40DA47}.Release|Any CPU.Build.0 = Release|Any CPU
		{76A0340C-3900-4966-8D93-FCDE04F958D3}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{76A0340C-3900-4966-8D93-FCDE04F958D3}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{76A0340C-3900-4966-8D93-FCDE04F958D3}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{76A0340C-3900-4966-8D93-FCDE04F958D3}.Release|Any CPU.Build.0 = Release|Any CPU
		{2F674468-630C-4EF6-AA5D-2C2FFC4934BC}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{2F674468-630C-4EF6-AA5D-2C2FFC4934BC}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{2F674468-630C-4EF6-AA5D-2C2FFC4934BC}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{2F674468-630C-4EF6-AA5D-2C2FFC4934BC}.Release|Any CPU.Build.0 = Release|Any CPU
		{CA5FE769-4F0F-4D5A-AA7F-5B439A48155F}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{CA5FE769-4F0F-4D5A-AA7F-5B439A48155F}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{CA5FE769-4F0F-4D5A-AA7F-5B439A48155F}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{CA5FE769-4F0F-4D5A-AA7F-5B439A48155F}.Release|Any CPU.Build.0 = Release|Any CPU
		{E368106D-D2DE-4768-B4DE-611601FAFE76}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{E368106D-D2DE-4768-B4DE-611601FAFE76}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{E368106D-D2DE-4768-B4DE-611601FAFE76}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{E368106D-D2DE-4768-B4DE-611601FAFE76}.Release|Any CPU.Build.0 = Release|Any CPU
		{********-DE91-4EF2-AB52-9FF69C65D811}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{********-DE91-4EF2-AB52-9FF69C65D811}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{********-DE91-4EF2-AB52-9FF69C65D811}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{********-DE91-4EF2-AB52-9FF69C65D811}.Release|Any CPU.Build.0 = Release|Any CPU
		{487E6848-0A8D-4FC6-B0ED-6ED73E758568}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{487E6848-0A8D-4FC6-B0ED-6ED73E758568}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{487E6848-0A8D-4FC6-B0ED-6ED73E758568}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{487E6848-0A8D-4FC6-B0ED-6ED73E758568}.Release|Any CPU.Build.0 = Release|Any CPU
		{6E594F05-6830-4370-8E36-E231BAE86E0A}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{6E594F05-6830-4370-8E36-E231BAE86E0A}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{6E594F05-6830-4370-8E36-E231BAE86E0A}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{6E594F05-6830-4370-8E36-E231BAE86E0A}.Release|Any CPU.Build.0 = Release|Any CPU
		{C40B3C78-EDE6-446E-9AF2-A8B23D6192F3}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{C40B3C78-EDE6-446E-9AF2-A8B23D6192F3}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{C40B3C78-EDE6-446E-9AF2-A8B23D6192F3}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{C40B3C78-EDE6-446E-9AF2-A8B23D6192F3}.Release|Any CPU.Build.0 = Release|Any CPU
		{20CC6BC4-7D56-4AC1-93C9-975E52558FC9}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{20CC6BC4-7D56-4AC1-93C9-975E52558FC9}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{20CC6BC4-7D56-4AC1-93C9-975E52558FC9}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{20CC6BC4-7D56-4AC1-93C9-975E52558FC9}.Release|Any CPU.Build.0 = Release|Any CPU
		{FFA54FD5-63B7-4A5D-82E7-2AD563CA0349}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{FFA54FD5-63B7-4A5D-82E7-2AD563CA0349}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{FFA54FD5-63B7-4A5D-82E7-2AD563CA0349}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{FFA54FD5-63B7-4A5D-82E7-2AD563CA0349}.Release|Any CPU.Build.0 = Release|Any CPU
		{DA994D28-3F87-430E-9845-9DD3F6D7726C}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{DA994D28-3F87-430E-9845-9DD3F6D7726C}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{DA994D28-3F87-430E-9845-9DD3F6D7726C}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{DA994D28-3F87-430E-9845-9DD3F6D7726C}.Release|Any CPU.Build.0 = Release|Any CPU
		{DBE7DDCC-4996-4311-A46F-B553431AE3DA}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{DBE7DDCC-4996-4311-A46F-B553431AE3DA}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{DBE7DDCC-4996-4311-A46F-B553431AE3DA}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{DBE7DDCC-4996-4311-A46F-B553431AE3DA}.Release|Any CPU.Build.0 = Release|Any CPU
		{C41EF9A2-F649-4464-BB66-16EA6656CC2D}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{C41EF9A2-F649-4464-BB66-16EA6656CC2D}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{C41EF9A2-F649-4464-BB66-16EA6656CC2D}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{C41EF9A2-F649-4464-BB66-16EA6656CC2D}.Release|Any CPU.Build.0 = Release|Any CPU
		{15DD5A7C-F673-4A8C-8296-807E487D5ED7}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{15DD5A7C-F673-4A8C-8296-807E487D5ED7}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{15DD5A7C-F673-4A8C-8296-807E487D5ED7}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{15DD5A7C-F673-4A8C-8296-807E487D5ED7}.Release|Any CPU.Build.0 = Release|Any CPU
		{17042439-CAAE-4AF6-B420-46E5BEF28DCA}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{17042439-CAAE-4AF6-B420-46E5BEF28DCA}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{17042439-CAAE-4AF6-B420-46E5BEF28DCA}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{17042439-CAAE-4AF6-B420-46E5BEF28DCA}.Release|Any CPU.Build.0 = Release|Any CPU
		{34FDBBBE-A582-42D0-8984-6247F3AB7A51}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{34FDBBBE-A582-42D0-8984-6247F3AB7A51}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{34FDBBBE-A582-42D0-8984-6247F3AB7A51}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{34FDBBBE-A582-42D0-8984-6247F3AB7A51}.Release|Any CPU.Build.0 = Release|Any CPU
		{CB6904F6-5A30-42AA-B30F-C8A916F37C5A}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{CB6904F6-5A30-42AA-B30F-C8A916F37C5A}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{CB6904F6-5A30-42AA-B30F-C8A916F37C5A}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{CB6904F6-5A30-42AA-B30F-C8A916F37C5A}.Release|Any CPU.Build.0 = Release|Any CPU
		{87495E7D-F708-4D40-A0C2-CCEC7D5128AE}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{87495E7D-F708-4D40-A0C2-CCEC7D5128AE}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{87495E7D-F708-4D40-A0C2-CCEC7D5128AE}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{87495E7D-F708-4D40-A0C2-CCEC7D5128AE}.Release|Any CPU.Build.0 = Release|Any CPU
		{DC712BF4-8910-05F8-8C64-2D1B3A945BED}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{DC712BF4-8910-05F8-8C64-2D1B3A945BED}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{DC712BF4-8910-05F8-8C64-2D1B3A945BED}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{DC712BF4-8910-05F8-8C64-2D1B3A945BED}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{884A2634-8904-45A4-B59A-CE5498E5695D} = {4E0A3C88-470E-4CCD-8193-2D4F49C29C41}
		{3DE48113-17FA-4774-8235-CFC636315474} = {D81254CC-76BE-48CA-A3F2-EC319566978E}
		{567D2C46-CC4B-42A7-AF2D-A75EBEEE3CA8} = {4E0A3C88-470E-4CCD-8193-2D4F49C29C41}
		{81B9D2B8-212B-4F8E-97C3-13F2FEA261C3} = {567D2C46-CC4B-42A7-AF2D-A75EBEEE3CA8}
		{E80F52EE-6F3F-45D9-956E-96657169213A} = {567D2C46-CC4B-42A7-AF2D-A75EBEEE3CA8}
		{059C568F-B335-4F32-A705-C8189F98D678} = {567D2C46-CC4B-42A7-AF2D-A75EBEEE3CA8}
		{5B7A9961-0117-4D73-9056-B6F34F1E95FC} = {567D2C46-CC4B-42A7-AF2D-A75EBEEE3CA8}
		{D3A8B248-4EEA-4C0E-AF6C-2A37AC0CAD22} = {567D2C46-CC4B-42A7-AF2D-A75EBEEE3CA8}
		{B2D0DCD8-A766-4B73-9768-C37A478E9F6E} = {4E0A3C88-470E-4CCD-8193-2D4F49C29C41}
		{3944770A-AD16-40E3-8E78-03CA9278B545} = {B2D0DCD8-A766-4B73-9768-C37A478E9F6E}
		{E61ED45B-E955-4B2B-8AE9-E2114E43463C} = {B2D0DCD8-A766-4B73-9768-C37A478E9F6E}
		{7DFD98A4-4FE1-4E85-957A-D7C7F109D638} = {D81254CC-76BE-48CA-A3F2-EC319566978E}
		{55C7FFE2-EB66-4C13-AB91-D0C778FFB754} = {7DFD98A4-4FE1-4E85-957A-D7C7F109D638}
		{72905EB1-CD5C-46F0-A77B-A7419B52A3D3} = {7DFD98A4-4FE1-4E85-957A-D7C7F109D638}
		{CA3D9162-DC2B-4C42-AA5D-B350288CC6CF} = {7DFD98A4-4FE1-4E85-957A-D7C7F109D638}
		{34A335E1-3F06-4292-8CAA-BA63FEAA53BA} = {7DFD98A4-4FE1-4E85-957A-D7C7F109D638}
		{3B4EF92E-A183-4B74-A751-6936D294BFDB} = {7DFD98A4-4FE1-4E85-957A-D7C7F109D638}
		{CA778DF7-AF98-4FC6-B34D-99BDDB2413E2} = {4E0A3C88-470E-4CCD-8193-2D4F49C29C41}
		{8567CB5F-6C48-44CE-9A04-733A2AF7C1D6} = {D81254CC-76BE-48CA-A3F2-EC319566978E}
		{F612CBF7-530D-4590-802C-88B5CB40DA47} = {D81254CC-76BE-48CA-A3F2-EC319566978E}
		{76A0340C-3900-4966-8D93-FCDE04F958D3} = {4E0A3C88-470E-4CCD-8193-2D4F49C29C41}
		{2F674468-630C-4EF6-AA5D-2C2FFC4934BC} = {D81254CC-76BE-48CA-A3F2-EC319566978E}
		{CA5FE769-4F0F-4D5A-AA7F-5B439A48155F} = {4E0A3C88-470E-4CCD-8193-2D4F49C29C41}
		{E368106D-D2DE-4768-B4DE-611601FAFE76} = {4E0A3C88-470E-4CCD-8193-2D4F49C29C41}
		{********-DE91-4EF2-AB52-9FF69C65D811} = {4E0A3C88-470E-4CCD-8193-2D4F49C29C41}
		{487E6848-0A8D-4FC6-B0ED-6ED73E758568} = {D81254CC-76BE-48CA-A3F2-EC319566978E}
		{6E594F05-6830-4370-8E36-E231BAE86E0A} = {D81254CC-76BE-48CA-A3F2-EC319566978E}
		{C40B3C78-EDE6-446E-9AF2-A8B23D6192F3} = {D81254CC-76BE-48CA-A3F2-EC319566978E}
		{20CC6BC4-7D56-4AC1-93C9-975E52558FC9} = {D81254CC-76BE-48CA-A3F2-EC319566978E}
		{FFA54FD5-63B7-4A5D-82E7-2AD563CA0349} = {4E0A3C88-470E-4CCD-8193-2D4F49C29C41}
		{DA994D28-3F87-430E-9845-9DD3F6D7726C} = {D81254CC-76BE-48CA-A3F2-EC319566978E}
		{DBE7DDCC-4996-4311-A46F-B553431AE3DA} = {4E0A3C88-470E-4CCD-8193-2D4F49C29C41}
		{C41EF9A2-F649-4464-BB66-16EA6656CC2D} = {4E0A3C88-470E-4CCD-8193-2D4F49C29C41}
		{15DD5A7C-F673-4A8C-8296-807E487D5ED7} = {4E0A3C88-470E-4CCD-8193-2D4F49C29C41}
		{17042439-CAAE-4AF6-B420-46E5BEF28DCA} = {D81254CC-76BE-48CA-A3F2-EC319566978E}
		{906BD338-9E48-47F3-A18A-A05EDA93DAAF} = {F35795DB-B566-4A92-949A-83F95BA5116A}
		{34FDBBBE-A582-42D0-8984-6247F3AB7A51} = {906BD338-9E48-47F3-A18A-A05EDA93DAAF}
		{CB6904F6-5A30-42AA-B30F-C8A916F37C5A} = {906BD338-9E48-47F3-A18A-A05EDA93DAAF}
		{DC712BF4-8910-05F8-8C64-2D1B3A945BED} = {906BD338-9E48-47F3-A18A-A05EDA93DAAF}
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {BA2BC4D5-891E-4A08-8891-52BD49FC3B37}
	EndGlobalSection
EndGlobal
