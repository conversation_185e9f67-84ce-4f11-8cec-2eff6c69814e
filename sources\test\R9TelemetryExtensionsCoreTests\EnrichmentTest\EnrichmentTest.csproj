<Project Sdk="Microsoft.NET.Sdk">
    <PropertyGroup>
        <TargetFrameworks>net472;net6.0;net8.0</TargetFrameworks>
        <DocumentationFile>bin\$(Configuration)\$(TargetFramework)\$(AssemblyName).xml</DocumentationFile>
        <Authors>SOTELS</Authors>
        <RootNamespace>Microsoft.M365.Core.Telemetry.EnrichmentTest</RootNamespace>
        <AssemblyName>Microsoft.M365.Core.Telemetry.EnrichmentTest</AssemblyName>
        <IsCodedUITest>False</IsCodedUITest>
        <TestProjectType>UnitTest</TestProjectType>
        <PlatformTarget>anycpu</PlatformTarget>
        <LangVersion>9</LangVersion>
        <SuppressTfmSupportBuildWarnings>true</SuppressTfmSupportBuildWarnings>
    </PropertyGroup>
    <ItemGroup>
        <ProjectReference Include="..\..\..\dev\R9TelemetryExtensionsCore\ECSClient\ECSClient.csproj" />
        <ProjectReference Include="..\..\..\dev\R9TelemetryExtensionsCore\Enrichment\Enrichment.csproj" />
        <ProjectReference Include="..\..\..\dev\TestCommon\TestCommon.csproj" />
    </ItemGroup>
    <ItemGroup>
        <PackageReference Include="FluentAssertions" />
        <PackageReference Include="Microsoft.NET.Test.Sdk" />
        <PackageReference Include="Microsoft.R9.Extensions.Essentials" />
        <PackageReference Include="xunit.runner.visualstudio" />
        <PackageReference Include="xunit" />
    </ItemGroup>
</Project>