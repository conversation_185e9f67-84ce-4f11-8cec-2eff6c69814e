// <copyright file="DefaultStringFormatter.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

namespace Microsoft.M365.Core.Telemetry.FileExporter
{
    /// <summary>
    /// The default formatter for <see cref="string"/>.
    /// </summary>
    internal static class DefaultStringFormatter
    {
        /// <summary>
        /// Format ODL NRT log.
        /// </summary>
        /// <param name="str"><see cref="string"/>.</param>
        internal static string FormatString(string str)
        {
            return str;
        }
    }
}
