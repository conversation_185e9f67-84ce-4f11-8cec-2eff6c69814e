﻿// <copyright file="InternalSDKR9Services.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.Diagnostics.CodeAnalysis;
using System.Runtime.InteropServices;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.R9.Extensions.Logging;
using Microsoft.R9.Extensions.Logging.Exporters;
using Microsoft.R9.Extensions.Metering;

namespace Microsoft.M365.Core.Telemetry.SDKLogger
{
    /// <summary>
    /// Internal R9 objects. Noop in net framework.
    /// </summary>
    [ExcludeFromCodeCoverage]
    internal static class InternalSDKR9Services
    {
        /// <summary>
        /// BypassSDKLogger
        /// </summary>
        internal static bool BypassSDKLogger { get; private set; } = false;

        private static IServiceCollection serviceCollection = CreateServiceCollection();

        private static IServiceProvider serviceProvider = serviceCollection.BuildServiceProvider();

        private static ServiceCollection CreateServiceCollection()
        {
            ServiceCollection services = new ServiceCollection();
            services.AddGenevaMetering(options =>
            {
                options.Protocol = RuntimeInformation.IsOSPlatform(OSPlatform.Linux) ? TransportProtocol.Tcp : TransportProtocol.Etw;
                options.MonitoringAccount = "O365_Account";
                options.MonitoringNamespace = "R9DebugMetric";
            });

            // ConsoleExporter introduces Microsoft.Extensions.DependencyInjection 6.0.0. Will add back after version upgrade.
            // if ("Console".Equals(Environment.GetEnvironmentVariable("Microsoft_M365_Core_Telemetry_SDKLogger_Exporter"), StringComparison.OrdinalIgnoreCase))
            // {
            //     services.AddLogging(builder => builder
            //                .AddOpenTelemetryLogging()
            //                .AddConsoleExporter());
            // }
            // else
            // {
            services.AddLogging(builder => builder
            .AddOpenTelemetryLogging()
            .AddGenevaExporter(genevaOptions =>
            {
                genevaOptions.ConnectionString = RuntimeInformation.IsOSPlatform(OSPlatform.Linux) ?
                    "Endpoint=unix:/var/run/mdsd/default_fluent.socket" :
                    "EtwSession=o365PassiveMonitoringSessionR9";
                genevaOptions.TableNameMappings = new Dictionary<string, string>
                {
                    ["*"] = "M365TelemetrySDKLog",
                };
            }));

            Enrichment.CommonInit.InitSubstrateEnrichers(services);
            return services;
        }

        /// <summary>
        /// ResetInUnitTest
        /// </summary>
        internal static void ResetInUnitTest()
        {
            serviceCollection = CreateServiceCollection();
            serviceProvider = serviceCollection.BuildServiceProvider();
        }

        /// <summary>
        /// Get an ILogger, used to export Log data to designated Geneva table.
        /// </summary>
        /// <typeparam name="T">Log category. Map from category to Geneva table is defined in
        /// TableNamesMapping.</typeparam>
        /// <returns></returns>
        public static ILogger<T> CreateLogger<T>()
        {
            return serviceProvider.GetRequiredService<ILoggerFactory>().CreateLogger<T>();
        }

        /// <summary>
        /// Get an IMeter, used to export Metric data to designated Geneva monitoring
        /// account and namespace.
        /// </summary>
        /// <typeparam name="T">Meter category. Map from category to monitoring account
        /// and namespace are defined in MonitoringAccountOverrides and
        /// MonitoringNamespaceOverrides.</typeparam>
        /// <returns></returns>
        public static IMeter<T> GetMeter<T>()
        {
            return serviceProvider.GetRequiredService<IMeter<T>>();
        }

        /// <summary>
        /// EnableSDKLogger
        /// </summary>
        [Obsolete("Enable by default.")]
        public static void EnableSDKLogger()
        {
        }

        /// <summary>
        /// DisableSDKLogger
        /// </summary>
        public static void DisableSDKLogger()
        {
            BypassSDKLogger = true;
        }
    }
}
