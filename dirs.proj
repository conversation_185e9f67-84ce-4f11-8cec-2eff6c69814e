﻿<Project Sdk="Microsoft.Build.Traversal">
  <ItemGroup>
    <ProjectFile Include="sources\dirs.proj" Condition="Exists('sources\')" />
    <ProjectFile Include="sources\PartnerInternal\**\*.*proj" Condition="Exists('sources\PartnerInternal')" />
  </ItemGroup>

  

  <!-- Trick to add versiongen.proj as a projectfile item only if restore has not run on it.  This allows setting RestoreUseStaticGraphEvaluation=true for faster restores. Because version project should very rarely if ever change on local build a git clean would need to be done to trigger another restore of this project. -->
  <ItemGroup>
    <ProjectFile Include="build/versiongen/versiongen.proj" Condition="!Exists('build/versiongen/obj')"/>
  </ItemGroup>
  <Target Name="RunGenerateVersionProject" AfterTargets="Restore">
      <Message Text = "Building version generation project" />
      <!-- Adding a global property to ensure no pre-restore MSBuild evaluation is reused -->
      <MSBuild Projects="build/versiongen/versiongen.proj" Properties="VersionGenSessionId=$([System.Guid]::NewGuid());ImportProjectExtensionProps=true" />
      <MakeDir Directories="build/versiongen/obj" />
  </Target>
</Project>