﻿// <copyright file="SearchCodeSkill.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Text;
using System.Text.Json.Serialization;
using System.Threading.Tasks;
using System.Xml;
using System.Xml.Linq;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using R9MigrationHelper.Model;

namespace R9MigrationHelper.Skills.GitSkills
{
    /// <summary>
    /// SearchCodeSkill.
    /// </summary>
    public class SearchCodeSkill
    {
        private const string PAT = "PAT";

        private Filter Filter { get; set; }
        
        private string organization = "O365Exchange";
        private string project = "O365 Core";
        private List<string> repo = new ();
        private List<string> branch = new ();
        private List<string> rootPath = new ();

        /// <summary>
        /// Search Context Organization
        /// </summary>
        public string Organization 
        {
            get { return organization; }
            set { organization = value; } 
        }

        /// <summary>
        /// Search Context Project
        /// Consistent with portal, only one project is allowed to be searched here
        /// Value must be real name rather than id here, e.g. "O365 Core"
        /// </summary>
        public string Project 
        {
            get { return project; }
            set { project = value; }  
        }

        /// <summary>
        /// Search Context Repo
        /// Value must be real name rather than id here, e.g. "Substrate"
        /// Consistent with portal, if multiple repositories are selected, Branch and Path filters will be disabled
        /// </summary>
        public List<string> Repo 
        {
            get { return repo; }

            set 
            {
                if (value.Count == 0)
                {
                    throw new Exception("Repo cannot be null in search text.");
                }
                else if (value.Count == 1)
                {
                    repo = value.ToList();
                }
                else
                {
                    Branch = new List<string>();
                    RootPath = new List<string>();
                    Filter.Branch = Branch;
                    Filter.Path = RootPath;
                }
            } 
        } 

        /// <summary>
        /// Search Context Branch
        /// </summary>
        public List<string> Branch 
        {
            get { return branch; }

            set
            {
                if (repo.Count > 1) // count will not be 0 here because repo is initialized before branch and fileInfo filter
                {
                    throw new Exception("Cannot set branch filter since repositories are more than 1");
                }    
                branch = value;
                Filter.Branch = branch;
            }
        }

        /// <summary>
        /// Root path filter
        /// </summary>
        public List<string> RootPath
        {
            get { return rootPath; }

            set
            {
                if (repo.Count > 1) // count will not be 0 here because repo is initialized before branch and fileInfo filter
                {
                    throw new Exception("Cannot set path filter since repositories are more than 1");
                }
                rootPath = value;
                Filter.Path = rootPath;
            }
        } 

        /// <summary>
        /// Public constructor
        /// </summary>
        /// <param name="organization"></param>
        /// <param name="project"></param>
        /// <param name="repo"></param>
        /// <param name="rootPath"></param>
        /// <param name="branch"></param>
        public SearchCodeSkill(string organization, string project, List<string> repo, List<string> branch, List<string> rootPath) 
        {
            // initialize in order, change the order will cause NPE
            Filter = new Filter(new List<string>() { project }, repo, branch, rootPath);
            Organization = organization;
            Project = project;
            Repo = repo.ToList();
            Branch = branch.ToList();
            RootPath = rootPath;
        }

        private async Task<string> SearchCodeAsync(string searchText, int top = 1000, int skip = 0, List<Dictionary<string, string>>? orderBy = null)
        {
            try
            {
                using var client = GetHttpClient();
                var requestBody = new RequestBody(searchText, Filter, top, skip, orderBy);
                var httpContent = new StringContent(JsonConvert.SerializeObject(requestBody), Encoding.UTF8, "application/json");
                var response = await client.PostAsync("https://almsearch.dev.azure.com/O365Exchange/_apis/search/codesearchresults?api-version=7.2-preview.1", httpContent).ConfigureAwait(false);
                var message = await response.Content.ReadAsStringAsync().ConfigureAwait(false);
                return message;
            }
            catch (Exception ex)
            {
                Console.WriteLine(ex.ToString());
            }
            return string.Empty;
        }

        /// <summary>
        /// Parse csproj files
        /// </summary>
        /// <param name="searchText"></param>
        /// <param name="top"></param>
        /// <param name="skip"></param>
        /// <param name="orderBy"></param>
        /// <returns></returns>
        public async Task<List<CsprojForDrop>> ParseCsprojFiles(string searchText, int top = 1000, int skip = 0, List<Dictionary<string, string>>? orderBy = null)
        {
            List<CsprojForDrop> ret = new ();
            var dlls = new List<string>() { "Microsoft.Exchange.Data.Directory", "Microsoft.Exchange.Data.StoreObjects", "Microsoft.Exchange.OAuth.UserMode" };
            var pathToContent = await GetFileContentsAsync(searchText, top, skip, orderBy).ConfigureAwait(true);
            foreach (var (path, content) in pathToContent)
            {
#pragma warning disable CA1307 // Specify StringComparison
                var needEncoding = content.StartsWith("<?xml version=\"1.0\" encoding=\"utf-8\"?>");
#pragma warning restore CA1307 // Specify StringComparison
                var xml = XDocument.Parse(content);
                ret.Add(new CsprojForDrop(xml, path, dlls, needEncoding));
            }
            return ret;
        }

        private HttpClient GetHttpClient()
        {
            HttpClient client = new HttpClient();
            client.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));
#pragma warning disable CA1305 // Specify IFormatProvider
            client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Basic", Convert.ToBase64String(Encoding.ASCII.GetBytes(string.Format("{0}:{1}", string.Empty, PAT))));
#pragma warning restore CA1305 // Specify IFormatProvider
            return client;
        }

        /// <summary>
        /// Get file paths satisfied by search text
        /// </summary>
        /// <param name="searchText"></param>
        /// <param name="top"></param>
        /// <param name="skip"></param>
        /// <param name="orderBy"></param>
        /// <returns></returns>
        public async Task<List<FileInfo>> GetFileInfoListAsync(string searchText, int top = 1000, int skip = 0, List<Dictionary<string, string>>? orderBy = null)
        {
            var response = await SearchCodeAsync(searchText).ConfigureAwait(false);
            var json = JObject.Parse(response);
#pragma warning disable CA1305 // Specify IFormatProvider
            var count = int.Parse(json["count"] !.ToString());
#pragma warning restore CA1305 // Specify IFormatProvider
            var ret = new List<FileInfo>();
            while (count > 0)
            {
                var currentTop = count > top ? top : count;
                response = await SearchCodeAsync(searchText, top: currentTop, skip: skip, orderBy: orderBy).ConfigureAwait(false);
                json = JObject.Parse(response);
                var res = json["results"];
                foreach (var file in res!)
                {
                    var fileName = $"{file["fileName"]}";
                    var path = $"{file["path"]}";
                    var project = $"{file["project"]}";
                    var projectName = $"{file["project"] !["name"]}";
                    var projectId = $"{file["project"] !["id"]}";
                    var repoName = $"{file["repository"] !["name"]}";
                    var repoId = $"{file["repository"] !["id"]}";
                    ret.Add(new FileInfo(fileName, path, projectName, projectId, repoName, repoId));
                }
                skip += top;
                count -= top;
            }
            return ret;
        }

        /// <summary>
        /// Get file contents
        /// </summary>
        /// <param name="searchText"></param>
        /// <param name="top"></param>
        /// <param name="skip"></param>
        /// <param name="orderBy"></param>
        /// <returns></returns>
        public async Task<List<Tuple<string, string>>> GetFileContentsAsync(string searchText, int top = 1000, int skip = 0, List<Dictionary<string, string>>? orderBy = null)
        {
            List<Tuple<string, string>> ret = new ();
            try
            {
                using var client = GetHttpClient();
                client.DefaultRequestHeaders.Accept.Add(
                new MediaTypeWithQualityHeaderValue("application/json"));
#pragma warning disable CA1305 // Specify IFormatProvider
                client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Basic", Convert.ToBase64String(Encoding.ASCII.GetBytes(string.Format("{0}:{1}", String.Empty, PAT))));
#pragma warning restore CA1305 // Specify IFormatProvider
                var fileInfoList = await GetFileInfoListAsync(searchText, top, skip, orderBy).ConfigureAwait(false);
                List<Tuple<Task<HttpResponseMessage>, string>> readFileTasks = new ();
                foreach (var fileInfo in fileInfoList)
                {
                    var project = fileInfo.ProjectName;
                    var repo = fileInfo.RepositoryName;
                    var path = fileInfo.Path;
                    var url = $"https://dev.azure.com/{Organization}/{project}/_apis/sourceProviders/tfsGit/filecontents?repository={repo}&commitOrBranch=master&path={path}&api-version=7.2-preview.1";
                    readFileTasks.Add(new (client.GetAsync(url), path));
                }
                var allFiles = Task.WhenAll(readFileTasks.Select(f => f.Item1)).Result;
                foreach (var file in readFileTasks)
                {
                    var respsonse = file.Item1.Result;
                    var path = file.Item2;
                    respsonse.EnsureSuccessStatusCode();
                    var content = await respsonse.Content.ReadAsStringAsync().ConfigureAwait(false);
                    ret.Add(Tuple.Create(path, content));
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine(ex.ToString());
            }
            return ret;
        }
    }

    /// <summary>
    /// Requset body required by code search api
    /// </summary>
    internal class RequestBody
    {
        /// <summary>
        /// Search Text
        /// </summary>
        [JsonPropertyName("searchText")]
        public string SearchText { get; set; }

        /// <summary>
        /// Filter
        /// </summary>
        [JsonPropertyName("filters")]
        public Filter Filters { get; set; }

        /// <summary>
        /// Number of results to return, limit 1000
        /// </summary>
        [JsonProperty(PropertyName = "$top")]
        public int Top { get; set; }

        /// <summary>
        /// Number of results to skip, limit 5000
        /// </summary>
        [JsonProperty(PropertyName = "$skip")]
        public int Skip { get; set; }

        /// <summary>
        /// Order by
        /// Valid value = {"ASC", "DESC"}
        /// </summary>
        [JsonProperty(PropertyName = "$orderBy")]
        public List<Dictionary<string, string>>? OrderBy { get; set; }

        /// <summary>
        /// Public constructor for user input
        /// </summary>
        /// <param name="searchText"></param>
        /// <param name="filter"></param>
        /// <param name="top"></param>
        /// <param name="skip"></param>
        /// <param name="orderBy"></param>
        public RequestBody(string searchText, Filter filter, int top, int skip, List<Dictionary<string, string>>? orderBy)
        {
            SearchText = searchText;
            Filters = filter;
            Top = top;
            Skip = skip;
            OrderBy = orderBy;
        }
    }

    /// <summary>
    /// Filter
    /// </summary>
    internal class Filter
    {
        /// <summary>
        /// Project
        /// </summary>
        public List<string> Project { get; set; }

        /// <summary>
        /// Repository
        /// </summary>
        public List<string> Repository { get; set; }

        /// <summary>
        /// Path
        /// </summary>
        public List<string> Path { get; set; }

        /// <summary>
        /// Branch
        /// </summary>
        public List<string> Branch { get; set; }

        // We don't use CodeElement, it is designed for single word in serach text
        // Instead, write code element in search text directly, e.g. "ext:csproj ..."
        // public List<string> CodeElement { get; set; }

        /// <summary>
        /// Constructor with user input
        /// </summary>
        /// <param name="project"></param>
        /// <param name="repository"></param>
        /// <param name="path"></param>
        /// <param name="branch"></param>
        public Filter(List<string> project, List<string> repository, List<string> branch, List<string> path)
        {
            Project = project;
            Repository = repository;
            Branch = branch;
            Path = path;
        }
    }

    /// <summary>
    /// File info returned by code search result
    /// </summary>
    public class FileInfo
    {
        /// <summary>
        /// File name
        /// </summary>
        public string FileName { get; set; }
        
        /// <summary>
        /// Path
        /// </summary>
        public string Path { get; set; }

        /// <summary>
        /// Project Name
        /// </summary>
        public string ProjectName { get; set; }

        /// <summary>
        /// Project Id
        /// </summary>
        public string ProjectId { get; set; }

        /// <summary>
        /// Repository name
        /// </summary>
        public string RepositoryName { get; set; }

        /// <summary>
        /// Repository Id
        /// </summary>
        public string RepositoryId { get; set; }

        /// <summary>
        /// Public constructor
        /// </summary>
        /// <param name="fileName"></param>
        /// <param name="path"></param>
        /// <param name="projectName"></param>
        /// <param name="projectId"></param>
        /// <param name="repositoryName"></param>
        /// <param name="repositoryId"></param>
        public FileInfo(string fileName, string path, string projectName, string projectId, string repositoryName, string repositoryId)
        {
            FileName = fileName;
            Path = path;
            ProjectName = projectName;
            ProjectId = projectId;
            RepositoryName = repositoryName;
            RepositoryId = repositoryId;
        }
    }
}

