# Dyeing Based Trace Sampling

## 0. Prerequisite:

1. Dyeing based sampling is based on the `DyeBasedSamplingOption` configuration and Baggage API to decide if need to dye a span, and based on the dyeing key to decide whether to sample or drop the span
1. The main challenge for Dyeing Based Sampling is how to propagate the dyeing key from parent spans to child spans - we use Baggage API to do this

There are 3 components for Dyeing Based Sampling:

1. `BaggageHandler` - Support check and decide whether sample or drop the span based on the parent's baggage
1. `<PERSON><PERSON>andler` - Support check and decide whether sample or drop the span based on the `Tags` section in `DyeBasedSamplingOption`
1. `UrlPatternsHandler` - Support check and decide whether sample or drop the span based on the  `UrlPatterns` section in `DyeBasedSamplingOption`


## 1. Usage

1. You need to add the following option to `DyeBasedSamplingOption`:

```json
{
  "DyeingBasedTraceSampling": {
	"IsEnabled": true,
      "Tags": {
        "Sample": [ "SampleName:SampleValue", "tagName" ],
        "Drop": [ "DropName:DropValue" ]
      },
      "UrlPatterns": {
        "Sample": {
          "Prefixes": [ "users/abc", "users/bcd" ],
          "Keywords": [ "abc", "def" ]
        },
        "Drop": {
          "Prefixes": [ "email/abc", "email/bcd" ],
          "Keywords": [ "xyz" ]
        }
    }
}
```

The properties above shows all the types which you can add, please just add the sample or drop configuration you need, do not need to add all the configuration.

Such as if you only need to use the `Tags` section to decide the sample action, you can just add the `Tags` and `Sample` section to the config option.

```json
{
  "DyeingBasedTraceSampling": {
	"IsEnabled": true,
      "Tags": {
        "Sample": [ "SampleName:SampleValue", "tagName" ]
      }
    }
}
```

2. Add the following code to your `Startup.cs`, there needs two properties to be set in `DyeingBasedSampler(DyeBasedSamplingOption, DefaultSampler)`

```c#
public void ConfigureServices(IServiceCollection services)
{
    _ = services
        .AddRouting()
        .AddControllers();

    var tracerProvider = Sdk.CreateTracerProviderBuilder()
        .AddSource(Utility.sourceName)
        .AddPostTracing()
        .AddGenevaTraceExporter(configure: option => { option.ConnectionString = "EtwSession=OpenTelemetry"; }, new DyeingBasedSampler(DyeBasedSamplingOption, new AlwaysOnSampler()))
        .Build();
}
```

## 2. Effective Rule

- If the `Tags` and `UrlPattern` section are both empty, the dyeing based sampling will not work. Will return the DefaultSampler's decision.
- If the configuration in `Sample` and `Drop` sections have conflict, `Sample` will be the priority.
- If the configuration in `Tags` and `UrlPatterns` have conflict, `Tags` will be the priority.

**Example:**
*How will the `Sample` decision made? (`Drop` in the similar way)*
- If Parent *Baggage* has the `SampleName` key and the value is `SampleValue`, return `Sample`.
- If the `Tags` section `Sample` is not empty and hint current tags, return `Sample`.  
- If the `Tags` section `Drop` is not empty and does not hint current tags, return `Sample`.
- Continue to check the `UrlPatterns` section in the similar way as in `Tags`.

