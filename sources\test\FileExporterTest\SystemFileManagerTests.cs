// <copyright file="SystemFileManagerTests.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>
using System.IO;
using Xunit;

namespace Microsoft.M365.Core.Telemetry.FileExporter.Tests
{
    /// <summary>
    /// Test of <see cref="SystemFileManager" /> to dynamic.
    /// </summary>
    public class SystemFileManagerTests
    {
        /// <summary>
        /// Test CreateDirectory
        /// </summary>
        [Fact]
        public void CreateDirectory()
        {
            var testDirName = "testdir";

            if (Directory.Exists(testDirName))
            {
                Directory.Delete(testDirName);
            }

            SystemFileManager.Instance.CreateDirectory(testDirName);

            Assert.True(Directory.Exists(testDirName));
        }

        /// <summary>
        /// Test CreateFile
        /// </summary>
        [Fact]
        public void CreateFile()
        {
            var testFileName = "testfile";

            if (File.Exists(testFileName))
            {
                File.Delete(testFileName);
            }

            SystemFileManager.Instance.CreateFile(testFileName);

            Assert.True(File.Exists(testFileName));
        }

        /// <summary>
        /// Test RenameFile
        /// </summary>
        [Fact]
        public void RenameFile()
        {
            var testFileName1 = "testfile1";
            var testFileName2 = "testfile2";

            if (File.Exists(testFileName1))
            {
                File.Delete(testFileName1);
            }

            if (File.Exists(testFileName2))
            {
                File.Delete(testFileName2);
            }

            File.Create(testFileName1).Close();

            SystemFileManager.Instance.RenameFile(testFileName1, testFileName2);

            Assert.False(SystemFileManager.Instance.FileExists(testFileName1));
            Assert.True(SystemFileManager.Instance.FileExists(testFileName2));
        }
    }
}