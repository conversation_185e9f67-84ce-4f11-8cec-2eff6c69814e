# Step 5 - Update Monitoring Agent

After valided the SDK changes and Monitoring Configuration change, we can Onboard MDS event to Passive Monitoring.

For more details, please check [Onboard MDS event](https://eng.ms/cid/de671234-ca8f-4a30-85e7-c53880566719/fid/3ef27e5327104419fdea91c2d84baf215bedcd62ef99b6fd3f9f827a78e28884). Besides MDS, If you want to dump data to Kusto as well, please check [Onboard Kusto](https://eng.ms/docs/experiences-devices/m365-core/o365-substrate-fabric-extended-suzhou/o365-telemetry/m365-passive-monitoring/m365-passive-monitoring/kusto/kusto-data-onboard).

For **the services newly integrate logging**, everything is done now.

For **current Passive Monitoring SDK users**, there are some differiences that may cause potential risks.
We also recommend to have a dual-ingestion phase and validate the new logs to achieve a seamless migration.

Please continue **Next Step**: [Check Potential Risk](./PotentialRisks.md)