﻿// <copyright file="RedactionComplementEnricherTest.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

#if !NETFRAMEWORK
namespace Microsoft.M365.Core.Telemetry.R9.Tracing.Instrumentation.Accelerator.Tests
{
    using System.Diagnostics;
    using Xunit;

    /// <summary>
    /// RedactionComplementEnricherTest
    /// </summary>
    public class RedactionComplementEnricherTest
    {
        /// <summary>
        /// EnrichTest
        /// </summary>
        [Fact]
        public void EnrichTest()
        {
            var activity = new Activity("test");
            var enricher = new RedactionComplementEnricher();
            enricher.Enrich(activity);
            Assert.Null(activity.GetTagItem(Constants.HttpUrl));

            activity.SetTag(Constants.HttpUrlBackup, Constants.HttpUrl);
            enricher.Enrich(activity);
            Assert.Equal(Constants.HttpUrl, activity.GetTagItem(Constants.HttpUrl));

            enricher.Enrich(null);
        }
    }
}
#endif