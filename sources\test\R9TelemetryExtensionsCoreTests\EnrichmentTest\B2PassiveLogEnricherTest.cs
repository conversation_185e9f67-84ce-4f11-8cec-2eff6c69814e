﻿// <copyright file="B2PassiveLogEnricherTest.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using Microsoft.M365.Core.Telemetry.Enrichment;
using Microsoft.M365.Core.Telemetry.TestCommon;
using Microsoft.Skype.ECS.Core.Shims.Bond.Tag;
using Xunit;
using Xunit.Abstractions;

namespace Microsoft.M365.Core.Telemetry.EnrichmentTest
{
    /// <summary>
    /// B2PassiveLogEnricherTest
    /// </summary>
    [Collection("Do not parallel")]
    public class B2PassiveLogEnricherTest : BaseTest
    {
        /// <summary>
        /// B2PassiveLogEnricherTest
        /// </summary>
        /// <param name="output"></param>
        public B2PassiveLogEnricherTest(ITestOutputHelper output) : base(output)
        {
        }

        /// <summary>
        /// Default
        /// </summary>
        [Fact]
        public void Default()
        {
            var enricher = new B2PassiveLogEnricher();
            var enrichedProperties = new TestPropertyBag();
            enricher.Enrich(enrichedProperties);
            var enrichedState = enrichedProperties.Properties;

            Assert.Equal(true, enrichedState[B2PassiveEnricherDimensions.IsR9]);
        }

        /// <summary>
        /// TestThrowingExceptionsEmptyKey
        /// </summary>
        [Fact]
        public void TestThrowingExceptionsEmptyKey()
        {
            var enrichedProperties = new TestPropertyBag();
            Assert.Throws<ArgumentException>(() => enrichedProperties.Add(string.Empty, new object()));
        }

        /// <summary>
        /// TestThrowingExceptionsEmptyValue
        /// </summary>
        [Fact]
        public void TestThrowingExceptionsEmptyValue()
        {
            var enrichedProperties = new TestPropertyBag();
            int? obj = null;
            Assert.Throws<ArgumentNullException>(() => enrichedProperties.Add("FakeKey", obj));
        }

        /// <summary>
        /// Assure no exception thrown
        /// </summary>
        [Fact]
        public void TestNullPropertyBag()
        {
            var enricher = new B2PassiveLogEnricher();
            enricher.Enrich(null);
        }

        /// <summary>
        /// TestAddKeyValuePairObjectValue
        /// </summary>
        [Fact]
        public void TestAddKeyValuePairObjectValue()
        {
            var enricher = new B2PassiveLogEnricher();
            var enrichedProperties = new TestPropertyBag();
            var kvpArr = new KeyValuePair<string, Object>[]
            {
                    new KeyValuePair<string, Object>("FakeKey1", 1),
                    new KeyValuePair<string, Object>("FakeKey2", 2),
            };
            var param = new ReadOnlySpan<KeyValuePair<string, Object>>(kvpArr);
            enrichedProperties.Add(param);
            enricher.Enrich(enrichedProperties);
            var enrichedState = enrichedProperties.Properties;
            Assert.Equal(1, enrichedState["FakeKey1"]);
        }

        /// <summary>
        /// TestAddKeyValuePairStringValue
        /// </summary>
        [Fact]
        public void TestAddKeyValuePairStringValue()
        {
            var enricher = new B2PassiveLogEnricher();
            var enrichedProperties = new TestPropertyBag();
            var kvpArr = new KeyValuePair<string, string>[]
            {
                    new KeyValuePair<string, string>("FakeKey1", "FakeValue1"),
                    new KeyValuePair<string, string>("FakeKey2", "FakeValue2"),
            };
            var param = new ReadOnlySpan<KeyValuePair<string, string>>(kvpArr);
            enrichedProperties.Add(param);
            enricher.Enrich(enrichedProperties);
            var enrichedState = enrichedProperties.Properties;
            Assert.Equal("FakeValue1", enrichedState["FakeKey1"]);
        }
    }
}
