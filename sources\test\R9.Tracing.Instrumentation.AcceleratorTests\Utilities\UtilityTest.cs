﻿// <copyright file="UtilityTest.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System.Diagnostics.CodeAnalysis;
#if !NETFRAMEWORK
using System.Net.Http;
using Microsoft;
using Microsoft.AspNetCore.Http;
#endif
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Xunit;

namespace Microsoft.M365.Core.Telemetry.R9.Tracing.Instrumentation.Accelerator.Tests.Utilities
{
    /// <summary>
    /// UtilityTest
    /// </summary>
    [ExcludeFromCodeCoverage]
    public class UtilityTest
    {
        /// <summary>
        /// ExtractDeploymentFromPodNameTest
        /// </summary>
        [Fact]
        public void ExtractDeploymentFromPodNameTest()
        {
            Assert.Equal(string.Empty, Utility.ExtractDeploymentFromPodName(string.Empty));
            Assert.Equal("aaa", Utility.ExtractDeploymentFromPodName("aaa-bbb"));
            Assert.Equal("aaa", Utility.ExtractDeploymentFromPodName("aaa-bbb-ccc"));
            Assert.Equal(string.Empty, Utility.ExtractDeploymentFromPodName("-bbb-ccc"));
            Assert.Equal(string.Empty, Utility.ExtractDeploymentFromPodName("-bbb"));
        }

        /// <summary>
        /// IsCosmicServiceTest
        /// </summary>
        [Fact]
        public void IsCosmicServiceTest()
        {
            Assert.False(ConfigurationUtility.IsCosmicService(string.Empty));
            Assert.False(ConfigurationUtility.IsCosmicService(null));
            Assert.False(ConfigurationUtility.IsCosmicService("A"));
            Assert.True(ConfigurationUtility.IsCosmicService(Constants.Cosmic));
        }

        /// <summary>
        /// ODLExporterRelatedOptionsTest
        /// </summary>
        [Fact]
        public void ODLExporterRelatedOptionsTest()
        {
            var services = new ServiceCollection();
            var configuration = new ConfigurationBuilder().AddJsonFile("appsettings.json").Build();
            var acceleratorOptions = ConfigurationUtility.ParseAndValidateTracingAcceleratorOptions(services, configuration.GetSection("Microsoft_m365_core_telemetry:Tracing"));

            Assert.Equal(0.9, acceleratorOptions.GenevaTraceExporter.TraceIdBasedSampleRatio);
            Assert.False(acceleratorOptions.ODLTraceExporter.BatchExport);
        }

#if !NETFRAMEWORK
        /// <summary>
        /// DeriveHttpUrlTest
        /// </summary>
        [Fact]
        public void DeriveHttpUrlTest()
        {
            var request = new DefaultHttpContext().Request;
            var httpRequest = new HttpRequestMessage();

            Assert.Equal(":///", Utility.DeriveHttpUrl(request, string.Empty));
            Assert.Equal("://:/", Utility.DeriveHttpUrl(httpRequest, string.Empty));
        }
#endif
    }
}
