﻿// <copyright file="TracingAcceleratorOptions.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

namespace Microsoft.M365.Core.Telemetry.R9.Tracing.Instrumentation.Accelerator
{
    using System;
    using Microsoft.M365.Core.Telemetry.R9.Tracing.Instrumentation.Accelerator.Options;

    /// <summary>
    /// TracingAcceleratorOptions
    /// </summary>
    public class TracingAcceleratorOptions
    {
        /// <summary>
        /// SchemaVersion
        /// </summary>
        public ConfigSchemaSupportedVersions SchemaVersion { get; set; } = ConfigSchemaSupportedVersions.V1;

        /// <summary>
        /// ActivitySources
        /// </summary>
#pragma warning disable CA1819 // Properties should not return arrays
        public string[] ActivitySources { get; set; } = Array.Empty<string>();
#pragma warning restore CA1819 // Properties should not return arrays

        /// <summary>
        /// HttpClientTracing
        /// </summary>
        public HttpClientTracingOptionsInherited HttpClientTracing { get; set; } = new HttpClientTracingOptionsInherited();

        /// <summary>
        /// HttpTracing
        /// </summary>
        public HttpTracingOptionsInherited HttpTracing { get; set; } = new HttpTracingOptionsInherited();

        /// <summary>
        /// GenevaTraceExporter
        /// </summary>
        public GenevaTraceExporterOptionsInherited GenevaTraceExporter { get; set; } = new GenevaTraceExporterOptionsInherited
        {
            ConnectionString = Constants.DefaultGenevaTraceExporterConnectionString,
            TraceIdBasedSampleRatio = Constants.GenevaTraceExporterTraceIdBasedSampleRatio
        };

        /// <summary>
        /// ODLTraceExporter
        /// </summary>
        public ODLTraceExporterOptionsInherited ODLTraceExporter { get; set; } = new ODLTraceExporterOptionsInherited();

        /// <summary>
        /// ConsoleTracingExporter
        /// </summary>
        public ConsoleTracingExporterOptionsInherited ConsoleTracingExporter { get; set; } = new ConsoleTracingExporterOptionsInherited();

        /// <summary>
        /// ODLTcpTraceExporter
        /// </summary>
        public ODLTcpTraceExporterOptionsInherited ODLTcpTraceExporter { get; set; } = new ODLTcpTraceExporterOptionsInherited();

        /// <summary>
        /// TracingSamplerAndEnable
        /// </summary>
#pragma warning disable CA1819 // Properties should not return arrays
        public TracingSamplerAndEnableOptions[] TracingSamplerAndEnabled { get; set; } = Array.Empty<TracingSamplerAndEnableOptions>();
#pragma warning restore CA1819 // Properties should not return arrays
    }
}