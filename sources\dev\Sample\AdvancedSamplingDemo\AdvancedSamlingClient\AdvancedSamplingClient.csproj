﻿<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
	  <RootNamespace>Microsoft.M365.Core.Tracing.AdvancedSamplingClient</RootNamespace>
	  <AssemblyName>Microsoft.M365.Core.Tracing.AdvancedSamplingClient</AssemblyName>
    <TargetFramework>net6.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>

	<ItemGroup>
		<!--Please use PackageReference in your project <PackageReference Include="Microsoft.M365.Core.Telemetry.R9.Tracing.Instrumentation.Accelerator"  />-->
		<ProjectReference Include="..\..\..\R9.Tracing.Instrumentation.Accelerator\R9.Tracing.Instrumentation.Accelerator.csproj" />
		
		<!--Please use PackageReference in your project <PackageReference Include="Microsoft.M365.Core.Telemetry.R9.Tracing.AdvancedSampling" />-->
		<ProjectReference Include="..\..\..\R9.Tracing.AdvancedSampling\R9.Tracing.AdvancedSampling.csproj" />
	</ItemGroup>

	<!--<ItemGroup>
		<PackageReference Include="Microsoft.M365.Core.Telemetry.R9.Exporter.Filters" />
	</ItemGroup>-->
	
	<ItemGroup>
		<None Update="AppSettings.json">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</None>
	</ItemGroup>
</Project>