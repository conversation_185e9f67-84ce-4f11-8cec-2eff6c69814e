﻿// <copyright file="Log.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using Microsoft.Extensions.Logging;
using Microsoft.R9.Extensions.Logging;

namespace Microsoft.M365.Core.Telemetry.SampleConsoleApp
{
    internal static partial class Log
    {
        [LoggerMessage(0, LogLevel.Critical, "Foo message: {var1}, {var2}")]
        public static partial void Foo(ILogger logger, string var1, int var2);

        [LoggerMessage(1, LogLevel.Critical, "")]
#pragma warning disable R9G014 // Argument is not referenced from the logging message
        public static partial void EmptyBody(ILogger logger, string var1, string var2);
#pragma warning restore R9G014 // Argument is not referenced from the logging message
    }
}
