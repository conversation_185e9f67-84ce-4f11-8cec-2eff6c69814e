// <copyright file="IFileLogger.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System;
using Microsoft.R9.Extensions.SecurityTelemetry;

namespace Microsoft.M365.Core.Telemetry.FileExporter
{
    /// <summary>
    /// Standard file logger interface for the <see cref="NRTFileExporter"/>.
    /// </summary>
    /// <typeparam name="T">The generic type parameter.</typeparam>
    internal interface IFileLogger<T> : IDisposable
        where T : class
    {
        /// <summary>
        /// Write log to file.
        /// </summary>
        /// <param name="batch">A batch of logs to be exported to file.</param>
        void Log(in Batch<T> batch);

        /// <summary>
        /// Write log to file.
        /// </summary>
        /// <param name="logLine">Single log to be exported to file.</param>
        void Log(T logLine);
    }
}
