﻿// <copyright file="ECSClientUtilitiesTest.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.M365.Core.Telemetry.ECSClient;
using Microsoft.Skype.ECS.Client;
using Newtonsoft.Json.Linq;
using NSubstitute;
using Xunit;

namespace Microsoft.M365.Core.Telemetry.ECSClientTest
{
    /// <summary>
    /// ECSClientUtilitiesTest
    /// </summary>
    public class ECSClientUtilitiesTest
    {
        /// <summary>
        /// TestGetUnifiedTelemetryConfig
        /// </summary>
        /// <exception cref="Exception">Exception</exception>
        [Fact]
        public void TestGetUnifiedTelemetryConfig()
        {
            Dictionary<string, string> context = new Dictionary<string, string> { { "Filter", "Test" } };
            IECSConfigSettings mockedConfigSettings = Substitute.For<IECSConfigSettings>();
            JObject configRoot;
            mockedConfigSettings.TryGetRootValue("testTeam", out configRoot).Returns(x => { throw new Exception(); });
            IECSConfigurationRequester ecsRequester = Substitute.For<IECSConfigurationRequester>();
            var res = ECSClientUtilities.GetUnifiedTelemetryConfig("testTeam", context, ecsRequester);
            Assert.Null(res);
        }

        /// <summary>
        /// TestGetSettingsWithRetry
        /// </summary>
        [Fact]
        public void TestGetSettingsWithRetry()
        {
            Dictionary<string, string> context = new Dictionary<string, string> { { "Filter", "Test" } };
            Assert.Throws<ArgumentNullException>(() => ECSClientUtilities.GetSettingsWithRetry(2, context, null));

            IECSConfigurationRequester ecsRequester = Substitute.For<IECSConfigurationRequester>();
            ecsRequester.Initialize(new ECSClientConfiguration()).GetAwaiter().GetResult();

            ecsRequester.GetSettings(context).Returns(Task.FromException<SettingsETag>(new Exception("some error")));
            var res = ECSClientUtilities.GetSettingsWithRetry(2, context, ecsRequester);
            Assert.Null(res);
        }

        /// <summary>
        /// ParseConfigConfigNameExists
        /// </summary>
        [Fact]
        public void ParseConfigConfigNameExists()
        {
            // Arrange
            var configRoot = new JObject
            {
                { "existingConfig", "value" }
            };
            var configName = "existingConfig";
            var expected = JToken.FromObject("value");

            // Act
            var result = ECSClientUtilities.ParseConfig(configRoot, configName);

            // Assert
            Assert.Equal(expected, result);
        }

        /// <summary>
        /// ParseConfigConfigNameDoesNotExist
        /// </summary>
        [Fact]
        public void ParseConfigConfigNameDoesNotExist()
        {
            // Arrange
            var configRoot = new JObject();
            var configName = "nonExistingConfig";

            // Act
            var result = ECSClientUtilities.ParseConfig(configRoot, configName);

            // Assert
            Assert.Null(result);
        }

        /// <summary>
        /// TestDirectoryEqualsSameReference
        /// </summary>
        [Fact]
        public void TestDictionaryEquals()
        {
            Dictionary<string, object> dict1 = new Dictionary<string, object>
            {
                { "key1", "value1" },
                { "key2", "value2" }
            };

            Dictionary<string, object> dict2 = dict1;

            Dictionary<string, object> dict3 = new Dictionary<string, object>
            {
                { "key1", "value1" },
                { "key2", "value2" }
            };
            Assert.True(ECSClientUtilities.DictionaryEquals(dict1, dict2));
            Assert.True(ECSClientUtilities.DictionaryEquals(dict1, dict3));
        }

        /// <summary>
        /// TestDirectoryEqualsNullDicts
        /// </summary>
        [Fact]
        public void TestDictionaryEqualsNullDicts()
        {
            Dictionary<string, object> dict1 = null;

            Dictionary<string, object> dict2 = null;
            Dictionary<string, object> dict3 = new Dictionary<string, object>
            {
                { "key1", "value1" },
                { "key2", "value2" },
                { "key3", "value3" }
            };
            Dictionary<string, object> dict4 = new Dictionary<string, object>
            {
                { "key1", "value1" },
                { "key2", "value3" }
            };
            Assert.False(ECSClientUtilities.DictionaryEquals(dict1, dict3));
            Assert.False(ECSClientUtilities.DictionaryEquals(dict2, dict3));
            Assert.False(ECSClientUtilities.DictionaryEquals(dict3, dict4));
        }

        /// <summary>
        /// TestDirectoryEqualsDifferentContent
        /// </summary>
        [Fact]
        public void TestDictionaryEqualsDifferentContent()
        {
            Dictionary<string, object> dict1 = new Dictionary<string, object>
            {
                { "key1", "value1" },
                { "key2", "value2" }
            };
            Dictionary<string, object> dict2 = new Dictionary<string, object>
            {
                { "key1", "value1" },
                { "key2", "value3" }
            };
            Dictionary<string, object> dict3 = new Dictionary<string, object>
            {
                { "key1", "value1" },
                { "key3", "value3" }
            };

            Assert.False(ECSClientUtilities.DictionaryEquals(dict1, dict2));
            Assert.False(ECSClientUtilities.DictionaryEquals(dict3, dict1));
        }
    }
}
