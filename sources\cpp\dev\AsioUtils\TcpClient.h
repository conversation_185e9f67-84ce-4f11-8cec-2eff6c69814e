#pragma once
#include <boost/asio.hpp>
#include <opentelemetry/logs/provider.h>
#include <opentelemetry/logs/logger.h>

#include <memory>
#include <string>
#include <deque>

namespace Microsoft {
namespace M365 {
namespace Exporters {

struct TcpClientOptions {
    std::string host;
    std::string port;
    int reconnect_interval_ms = 1000;
    int monitor_interval_s = 10;
};

// Theoretically this class can throw. Mark noexcept to explicity crash the process if an exception is thrown.
// TODO(jiayiwang): Catch and log the exception before going to SDF.
class TcpClient {
public:
    TcpClient(TcpClientOptions options, boost::asio::io_context& io_context, opentelemetry::nostd::shared_ptr<opentelemetry::logs::Logger> logger) noexcept;
    void Send(std::shared_ptr<std::string> data) noexcept;

    int success_count() const { return success_count_; }
    int failure_count() const { return failure_count_; }

    void ShutDown() noexcept;

private:
    void start_connect();
    void retry_connect();
    void handle_connect(const boost::system::error_code& error);
    void handle_write(const boost::system::error_code& error, std::size_t bytes_transferred, std::shared_ptr<std::string> data);
    void do_write(std::shared_ptr<std::string> data);
    void start_monitor_timer();
    void handle_monitor_timer(const boost::system::error_code& error);

    // TODO(jiayiwang): Add mdm and mds monitoring.
    TcpClientOptions options_;
    boost::asio::io_context& io_context_;
    boost::asio::ip::tcp::resolver resolver_;
    // TODO(jiayiwang): I feel one socket is enough for the job.
    // Get some benchmark results.
    std::unique_ptr<boost::asio::ip::tcp::socket> socket_;
    opentelemetry::nostd::shared_ptr<opentelemetry::logs::Logger> logger_;
    int success_count_ = 0;
    int failure_count_ = 0;
    size_t success_size_ = 0;
    boost::asio::steady_timer monitor_timer_;

    boost::asio::steady_timer reconnect_timer_;
    // True after a successfull handle_connect. False after a failed handle_write.
    bool is_connected_ = false;
    // TODO(jiayiang): Now we lock all function bodies in whole. Refine the lock scope.
    std::mutex mutex_;
};

} // namespace Exporters
} // namespace M365
} // namespace Microsoft