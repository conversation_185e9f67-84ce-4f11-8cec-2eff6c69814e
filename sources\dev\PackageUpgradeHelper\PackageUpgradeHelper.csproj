﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <NoWarn>CS8618,cs8602,CA1707,SA1516,CA1305,SA1011,SA1515,CA1307,CA2227,SA1611,CA1822,SA1512</NoWarn>
  </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="Microsoft.Identity.Client" VersionOverride="4.68.0" />
        <PackageReference Include="NuGet.Packaging" VersionOverride="6.12.1" />
        <PackageReference Include="System.Text.Json" VersionOverride="8.0.5" />
    </ItemGroup>

    <ItemGroup>
      <None Update="OutputFiles\AddedPackages.txt">
        <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      </None>
      <None Update="OutputFiles\filterPackages.txt">
        <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      </None>
      <None Update="OutputFiles\RootNewPackageMap.txt">
        <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      </None>
      <None Update="OutputFiles\targetPackages.txt">
        <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      </None>
      <None Update="OutputFiles\UpgradedPackages.txt">
        <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      </None>
    </ItemGroup>

    <ItemGroup>
      <Folder Include="ModifiedFiles\" />
      <Folder Include="OriginalFiles\" />
    </ItemGroup>
	
	<ItemGroup>
		<None Update="Scripts\*">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</None>
	</ItemGroup>

</Project>
