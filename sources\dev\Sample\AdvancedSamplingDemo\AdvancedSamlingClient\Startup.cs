﻿// <copyright file="Startup.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using Microsoft.M365.Core.Telemetry.R9.Tracing.AdvancedSampling;
using Microsoft.M365.Core.Telemetry.R9.Tracing.AdvancedSampling.DyeBasedTraceSampling;
using Microsoft.M365.Core.Telemetry.R9.Tracing.Instrumentation.Accelerator;
using Newtonsoft.Json;
using OpenTelemetry.Exporter.Filters;
using OpenTelemetry.Trace;

namespace Microsoft.M365.Core.Tracing.AdvancedSamplingClient
{
    /// <summary>
    /// Startup
    /// </summary>
    public class Startup
    {
        /// <inheritdoc/>
        public Startup(IConfiguration configuration)
        {
            Configuration = configuration;
        }

        /// <inheritdoc/>
        public IConfiguration Configuration { get; private set; }

        /// <inheritdoc/>
        public static void Configure(IApplicationBuilder app)
        {
            _ = app
                .UseRouting()
                .UseMiddleware<PostTraceMiddleware>()
                .UseEndpoints(endpoints =>
                {
                    endpoints.MapControllers();
                });
        }

        /// <inheritdoc/>
        public void ConfigureServices(IServiceCollection services)
        {
            _ = services
                .AddRouting()
                .AddControllers();

            var option = new DyeBasedSamplingOption();
            IConfigurationSection section = Configuration.GetSection("Microsoft_m365_core_telemetry:DyeingSamplingOption");
            section.Bind(option);
            section.Value = JsonConvert.SerializeObject(option);

            Action<TracerProviderBuilder> action = (builder) =>
            {
                builder
                .AddAspNetCoreInstrumentation()
                .AddHttpClientInstrumentation()
                .AddPostTracing()
                .SetSampler(new AlwaysOnSampler())
                .AddDyeBasedSampling(option)
                .AddConsoleExporter(new BaggageFilter());
            };

            services.AddDistributedTracingService(
                Configuration.GetSection("Microsoft_m365_core_telemetry:ServiceMetadata"),
                Configuration.GetSection("Microsoft_m365_core_telemetry:Tracing"),
                action);
        }
    }
}
