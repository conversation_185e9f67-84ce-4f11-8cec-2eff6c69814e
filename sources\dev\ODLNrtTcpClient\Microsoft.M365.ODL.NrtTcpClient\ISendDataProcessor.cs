﻿// <copyright file="ISendDataProcessor.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Microsoft.Office.BigData.DataLoader;

namespace Microsoft.M365.ODL.NrtTcpClient
{
    /// <summary>
    /// The processor to be executed before/after data sending
    /// </summary>
    public interface ISendDataProcessor
    {
        /// <summary>
        /// What to do before data sending
        /// </summary>
        /// <param name="currentThread">current thread for data sending</param>
        /// <param name="data">the data to be sent</param>
        void BeforeSendData(NrtTcpClientThread currentThread, ODLNRTRequest data);

        /// <summary>
        /// what to do after data sending
        /// </summary>
        /// <param name="currentThread">current thread for data sending</param>
        /// <param name="data">the data to be sent</param>
        /// <param name="response">the response after data sending</param>
        void AfterSendData(NrtTcpClientThread currentThread, ODLNRTRequest data, ODLNRTResponse response);

        /// <summary>
        /// what to do after reconnect
        /// </summary>
        /// <param name="currentThread">current thread for data sending</param>
        /// <returns>true if the operation end successfully</returns>
        bool AfterReconnect(NrtTcpClientThread currentThread);
    }
}
