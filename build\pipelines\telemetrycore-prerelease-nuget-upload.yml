# This Yaml Document has been converted by ESAI Yaml Pipeline Conversion Tool.
# Please make sure to check all the converted content, it is your team's responsibility to make sure that the pipeline is still valid and functions as expected.
# This pipeline will be extended to the OneESPT template
# The pool section has been filled with placeholder values, check the following link for guidance: https://eng.ms/docs/cloud-ai-platform/devdiv/one-engineering-system-1es/1es-docs/1es-pipeline-templates/onboardingesteams/overview, replace the pool section with your hosted pool, os, and image name. If you are using a Linux image, you must specify an additional windows image for SDL: https://eng.ms/docs/cloud-ai-platform/devdiv/one-engineering-system-1es/1es-docs/1es-pipeline-templates/features/sdlanalysis/overview#how-to-specify-a-windows-pool-for-the-sdl-source-analysis-stage
# The 'artifactDropDownloadTask@1' tasks have been converted to inputs within the `templateContext` section of each job.
trigger: none
name: $(Date:yyyyMMdd).$(Rev:r)
variables:
- name: ArtifactServices.Drop.PAT
  value: $(OssInfraReadDrop)
- name: Certificates
  value: officialbuild
- name: CodeRead_PAT
  value: $(O365ExchangeCodeRead)
- name: ENVIRONMENT
  value: Azure
- name: PACKAGE_PAT
  value: $(O365ExchangeArtifactRW)
- group: AuditReportingDBCertificate
- group: OssInfraReadDrop
resources:
  pipelines:
  - pipeline: 'TelemetryCoreOfficialyaml'
    project: 'O365 Core'
    source: 'Office Data Loader\TelemetryCore\Official Builds\TelemetryCore Official (yaml)'
    trigger: true
  repositories:
  - repository: 1ESPipelineTemplates
    type: git
    name: 1ESPipelineTemplates/1ESPipelineTemplates
    ref: refs/tags/release
extends:
  template: v1/1ES.Official.PipelineTemplate.yml@1ESPipelineTemplates
  parameters:
    pool:
      name: Azure-Pipelines-1ESPT-ExDShared
      image: windows-2022
      os: windows
    customBuildTags:
    - ES365AIMigrationTooling-Release
    stages:
    - stage: Stage_1
      displayName: Staging
      jobs:
      - job: PreDeploymentApprovalJob
        displayName: Pre-Deployment Approval
        condition: succeeded()
        timeoutInMinutes: 4320
        pool: server
        steps:
        - task: ManualValidation@1
          inputs:
            notifyUsers: |-
              [TEAM FOUNDATION]\TelemetryCoreRepoAdmin
            approvers: |-
              [TEAM FOUNDATION]\TelemetryCoreRepoAdmin
      - job: Job_1
        displayName: Agent job - Publish Packages
        dependsOn: PreDeploymentApprovalJob
        condition: succeeded()
        timeoutInMinutes: 0
        templateContext:
          inputs:
          - input: artifactsDrop
            pipeline: 'TelemetryCoreOfficialyaml'
            dropMetadataContainerName: 'DropMetaData'
            displayName: 'Download from Artifact Services Drop'
            usePat: false
        steps:
        - template: build/pipelines/telemetrycore-prerelease-nuget-upload-task-group.yml@self
          parameters: {}