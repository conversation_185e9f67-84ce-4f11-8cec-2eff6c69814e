# Step-by-step guide for Substrate Logging Extension

## Introduction

This document is a step-by-step guide for users to start logging with Substrate Logging Extension. It will:
1. [Create a sample app](#step-1-create-a-sample-app)
2. [Create a local config and a set of ECS configs](#step-2-create-configs)
3. [Send logs in code which aligns with the configs](#step-3-send-configs-in-code)
4. [(for local test) Start a Geneva Monitoring Agent to capture logs](#step-4-start-monitoring-agent-for-local-test)
5. [Check logs on Geneva and Odl](#step-5-check-logs)
6. [Edit export (add/remove event, add/remove destination…)](#step-6-edit-export-config-on-ecs)

## Step 1: Create a Sample App

In our code repo, we have defined several sample apps of different scenarios [repo link](https://o365exchange.visualstudio.com/O365%20Core/_git/TelemetryCore?path=/sources/sample/R9.Logging.Substrate).

In this page, we take the Non-DI one as an example.
It loads configuration from `appsettings.json` and ECS with `ConfigurationHelper.LoadConfiguration()` and configures logging with `ConfigureSubstrateLogging()` or `AddSubstrateLogging()`.

```csharp
IConfiguration configuration = ConfigurationHelper.LoadConfiguration(
    Path.Combine(Directory.GetCurrentDirectory(), "ECSDefaultConfig", "ECSDefaultConfig.json"),
    Path.Combine(Directory.GetCurrentDirectory(), "appsettings.json"));

ILoggerFactory loggerFactory = LoggerFactory.Create(builder =>
{
    builder.ConfigureSubstrateLogging(configuration);
    builder.AddConsole();
});
```

## Step 2: Create Configs

Before sending logs with this sample app, we need to set up configurations. The definition of full configurations can be found in [this doc](https://o365exchange.visualstudio.com/O365%20Core/_git/TelemetryCore?path=/sources/dev/R9.Logging.Substrate/ReadMe.md&_a=preview).

### Local Config

Should be stored with app code. The path should align with the parameter of `LoadConfiguration()`.

In our sample app, we have created a local config, which includes 2 parts: one for fetching online configs from ECS, the other is basic settings of logging.

```json
{
  "SubstrateLogging": {
    "UseCompositeExporter": true,
    "CompositeExporter": {
      "VirtualTableMappings": {
        "TableA": "TableA",
        "TableB": "TableB",
        "*": "MyTable"
      },
      "Geneva": {
        "ConnectionString": "EtwSession=TestSession"
      },
      "OdlTcp": {
        "ConnectionString": "tcp://localhost:1234"
      }
    }
  },
  "ECSParameters": {
    "ECSIdentifiers": {
      "Service": "ServiceA"
    },
    "EnvironmentType": "Integration",
    "Client": "UnifiedTelemetry",
    "Agents": ["Logs"]
  }
}
```

In case we want to **temporally test without setting up ECS**, set arbitrary values to `ECSParameters:Client` and `ECSParameters:Agents`.
It will get empty value from ECS and only keep configs from local file.
For such temporal local test, local appsettings.json must contain extra section of `SubstrateLogging:CompositeExporter:VirtualTableExports` when using CompositeExporter, which is expected to be configured in ECS for production.
Besides, when a key-value is configured in both local file and ECS, the value on ECS will override.

### ECS Config

In our system, ECS is designed to store export configurations so that the destination of events can be flexibly changed.

Every event is designed to have a single page of configs, with at least one filter `Service`.

Here is an example:

![](../../.images/Logging/StepByStep/ecs-overall.png)

As defined in `ECSParameters:ECSIdentifiers`, the sample config should have `Service="ServiceA"`.
From the local config, we have defined virtual tables `TableA`, `TableB` and `MyTable`.
In this sample ECS config, we define the export of `TableA` to export it to both Geneva and OdlTcp exporter.

## Step 3: Send Configs in Code

Now we have defined the export of virtual table `TableA`, let’s send some logs in code.

```csharp
var logger1 = loggerFactory.CreateLogger("TableA");
var logger2 = loggerFactory.CreateLogger("TableB");
var cnt = 0;
while (true)
{
    logger1.LogInformation("Test TableA: {cnt}", ++cnt);
    logger2.LogInformation("Test TableB: {cnt}", cnt);
    Thread.Sleep(5000);
}
```

The loggers are created with a given Category Name, which aligns with the keys defined in VirtualTableExports.

Now in this app, it sends logs with category name `TableA`, they are routed as virtual table `TableA`.
They are sent to Geneva Exporter as `EventA` and sent to OdlTcp Exporter as `LogTypeA`.

In production, we recommend to use [Fast Logging (or Compile-time logging source generation)](https://learn.microsoft.com/en-us/dotnet/core/extensions/logger-message-generator). Please check the link for detail usage.

## Step 4: Start Monitoring Agent (for local test)

Logs/Events from app are collected by monitoring agents. On production machines, the agents are centralized managed. While in the local test, we need to manually set up an agent to help send logs to the final destination.

### Geneva

Refer M365 Passive Monitoring doc [(document link)](https://eng.ms/docs/experiences-devices/m365-core/o365-substrate-fabric-extended-suzhou/o365-telemetry/m365-passive-monitoring/m365-passive-monitoring/geneva/tds-setup) to setup Geneva on a TDS or dev machine.

The ETW session defined in Monitoring Agent should align with the local config (Geneva:ConnectionString).

```xml
<OneDSProviders>
  <OneDSProvider name="TestSession">
    <Event eventName="EventA" retentionInDays="7" storeType="CentralBond"/>
    <Event eventName="EventB" retentionInDays="7" storeType="CentralBond"/>
  </OneDSProvider>
</OneDSProviders>
```

### OdlTcp
For local tests, we don’t need to setup an agent for OdlTcp. The logs will be dumped to `C:\OfficeDataLoader\Dump\NRTLoggingSdk` or `D:\OfficeDataLoader\Dump\NRTLoggingSdk`.

## Step 5: Check Logs

### Geneva

Go on jarvis portal, navigate to Logs->DGrep. Choose the correct Endpoint, Namespace and Events (`EventA` as defined in our sample). We’ll see the logs if everything is correctly set.

![](../../.images/Logging/StepByStep/log-on-jarvis.png)

Alternatively, the logs can be checked in disk (under path which is set by MONITORING_DATA_DIRECTORY). The exported events will be saved with name of `{tableName}.tsf`. Use `table2csv.exe` to generate a readable csv version.

### OdlTcp

Logs dumped by OdlTcp are readable contents. It’s csv formatted content without header. The schema is composed of several parts in sequence. For detail, please check section [Odl export schema](OdlSchema.md).

## Step 6: Edit Export (config on ECS)

The VirtualTableExports of an event is defined as an array.Each Export includes an “ExporterType” (supports `Geneva` or `OdlTcp`) and an `ExportTable`.

![](../../.images/Logging/StepByStep/ecs-export.png)

### Remove Destination

Remove the element in this array and save on ECS, the CompositeExporter will stop sending logs to this destination. The latency depends on the refresh period of ECS, interval of logger… It should work in 30 minutes.

### Add Destination

Like removing, adding a section will tell CompositeExporter to add an export destination.

If this is the first time sending the event to this exporter, make sure the config of agent (see [step 4](#step-4-start-monitoring-agent-for-local-test)) is also updated to receive and upload this event.

### Add Event

Add a new config on ECS with the same structure (see [step 2](#step-2-create-configs)). Remember to edit `EventName` and `ExportTable`. Besides, keep the filters at the correct value and make the config running.

![](../../.images/Logging/StepByStep/ecs-add-event.png)

### Remove Event

Stop the config or archive/delete it to make it not fetchable by the service.