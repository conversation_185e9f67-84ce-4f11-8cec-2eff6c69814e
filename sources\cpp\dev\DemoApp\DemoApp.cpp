
#include "FileManager.h"
#include "LogFileExporter.h"
#include "OdlTraceExporter.h"
#include "TcpClient.h"
#include "TcpServer.h"

#include <boost/asio.hpp>
#include <boost/format.hpp>
#include <boost/container/deque.hpp>
#include <boost/bind.hpp>
#include <boost/thread.hpp>
#include <boost/lexical_cast.hpp>
#include <opentelemetry/context/context.h>
#include <opentelemetry/context/runtime_context.h>
#include <opentelemetry/exporters/geneva/metrics/exporter.h>
#include <opentelemetry/exporters/geneva/geneva_logger_exporter.h>
#include "opentelemetry/exporters/geneva/geneva_tracer_exporter.h"
#include <opentelemetry/logs/log_record.h>
#include <opentelemetry/logs/logger_provider.h>
#include <opentelemetry/logs/provider.h>
#include <opentelemetry/sdk/logs/exporter.h>
#include <opentelemetry/sdk/logs/logger_provider.h>
#include <opentelemetry/sdk/logs/logger_provider_factory.h>
#include <opentelemetry/sdk/logs/recordable.h>
#include <opentelemetry/sdk/logs/read_write_log_record.h>
#include <opentelemetry/sdk/logs/simple_log_record_processor_factory.h>
#include <opentelemetry/sdk/resource/resource.h>
#include <opentelemetry/sdk/trace/batch_span_processor_factory.h>
#include <opentelemetry/sdk/trace/batch_span_processor_options.h>
#include <opentelemetry/sdk/trace/processor.h>
#include <opentelemetry/sdk/trace/recordable.h>
#include <opentelemetry/sdk/trace/samplers/always_off_factory.h>
#include <opentelemetry/sdk/trace/samplers/parent_factory.h>
#include <opentelemetry/sdk/trace/tracer.h>
#include <opentelemetry/sdk/trace/tracer_provider.h>
#include <opentelemetry/sdk/trace/tracer_provider_factory.h>
#include <opentelemetry/trace/default_span.h>
#include <opentelemetry/trace/provider.h>
#include <opentelemetry/trace/span_id.h>
#include <opentelemetry/trace/span_metadata.h>
#include <opentelemetry/trace/span_startoptions.h>
#include <opentelemetry/trace/tracer_provider.h>
#include <opentelemetry/sdk/version/version.h>
#include <opentelemetry/trace/provider.h>
#include <opentelemetry/trace/tracer_provider.h>

#include <atomic>
#include <thread>
#include <iostream>
#include <sstream>
#include <chrono>
#include <mutex>

using namespace Microsoft::M365::Exporters;
using boost::asio::ip::tcp;

namespace common    = opentelemetry::common;
namespace geneva    = opentelemetry::exporter::geneva;
namespace nostd     = opentelemetry::nostd;
namespace trace_api = opentelemetry::trace;
namespace trace_sdk = opentelemetry::sdk::trace;

constexpr int kTracePeriod = 100; // milliseconds
constexpr int kRunningTime = 5; // seconds
constexpr int kBatchSize = 100; // spans.
constexpr int kExportPeriod = 1000; // milliseconds

constexpr uint8_t kTraceIdBuf[]       = {1, 2, 3, 4, 5, 6, 7, 8, 1, 2, 3, 4, 5, 6, 7, 8};
constexpr uint8_t kSpanIdBuf[]        = {1, 2, 3, 4, 5, 6, 7, 8};
constexpr uint8_t kParentSpanIdBuf[]  = {8, 7, 6, 5, 4, 3, 2, 1};

namespace
{
void f1(std::shared_ptr<trace_api::Tracer> tracer)
{
  auto scoped_span = trace_api::Scope(tracer->StartSpan("f1"));
}

void f2(std::shared_ptr<trace_api::Tracer> tracer)
{
  auto scoped_span = trace_api::Scope(tracer->StartSpan("f2"));

  f1(tracer);
  f1(tracer);
}

void foo_library(std::shared_ptr<trace_api::Tracer> tracer)
{
  auto scoped_span = trace_api::Scope(tracer->StartSpan("library"));

  f2(tracer);
}

void CallUnderSampledSpan(std::shared_ptr<trace_api::Tracer> tracer) {
  // Create a sampled SpanContext.
  trace_api::TraceId trace_id{kTraceIdBuf};
  trace_api::SpanId span_id{kSpanIdBuf};
  trace_api::SpanId parent_span_id{kParentSpanIdBuf};
  const auto trace_state = trace_api::TraceState::GetDefault()->Set("key1", "value");
  const trace_api::SpanContext span_context{
      trace_id, span_id, trace_api::TraceFlags{trace_api::TraceFlags::kIsSampled}, true,
      trace_state};

  nostd::shared_ptr<trace_api::Span> root_span = std::static_pointer_cast<trace_api::Span>(std::make_shared<trace_api::DefaultSpan>(span_context));
  trace_api::Scope scope(root_span);
  foo_library(tracer);
}
}  // namespace

void LoadTest(std::shared_ptr<trace_api::Tracer> tracer) {
  auto start_time = std::chrono::steady_clock::now();
  auto end_time = start_time + std::chrono::seconds(kRunningTime);
  int span_count = 0;
  while (std::chrono::steady_clock::now() < end_time)
  {
      for (int i = 0; i < 100; i++)
      {
          foo_library(tracer);
      }  // 400 spans per iterate.
      span_count += 400;
      std::this_thread::sleep_for(std::chrono::milliseconds(kTracePeriod));
  }
  std::cout << "Generated " << span_count << " spans in " << kRunningTime << " seconds." << std::endl;
}

void InitFileLog() {
    auto resource = opentelemetry::sdk::resource::Resource::GetEmpty();
    //auto file_exporter = std::make_unique<LogFileExporter>(LogFileExporterOptions{"C:\\Users\\<USER>\\Downloads\\log.txt", std::chrono::seconds(60)});
    auto file_exporter = std::make_unique<LogFileExporter>(LogFileExporterOptions{"C:\\TelemetryCore\\log.txt", std::chrono::seconds(60)});
    auto file_processor = opentelemetry::sdk::logs::SimpleLogRecordProcessorFactory::Create(std::move(file_exporter));
    std::shared_ptr<opentelemetry::sdk::logs::LoggerProvider> file_sdk_provider(
    opentelemetry::sdk::logs::LoggerProviderFactory::Create(std::move(file_processor), resource));
    // Set the global logger provider
    const std::shared_ptr<opentelemetry::logs::LoggerProvider> &api_provider = file_sdk_provider;
    opentelemetry::logs::Provider::SetLoggerProvider(api_provider);
}

std::unique_ptr<trace_api::TracerProvider> CreateOdlTracerProvider() {
    auto resource = opentelemetry::sdk::resource::Resource::GetEmpty();
    auto odl_exporter = std::make_unique<OdlTraceExporter>(OdlTraceExporterOptions{"localhost", "9527", "NanoProxy", {{"commonkey", "commonvalue"}}, 1000, 10});
    trace_sdk::BatchSpanProcessorOptions options{};
    // When queue is half full, Export will be called immediately even if the schedule_delay_millis is not reached.
    // But spans can still be lost if they are generated too fast that the queue is full before the Export finishes.
    // TODO(jiayiwang): Error: batch_span_processor.cc:71 BatchSpanProcessor queue is full - dropping span.
    // We are not able to catch and monitor this error. Figure out how to monitor.
    options.max_queue_size = kBatchSize * 10;
    // Time interval (in ms) between two consecutive exports.
    options.schedule_delay_millis = std::chrono::milliseconds(kExportPeriod);
    // We export `kNumSpans` after every `schedule_delay_millis` milliseconds.
    options.max_export_batch_size = kBatchSize;
    auto odl_processor = trace_sdk::BatchSpanProcessorFactory::Create(std::move(odl_exporter), options);

    // AlwaysOn sampler by default.
    return trace_sdk::TracerProviderFactory::Create(std::move(odl_processor), resource);

    // Parentbased Alwaysoff sampler.
    //return trace_sdk::TracerProviderFactory::Create(
    //    std::move(odl_processor), resource,
    //    std::move(trace_sdk::ParentBasedSamplerFactory::Create(std::move(trace_sdk::AlwaysOffSamplerFactory::Create()))));
}

void SendGenevaAndOdl(std::shared_ptr<trace_api::Tracer> odl_tracer, std::shared_ptr<trace_api::Tracer> geneva_tracer) {
    auto geneva_span = geneva_tracer->StartSpan("GenevaSpan");
    auto odl_span = odl_tracer->StartSpan("OdlSpan");
    opentelemetry::v1::exporter::etw::Span* etwSpan;
    etwSpan = dynamic_cast<opentelemetry::v1::exporter::etw::Span*>(geneva_span.get());
    auto etwAttributes = etwSpan->GetAttributes();
    // Simple span exporter also exports async after span is ended, when these attrs expire.
    // Need to find a way to hold these data in the span as string, not string_view.
    if (etwAttributes.find("env_dt_spanId") != etwAttributes.end())
    {
        odl_span->SetAttribute("otel_spanId", etwAttributes["env_dt_spanId"].ToAttributeValue());
    }
    if (etwAttributes.find("env_dt_traceId") != etwAttributes.end())
    {
        odl_span->SetAttribute("otel_traceId", etwAttributes["env_dt_traceId"].ToAttributeValue());
    }
    if (etwAttributes.find("parentId") != etwAttributes.end())
    {
        odl_span->SetAttribute("otel_parentId", etwAttributes["parentId"].ToAttributeValue());
    }

    odl_span->End();
    geneva_span->End();
} 

int main()
{
    try
    {
        TcpServer server(9527);
        std::this_thread::sleep_for(std::chrono::seconds(2));
        std::unique_ptr<trace_api::TracerProvider> odl_provider = CreateOdlTracerProvider();
        auto odl_tracer = odl_provider->GetTracer("foo_library", OPENTELEMETRY_SDK_VERSION);

        // geneva trace
        auto geneva_provider = std::make_unique<opentelemetry::exporter::etw::TracerProvider>();
        auto geneva_tracer = geneva_provider->GetTracer("NanoProxy-Geneva-Etw-Session");

        SendGenevaAndOdl(odl_tracer, geneva_tracer);
        LoadTest(odl_tracer);

        //CallUnderSampledSpan();
        // Wait for export finish.
        std::this_thread::sleep_for(std::chrono::seconds(120));
    }
    catch (std::exception& e)
    {
        std::cerr << e.what() << std::endl;
    }

    return 0;
}