﻿// <copyright file="FakeRegistryKey.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.IO;

using Microsoft.M365.Core.Telemetry.Enrichment;

namespace Microsoft.M365.Core.Telemetry.TestCommon
{
    internal class FakeRegistryKey : IRegistryKey
    {
        public static Dictionary<string, object> Registries;

        private string currentPath;

        public bool RemoveVariantKey;

        public const string DefaultBuildVersion = "15.20.5678.000";

        public static void ResetRegistries()
        {
            Registries = new Dictionary<string, object>(StringComparer.OrdinalIgnoreCase)
            {
                { B2PassiveEnricherDimensions.DeployRing, "DeployRing" },
                { B2PassiveEnricherDimensions.Role, "Role" },
                { B2PassiveEnricherDimensions.Forest, "Forest" },
                { B2PassiveEnricherDimensions.Region, "Region" },
                { DimensionValues.AvailabilityGroupRegistryName, "AvailabilityGroup" },
                { DimensionValues.ProductMajor, 15 },
                { DimensionValues.ProductMinor, 20 },
                { DimensionValues.BuildMajor, 5678 },
                { DimensionValues.BuildMinor, 0 },
                { DimensionValues.EnablePassiveECSAADAuthName, "false" }
            };
        }

        public FakeRegistryKey()
        {
            this.currentPath = string.Empty;
        }

        public FakeRegistryKey(string path)
        {
            this.currentPath = path;
        }

        public object GetValue(string name, object defaultValue = null)
        {
            if (currentPath == @"SOFTWARE\Microsoft\ExchangeServer\v15\VariantConfiguration" || currentPath == @"SOFTWARE\Microsoft\ExchangeServer\v15\Setup" || currentPath == @"SOFTWARE\Microsoft\ExchangeServer\V15\Diagnostics\Topology")
            {
                if (Registries.TryGetValue(name, out object result))
                {
                    return result;
                }
            }
            return defaultValue;
        }

        public IRegistryKey OpenSubKey(string path)
        {
            if (path == @"SOFTWARE\Microsoft\ExchangeServer\v15\VariantConfiguration" && RemoveVariantKey)
            {
                return null;
            }
            else
            {
                return new FakeRegistryKey(Path.Combine(currentPath, path));
            }
        }
    }
}
