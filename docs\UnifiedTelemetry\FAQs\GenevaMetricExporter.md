# Geneva Exporter for Metrics

## How do I see percentiles, <PERSON>/<PERSON> in Jarvis?
Percentiles, Min/Max are only supported when using OpenTelemetry Histogram instruments and must be [explicitly enabled](https://eng.ms/docs/products/geneva/metrics/management/metricminmaxpercentiles). [OpenTelemetry Histogram tutorial](https://eng.ms/docs/products/geneva/metrics/appmetrictutorial/appmetric-otel-histogram) is an end-to-end tutorial demonstrating this. Note that double values histograms are not supported in Geneva today.

## The percentiles seem incorrect. How do I fix them ?
If you are seeing that percentiles are not accurate (eg: you notice P99 value is greater than Max, P99 is a constant value etc.), it has to do with how percentiles work in Geneva.

Follow [this tutorial](https://eng.ms/docs/products/geneva/metrics/appmetrictutorial/appmetric-otel-histogram-percentiles) to learn more about this and how to fix.

You can learn more about histograms and their role in calculating percentiles by reading OpenTelemetry blog posts [Why Histograms?](https://opentelemetry.io/blog/2023/why-histograms/), [Histogram vs Summaries](https://opentelemetry.io/blog/2023/histograms-vs-summaries/).

## How do I send metrics to different accounts/namespaces other than what's specified in the Configuration?
By default, R9 metrics would be sent to the account/namespace values provided in the [Configuration](../Onboarding/MeteringSolution/GuideSteps/Configuration.md). There is a feature in Geneva Exporter to enable sending specific metrics to different accounts/namespaces by overriding the settings with custom tags:

- Add the dimension _microsoft_metrics_account and provide a string value for it as the destination account.
- Add the dimension _microsoft_metrics_namespace and provide a string value for it as the destination namespace.
  
With that, those metrics that have the custom tags will only be sent to the specified account/namespace. Note that this feature requires >= 1.5.0 version of Geneva Exporter and >= v2.2023.316.006 of MetricsExtension.

Currently, this feature is only supported in [**Direct creation**](../Onboarding/MeteringSolution/GuideSteps/Instrumentation.md#direct-creation) way when creating a metric and record measurement. **Fast metering** doesn't support metric accounts/namespaces override by dimensions yet.