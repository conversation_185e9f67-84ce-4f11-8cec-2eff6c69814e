<?xml version="1.0" encoding="utf-8"?>
<Project xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  
  <PropertyGroup>
    <MSBuildAllProjects>$(MSBuildAllProjects);$(MSBuildThisFileFullPath)</MSBuildAllProjects>

    <_SourceRootDirLength>$(ProjectDirRelativeToBaseDir.TrimStart('\').IndexOf('\'))</_SourceRootDirLength>
    <SourceRootDir Condition="'$(_SourceRootDirLength)' != '-1'">$(ProjectDirRelativeToBaseDir.TrimStart('\').Substring(0, $(_SourceRootDirLength)))</SourceRootDir>

    <SourceRootBuildProps Condition="'$(SourceRootDir)' != '' And '$(SourceRootBuildProps)' == ''">$(SourceRootDir).$(MSBuildThisFile)</SourceRootBuildProps>
  </PropertyGroup>
  
  <!-- NCrunch needs to be told to copy these files to its working environment -->
  <ItemGroup Condition="'$(NCrunch)'=='1'">
    <None Include="$(EnlistmentRoot)\build\**\*.*" />
  </ItemGroup>

  <Import Project="CloudBuild\BuildEnvironment.props" />



  <Import Project="Common\AssemblyInfo.props" />
  <Import Project="CloudBuild\CodeSign.props" Condition="'$(Build_IsSigned)' == 'true'" />
  <Import Project="$(SourceRootBuildProps)" Condition="'$(SourceRootBuildProps)' != '' and Exists('$(SourceRootBuildProps)')" />

  <Import Project="Common\Packaging.props" Condition="'$(IsPacked)' == 'true'" />

</Project>