﻿// <copyright file="RegistryAccessMask.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System;

namespace Microsoft.M365.Core.Telemetry.Enrichment
{
    /// <summary>
    /// RegistryAccessMask flags
    /// </summary>
    [Flags]
    internal enum RegistryAccessMask : uint
    {
        AllAccess = 0xF003F,
        CreateLink = 0x0020,
        CreateSubKey = 0x0004,
        EnumerateSubKeys = 0x0008,
        Execute = Read,
        Notify = 0x0010,
        QueryValue = 0x0001,
        Read = 0x20019,
        SetValue = 0x0002,
        Wow6432Key = 0x0200,
        Wow6464Key = 0x0100,
        Write = 0x20006,
        WriteDac = 0x40000,
    }
}
