﻿// <copyright file="ECSManagedServiceCollection.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System;
using System.Threading;

using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.M365.Core.Telemetry.SDKLogger;
using Microsoft.R9.Extensions.Logging;
using Microsoft.R9.Extensions.Logging.Exporters;
using Microsoft.R9.Extensions.Metering;

namespace Microsoft.M365.Core.Telemetry.ECSClient
{
    /// <summary>
    /// ECSManagedServiceCollection
    /// </summary>
    internal class ECSManagedServiceCollection : IManagedServiceCollection
    {
        /// <summary>
        /// Singleton of the instance
        /// </summary>
        private static ECSManagedServiceCollection instance;

        /// <summary>
        /// Singleton of the host
        /// </summary>
        private IServiceProvider serviceProvider;

        /// <summary>
        /// Singleton of the V2 host.
        /// </summary>
        private IServiceProvider serviceProviderV2;

        /// <summary>
        /// The public accessor of serviceProviderV2.
        /// </summary>
        public IServiceProvider ServiceProviderV2 { get => serviceProviderV2; }

        /// <summary>
        /// Lock for instance
        /// </summary>
        private ReaderWriterLockSlim instanceLock = new ReaderWriterLockSlim();

        /// <summary>
        /// Instance
        /// </summary>
        public static ECSManagedServiceCollection Instance
        {
            get
            {
                if (instance == null)
                {
                    instance = new ECSManagedServiceCollection();
                }

                return instance;
            }
        }

        /// <summary>
        /// SetService
        /// </summary>
        /// <param name="service"></param>
        public void SetService(IServiceProvider service)
        {
            this.serviceProvider = service;
            this.serviceProviderV2 = service;
        }

        /// <summary>
        /// GetMeter
        /// </summary>
        /// <typeparam name="T">T</typeparam>
        /// <returns></returns>
        public IMeter<T> GetMeter<T>()
        {
            this.instanceLock.EnterReadLock();
            try
            {
                return serviceProvider.GetRequiredService<IMeter<T>>();
            }
            finally
            {
                this.instanceLock.ExitReadLock();
            }
        }

        /// <summary>
        /// GetMeter
        /// </summary>
        /// <typeparam name="T">T</typeparam>
        /// <returns></returns>
        public IMeter<T> GetMeterV2<T>()
        {
            this.instanceLock.EnterReadLock();
            try
            {
                return serviceProviderV2.GetRequiredService<IMeter<T>>();
            }
            finally
            {
                this.instanceLock.ExitReadLock();
            }
        }

        /// <summary>
        /// GetLogger
        /// </summary>
        /// <typeparam name="T">T</typeparam>
        /// <returns></returns>
        public ILogger<T> GetLogger<T>()
        {
            this.instanceLock.EnterReadLock();
            try
            {
                return serviceProvider.GetRequiredService<ILoggerFactory>().CreateLogger<T>();
            }
            finally
            {
                this.instanceLock.ExitReadLock();
            }
        }

        /// <summary>
        /// GetLogger
        /// </summary>
        /// <typeparam name="T">T</typeparam>
        /// <returns></returns>
        public ILogger<T> GetLoggerV2<T>()
        {
            this.instanceLock.EnterReadLock();
            try
            {
                return serviceProviderV2.GetRequiredService<ILoggerFactory>().CreateLogger<T>();
            }
            finally
            {
                this.instanceLock.ExitReadLock();
            }
        }

        /// <summary>
        /// UpdateInstance
        /// </summary>
        /// <param name="option">option</param>
        public void UpdateInstance(IConfigurationRoot option)
        {
            SDKLog.Info("Update ECS Managed SC start.");
            IServiceProvider tmp = null;
            try
            {
                tmp = new HostBuilder()
                .ConfigureLogging(builder =>
                {
                    _ = builder
                        .AddOpenTelemetryLogging()
                        .AddGenevaExporter(option.GetSection("GenevaLogging"));
                })
                .ConfigureServices(services =>
                {
                    _ = services
                        .AddGenevaMetering(option.GetSection("GenevaMetering"));
                    Enrichment.CommonInit.InitSubstrateEnrichers(services);
                })
                .Build().Services;
            }
            catch (Exception ex)
            {
                SDKLog.Error($"Error when update the ECSManagedServiceCollection: {ex.Message}");
                return;
            }
            if (tmp != null)
            {
                this.instanceLock.EnterWriteLock();
                this.serviceProvider = tmp;
                this.instanceLock.ExitWriteLock();
                SDKLog.Info("Update ECS Managed SC success.");
            }
        }

        /// <summary>
        /// UpdateInstance
        /// </summary>
        /// <param name="option">option</param>
        public void UpdateInstanceV2(IConfigurationRoot option)
        {
            SDKLog.Info("Update ECS Managed SC V2 start.");
            IServiceProvider tmp = null;
            try
            {
                tmp = new HostBuilder()
                .ConfigureLogging(builder =>
                {
                    _ = builder
                        .AddOpenTelemetryLogging()
                        .AddGenevaExporter(option.GetSection("GenevaLogging"));
                })
                .ConfigureServices(services =>
                {
                    _ = services
                        .AddGenevaMetering(option.GetSection("GenevaMetering"));
                    Enrichment.CommonInit.InitSubstrateEnrichers(services);
                })
                .Build().Services;
            }
            catch (Exception ex)
            {
                SDKLog.Error($"Error when update the ECSManagedServiceCollection V2: {ex.Message}");
                return;
            }
            if (tmp != null)
            {
                this.instanceLock.EnterWriteLock();
                this.serviceProviderV2 = tmp;
                this.instanceLock.ExitWriteLock();
                SDKLog.Info("Update ECS Managed SC V2 success.");
            }
        }
    }
}
