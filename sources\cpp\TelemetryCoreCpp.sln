﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.12.35506.116
MinimumVisualStudioVersion = 10.0.40219.1
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "DemoApp", "dev\DemoApp\DemoApp.vcxproj", "{D7D0B355-FE24-4880-873B-DBC953BDA102}"
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "FileExporterCpp", "dev\FileExporterCpp\FileExporterCpp.vcxproj", "{E9DDFD22-06B4-4BE0-A7FC-6F1E66C38DB5}"
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ODLNRTMessageLib", "dev\ODLNRTMessageLib\ODLNRTMessageLib.vcxproj", "{D7D0B355-FE24-4880-873B-DBC953BDA103}"
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "AsioUtils", "dev\AsioUtils\AsioUtils.vcxproj", "{7062318E-CE9D-4C93-B616-20D096520E1E}"
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "OdlTraceExporterCpp", "dev\OdlTraceExporterCpp\OdlTraceExporterCpp.vcxproj", "{DFA75DFF-F6EA-45E8-90CE-7858EF65F042}"
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "AsioUtilsTest", "test\AsioUtilsTest\AsioUtilsTest.vcxproj", "{E6ADA230-C078-44B9-9456-729D98DF8E11}"
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "FileExporterCppTest", "test\FileExporterCppTest\FileExporterCppTest.vcxproj", "{57F5735F-871A-4F80-B9AA-12C0A63528BB}"
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "OdlTraceExporterCppTest", "test\OdlTraceExporterCppTest\OdlTraceExporterCppTest.vcxproj", "{DA9B5CF1-76A9-4436-BF4F-A09FE947FDCD}"
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "TcpServer", "test\TcpServer\TcpServer.vcxproj", "{738F538A-C392-40B8-A97F-7B2BAF9D7992}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|x64 = Debug|x64
		Debug|x86 = Debug|x86
		Release|x64 = Release|x64
		Release|x86 = Release|x86
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{D7D0B355-FE24-4880-873B-DBC953BDA102}.Debug|x64.ActiveCfg = Debug|x64
		{D7D0B355-FE24-4880-873B-DBC953BDA102}.Debug|x64.Build.0 = Debug|x64
		{D7D0B355-FE24-4880-873B-DBC953BDA102}.Debug|x86.ActiveCfg = Debug|Win32
		{D7D0B355-FE24-4880-873B-DBC953BDA102}.Debug|x86.Build.0 = Debug|Win32
		{D7D0B355-FE24-4880-873B-DBC953BDA102}.Release|x64.ActiveCfg = Release|x64
		{D7D0B355-FE24-4880-873B-DBC953BDA102}.Release|x64.Build.0 = Release|x64
		{D7D0B355-FE24-4880-873B-DBC953BDA102}.Release|x86.ActiveCfg = Release|Win32
		{D7D0B355-FE24-4880-873B-DBC953BDA102}.Release|x86.Build.0 = Release|Win32
		{E9DDFD22-06B4-4BE0-A7FC-6F1E66C38DB5}.Debug|x64.ActiveCfg = Debug|x64
		{E9DDFD22-06B4-4BE0-A7FC-6F1E66C38DB5}.Debug|x64.Build.0 = Debug|x64
		{E9DDFD22-06B4-4BE0-A7FC-6F1E66C38DB5}.Debug|x86.ActiveCfg = Debug|x64
		{E9DDFD22-06B4-4BE0-A7FC-6F1E66C38DB5}.Debug|x86.Build.0 = Debug|x64
		{E9DDFD22-06B4-4BE0-A7FC-6F1E66C38DB5}.Release|x64.ActiveCfg = Release|x64
		{E9DDFD22-06B4-4BE0-A7FC-6F1E66C38DB5}.Release|x64.Build.0 = Release|x64
		{E9DDFD22-06B4-4BE0-A7FC-6F1E66C38DB5}.Release|x86.ActiveCfg = Release|x64
		{E9DDFD22-06B4-4BE0-A7FC-6F1E66C38DB5}.Release|x86.Build.0 = Release|x64
		{D7D0B355-FE24-4880-873B-DBC953BDA103}.Debug|x64.ActiveCfg = Debug|x64
		{D7D0B355-FE24-4880-873B-DBC953BDA103}.Debug|x64.Build.0 = Debug|x64
		{D7D0B355-FE24-4880-873B-DBC953BDA103}.Debug|x86.ActiveCfg = Debug|x64
		{D7D0B355-FE24-4880-873B-DBC953BDA103}.Debug|x86.Build.0 = Debug|x64
		{D7D0B355-FE24-4880-873B-DBC953BDA103}.Release|x64.ActiveCfg = Release|x64
		{D7D0B355-FE24-4880-873B-DBC953BDA103}.Release|x64.Build.0 = Release|x64
		{D7D0B355-FE24-4880-873B-DBC953BDA103}.Release|x86.ActiveCfg = Release|x64
		{D7D0B355-FE24-4880-873B-DBC953BDA103}.Release|x86.Build.0 = Release|x64
		{7062318E-CE9D-4C93-B616-20D096520E1E}.Debug|x64.ActiveCfg = Debug|x64
		{7062318E-CE9D-4C93-B616-20D096520E1E}.Debug|x64.Build.0 = Debug|x64
		{7062318E-CE9D-4C93-B616-20D096520E1E}.Debug|x86.ActiveCfg = Debug|x64
		{7062318E-CE9D-4C93-B616-20D096520E1E}.Debug|x86.Build.0 = Debug|x64
		{7062318E-CE9D-4C93-B616-20D096520E1E}.Release|x64.ActiveCfg = Release|x64
		{7062318E-CE9D-4C93-B616-20D096520E1E}.Release|x64.Build.0 = Release|x64
		{7062318E-CE9D-4C93-B616-20D096520E1E}.Release|x86.ActiveCfg = Release|x64
		{7062318E-CE9D-4C93-B616-20D096520E1E}.Release|x86.Build.0 = Release|x64
		{DFA75DFF-F6EA-45E8-90CE-7858EF65F042}.Debug|x64.ActiveCfg = Debug|x64
		{DFA75DFF-F6EA-45E8-90CE-7858EF65F042}.Debug|x64.Build.0 = Debug|x64
		{DFA75DFF-F6EA-45E8-90CE-7858EF65F042}.Debug|x86.ActiveCfg = Debug|x64
		{DFA75DFF-F6EA-45E8-90CE-7858EF65F042}.Debug|x86.Build.0 = Debug|x64
		{DFA75DFF-F6EA-45E8-90CE-7858EF65F042}.Release|x64.ActiveCfg = Release|x64
		{DFA75DFF-F6EA-45E8-90CE-7858EF65F042}.Release|x64.Build.0 = Release|x64
		{DFA75DFF-F6EA-45E8-90CE-7858EF65F042}.Release|x86.ActiveCfg = Release|x64
		{DFA75DFF-F6EA-45E8-90CE-7858EF65F042}.Release|x86.Build.0 = Release|x64
		{E6ADA230-C078-44B9-9456-729D98DF8E11}.Debug|x64.ActiveCfg = Debug|x64
		{E6ADA230-C078-44B9-9456-729D98DF8E11}.Debug|x64.Build.0 = Debug|x64
		{E6ADA230-C078-44B9-9456-729D98DF8E11}.Debug|x86.ActiveCfg = Debug|x64
		{E6ADA230-C078-44B9-9456-729D98DF8E11}.Debug|x86.Build.0 = Debug|x64
		{E6ADA230-C078-44B9-9456-729D98DF8E11}.Release|x64.ActiveCfg = Release|x64
		{E6ADA230-C078-44B9-9456-729D98DF8E11}.Release|x64.Build.0 = Release|x64
		{E6ADA230-C078-44B9-9456-729D98DF8E11}.Release|x86.ActiveCfg = Release|x64
		{E6ADA230-C078-44B9-9456-729D98DF8E11}.Release|x86.Build.0 = Release|x64
		{57F5735F-871A-4F80-B9AA-12C0A63528BB}.Debug|x64.ActiveCfg = Debug|x64
		{57F5735F-871A-4F80-B9AA-12C0A63528BB}.Debug|x64.Build.0 = Debug|x64
		{57F5735F-871A-4F80-B9AA-12C0A63528BB}.Debug|x86.ActiveCfg = Debug|x64
		{57F5735F-871A-4F80-B9AA-12C0A63528BB}.Debug|x86.Build.0 = Debug|x64
		{57F5735F-871A-4F80-B9AA-12C0A63528BB}.Release|x64.ActiveCfg = Release|x64
		{57F5735F-871A-4F80-B9AA-12C0A63528BB}.Release|x64.Build.0 = Release|x64
		{57F5735F-871A-4F80-B9AA-12C0A63528BB}.Release|x86.ActiveCfg = Release|x64
		{57F5735F-871A-4F80-B9AA-12C0A63528BB}.Release|x86.Build.0 = Release|x64
		{DA9B5CF1-76A9-4436-BF4F-A09FE947FDCD}.Debug|x64.ActiveCfg = Debug|x64
		{DA9B5CF1-76A9-4436-BF4F-A09FE947FDCD}.Debug|x64.Build.0 = Debug|x64
		{DA9B5CF1-76A9-4436-BF4F-A09FE947FDCD}.Debug|x86.ActiveCfg = Debug|x64
		{DA9B5CF1-76A9-4436-BF4F-A09FE947FDCD}.Debug|x86.Build.0 = Debug|x64
		{DA9B5CF1-76A9-4436-BF4F-A09FE947FDCD}.Release|x64.ActiveCfg = Release|x64
		{DA9B5CF1-76A9-4436-BF4F-A09FE947FDCD}.Release|x64.Build.0 = Release|x64
		{DA9B5CF1-76A9-4436-BF4F-A09FE947FDCD}.Release|x86.ActiveCfg = Release|x64
		{DA9B5CF1-76A9-4436-BF4F-A09FE947FDCD}.Release|x86.Build.0 = Release|x64
		{738F538A-C392-40B8-A97F-7B2BAF9D7992}.Debug|x64.ActiveCfg = Debug|x64
		{738F538A-C392-40B8-A97F-7B2BAF9D7992}.Debug|x64.Build.0 = Debug|x64
		{738F538A-C392-40B8-A97F-7B2BAF9D7992}.Debug|x86.ActiveCfg = Debug|x64
		{738F538A-C392-40B8-A97F-7B2BAF9D7992}.Debug|x86.Build.0 = Debug|x64
		{738F538A-C392-40B8-A97F-7B2BAF9D7992}.Release|x64.ActiveCfg = Release|x64
		{738F538A-C392-40B8-A97F-7B2BAF9D7992}.Release|x64.Build.0 = Release|x64
		{738F538A-C392-40B8-A97F-7B2BAF9D7992}.Release|x86.ActiveCfg = Release|x64
		{738F538A-C392-40B8-A97F-7B2BAF9D7992}.Release|x86.Build.0 = Release|x64
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
EndGlobal
