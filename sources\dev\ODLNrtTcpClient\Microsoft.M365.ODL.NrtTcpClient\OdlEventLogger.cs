﻿// <copyright file="OdlEventLogger.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System.Diagnostics;
using System.Diagnostics.CodeAnalysis;

namespace Microsoft.M365.ODL.NrtTcpClient
{
    /// <summary>
    /// The ODL logger with EventLog
    /// </summary>
    [ExcludeFromCodeCoverage]
    public class OdlEventLogger
    {
        /// <summary>
        /// TcpClientEventSource
        /// </summary>
        public const string TcpClientEventSource = "ODL";

        /// <summary>
        /// the event log name
        /// </summary>
        public const string EventLogname = @"OfficeDataLoader";

        /// <summary>
        /// The singleton instance
        /// </summary>
        private static OdlEventLogger instance = new OdlEventLogger();

        private OdlEtwLogger etwLogger = null;

        /// <summary>
        /// The  member variable for the EventLog 
        /// </summary>
        private EventLog evtLogger = null;

        /// <summary>
        /// Ctor, in some special cases, there might be failure for creating Event logger or ETW logger
        /// </summary>
        private OdlEventLogger()
        {
            try
            {
                etwLogger = OdlEtwLogger.Instance;
            }
            catch
            {
                etwLogger = null;
            }

            try
            {
                evtLogger = new EventLog(EventLogname, ".", TcpClientEventSource);
                evtLogger.MaximumKilobytes = 51200;
                evtLogger.ModifyOverflowPolicy(OverflowAction.OverwriteAsNeeded, 14);
            }
            catch (Exception)
            {
                evtLogger = null;
            }
        }

        /// <summary>
        /// Log message
        /// </summary>
        /// <param name="message"> message to be logged </param>
        /// <param name="id"> Log id </param>
        /// <param name="level"> log level </param>
        public static void Log(string message, int? id, LogLevel level)
        {
            const int MaxEventLength = 31839;

            try
            {
                WriteEvent(message, id, level);
                return;
            }

            // The message length limit is 31,839 bytes on current Windows (32,766 bytes on Windows operating systems before Windows Vista).
            // .Net will throw ArgumentException if the length is longer than 32,766, however it will throw Win32Exception if the lenght is between 31,839 and 32,766.
            // Here we assume by default that it is due to length and try to cut
            catch (Exception)
            {
                if (message.Length > MaxEventLength)
                {
                    message = message.Substring(0, MaxEventLength);
                }
            }

            // just a retry after before exception
            try
            {
                WriteEvent(message, id, level);
                return;
            }
            catch (Exception ex)
            {
                string errorMsg = $"Fail to write event log for log name {EventLogname},source {TcpClientEventSource}, error:{ex}";
                Console.WriteLine(errorMsg);

                try
                {
                    instance.etwLogger?.Log(LogLevel.Error, LogEventId.LoggingEventError, errorMsg);
                }
                catch { }
            }
        }

        /// <summary>
        /// Write event log.
        /// </summary>
        /// <param name="message"> message to be logged </param>
        /// <param name="id"> Log id </param>
        /// <param name="level"> log level </param>
        private static void WriteEvent(string message, int? id, LogLevel level)
        {
            if (id.HasValue)
            {
                instance.evtLogger?.WriteEntry(message, (EventLogEntryType)level, id.Value);
            }
            else
            {
                instance.evtLogger?.WriteEntry(message, (EventLogEntryType)level);
            }
        }
    }
}
