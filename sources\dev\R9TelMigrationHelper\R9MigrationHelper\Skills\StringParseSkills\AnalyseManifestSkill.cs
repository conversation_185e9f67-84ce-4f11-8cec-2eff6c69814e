﻿// <copyright file="AnalyseManifestSkill.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System.Text.Json;
using System.Xml;
using R9MigrationHelper.Model;
using R9MigrationHelper.Skills.GitSkills;

namespace R9MigrationHelper.Skills.StringParseSkills
{
    /// <summary>
    /// AnalyseManifestSkill
    /// </summary>
    public class AnalyseManifestSkill
    {
        private const string DiagDllName = "System.Diagnostics.DiagnosticSource.dll";
        private const string XSODllName = "Microsoft.Exchange.Data.StoreObjects.dll";
        private const string OAuthDllName = "Microsoft.Exchange.OAuth.UserMode.dll";
        private const string ADDllName = "Microsoft.Exchange.Data.Directory.dll";
        private const string CrItemType = "cr:itemType"; // Need be jumped in manifest
        private List<ManifestEntry> manifestEntries = new ();
        private readonly List<string> targetDlls = new () { XSODllName, OAuthDllName, ADDllName };
        private readonly List<string> validDirectories = new ();

        /// <summary>
        /// Get xml full destinations for generating xml entry
        /// </summary>
        /// <param name="manifestFilePath"></param>
        /// <returns></returns>
        public List<XmlFullDestination> GetXmlFullDestinations(string manifestFilePath)
        {
            GetValidDirectories();
            HandleManifestDrop(manifestFilePath);
            return AnalyseManifest();
        }

        /// <summary>
        /// Parse manifest file from could build
        /// Find all paths include target dlls but not diag dll and then store in ManifestEntries object
        /// </summary>
        /// <param name="manifestFilePath"></param>
        private void HandleManifestDrop(string manifestFilePath)
        {
            var manifest = JsonSerializer.Deserialize<Manifest>(File.ReadAllText(manifestFilePath));
            manifest!.CrFiles.Remove(CrItemType);
            var entries = manifest.CrFiles.Keys;
            var targetEntries = entries.Where(d => d.EndsWith(".dll", StringComparison.Ordinal) && targetDlls.Contains(d[(d.LastIndexOf('/') + 1)..]));
            var distinctFolders = targetEntries.Select(e => e[..e.LastIndexOf('/')]).Distinct();
            Console.WriteLine($"Distinct folders contain [{string.Join(" ", targetDlls.ToArray())}]: ({distinctFolders.Count()})");
            foreach (string folder in distinctFolders)
            {
                Console.WriteLine(folder);
            }
            foreach (string folder in distinctFolders)
            {
                var diagDllPath = $"{folder}/{DiagDllName}";
                if (!entries.Contains(diagDllPath)) // judge whether diag dll exists in the same folder
                {
                    var diagDllEntry = new ManifestEntry(diagDllPath);
                    manifestEntries.Add(diagDllEntry);
                }
            }
            manifestEntries = manifestEntries.Distinct().ToList();
            Console.WriteLine($"Number of folders contain {DiagDllName} before filter: {manifestEntries.Count}");
        }

        private void GetValidDirectories()
        {
            ReadADOFileSkill readADOFileSkill = new ReadADOFileSkill();
            var organization = "O365Exchange";
            var project = "O365 Core";
            var repo = "Substrate";
            var path = "/build/configuration/public/xsd/directories.xsd";
            var xsdContent = readADOFileSkill.ReadADOFile(organization, project, repo, path).Result;
            XmlDocument doc = new XmlDocument();
            doc.LoadXml(xsdContent);
            var validDirectoryNodes = doc.GetElementsByTagName("xsd:enumeration"); // This is a rough judgment, and can be further categorized if there is a need to differentiate based on deliverable
            foreach (XmlNode n in validDirectoryNodes)
            {
                validDirectories.Add(n.Attributes!["value"] !.Value.Trim());
            }
        }

        private List<XmlFullDestination> AnalyseManifest()
        {
            var outOfRange = manifestEntries.Where(e => e.RecommendedDeliverable == null)
                .Select(e => e.Prefix)
                .Distinct()
                .ToList();
            outOfRange.Sort();
            Console.WriteLine($"Paths out of range ({outOfRange.Count}): ");
            outOfRange.ForEach(Console.WriteLine);
            var toBeGenerated = manifestEntries.Where(e => e.RecommendedDeliverable != null)
                .Distinct()
                .ToList();
            Console.WriteLine($"Number of folders contain {DiagDllName} after filter: {toBeGenerated.Count}");
            return toBeGenerated
                .Where(e => validDirectories.Contains(e.Destination))
                .Select(e => new XmlFullDestination() { Root = e.RecommendedDeliverable!, Destination = e.Destination })
                .ToList();
        }
    }
}
