﻿// <copyright file="SamplerFilter.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System;
using System.Diagnostics;
using OpenTelemetry.Exporter.Filters.Internal;
using OpenTelemetry.Trace;

namespace OpenTelemetry.Exporter.Filters
{
    /// <summary>
    /// A Filter using sampler logic to deicde whether to filter.
    /// </summary>
    public class SamplerFilter : BaseFilter<Activity>
    {
        private const string Description = "A Filter using sampler logic to deicde whether to filter.";

        private readonly Sampler sampler;

        private bool disposed;

        /// <summary>
        /// Initializes a new instance of the <see cref="SamplerFilter"/> class.
        /// </summary>
        /// <param name="sampler">predefined sampler</param>
        public SamplerFilter(Sampler sampler)
        {
            Guard.ThrowIfNull(sampler, nameof(sampler));
            this.sampler = sampler;
        }

        public override string GetDescription()
        {
            return Description;
        }

        /// <summary>
        /// decide whether to filter data by the sampling result
        /// </summary>
        /// <param name="activity">completed activity</param>
        /// <returns></returns>
        public override bool ShouldFilter(Activity activity)
        {
            if (activity == null)
            {
                return false;
            }
            var samplingParameters = new SamplingParameters(
                default,
                activity.TraceId,
                activity.DisplayName,
                activity.Kind,
                activity.TagObjects,
                activity.Links);

            return this.sampler.ShouldSample(samplingParameters).Decision.Equals(SamplingDecision.RecordAndSample);
        }
        
        /// <summary>
        /// dispose the instance
        /// </summary>
        /// <param name="disposing"></param>
        protected override void Dispose(bool disposing)
        {
            if (!this.disposed)
            {
                if (disposing)
                {
                    (this.sampler as IDisposable)?.Dispose();
                }

                this.disposed = true;
            }
            base.Dispose(disposing);
        }
    }
}
