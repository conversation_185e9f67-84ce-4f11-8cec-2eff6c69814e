{"files.associations": {"memory": "cpp", "type_traits": "cpp", "xstring": "cpp", "algorithm": "cpp", "any": "cpp", "array": "cpp", "atomic": "cpp", "bit": "cpp", "bitset": "cpp", "cctype": "cpp", "charconv": "cpp", "chrono": "cpp", "clocale": "cpp", "cmath": "cpp", "codecvt": "cpp", "compare": "cpp", "complex": "cpp", "concepts": "cpp", "condition_variable": "cpp", "coroutine": "cpp", "csignal": "cpp", "cstdarg": "cpp", "cstddef": "cpp", "cstdint": "cpp", "cstdio": "cpp", "cstdlib": "cpp", "cstring": "cpp", "ctime": "cpp", "cwchar": "cpp", "deque": "cpp", "exception": "cpp", "resumable": "cpp", "filesystem": "cpp", "format": "cpp", "forward_list": "cpp", "fstream": "cpp", "functional": "cpp", "future": "cpp", "initializer_list": "cpp", "iomanip": "cpp", "ios": "cpp", "iosfwd": "cpp", "iostream": "cpp", "istream": "cpp", "iterator": "cpp", "limits": "cpp", "list": "cpp", "locale": "cpp", "map": "cpp", "mutex": "cpp", "new": "cpp", "numeric": "cpp", "optional": "cpp", "ostream": "cpp", "random": "cpp", "ratio": "cpp", "regex": "cpp", "set": "cpp", "shared_mutex": "cpp", "source_location": "cpp", "span": "cpp", "sstream": "cpp", "stdexcept": "cpp", "stdfloat": "cpp", "stop_token": "cpp", "streambuf": "cpp", "string": "cpp", "strstream": "cpp", "system_error": "cpp", "thread": "cpp", "tuple": "cpp", "typeindex": "cpp", "typeinfo": "cpp", "unordered_map": "cpp", "unordered_set": "cpp", "utility": "cpp", "variant": "cpp", "vector": "cpp", "xfacet": "cpp", "xhash": "cpp", "xiosbase": "cpp", "xlocale": "cpp", "xlocbuf": "cpp", "xlocinfo": "cpp", "xlocmes": "cpp", "xlocmon": "cpp", "xlocnum": "cpp", "xloctime": "cpp", "xmemory": "cpp", "xtr1common": "cpp", "xtree": "cpp", "xutility": "cpp", "*.ipp": "cpp", "cwctype": "cpp", "ranges": "cpp", "valarray": "cpp", "barrier": "cpp", "cfenv": "cpp", "cinttypes": "cpp", "csetjmp": "cpp", "cuchar": "cpp", "execution": "cpp", "expected": "cpp", "hash_map": "cpp", "hash_set": "cpp", "latch": "cpp", "memory_resource": "cpp", "numbers": "cpp", "queue": "cpp", "scoped_allocator": "cpp", "semaphore": "cpp", "spanstream": "cpp", "stack": "cpp", "stacktrace": "cpp", "syncstream": "cpp", "*.inc": "cpp", "xstddef": "cpp"}}