﻿// <copyright file="FilterTests.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Diagnostics.CodeAnalysis;
using System.Threading;
using OpenTelemetry.Exporter.Filters;
using OpenTelemetry.Trace;
using Xunit;
using Assert = Xunit.Assert;

namespace Microsoft.M365.Core.Telemetry.OpenTelemetry.Exporter.Filters.Test
{
    [ExcludeFromCodeCoverage]
    public class FilterTests
    {
        [Fact]
        public void SamplerFilter_SamplerDrop_FilterFail()
        {
            SamplerFilter filter = new SamplerFilter(new TestSampler());
            var activity = new Activity("fail");
            activity.AddTag("expectedResult", "fail");
            activity.Start();
            Assert.False(filter.ShouldFilter(activity));
        }

        [Fact]
        public void SamplerFilter_SamplerRecord_FilterSuccess()
        {
            SamplerFilter filter = new SamplerFilter(new TestSampler());
            var activity = new Activity("success");
            activity.AddTag("expectedResult", "success");
            activity.Start();
            Assert.True(filter.ShouldFilter(activity));
            filter.Dispose();
        }

        [Fact]
        public void SamplerFilter_FilterNullActivity_ThrowException()
        {
            SamplerFilter filter = new SamplerFilter(new TestSampler());
            Assert.False(filter.ShouldFilter(null));
        }

        [Fact]
        public void Filter_GetDescriptionAndName_Equal()
        {
            var smaplerFilter = new SamplerFilter(new TestSampler());

            Assert.Equal("A Filter using sampler logic to deicde whether to filter.", smaplerFilter.GetDescription());

            Assert.Equal("SamplerFilter", smaplerFilter.GetName());

            var specificTagFilter = new SpecificTagFilter(new Dictionary<string, string>
            {
                ["expectedResult"] = "fail",
            });

            Assert.Equal("A filter used to keep these data with one of the specified tags.", specificTagFilter.GetDescription());

            Assert.Equal("SpecificTagFilter", specificTagFilter.GetName());
        }

        [Fact]
        public void SpecificTagFilter_InvalidArgument_ThrowException()
        {
            Assert.Throws<ArgumentNullException>(() => new SpecificTagFilter(null));
        }

        [Fact]
        public void SpecificTagFilter_FilterNullActivity_ThrowException()
        {
            var filter = new SpecificTagFilter(new Dictionary<string, string>
            {
                ["expectedResult"] = "success",
            });
            Assert.False(filter.ShouldFilter(null));
        }

        [Fact]
        public void SpecificTagFilter_SamplerDrop_FilterFail()
        {
            var filter = new SpecificTagFilter(new Dictionary<string, string>
            {
                ["expectedResult"] = "success",
            });
            var activity = new Activity("fail");
            activity.AddTag("expectedResult", "fail");
            activity.Start();
            Assert.False(filter.ShouldFilter(activity));

            var activity2 = new Activity("fail");
            activity2.AddTag("noTargetTag", "fail");
            activity2.Start();
            Assert.False(filter.ShouldFilter(activity2));
        }

        [Fact]
        public void SpecificTagFilter_TagEmpty_FilterSuccess()
        {
            var filter = new SpecificTagFilter(new Dictionary<string, string>
            {
                ["expectedResult"] = null,
            });
            var activity = new Activity("success");
            activity.AddTag("expectedResult", null);
            activity.Start();
            Assert.True(filter.ShouldFilter(activity));

            var activity2 = new Activity("fail");
            activity2.AddTag("expectedResult", string.Empty);
            activity2.Start();
            Assert.True(filter.ShouldFilter(activity2));
        }

        [Fact]
        public void SpecificTagFilter_SamplerRecord_FilterSuccess()
        {
            var filter = new SpecificTagFilter(new Dictionary<string, string>
            {
                ["expectedResult"] = "success",
            });
            var activity = new Activity("success");
            activity.AddTag("expectedResult", "success");
            activity.Start();
            Assert.True(filter.ShouldFilter(activity));
            filter.Dispose();
        }
    }

    internal class TestSampler : Sampler
    {
        private long sampleCount;

        internal long SampleCount => sampleCount;

        public override SamplingResult ShouldSample(in SamplingParameters samplingParameters)
        {
            if (samplingParameters.Name.Equals("fail"))
            {
                return new SamplingResult(SamplingDecision.Drop);
            }
            else
            {
                Interlocked.Increment(ref sampleCount);
                return new SamplingResult(SamplingDecision.RecordAndSample);
            }
        }
    }
}
