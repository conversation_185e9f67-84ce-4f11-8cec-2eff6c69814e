﻿// <copyright file="Strategy.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.IO.Hashing;
using System.Linq;
using System.Text;
using System.Threading;
using Microsoft.Extensions.Logging.Abstractions;

namespace Microsoft.M365.Core.Telemetry.R9.Logging.Substrate.Sampler
{
    /// <summary>
    /// Abstract base class for sampling strategy.
    /// </summary>
    internal abstract class SamplingStrategy
    {
        /// <summary>
        /// Makes a sampling decision for the provided <paramref name="logEntry"/>.
        /// If sampled, keep the log. If not, discard the log.
        /// </summary>
        /// <param name="logEntry">The log entry used to make the sampling decision for.</param>
        /// <typeparam name="TState">The type of the log entry state.</typeparam>
        /// <returns><see langword="true" /> if the log record should be sampled; otherwise, <see langword="false" />.</returns>
        public abstract bool ShouldSample<TState>(in LogEntry<TState> logEntry);
    }
}
