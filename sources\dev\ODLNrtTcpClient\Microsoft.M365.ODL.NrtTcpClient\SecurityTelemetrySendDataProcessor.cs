﻿// <copyright file="SecurityTelemetrySendDataProcessor.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.Diagnostics.CodeAnalysis;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Microsoft.Office.BigData.DataLoader;

namespace Microsoft.M365.ODL.NrtTcpClient
{
    /// <summary>
    /// ISendDataProcessor for SecurityTelemetry scenario
    /// </summary>
    [ExcludeFromCodeCoverage]
    public class SecurityTelemetrySendDataProcessor : ISendDataProcessor
    {
        private IOdlLogger logger = OdlLogger.Instance;

        /// <summary>
        /// send timeout for sync data
        /// </summary>
        private int timeoutMillis;

        /// <summary>
        /// The ctor
        /// </summary>
        /// <param name="headerSyncData">the header sync request each time the TCP connection is rebuilt</param>
        /// <param name="timeoutMillis">send timeout for sync data</param>
        public SecurityTelemetrySendDataProcessor(ODLNRTRequest headerSyncData, int timeoutMillis)
        {
            this.HeaderSyncData = headerSyncData;
            this.timeoutMillis = timeoutMillis;
        }

        /// <summary>
        /// the header sync data
        /// </summary>
        public ODLNRTRequest HeaderSyncData { get; private set; }

        /// <summary>
        /// what to do after reconnect-- send the sync data
        /// </summary>
        /// <param name="currentThread">current thread for data sending</param>
        /// <returns>true if the operation end successfully</returns>
        public bool AfterReconnect(NrtTcpClientThread currentThread)
        {
            if (null == currentThread)
            {
                return false;
            }

            try
            {
                return currentThread.SendData(HeaderSyncData);
            }
            catch (Exception e)
            {
                logger.Log(LogLevel.Error, LogEventId.TcpClientCommonError, $"Error when sending sync header after connection established:{e}");
                return false;
            }
        }

        /// <summary>
        /// what to do after data sending
        /// </summary>
        /// <param name="currentThread">current thread for data sending</param>
        /// <param name="data">the data to be sent</param>
        /// <param name="response">the response after data sending</param>
        public void AfterSendData(NrtTcpClientThread currentThread, ODLNRTRequest data, ODLNRTResponse response)
        {
            return;
        }

        /// <summary>
        /// What to do before data sending
        /// </summary>
        /// <param name="currentThread">current thread for data sending</param>
        /// <param name="data">the data to be sent</param>
        public void BeforeSendData(NrtTcpClientThread currentThread, ODLNRTRequest data)
        {
            return;
        }
    }
}
