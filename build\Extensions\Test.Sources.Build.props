<?xml version="1.0" encoding="utf-8"?>
<!--
  This file is imported for all projects in 'tests' root directory
-->
<Project xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  
  <PropertyGroup>
    <MSBuildAllProjects>$(MSBuildAllProjects);$(MSBuildThisFileFullPath)</MSBuildAllProjects>
  </PropertyGroup>

  <!-- Enable instrumentation of the test directory when running our code coverage workflow -->
  <!-- Need to import this props manually if UT is not under test folder -->
  <PropertyGroup Condition=" ('$(GROUP)'=='test' or '$(QTestType)'!='') AND '$(EXCHANGE_CODE_COVERAGE_MODE)'!='true'">
    <QInstrumentForCoverage>false</QInstrumentForCoverage>
  </PropertyGroup>

  <Import Project="CloudBuild\Test.props" />
</Project>