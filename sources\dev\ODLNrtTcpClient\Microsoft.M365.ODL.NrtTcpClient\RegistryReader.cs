﻿// <copyright file="RegistryReader.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System.Diagnostics.CodeAnalysis;
using System.Text.RegularExpressions;
using Microsoft.Win32;

namespace Microsoft.M365.ODL.NrtTcpClient
{
    /// <summary>
    /// Util class for reading registry
    /// </summary>
    [ExcludeFromCodeCoverage]
    public static class RegistryReader
    {
        /// <summary>
        /// The windows registry path for nrt port
        /// </summary>
        public const string PortRegistryPath = @"SYSTEM\CurrentControlSet\Services\MSOfficeDataLoader";

        /// <summary>
        /// The windows registry key name for nrt port
        /// </summary>
        public const string PortRegistryKey = @"NRTTCPPort";

        /// <summary>
        /// Invalid port
        /// </summary>
        public const int InvalidPort = -1;

        /// <summary>
        /// the logger
        /// </summary>
        private static IOdlLogger logger = OdlLogger.Instance;

        /// <summary>
        /// Get Tcp port from registery with default RegistryPath and RegistryKey
        /// </summary>
        /// <returns>-1 if fail to read or parse the value from registry</returns>
        public static int GetTcpPort()
        {
            try
            {
                object? rawValue = GetRegistryValue();
                if (rawValue is int && IsValidPort((int)rawValue))
                {
                    return (int)rawValue;
                }
                else
                {
                    logger.Log(LogLevel.Error, LogEventId.RegistryReaderError, $"Read out invalid Tcp port from registry: {rawValue}");
                }
            }
            catch (Exception ex)
            {
                logger.Log(LogLevel.Error, LogEventId.RegistryReaderError, $"Fail to read Tcp port from registry, error is :{ex}");
            }

            return InvalidPort;
        }

        /// <summary>
        /// the port is valid or not
        /// </summary>
        /// <param name="port"></param>
        /// <returns></returns>
        public static bool IsValidPort(int port)
        {
            return port > 1024;
        }

        /// <summary>
        /// Get the value from the default RegistryPath and RegistryKey
        /// </summary>
        /// <returns>the value or null</returns>
        public static object? GetRegistryValue()
        {
            return GetRegistryValue(PortRegistryPath, PortRegistryKey);
        }

        /// <summary>
        /// Get value of <paramref name="keyName"/> from the registry path <paramref name="keyPath"/>
        /// </summary>
        /// <param name="keyPath">the key path</param>
        /// <param name="keyName">the key name</param>
        /// <returns>the value or null</returns>
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Interoperability", "CA1416:Validate platform compatibility", Justification = "Only support windows at this moment")]
        public static object? GetRegistryValue(string keyPath, string keyName)
        {
            RegistryKey baseKey = Registry.LocalMachine;
            keyPath = Regex.Replace(keyPath, "HKLM:\\\\", string.Empty, RegexOptions.IgnoreCase);

            using (RegistryKey? key = baseKey.OpenSubKey(keyPath))
            {
                if (key != null)
                {
                    return key.GetValue(keyName);
                }
            }

            return null;
        }
    }
}
