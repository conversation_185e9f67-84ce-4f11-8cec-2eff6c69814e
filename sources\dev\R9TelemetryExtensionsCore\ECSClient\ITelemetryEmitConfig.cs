﻿// <copyright file="ITelemetryEmitConfig.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System.Collections.Generic;

namespace Microsoft.M365.Core.Telemetry.ECSClient
{
    /// <summary>
    /// Hold the items which are reuqired to control emitting events&metrics.
    /// </summary>
    public interface ITelemetryEmitConfig
    {
        /// <summary>
        /// Get certain config of certain event.
        /// </summary>
        /// <typeparam name="T">The type to parse.</typeparam>
        /// <param name="eventName">The event name.</param>
        /// <param name="configName">The config name.</param>
        /// <returns>The parsed config value.</returns>
        public T QueryEventConfig<T>(string eventName, string configName);

        /// <summary>
        /// Get certain config of certain metric.
        /// </summary>
        /// <typeparam name="T">The type to parse.</typeparam>
        /// <param name="metricName"></param>
        /// <param name="configName"></param>
        /// <returns>The parsed config value.</returns>
        public T QueryMetricConfig<T>(string metricName, string configName);
    }
}
