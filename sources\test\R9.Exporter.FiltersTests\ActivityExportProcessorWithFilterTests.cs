﻿// <copyright file="ActivityExportProcessorWithFilterTests.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System;
using System.Diagnostics;
using System.Diagnostics.CodeAnalysis;
using System.Threading;
using Microsoft.Extensions.Options;
using Microsoft.R9.Extensions.Telemetry.Exporter.Filters;
using Microsoft.R9.Extensions.Tracing.Exporters;
using OpenTelemetry;
using OpenTelemetry.Exporter.Filters;
using Xunit;

namespace Microsoft.M365.Core.Telemetry.R9.Exporter.Filters.Test
{
    [ExcludeFromCodeCoverage]
    public class ProcessorWithFilterTests
    {
        private ProcessorTestFilter mockBaseFilter;
        IOptions<GenevaTraceExporterOptions> options;

        private Batch<Activity> mockBatch;
        private Activity mockActivity;

        public ProcessorWithFilterTests()
        {
            mockBaseFilter = new ProcessorTestFilter();
            mockActivity = CreateActivity("testProcessorSuccess", isSuccess: true);
            mockBatch = new Batch<Activity>(new Activity[2] { mockActivity, CreateActivity("testProcessorFailed", isSuccess: false) }, 2);
            options = Options.Create(new GenevaTraceExporterOptions
            {
                ConnectionString = "EtwSession=OpenTelemetry",
            });
        }

        private GenevaBatchActivityExportProcessorWithFilter CreateGenevaBatchActivityExportProcessorWithFilter()
        {
            return new GenevaBatchActivityExportProcessorWithFilter(
                options, mockBaseFilter);
        }

        private GenevaReentrantActivityExportProcessorWithFilter CreateGenevaReentrantActivityExportProcessorWithFilter()
        {
            return new GenevaReentrantActivityExportProcessorWithFilter(
                options, mockBaseFilter);
        }

        [Fact]
        public void CheckNullExporterAndFilter()
        {
            Assert.Throws<NullReferenceException>(() => new GenevaBatchActivityExportProcessorWithFilter(null, null));
            Assert.Throws<NullReferenceException>(() => new GenevaBatchActivityExportProcessorWithFilter(null, mockBaseFilter));
            Assert.Throws<ArgumentNullException>(() => new GenevaBatchActivityExportProcessorWithFilter(options, null));
            Assert.Throws<NullReferenceException>(() => new GenevaReentrantActivityExportProcessorWithFilter(null, null));
            Assert.Throws<NullReferenceException>(() => new GenevaReentrantActivityExportProcessorWithFilter(null, mockBaseFilter));
            Assert.Throws<ArgumentNullException>(() => new GenevaReentrantActivityExportProcessorWithFilter(options, null));
        }

        [Fact]
        public void GenevaBatchActivityExportProcessorWithFilter_OnEnd_DropTheFailActivityAndKeepSuccessOne()
        {
            // Arrange
            var batchActivityExportProcessorWithFilter = CreateGenevaBatchActivityExportProcessorWithFilter();

            // Act
            foreach (var item in mockBatch)
            {
                batchActivityExportProcessorWithFilter.OnEnd(
                   item);
            }

            // Shutdown(0) will trigger flush and return immediately, so let's sleep for a while
            Thread.Sleep(1_000);

            Assert.Equal(1, mockBaseFilter.SampleCount);

            //make sure will not impact by other tests 
            mockBaseFilter.ClearCount();
            batchActivityExportProcessorWithFilter.Dispose();
        }

        [Fact]
        public void GenevaReentrantActivityExportProcessorWithFilter_OnEnd_DropTheFailActivityAndKeepSuccessOne()
        {
            // Arrange
            var genevaReentrantActivityExportProcessorWithFilter = CreateGenevaReentrantActivityExportProcessorWithFilter();

            // Act
            foreach (var item in mockBatch)
            {
                genevaReentrantActivityExportProcessorWithFilter.OnEnd(
                   item);
            }

            Assert.Equal(1, mockBaseFilter.SampleCount);

            //make sure will not impact by other tests 
            mockBaseFilter.ClearCount();
            genevaReentrantActivityExportProcessorWithFilter.Dispose();
        }

        private Activity CreateActivity(string operationName, bool isSuccess)
        {
            Activity data = new Activity(operationName)
            {
                ActivityTraceFlags = ActivityTraceFlags.Recorded,
            };
            data.AddTag("expectedResult", isSuccess ? "success" : "fail");
            return data;
        }
    }

    internal class ProcessorTestFilter : BaseFilter<Activity>
    {
        private long sampleCount;

        internal long SampleCount => sampleCount;
        
        public override string GetDescription()
        {
            return "Filter for Test.";
        }

        public override bool ShouldFilter(Activity t)
        {
            if ("success".Equals(t.GetTagItem("expectedResult")))
            {
                Interlocked.Increment(ref sampleCount);
                return true;
            }
            return false;
        }

        public void ClearCount()
        {
            sampleCount = 0;
        }
    }
}
