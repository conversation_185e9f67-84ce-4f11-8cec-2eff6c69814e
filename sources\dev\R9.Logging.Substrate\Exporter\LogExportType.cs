// <copyright file="LogExportType.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

namespace Microsoft.M365.Core.Telemetry.R9.Logging.Substrate
{
    /// <summary>
    /// Specify exporter to use. Flags can be combined like (LogExportType.Geneva | LogExportType.OdlTcp), which means export to both.
    /// </summary>
    internal enum LogExportType
    {
        /// <summary>
        /// No export.
        /// </summary>
        None = 0,

        /// <summary>
        /// Export to Geneva.
        /// </summary>
        Geneva = 0b01,

        /// <summary>
        /// Export to OdlTcp.
        /// </summary>
        OdlTcp = 0b10,
    }
}
