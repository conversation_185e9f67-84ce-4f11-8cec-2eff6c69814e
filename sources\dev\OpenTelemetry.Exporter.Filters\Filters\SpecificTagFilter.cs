﻿// <copyright file="SpecificTagFilter.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.Diagnostics;
using OpenTelemetry.Exporter.Filters.Internal;

namespace OpenTelemetry.Exporter.Filters
{
    /// <summary>
    /// A filter used to keep these data with one of the specified tags
    /// </summary>
    public class SpecificTagFilter : BaseFilter<Activity>
    {
        private const string Description = "A filter used to keep these data with one of the specified tags.";

        private readonly IReadOnlyDictionary<string, string> filterMappings;
        
        public SpecificTagFilter(IReadOnlyDictionary<string, string> filterMappings)
        {
            Guard.ThrowIfNull(filterMappings);
            this.filterMappings = filterMappings;
        }

        public override string GetDescription()
        {
            return Description;
        }

        /// <summary>
        /// check if the tags contains one of the filter key value pairs, else return false and will be dropped.
        /// </summary>
        /// <param name="activity"></param>
        /// <returns></returns>
        public override bool ShouldFilter(Activity activity)
        {
            if (activity == null)
            {
                return false;
            }

            foreach (var kvp in activity.Tags)
            {
                if (this.filterMappings.TryGetValue(kvp.Key, out string value))
                {
                    return string.Equals(value ?? string.Empty, kvp.Value ?? string.Empty, StringComparison.Ordinal);
                }
            }

            return false;
        }

        /// <summary>
        /// Dispose instance
        /// </summary>
        /// <param name="disposing">Dispose or finalize</param>
        protected override void Dispose(bool disposing)
        {
            base.Dispose(disposing);
        }
    }
}
