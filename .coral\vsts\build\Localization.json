{"options": [{"enabled": false, "definition": {"id": "5d58cc01-7c75-450c-be18-a388ddb129ec"}, "inputs": {"branchFilters": "[\"+refs/heads/*\"]", "additionalFields": "{}"}}, {"enabled": false, "definition": {"id": "a9db38f9-9fdc-478c-b0f9-464221e58316"}, "inputs": {"workItemType": "Bug", "assignToRequestor": "true", "additionalFields": "{}"}}], "variables": {"applicationId": {"value": "99999999-9999-9999-9999-999999999999"}, "applicationKey": {"value": "AddApplicationKeyHere"}, "defaultOutputDirectory": {"value": "add\\default\\output\\directory\\here"}, "displayName": {"value": "AddDisplayNameHere"}, "resourceFilePaths": {"value": "AddResourceFilePathsHere"}, "resourceRootDirectory": {"value": "add\\resource\\root\\directory\\here"}, "system.debug": {"value": "false", "allowOverride": true}, "teamId": {"value": "9999"}}, "retentionRules": [{"branches": ["+refs/heads/*"], "artifacts": [], "artifactTypesToDelete": [], "daysToKeep": 30, "minimumToKeep": 10, "deleteBuildRecord": false, "deleteTestResults": false}], "tags": [], "jobAuthorizationScope": 1, "jobTimeoutInMinutes": 60, "jobCancelTimeoutInMinutes": 5, "process": {"phases": [{"steps": [{"environment": {}, "enabled": true, "continueOnError": true, "alwaysRun": true, "displayName": "Task group: {{RepositoryName}} M365Coral Localization", "timeoutInMinutes": 0, "condition": "succeededOrFailed()", "task": {"id": "dae5f51d-85cc-4b33-a6bb-927c6d382f95", "versionSpec": "1.*", "definitionType": "metaTask"}, "inputs": {"applicationId": "$(applicationId)", "applicationKey": "$(applicationKey)", "defaultOutputDirectory": "$(defaultOutputDirectory)", "resourceRootDirectory": "$(resourceRootDirectory)", "teamId": "$(teamId)"}}], "name": "M365Coral Localization ", "refName": "Job_1", "condition": "succeeded()", "target": {"executionOptions": {"type": 0}, "allowScriptsAuthAccessOption": true, "type": 1}, "jobAuthorizationScope": 1, "jobCancelTimeoutInMinutes": 1}], "type": 1}, "repository": {"properties": {"cleanOptions": "0", "labelSources": "0", "labelSourcesFormat": "$(build.buildNumber)", "reportBuildStatus": "true", "gitLfsSupport": "false", "skipSyncSource": "false", "checkoutNestedSubmodules": "false", "fetchDepth": "0"}, "id": "{{RepositoryId}}", "type": "TfsGit", "name": "{{RepositoryName}}", "url": "{{RepositoryUrl}}", "defaultBranch": "refs/heads/master", "clean": "true", "checkoutSubmodules": false}, "processParameters": {}, "queue": {"pool": {"id": 11, "name": "Official"}, "id": 26, "name": "Official"}, "name": "{{RepositoryName}} Localization", "path": "\\{{RepositoryName}}\\Official Builds", "type": 2, "queueStatus": 0, "revision": 1}