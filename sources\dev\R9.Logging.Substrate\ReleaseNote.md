# Release Note for Substrate R9 Logging Extension

## 2025-04-28
### Version: 1.2.1
### Summary
Add EventSchema, ScenarioSchema and TraceSchema to R9.Logging.Substrate
### Details
- Add EventSchema, ScenarioSchema and TraceSchema to R9.Logging.Substrate
- These schemas was originally introduced in [Passive.bond](https://o365exchange.visualstudio.com/O365%20Core/_git/Root?path=/sources/Core/Monitoring/Passive/dev/Event/Passive.bond&_a=contents&version=GBmaster).
- By adding them to R9.Logging.Substrate, users don't need to depend of bond files to create their events.
### Pull Requests
- [[Minor] add schema for substrate logging](https://o365exchange.visualstudio.com/O365%20Core/_git/TelemetryCore/pullrequest/4139726)

## 2025-04-22
### Version: 1.2.0
### Summary
Migrate to ECS SDK v20 and support ECS AAD authentication with validation.
### Details
- Migrate ECS SDK from v16 to v20.
- Add validation for ECS related settings in different scenarios.
- Add default ECS behavior and allow customization.
- Support AAD Authentication in Model A/B/B2, Cosmic.
- Enhance sample apps for easy validation of ECS.
### Pull Requests
- [[Major] Enable AAD Authentication for Substrate Telemetry Extension](https://o365exchange.visualstudio.com/O365%20Core/_git/TelemetryCore/pullrequest/4127221)

## 2025-02-[Pending]
### Version: [Pending]
### Summary
Finish local end to end test. Ready for Beta.
### Details
- Simplify sample app for step-by-step guide.
- Force OdlTcp export format
- Enhance UT for legacy use.
- Fix usage on UT.
- Add note on enrichment to protect odl export schema unchanged.
### Pull Requests
- [[Minor] Sample for e2e test and some enhancement](https://o365exchange.visualstudio.com/O365%20Core/_git/TelemetryCore/pullrequest/3981076)

## 2025-02-10
### Version: 1.1.1-rc
### Summary
Introduce Configuration APIs.
Allow customizing ECS-related parameters in `appsettings.json`.
Bug fix.
### Details
- Introduce 2 APIs to fetch configuration from appsettings.json and ECS for DI and Non-DI scenarios specifically.
- Modifying the usage of ECS API. Switch to provide configuration sections to ECS API instead of parsing them and building ECSConfiguration object manually.
- Fix the issue that the `CompositeExporter` does not work as expected when adding new export.
### Pull Requests
- [[Minor] Configuration worker API](https://o365exchange.visualstudio.com/O365%20Core/_git/TelemetryCore/pullrequest/3892331)
- [[Minor]Allow customized identifiers](https://o365exchange.visualstudio.com/O365%20Core/_git/TelemetryCore/pullrequest/3925542)
- [[Minor] Logging - Refresh exporters when config updated](https://o365exchange.visualstudio.com/O365%20Core/_git/TelemetryCore/pullrequest/3950871)

## 2024-12
### Version: 1.1.0-rc
### Summary
Introduce `CompositeExporter`.
### Detail
- New `CompositeExporter` that wraps multiple exporters and routes log records to them according to the configuration. (Hard code to Geneva and OdlTcp for now)
- Use config `SubstrateLogging:UseCompositeExporter` to decide the configured exporter with `AddSubstrateLogging()` and `ConfigureSubstrateLogging()`. If not set, use the simple geneva exporter(keep behavior of previous versions).
- Pull Request: [[Major] Logging-  Composite Exporter for Substrate R9 Logging Extension](https://o365exchange.visualstudio.com/O365%20Core/_git/TelemetryCore/pullrequest/3871642)

## 2024-11-28
### Version: 1.0.1-rc
### Summary
Update R9 to 8.11.0

## 2024-11-11
### Version: 1.0.0-rc
### Summary
New package released.
### Details
- Add extension methods for `ILoggingBuilder` and `IServiceCollection` to configure logging.
- It configures Open Telemetry logging with a `Geneva Exporter`. The configuration is input from function parameter as `IConfiguration`.
- [Pull Request](https://o365exchange.visualstudio.com/O365%20Core/_git/TelemetryCore/pullrequest/3788197)
