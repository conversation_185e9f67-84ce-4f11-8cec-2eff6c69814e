﻿// <copyright file="TlsOptions.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System.Diagnostics.CodeAnalysis;
using System.Security.Cryptography.X509Certificates;

namespace Microsoft.M365.ODL.NrtTcpClient
{
    /// <summary>
    /// Option settings for m-Tls auth in NrtTcpClient
    /// </summary>
    [ExcludeFromCodeCoverage]
    public class TlsOptions
    {
        /// <summary>
        /// If current env is cosmic
        /// </summary>
        public bool IsCosmic { get; set; } = false;

        /// <summary>
        /// If current env is Cosmic NPE
        /// </summary>
        public bool IsNPE { get; set; } = false;

        /// <summary>
        /// Enable Tls switch
        /// </summary>
        public bool EnableTlsAuth { get; set; } = false;

        /// <summary>
        /// Tls certs folder path containing tls.pfx and tlsca.crt. 
        /// If this folder path is set, and no TlsCertFilePath or TlsCACertFilePath set,
        /// tls or ca cert will be loaded from this folder 
        /// according to a namepattern "*tls*.pfx" or "*ca*.crt".
        /// </summary>
        public string TlsCertsFolderPath { get; set; } = string.Empty;

        /// <summary>
        /// Tls pfx password, must be provide unless the pfx file is not password protected.
        /// </summary>
        public string TlsPfxPassword { get; set; } = string.Empty;

        /// <summary>
        /// Tls cert Path. e.g. "D:/tlsfolder/tls.pfx"
        /// If TlsCertFilePath is set, TlsCertsFolderPath will be ignored.
        /// </summary>
        public string TlsCertFilePath { get; set; } = string.Empty;

        /// <summary>
        /// Tls CA cert path. e.g. "D:/tlsfolder/tlsca.crt"
        /// If TlsCACertFilePath is set, TlsCertsFolderPath will be ignored.
        /// </summary>
        public string TlsCACertFilePath { get; set; } = string.Empty;

        /// <summary>
        /// Tls cert subject name.
        /// If TlsCertSubjectName is set, TlsCertsFolderPath will be ignored.
        /// cert must be installed in root store.
        /// </summary>
        public string TlsCertSubjectName { get; set; } = string.Empty;

        /// <summary>
        /// Gets or sets the Tls white list cert name to ignore remotecertnamemismatch error.
        /// </summary>
        public string TlsCertWhiteList { get; set; } = string.Empty;

        /// <summary>
        /// Store location for cert store
        /// </summary>
        public StoreLocation StoreLocation { get; set; } = StoreLocation.LocalMachine;

        /// <summary>
        /// Store name for cert store
        /// </summary>
        public StoreName StoreName { get; set; } = StoreName.Root;
    }
}
