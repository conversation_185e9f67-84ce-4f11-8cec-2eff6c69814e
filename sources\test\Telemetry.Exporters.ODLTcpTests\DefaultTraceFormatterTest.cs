﻿// <copyright file="DefaultTraceFormatterTest.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Globalization;
using Microsoft.M365.Core.Telemetry.Exporters.ODLTCP.Trace;
using Xunit;

namespace Microsoft.R9.Extensions.Telemetry.Exporters.ODLTcp.Test
{
    /// <summary>
    /// Test ActivitySerializer
    /// </summary>
    public class DefaultTraceFormatterTest
    {
        private Activity activity;
        DateTime startTime = DateTime.ParseExact("2024-06-07T08:48:56.6786515Z", format: "o", CultureInfo.InvariantCulture).ToUniversalTime();

        public DefaultTraceFormatterTest()
        {
            activity = new Activity("TestActivity");
            activity.SetStartTime(startTime);
            activity.SetEndTime(startTime.AddSeconds(1));
            activity.SetTag("tagstr", "value1");
            activity.SetTag("tagint", 1);
            activity.SetTag("tagbool", true);
            activity.SetTag("tagdouble", 1.1);
            activity.SetTag("taglong", 1L);
            activity.SetTag("tagObject", new Dictionary<string, string>());
            activity.AddEvent(new ActivityEvent("Event1", startTime, new ActivityTagsCollection(activity.TagObjects)));
        }

        [Fact]
        public void TestSerializeActivity_All()
        {
            var resString = DefaultTraceFormatter.Serialize(activity, new Dictionary<string, string>() { ["TestField"] = "TestValue" });
            Assert.Equal("{\"schemaVersion\":\"2.0\",\"traceId\":\"00000000000000000000000000000000\",\"spanId\":\"0000000000000000\",\"kind\":\"Internal\",\"status\":\"Unset\",\"operationName\":\"TestActivity\",\"displayName\":\"TestActivity\",\"source\":{\"Name\":\"\",\"Version\":\"\"},\"duration\":\"00:00:01\",\"startTimeUtc\":\"2024-06-07T08:48:56.6786515Z\",\"tags\":{\"tagstr\":\"value1\",\"tagint\":1,\"tagbool\":true,\"tagdouble\":1.1,\"taglong\":1,\"tagObject\":\"unsupported type\",\"TestField\":\"TestValue\"},\"events\":[{\"Name\":\"Event1\",\"Timestamp\":\"2024-06-07T08:48:56.6786515+00:00\",\"Tags\":{\"tagstr\":\"value1\",\"tagint\":1,\"tagbool\":true,\"tagdouble\":1.1,\"taglong\":1,\"tagObject\":\"unsupported type\"}}],\"parentId\":\"\"}", resString);
        }
    }
}
