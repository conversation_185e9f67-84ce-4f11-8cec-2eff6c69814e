class MyLogging
{
    /// <summary>
    /// Logger name
    /// </summary>
    internal const string LoggerName = "customized logger name";

    /// <summary>
    /// Create static logger
    /// </summary>
    internal static readonly Lazy<ILogger> Logger = new Lazy<ILogger>(() => LoggerFactoryInstance.Value.CreateLogger(LoggerName));

    /// <summary>
    /// create loggerfactory
    /// </summary>
    internal static readonly Lazy<ILoggerFactory> LoggerFactoryInstance = new Lazy<ILoggerFactory>(() =>
    {
        /// option 1: load from disk file
        IConfiguration logConfig = new ConfigurationBuilder().AddJsonFile("appsettings.json").Build();
        /// option 2: Load from JsonStream
        IConfiguration logConfig = new ConfigurationBuilder().AddJsonStream(new MemoryStream(Encoding.UTF8.GetBytes(configDataInJSON.ToString()))).Build();
        return LoggerFactory.Create(builder =>
        {
            /// This is the R9 initialization API provided by Unified Telemetry team in Microsoft.M365.Core.Telemetry.R9.Logging.Substrate.
            builder.ConfigureSubstrateLogging(logConfig);
        });
    });
}