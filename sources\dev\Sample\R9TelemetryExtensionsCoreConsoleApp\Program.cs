﻿// <copyright file="Program.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System;
using System.Threading;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.M365.Core.Telemetry.ECSClient;
using Microsoft.M365.Core.Telemetry.R9;
using Microsoft.M365.Core.Telemetry.SampleConsoleApp;
using Microsoft.M365.Core.Telemetry.SDKLogger;

var myMeter = R9Host.GetMeter<Program>();
var myLogger = R9Host.CreateLogger<Program>();
var requests = Metric.CreateRequests(myMeter);

requests.Add(1, "OK");
Log.Foo(myLogger, "hello", 123);

SDKLog.Info("SDK log info");
SDKMeter.RecordSDKCounter("foo_event", "foo_type");

// test ECSClient
var config = R9Host.GetServices().GetService<IConfiguration>();
Console.WriteLine("R9MetricEnabled: {0}", R9Services.GetPassiveR9Config().R9MetricEnabled);
Console.WriteLine("ServiceName: {0}", UnifiedTelemetryECSClient.EcsClientInstance(config).ServiceName);
var tracingConfig = new R9TracingConfig(config);
Console.WriteLine("R9DTEnabled: {0}", tracingConfig.R9DTEnabled);
int counter = 0;
const int sleepTime = 5000;
while (true)
{
    Console.WriteLine("Running {0} ...", counter);
    counter++;
    Console.WriteLine("Sleeping for {0} ms", sleepTime);
    Thread.Sleep(sleepTime);
    Console.WriteLine("R9PassiveConfig: {0}", R9Services.GetPassiveR9Config().R9MetricEnabled);
    Console.WriteLine("R9DTConfig: {0}", tracingConfig.R9DTEnabled);
}
