﻿// <copyright file="Deliverable.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml;

namespace R9MigrationHelper.Model
{
    /// <summary>
    /// Deliverable
    /// </summary>
    public class Deliverable
    {
        /// <summary>
        /// Languages
        /// </summary>
        public List<string> Languages { get; set; }

        /// <summary>
        /// Flavors
        /// </summary>
        public List<string> Flavors { get; set; }

        /// <summary>
        /// Platforms
        /// </summary>
        public List<string> Platforms { get; set; }

        /// <summary>
        /// Destinations
        /// </summary>
        public List<string> Destinations { get; set; }

        /// <summary>
        /// Paths
        /// </summary>
        public List<string> Paths { get; set; }

        /// <summary>
        /// Public constructor
        /// </summary>
        /// <param name="node"></param>
#pragma warning disable CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
        public Deliverable(XmlNode node)
#pragma warning restore CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
        {
            ParseDeliverable(node);
            GeneratePaths();
        }

        private void ParseDeliverable(XmlNode node)
        {
            var deliverableName = node.Name.Trim();
            switch (deliverableName)
            {
                case "PARTNERS":
                case "PARTNERS_TEST":
                case "TOOLS":
                    Languages = new () { "all" };
                    Flavors = new () { "debug", "retail" };
                    Platforms = new () { "amd64", "i386" };
                    break;

                case "PATCHES_EXCHANGE":
                    Languages = new () { "server" };
                    Flavors = new () { "debug", "retail" };
                    Platforms = new () { "amd64", "i386" };
                    break;

                case "PATCHES_UPDATE":
                    Languages = new () { "server" };
                    Flavors = new () { "debug", "retail" };
                    Platforms = new () { "amd64" };
                    break;

                case "PATCHES_MSFTE":
                    Languages = new () { "en" };
                    Flavors = new () { "debug", "retail" };
                    Platforms = new () { "amd64" };
                    break;

                case "HOLIDAYCALENDARS":
                    Languages = new () { "all" };
                    Flavors = new () { "retail" };
                    Platforms = new () { "amd64" };
                    break;

                case "VERSION_FILES":
                    Languages = new () { "en" };
                    Flavors = new () { "debug", "retail" };
                    Platforms = new () { "amd64", "i386" };
                    break;

                case "DATACENTEROPS":
                case "DATACENTERENV":
                case "INTERNALPACKAGES":
                case "PRODUCT_TEST" or "EXCHANGE14_TEST":
                case "FIPS":
                    Languages = new () { "all" };
                    Flavors = new () { "debug", "retail" };
                    Platforms = new () { "amd64" };
                    break;

                case "COMPONENTS":
                case "DEPLOYMENT":
                case "DATACENTER":
                case "PRODUCT":
                    Languages = new () { "all", "en" };
                    Flavors = new () { "debug", "retail" };
                    Platforms = new () { "amd64" };
                    break;

                case "DSL":
                case "EXCHANGE14":
                case "CORE":
                case "PATCHTRAIN":
                case "PRODUCT_CLIENT_LANGPACKS" or "EXCHANGE14_CLIENT_LANGPACKS":
                case "PRODUCT_SERVER_LANGPACKS" or "EXCHANGE14_SERVER_LANGPACKS":
                case "PRODUCT_OWAPLUS_LANGPACKS" or "OWAPLUS_LANGPACKS":
                    Languages = new () { "en" };
                    Flavors = new () { "debug", "retail" };
                    Platforms = new () { "amd64" };
                    break;
                default:
                    break;
            }
            ParseDestinations(node.SelectSingleNode("DESTINATIONS")!);
        }

        private void ParseDestinations(XmlNode node)
        {
            Destinations = new ();
            foreach (XmlNode destination in node)
            {
                Destinations.Add(destination.InnerText.Trim());
            }
        }

        private void GeneratePaths()
        {
            Paths = new ();
            foreach (var language in Languages)
            {
                foreach (var flavor in Flavors)
                {
                    foreach (var platform in Platforms)
                    {
                        foreach (var destination in Destinations)
                        {
                            Paths.Add($"{language}/{flavor}/{platform}/{destination}");
                        }
                    }
                }
            }
        }
    }
}
