﻿// <copyright file="DyeBasedSamplingTests.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System.Diagnostics;
using System.Text;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.M365.Core.Telemetry.R9.Tracing.AdvancedSampling;
using Microsoft.M365.Core.Telemetry.R9.Tracing.AdvancedSampling.DyeBasedTraceSampling;
using Newtonsoft.Json;
using OpenTelemetry;
using OpenTelemetry.Exporter.Filters;
using OpenTelemetry.Trace;
using Xunit;

namespace R9.Tracing.AdvancedSamplingTests
{
    /// <summary>
    /// DyeBasedSamplingTests
    /// </summary>
    public class DyeBasedSamplingTests
    {
        /// <summary>
        /// DyeBasedSamplingOption preparation
        /// </summary>
        /// <param name="jsonString"></param>
        /// <returns></returns>
        public static DyeBasedSamplingOption PrepareOption(string jsonString)
        {
            var config = new ConfigurationBuilder()
                            .AddJsonStream(new MemoryStream(Encoding.UTF8.GetBytes(jsonString)))
                            .Build();

            IConfigurationSection section = config.GetSection("Microsoft_m365_core_telemetry:DyeingSamplingOption");
            var option = new DyeBasedSamplingOption();
            section.Bind(option);
            section.Value = JsonConvert.SerializeObject(option);

            return option;
        }

/// <summary>
/// test DyeBasedFilter
/// </summary>
        [Fact]
        public void TestDyeBasedEnrichSample()
        {
            var activitySources = new HashSet<string> { "ActivitySource" };
            var sampleTags = new HashSet<string> { "SampleKey" };

            var option = new DyeBasedSamplingOption(activitySources, sampleTags);

            ActivitySource activitySource = new ActivitySource("ActivitySource");

            var exception = Record.Exception(() =>
            {
#pragma warning disable CS0618 // Type or member is obsolete
                using var host = new HostBuilder()
                                           .ConfigureServices(service => service
                                               .AddOpenTelemetryTracing(builder => builder
                                               .SetSampler(new AlwaysOnSampler())
                                               .AddDyeBasedSampling(option)
                                               .AddConsoleExporter(new BaggageFilter())))
                                           .Build();
#pragma warning restore CS0618 // Type or member is obsolete
            });

            var id = Activity.Current?.Id;
            var activity = activitySource.CreateActivity("activity", ActivityKind.Internal);
            activity?.SetTag("SampleKey", "SampleVal");
            activity?.Start();

            var baggage = Baggage.GetBaggage(Constants.SampleBaggageName);

            Assert.True(baggage == Constants.SampleBaggage);

            var activityChild = activitySource.CreateActivity("activityChild", ActivityKind.Internal);
            activityChild?.Start();

            var baggageChild = Baggage.GetBaggage(Constants.SampleBaggageName);

            Assert.True(baggageChild == Constants.SampleBaggage);
        }

        /// <summary>
        /// Test Dye Based Enrich with drop tags
        /// </summary>
        [Fact]
        public void TestDyeBasedEnrichDrop()
        {
            var activitySources = new HashSet<string> { "ActivitySource", "TestSource" };
            var dropTags = new HashSet<string> { "DropKey:DropVal" };
            var options = new DyeBasedSamplingOption(activitySources, null, dropTags);

            ActivitySource activitySource = new ActivitySource("ActivitySource");

            var exception = Record.Exception(() =>
            {
#pragma warning disable CS0618 // Type or member is obsolete
                using var host = new HostBuilder()
                                           .ConfigureServices(service => service
                                               .AddOpenTelemetryTracing(builder => builder
                                               .SetSampler(new AlwaysOnSampler())
                                               .AddDyeBasedSampling(options)
                                               .AddConsoleExporter(new BaggageFilter())))
                                           .Build();
#pragma warning restore CS0618 // Type or member is obsolete
            });

            Assert.Null(exception);

            var activityWithEmptyTags = activitySource.CreateActivity("activityWithEmptyTags", ActivityKind.Internal);
            activityWithEmptyTags?.Start();

            var baggageWithEmptyTags = Baggage.GetBaggage(Constants.SampleBaggageName);

            Assert.True(baggageWithEmptyTags == null);

            var activity = activitySource.CreateActivity("activity", ActivityKind.Internal);
            activity?.SetTag("DropKey", "DropVal");
            activity?.Start();

            var baggage = Baggage.GetBaggage(Constants.SampleBaggageName);

            Assert.True(baggage == Constants.DropBaggage);

            var activityChild = activitySource.CreateActivity("activityChild", ActivityKind.Internal);
            activityChild?.Start();

            var baggageChild = Baggage.GetBaggage(Constants.SampleBaggageName);

            Assert.True(baggageChild == Constants.DropBaggage);
        }

        /// <summary>
        /// Test Option and builder Null Excepetion
        /// </summary>
        [Fact]
        public void TestOptionNullExcepetion()
        {
            var nullConfigurationExcepetion = () =>
            {
#pragma warning disable CS0618 // Type or member is obsolete
                using var host = new HostBuilder()
                                           .ConfigureServices(service => service
                                               .AddOpenTelemetryTracing(builder => builder
                                               .AddDyeBasedSampling(null)
                                               .AddConsoleExporter(new BaggageFilter())))
                                           .Build();
#pragma warning restore CS0618 // Type or member is obsolete
            };

            Assert.Throws<ArgumentNullException>(nullConfigurationExcepetion);

            var nullBuilderExcepetion = () =>
            {
                DyeBasedSamplingExtension.AddDyeBasedSampling(null, null);
            };

            Assert.Throws<ArgumentNullException>(nullBuilderExcepetion);
        }

        /// <summary>
        /// Test Coner Coverage
        /// </summary>
        [Fact]
        public void TestConerCoverage()
        {
            BaggageFilter filter = new BaggageFilter();

            var activityWithPostBaggage = new Activity("activity");
            Baggage.SetBaggage(Constants.SampleBaggageName, Constants.SampleBaggage);
            Assert.True(filter.ShouldFilter(activityWithPostBaggage));

            var activitySources = new HashSet<string> { "ActivitySource" };
            var dropTags = new HashSet<string> { "DropKey:DropVal" };

            var option = new DyeBasedSamplingOption(activitySources, drop: dropTags);

            var exception = Record.Exception(() => DyeBasedTraceEnricher.EnrichDyeTraceBaggage(null, "OnStartActivity", null));

            Assert.Null(exception);
        }

        /// <inheritdoc/> Test contrustor
        [Fact]
        public void TestDyeBasedSamplingConstructor()
        {
            ECSOption eCSOption = new ECSOption();
            eCSOption.Sources.Add("Test");

            var option = new DyeBasedSamplingOption(eCSOption.Sources, eCSOption.Tags);

            var exception = Record.Exception(() =>
            {
#pragma warning disable CS0618 // Type or member is obsolete
                using var host = new HostBuilder()
                                           .ConfigureServices(service => service
                                               .AddOpenTelemetryTracing(builder => builder
                                               .SetSampler(new AlwaysOnSampler())
                                               .AddDyeBasedSampling(option)
                                               .AddConsoleExporter(new BaggageFilter())))
                                           .Build();
#pragma warning restore CS0618 // Type or member is obsolete
            });

            var activitySource = new ActivitySource("ActivitySource");
            var activity1 = activitySource.StartActivity("activity1");

            Assert.True(Baggage.GetBaggage(Constants.SampleBaggageName) == null);

            eCSOption.Tags.Add("SampleKeyA");

            var activity2 = activitySource.CreateActivity("activity2", ActivityKind.Internal);
            activity2?.SetTag("SampleKeyA", "SampleVal");
            activity2?.Start();

            Assert.True(Baggage.GetBaggage(Constants.SampleBaggageName) == Constants.SampleBaggage);
        }

        class ECSOption
        {
            public ISet<string> Sources { get; set; } = new HashSet<string>() { "ActivitySource" };

            public ISet<string> Tags { get; set; } = new HashSet<string>();
        }
    }
}
