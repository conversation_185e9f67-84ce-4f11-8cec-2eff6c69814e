// <copyright file="ODLTraceExporterOptionsInherited.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using Microsoft.R9.Extensions.Tracing.Exporters;

namespace Microsoft.M365.Core.Telemetry.R9.Tracing.Instrumentation.Accelerator
{
    /// <summary>
    /// ODLTraceExporterOptionsInherited
    /// </summary>
    public class ODLTraceExporterOptionsInherited : ODLTraceExporterOptions
    {
        /// <summary>
        /// IsEnabled
        /// </summary>
        public bool IsEnabled { get; set; } = true;

        /// <summary>
        /// IsEnabled is always false now as we have not implemented file logger where it will fall back in
        /// </summary>
#pragma warning disable CA1822 // Mark members as static
        public new bool EnableFallBack
#pragma warning restore CA1822 // Mark members as static
        {
            get { return false; }
        }

        /// <summary>
        /// BatchExport
        /// </summary>
        public bool BatchExport { get; set; } = true;
    }
}
