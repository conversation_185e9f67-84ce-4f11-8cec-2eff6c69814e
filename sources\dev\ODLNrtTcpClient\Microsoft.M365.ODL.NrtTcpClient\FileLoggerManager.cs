﻿// <copyright file="FileLoggerManager.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Microsoft.M365.Core.Telemetry.FileExporter;

namespace Microsoft.M365.ODL.NrtTcpClient
{
    /// <summary>
    /// Managing file logger for fallback to disk purpose
    /// </summary>
    public sealed class FileLoggerManager
    {
        /// <summary>
        /// Singleton file logger for each sdkSource, each NRTFileExporter needs to be thread-safe
        /// </summary>
        private static readonly Dictionary<string, NRTFileExporter> Loggers = new Dictionary<string, NRTFileExporter>();

        private static object loggersLock = new object();

        /// <summary>
        /// get file logger option for the given sdkSource
        /// </summary>
        /// <param name="sdkName">the sdk source</param>
        /// <param name="enableCleanUpArchivedFiles"></param>
        /// <param name="retentionInterval"></param>
        /// <param name="retentionCount"></param>
        /// <returns>the file logger</returns>
        public static TextFileLoggerOptions GetFileLoggerOptions(string sdkName, bool enableCleanUpArchivedFiles, TimeSpan retentionInterval, long retentionCount)
        {
            TextFileLoggerOptions options = new TextFileLoggerOptions();
            options.Directory = CommonSettings.GetDumpFolderofLogtype(sdkName);
            options.FileName = $"{sdkName}";
            options.FileExtension = @".log";
            options.StringFormatter = (str) => str;
            options.EnableCleanUpArchivedFiles = enableCleanUpArchivedFiles;
            options.RetentionInterval = retentionInterval;
            options.RetentionCount = retentionCount;
            return options;
        }

        /// <summary>
        /// Create a <see cref="ODLFileExporter"/> instance for the given sdk source
        /// </summary>
        /// <param name="sdkName">sdk name</param>
        /// <param name="enableCleanUpArchivedFiles"></param>
        /// <param name="retentionInterval"></param>
        /// <param name="retentionCount"></param>
        /// <returns>The configured <see cref="ODLFileExporter"/>.</returns>
        public static NRTFileExporter GetNRTFileExporter(string sdkName, bool enableCleanUpArchivedFiles, TimeSpan retentionInterval, long retentionCount)
        {
            if (string.IsNullOrEmpty(sdkName))
            {
                throw new ArgumentNullException($"SdkName should not be null or empty: {sdkName}");
            }

            NRTFileExporter result = null;
            lock (loggersLock)
            {
                if (!Loggers.TryGetValue(sdkName, out result))
                {
                    Loggers.Add(sdkName, result = FileExporterExtensions.CreatNRTFileExporter(GetFileLoggerOptions(sdkName, enableCleanUpArchivedFiles, retentionInterval, retentionCount)));
                }
            }

            return result;
        }
    }
}
