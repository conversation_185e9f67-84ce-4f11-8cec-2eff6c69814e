﻿// <copyright file="ReentrantExportProcessor.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System;
using System.Linq.Expressions;
using System.Reflection;
using OpenTelemetry;

namespace Microsoft.M365.Core.Telemetry.R9.Logging.Substrate.Exporter
{
    /// <summary>
    /// Reentrant export processor.
    /// </summary>
    /// <remarks>
    /// This is copied from ReentrantExportProcessor from R9 SDK repo (which is copied from GenevaExporter repo)
    /// as the class is internal and not visible to this project. This will be removed from Substrate R9 Extension
    /// in one of the conditions below.
    ///  - This class will be added to OpenTelemetry project as public. (Planned)
    ///  - CompositeLogRecordExporterProcessor will be merged into R9 SDK.
    /// </remarks>
    /// <typeparam name="T">Type of data to be exported.</typeparam>
    internal class ReentrantExportProcessor<T> : BaseExportProcessor<T>
        where T : class
    {
        private static Func<T, Batch<T>> GetCreateBatchFunc()
        {
#pragma warning disable S3011 // Reflection should not be used to increase accessibility of classes, methods, or fields
            var ctor = typeof(Batch<T>).GetConstructor(BindingFlags.Instance | BindingFlags.NonPublic, null, new[] { typeof(T) }, null)!;
#pragma warning restore S3011 // Reflection should not be used to increase accessibility of classes, methods, or fields
            var value = Expression.Parameter(typeof(T), null);
            var lambda = Expression.Lambda<Func<T, Batch<T>>>(Expression.New(ctor, value), value);
            return lambda.Compile();
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="ReentrantExportProcessor{T}"/> class.
        /// </summary>
        /// <param name="exporter"></param>
        public ReentrantExportProcessor(BaseExporter<T> exporter)
            : base(exporter)
        {
        }

        /// <inheritdoc/>
        protected override void OnExport(T data)
        {
            _ = exporter.Export(createBatch(data));
        }

        private static readonly Func<T, Batch<T>> createBatch = GetCreateBatchFunc();
    }
}
