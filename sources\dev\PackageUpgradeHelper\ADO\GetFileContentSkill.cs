﻿// <copyright file="GetFileContentSkill.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net.Http.Headers;
using System.Text;
using System.Threading.Tasks;

namespace PackageUpgradeHelper.ADO
{
    /// <summary>
    /// Get file content
    /// https://learn.microsoft.com/en-us/rest/api/azure/devops/build/source-providers/get-file-content?view=azure-devops-rest-7.2
    /// </summary>
    class GetFileContentSkill
    {
        private readonly string org = Globals.ORGANIZATION;
        private readonly string proj = Globals.PROJECT;
        private readonly string repo = Globals.REPOSITORY;
        private readonly string apiVersion = Globals.ADO_API_VERSION;
        private readonly string sourceProvider = Globals.SOURCE_PROVIDER;
        private readonly string pat = Globals.PAT;

        /// <summary>
        /// Get file content for one path
        /// </summary>
        /// <param name="path"></param>
        /// <param name="commitOrBranch"></param>
        /// <returns></returns>
        public async Task<string> GetFileContent(string path, string commitOrBranch)
        {
            using HttpClient client = new HttpClient();
            client.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));
            client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Basic", Convert.ToBase64String(System.Text.ASCIIEncoding.ASCII.GetBytes(string.Format("{0}:{1}", string.Empty, pat))));

            var url = $"https://dev.azure.com/{org}/{proj}/_apis/sourceProviders/{sourceProvider}/filecontents?repository={repo}&commitOrBranch={commitOrBranch}&path={path}&api-version={apiVersion}";
            var res = await client.GetAsync(url).ConfigureAwait(false);
            if (res.IsSuccessStatusCode)
            {
                return await res.Content.ReadAsStringAsync().ConfigureAwait(false);
            }
            else
            {
                throw new Exception("Fail to read file content");
            }
        }
        
        /// <summary>
        /// Get file content for severala paths
        /// </summary>
        /// <param name="paths"></param>
        /// <param name="commitOrBranch"></param>
        /// <returns></returns>
        public async Task<List<Tuple<string, string>>> GetFileContent(List<string> paths, string commitOrBranch)
        {
            List<Tuple<string, string>> ret = new ();

            foreach (var path in paths)
            {
                 ret.Add(new (path, await GetFileContent(path, commitOrBranch).ConfigureAwait(false)));
            }
            return ret;
        }
    }
}
