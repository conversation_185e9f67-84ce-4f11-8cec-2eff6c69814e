﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{E6ADA230-C078-44B9-9456-729D98DF8E11}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <WindowsTargetPlatformVersion>10.0</WindowsTargetPlatformVersion>
    <ConfigurationType>Application</ConfigurationType>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
    <ResolveNuGetPackages>false</ResolveNuGetPackages>
    <CopyLocalProjectReference>true</CopyLocalProjectReference>
    <CopyLocalDebugSymbols>true</CopyLocalDebugSymbols>
    <!--These tests access file and network. Can't run on cloudbuild. Only run locally.-->
    <EnableQTest>false</EnableQTest>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <UseDebugLibraries>true</UseDebugLibraries>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <UseDebugLibraries>false</UseDebugLibraries>
    <WholeProgramOptimization>true</WholeProgramOptimization>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings" />
  <ImportGroup Label="Shared" />
  <ImportGroup Label="PropertySheets" />
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <IncludePath>$(EnlistmentRoot)\sources\cpp\dev\AsioUtils;$(IncludePath)</IncludePath>
  </PropertyGroup>
  <ItemDefinitionGroup />
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <Optimization>Disabled</Optimization>
      <PreprocessorDefinitions>_ITERATOR_DEBUG_LEVEL=2;X64;_DEBUG;_CONSOLE;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <WarningLevel>Level3</WarningLevel>
      <AdditionalIncludeDirectories>
        $(EnlistmentRoot)\sources\cpp\dev\AsioUtils;
        $(EnlistmentRoot)\sources\cpp\test\TcpServer;
        %(AdditionalIncludeDirectories)
      </AdditionalIncludeDirectories>
    </ClCompile>
    <Link>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <SubSystem>Console</SubSystem>
      <AdditionalDependencies>
        gtest_main.lib;
        $(VcpkgDebugLibDir)\*.lib;
      </AdditionalDependencies>
      <AdditionalLibraryDirectories>
        $(DistribRoot)\$(Configuration)\$(Platform)\AsioUtils;
        $(DistribRoot)\$(Configuration)\$(Platform)\TcpServer;
        %(AdditionalLibraryDirectories)
      </AdditionalLibraryDirectories>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <PreprocessorDefinitions>_ITERATOR_DEBUG_LEVEL=0;X64;NDEBUG;_CONSOLE;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <WarningLevel>Level3</WarningLevel>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <AdditionalIncludeDirectories>
        $(EnlistmentRoot)\sources\cpp\dev\AsioUtils;
        $(EnlistmentRoot)\sources\cpp\test\TcpServer;
        %(AdditionalIncludeDirectories)
      </AdditionalIncludeDirectories>
    </ClCompile>
    <Link>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <SubSystem>Console</SubSystem>
      <OptimizeReferences>true</OptimizeReferences>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <AdditionalDependencies>
        gtest_main.lib;
        $(VcpkgReleaseLibDir)\*.lib;
      </AdditionalDependencies>
      <AdditionalLibraryDirectories>
        $(DistribRoot)\$(Configuration)\$(Platform)\AsioUtils;
        $(DistribRoot)\$(Configuration)\$(Platform)\TcpServer;
        %(AdditionalLibraryDirectories)
      </AdditionalLibraryDirectories>
    </Link>
  </ItemDefinitionGroup>
  <ItemGroup>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="TcpClientTest.cpp" />
    <ClCompile Include="FileManagerTest.cpp" />
    <ClCompile Include="AsioContextTest.cpp" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="$(EnlistmentRoot)\sources\cpp\dev\AsioUtils\AsioUtils.vcxproj">
      <Project>{7062318E-CE9D-4C93-B616-20D096520E1E}</Project>
    </ProjectReference>
    <ProjectReference Include="$(EnlistmentRoot)\sources\cpp\test\TcpServer\TcpServer.vcxproj">
      <Project>{738F538A-C392-40B8-A97F-7B2BAF9D7992}</Project>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <None Include="packages.config" />
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
</Project>