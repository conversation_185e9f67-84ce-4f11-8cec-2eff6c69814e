﻿// <copyright file="TracerSdk.netfx.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

#if NETFRAMEWORK
using System;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using OpenTelemetry.Trace;

namespace Microsoft.M365.Core.Telemetry.R9.Tracing.Instrumentation.Accelerator
{
    /// <summary>
    /// TracerSdk for Distributed Tracing
    /// </summary>
    public static class TracerSdk
    {
        /// <summary>
        /// Init Distributed Tracing Service
        /// the overall flow is as below:
        /// 
        /// new ServiceCollection() 
        /// AddDistributedTracingService() to ServiceCollection which contains:
        ///     1. ECS controlled Dynamic Sampler
        ///     2. Auto-collect in/out http requests with all tags that might contains EUII redacted
        ///     3. Geneva trace exporter
        ///     4. Necessary enrichers for mandatory information, like env_cloud_role, env_cloud_roleInstance etc.
        ///     5. Customized activity sources
        /// Start all hosted services in ServiceCollection
        /// 
        /// </summary>
        /// <param name="serviceMetaDataConfig"></param>
        /// <param name="tracingAcceleratorConfig"></param>
        /// <param name="extraTraceProviderBuilderConfigure"></param>
        /// <returns>Service Provider built from serviceCollection inside</returns>
        public static ServiceProvider InitDistributedTracingService(IConfiguration serviceMetaDataConfig, IConfiguration tracingAcceleratorConfig = default, Action<TracerProviderBuilder> extraTraceProviderBuilderConfigure = default)
        {
            ServiceCollection services = new ServiceCollection();
            services.AddDistributedTracingService(serviceMetaDataConfig, tracingAcceleratorConfig, extraTraceProviderBuilderConfigure);
            return services.StartServices();
        }
    }
}
#endif
