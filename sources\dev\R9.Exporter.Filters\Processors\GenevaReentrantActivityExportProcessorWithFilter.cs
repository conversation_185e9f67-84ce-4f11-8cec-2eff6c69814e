﻿// <copyright file="GenevaReentrantActivityExportProcessorWithFilter.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System.Diagnostics;
using Microsoft.Extensions.Options;
using Microsoft.R9.Extensions.Tracing.Exporters;
using OpenTelemetry.Exporter.Filters;

namespace Microsoft.R9.Extensions.Telemetry.Exporter.Filters
{
    /// <summary>
    /// Geneva Reentrant Exporter
    /// </summary>
    internal class GenevaReentrantActivityExportProcessorWithFilter : GenevaReentrantActivityExportProcessor
    {
        /// <summary>
        /// internal filter
        /// </summary>
        internal readonly BaseFilter<Activity> Filter;

        /// <summary>
        /// initial the instance with exporter and filter
        /// </summary>
        /// <param name="options">Geneva trace exporter options</param>
        /// <param name="filter">internal filter</param>
        public GenevaReentrantActivityExportProcessorWithFilter(IOptions<GenevaTraceExporterOptions> options, BaseFilter<Activity> filter)
            : base(options)
        {
            Guard.ThrowIfNull(filter);
            this.Filter = filter;
        }

        /// <summary>
        /// filter the data before they are actually exported.
        /// </summary>
        /// <param name="data">completed activity</param>
        public override void OnEnd(Activity data)
        {
            if (this.Filter.ShouldFilter(data))
            {
                base.OnEnd(data);
            }
        }
    }
}
