﻿// <copyright file="Constants.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.Diagnostics.CodeAnalysis;

namespace Microsoft.M365.Core.Telemetry.ECSClient
{
    /// <summary>
    /// Constants class
    /// </summary>
    [ExcludeFromCodeCoverage]
    public static class Constants
    {
        /// <summary>
        /// Default process name as ECS context filter.
        /// </summary>
        public const string DefaultProcessName = "Unknown";

        /// <summary>
        /// Default ECS refresh time interval in minute.
        /// </summary>
        public const int DefaultRefreshInterval = 5;

        /// <summary>
        /// Default value for retry.
        /// </summary>
        internal const int DefaultRetryTimes = 3;

        /// <summary>
        /// Default wait time that blocks caller thread in second.
        /// </summary>
        internal const int DefaultBlockedTime = 2;

        /// <summary>
        /// The ECS client name for Unified Telemetry.
        /// </summary>
        internal const string ECSClientName = "UnifiedTelemetry";

        /// <summary>
        /// The application id used to connect to AAD.
        /// </summary>
        public static readonly string ApplicationId = "a65f7e1e-db61-4b02-b524-882bef441e18";

        /// <summary>
        /// The ECS team name for PassiveMon.
        /// </summary>
        internal const string PassiveTeamName = "PassiveMon";

        /// <summary>
        /// The ECS team name for Distributed Tracing.
        /// </summary>
        internal const string DistributedTraceingTeamName = "SOTELSTracing";

        /// <summary>
        /// The ECS team names in Int environment.
        /// </summary>
        internal static readonly string[] ECSTeamNameInt = new string[] { PassiveTeamName, DistributedTraceingTeamName };

        /// <summary>
        /// THe ECS team names in prod environment.
        /// </summary>
        internal static readonly string[] ECSTeamNameProd = new string[] { PassiveTeamName, DistributedTraceingTeamName };

        /// <summary>
        /// The subject name of EXO certificate.
        /// </summary>
        public static readonly string EXOCertificateName = "unifiedtelemetry-prod-outlook-com";

        /// <summary>
        /// The subject name of EOP certificate.
        /// </summary>
        public static readonly string EOPCertificateName = "unifiedtelemetry-prod-eop-outlook-com";

        /// <summary>
        /// The subject name of Gallatin certificate.
        /// </summary>
        public static readonly string GallatinCertificateName = "unifiedtelemetry-prod-gallatin-outlook-com";

        /// <summary>
        /// The subject name of Itar certificate.
        /// </summary>
        public static readonly string ItarCertificateName = "unifiedtelemetry-prod-itar-outlook-com";

        /// <summary>
        /// The subject name of Cosmic certificate.
        /// </summary>
        public static readonly string CosmicCertificateName = "unifiedtelemetry-prod-cosmic-outlook-com";

        /// <summary>
        /// The subject name of ModelD certificate.
        /// </summary>
        public static readonly string ModelDCertificateName = "unifiedtelemetry-prod-modeld-outlook-com";

        /// <summary>
        /// The host to request authority.
        /// </summary>
        public static readonly string MicrosoftHost = "https://login.microsoftonline.com";

        /// <summary>
        /// The tenant id of application.
        /// </summary>
        public static readonly string TenantId = "cdc5aeea-15c5-4db6-b079-fcadd2505dc2";

        #region PassiveMon Flights

        /// <summary>
        /// The default connection string.
        /// </summary>
        internal const string ConnectionString = "EtwSession=o365PassiveMonitoringSessionR9";

        /// <summary>
        /// The default table name mappings.
        /// </summary>
        internal static readonly IReadOnlyDictionary<string, string> TableNameMappings = new Dictionary<string, string>() { { "*", "PassiveR9" } };

        /// <summary>
        /// The default monitoring account.
        /// </summary>
        internal const string MonitoringAccount = "M365PassiveInfra";

        /// <summary>
        /// The default monitoring namespace.
        /// </summary>
        internal const string MonitoringNamespace = "M365Passive";

        /// <summary>
        /// Debug name
        /// </summary>
        internal const string Debug = "Debug";

        /// <summary>
        /// The template to fetch config of specific event from ECS.
        /// </summary>
        internal const string EventTemplate = "Event_{0}";

        /// <summary>
        /// The template to fetch config of specific metric from ECS.
        /// </summary>
        internal const string MetricTemplate = "Metric_{0}";

        /// <summary>
        /// If R9 is enabled for specific event/metric.
        /// </summary>
        internal const string R9Enabled = "R9Enabled";

        /// <summary>
        /// If R9 Event is enabled.
        /// </summary>
        internal const string R9EventEnabled = "R9EventEnabled";

        /// <summary>
        /// If R9 Metric is enabled.
        /// </summary>
        internal const string R9MetricEnabled = "R9MetricEnabled";

        /// <summary>
        /// If Ifx is disabled for specific event/metric.
        /// </summary>
        internal const string IfxDisabled = "IfxDisabled";

        /// <summary>
        /// If Ifx Event is enabled.
        /// </summary>
        internal const string IfxEventEnabled = "IfxEventEnabled";

        /// <summary>
        /// If Ifx Metric is enabled.
        /// </summary>
        internal const string IfxMetricEnabled = "IfxMetricEnabled";

        /// <summary>
        /// DisabledIfxMetrics
        /// </summary>
        internal const string DisabledIfxMetrics = "DisabledIfxMetrics";

        /// <summary>
        /// DisabledIfxEvents
        /// </summary>
        internal const string DisabledIfxEvents = "DisabledIfxEvents";

        /// <summary>
        /// EnabledR9Metrics
        /// </summary>
        internal const string EnabledR9Metrics = "EnabledR9Metrics";

        /// <summary>
        /// EnabledR9Events
        /// </summary>
        internal const string EnabledR9Events = "EnabledR9Events";

        /// <summary>
        /// [For debug] If mds trace is enabled.
        /// </summary>
        internal const string MdsTraceEnabled = "MdsTraceEnabled";

        /// <summary>
        /// [For debug] EventId list that included in TraceLog.
        /// </summary>
        internal const string IncludedTraceLog = "IncludedTraceLog";

        /// <summary>
        /// [For debug] If debug info collection is enabled.
        /// </summary>
        internal const string IsDebugInfoCollectionEnabled = "IsDebugInfoCollectionEnabled";

        /// <summary>
        /// Library R9 config
        /// </summary>
        internal const string LibraryR9Config = "InternalR9Config";

        /// <summary>
        /// Service log R9 config
        /// </summary>
        internal const string ServiceLogR9Config = "Logging";

        /// <summary>
        /// Service metric R9 config
        /// </summary>
        internal const string ServiceMetricR9Config = "Metering";

        /// <summary>
        /// TableNameMapsTo
        /// </summary>
        internal const string TableNameMapsTo = "TableNameMapsTo";

        /// <summary>
        /// EtwProtocol
        /// </summary>
        internal const string EtwProtocol = "Etw";

        #endregion

        #region SOTELSTracing Flights

        /// <summary>
        /// If R9 distributed tracing enabled
        /// </summary>
        public const string R9DTEnabled = "R9DTEnabled";

        /// <summary>
        /// trace id ratio based sample rate
        /// </summary>
        public const string TraceSampleRate = "TraceSampleRate";

        /// <summary>
        /// SamplerType
        /// </summary>
        public const string SamplerType = "SamplerType";

        /// <summary>
        /// ParentRootSamplerType
        /// </summary>
        public const string ParentRootSamplerType = "ParentRootSamplerType";

        /// <summary>
        /// dynamic sampler type
        /// </summary>
        public enum DynamicSamplerType
        {
            /// <summary>
            /// Always samples traces.
            /// </summary>
            AlwaysOn,

            /// <summary>
            /// Never samples traces.
            /// </summary>
            AlwaysOff,

            /// <summary>
            /// dynamic trace id ratio based sampler
            /// </summary>
            RatioBased,

            /// <summary>
            /// dynamic parent based sampler
            /// </summary>
            ParentBased
        }

        #endregion

        /// <summary>
        /// service runtime model
        /// </summary>
        internal enum RuntimeModel
        {
            ModelA,
            ModelB,
            ModelB2,
            ModelD,
            ModelD2,
            Cosmic
        }

        /// <summary>
        /// ECS agent type.
        /// </summary>
        [Flags]
        internal enum AgentType
        {
            PassiveMon = 1,
            SOTELSTracing = 2
        }
    }
}