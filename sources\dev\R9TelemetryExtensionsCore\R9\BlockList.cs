﻿// <copyright file="BlockList.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System.Collections.Concurrent;

using Microsoft.M365.Core.Telemetry.SDKLogger;

namespace Microsoft.M365.Core.Telemetry.R9
{
    /// <inheritDoc/>
    /// <summary>
    /// Block and log buggy metrics and logs from sending with R9.
    /// </summary>
    internal class BlockList : IBlockList
    {
        // Metric name to category.
        private ConcurrentDictionary<string, string> metrics = new ConcurrentDictionary<string, string>();

        /// <inheritDoc/>
        /// <summary>
        /// Block a metric if it has been logged with another category before.
        /// Technically using another category with the same account&namespace is ok. We block them anyway.
        /// </summary>
        /// <param name="name">Metric name.</param>
        /// <param name="category">Metric category.</param>
        public bool ShouldBlockMetric(string name, string category)
        {
            if (!metrics.ContainsKey(name))
            {
                metrics[name] = category;
                return false;
            }
            else if (metrics[name] == category)
            {
                return false;
            }
            else
            {
                SDKLog.Warning($"Block R9Metric: {name}, category: {category}, existing category: {metrics[name]}");
                return true;
            }
        }
    }
}
