﻿// <copyright file="CompositeLogRecordExporterInternalExtension.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using Microsoft.Extensions.Configuration;
using Microsoft.M365.Core.Telemetry.Exporters.ODLTCP.Log;

namespace Microsoft.M365.Core.Telemetry.R9.Logging.Substrate
{
    /// <summary>
    /// Internal extension methods for CompositeLogRecordExporter.
    /// </summary>
    internal static class CompositeLogRecordExporterInternalExtension
    {
        /// <summary>
        /// Build routing maps with CompositeLogRecordExporterOptions.
        /// </summary>
        /// <param name="options"></param>
        /// <returns></returns>
        internal static (ConcurrentDictionary<string, LogExportType>, Dictionary<string, string>, Dictionary<string, string>) BuildRoutingMaps(this CompositeLogRecordExporterOptions options)
        {
            var eventRoutingMappings = new ConcurrentDictionary<string, LogExportType>();
            var genevaTableNameMappings = new Dictionary<string, string>();
            var odlTcpTableNameMappings = new Dictionary<string, string>();

            foreach (var mapping in options.VirtualTableMappings)
            {
                var category = mapping.Key;
                var tableName = mapping.Value;
                eventRoutingMappings.TryAdd(category, LogExportType.None);
                if (options.VirtualTableExports.TryGetValue(tableName, out var eventDestinations))
                {
                    foreach (var destination in eventDestinations)
                    {
                        if (destination.ExporterType.Equals("Geneva", StringComparison.CurrentCultureIgnoreCase))
                        {
                            eventRoutingMappings[category] |= LogExportType.Geneva;
                            genevaTableNameMappings[category] = destination.ExportTable;
                        }
                        if (destination.ExporterType.Equals("OdlTcp", StringComparison.CurrentCultureIgnoreCase))
                        {
                            eventRoutingMappings[category] |= LogExportType.OdlTcp;
                            odlTcpTableNameMappings[category] = destination.ExportTable;
                        }
                    }
                }
            }

            return (eventRoutingMappings, genevaTableNameMappings, odlTcpTableNameMappings);
        }

        /// <summary>
        /// Try to match the category with the routing map.
        /// </summary>
        /// <param name="eventRoutingMappings"></param>
        /// <param name="category"></param>
        /// <param name="destination"></param>
        /// <returns></returns>
        internal static bool TryMatchValue(this ConcurrentDictionary<string, LogExportType> eventRoutingMappings, string category, out LogExportType destination)
        {
            var matched = false;
            destination = LogExportType.None;

            matched |= eventRoutingMappings.TryGetValue(category, out destination);
            if (!matched)
            {
                matched |= eventRoutingMappings.TryGetValue("*", out destination);
            }

            return matched;
        }

        /// <summary>
        /// Modify configuration to force OdlTcp export format.
        /// </summary>
        /// <param name="configuration"></param>
        /// <returns></returns>
        internal static IConfiguration ForceOdlTcpExportFormat(this IConfiguration configuration)
        {
            configuration.GetSection(nameof(ODLTcpLogExporterOptions.Delimiter)).Value = ",";
            configuration.GetSection(nameof(ODLTcpLogExporterOptions.LogSchema)).Value = nameof(LogSchema.GenevaSchema4);
            configuration.GetSection(nameof(ODLTcpLogExporterOptions.NeedEnvProperties)).Value = "false";
            configuration.GetSection(nameof(ODLTcpLogExporterOptions.NeedExceptionOutput)).Value = "false";
            configuration.GetSection(nameof(ODLTcpLogExporterOptions.NeedEventIdNumber)).Value = "false";
            return configuration;
        }
    }
}
