# Step 2 - Initialize R9 Telemetry

Initialization of the R9 Telemetry SDK involves a few crucial steps to ensure the service starts appropriately and logs events as expected.

For more details of .net Logging, please check [Logging in C# - .NET | Microsoft Learn](https://learn.microsoft.com/en-us/dotnet/core/extensions/logging?tabs=command-line)

The Unified Telemetry team has implemented an extension **Microsoft.M365.Core.Telemetry.R9.Logging.Substrate** to initialize R9 in the substrate. We recommend using this extension to initialize R9 and connect to ECS if necessary.

For more details of Microsoft.M365.Core.Telemetry.R9.Logging.Substrate, please check [Reference Pages](../../../SDKs/LoggingExtensions/About.md).

**Some key concepts of logging:**
*   **ILogger:** This is the primary interface for logging in .NET. It provides methods to log messages at different severity levels (e.g., LogInformation, LogWarning, LogError). ILogger is typically used with dependency injection, allowing it to be injected into classes and services.
*   **Logger:** This is an implementation of the ILogger interface. It is responsible for writing log messages to the configured logging providers. The Logger class is usually not directly instantiated by developers; instead, it is created by the LoggerFactory.
*   **LoggerFactory:** This is a factory class that creates instances of ILogger. It stores all the configuration that determines where log messages are sent (e.g., console, file, or external systems). The LoggerFactory can be configured with different logging providers to direct log messages to various outputs.

**Here is how to initialize the R9 Telemetry SDK:**

Firstly, please find out whether your project is using Dependency Injection.

## [Optional] Add ECS source for configuration

We provide methods to load both local appsettings.json file and ECS.
For the same key, value from ECS will override it in the local file.

# [DI](#tab/DI)

If you are using `IHostBuilder`, append [`IHostBuilder.RegisterConfiguration()`](../../../SDKs/LoggingExtensions/Reference/APIRef.md#registerconfigurationihostbuilder-string-bool) to add an extra ECS source.

**Note**: Please ensure the configuration from `appsettings.json` has been loaded before calling this API.

```csharp
var host = Host.CreateDefaultBuilder(args)
    .ConfigureAppConfiguration((context, config) =>
    {
        config.SetBasePath(Directory.GetCurrentDirectory());
        config.AddJsonFile("appsettings.json", optional: false, reloadOnChange: true);
    })
    .RegisterConfiguration()
```

# [Non-DI](#tab/nonDI)

If you are not using `IHostBuilder`, use [`ConfigurationHelper.LoadConfiguration()`](../../../SDKs/LoggingExtensions/Reference/APIRef.md#loadconfigurationstring-string-bool) to get the `IConfiguration`.
The result `IConfiguration` combines setting from local file (specified by parameter) and ECS.

**Note**: Please ensure the configuration from `appsettings.json` has been loaded before calling this API.

```csharp
    IConfiguration configuration = ConfigurationHelper.LoadConfiguration(
        Path.Combine(Directory.GetCurrentDirectory(), "appsettings.json")
    );
```

---


## Initialize R9

# [DI](#tab/DI)
For DI projects, the configuration is loaded in Host. And R9 will be initialized in IServiceCollection.

Use method [`IServiceCollection.AddSubstrateLogging()`](../../../SDKs/LoggingExtensions/Reference/APIRef.md#addsubstrateloggingiservicecollection-iconfiguration-action) to initialize.

```csharp
    /// Load configuration in host
    var host = Host.CreateDefaultBuilder(args)
    .ConfigureAppConfiguration((context, config) =>
    {
        /// option 1: load disk file
        config.SetBasePath(Directory.GetCurrentDirectory());
        config.AddJsonFile("appsettings.json", optional:false, reloadOnChange:true);
        /// option 2: load from JSON stream
        config.AddJsonStream(new MemoryStream(Encoding.UTF8.GetBytes(configDataInJSON.ToString())));
    })
    .ConfigureServices((context, services) =>
    {
        /// This is the R9 initialization API provided by Unified Telemetry team in Microsoft.M365.Core.Telemetry.R9.Logging.Substrate.
        services.AddSubstrateLogging(context.Configuration);
        services.AddSingleton<IHostedService, SampleService>();
    })
    .Build();
```

# [Non-DI](#tab/nonDI)
For Non-DI projects, we’ll create a static LoggerFacotry and load configuration manually to configure it.

Use [`ILoggerFactory.ConfigureSubstrateLogging`](../../../SDKs/LoggingExtensions//Reference/APIRef.md#configuresubstrateloggingiloggingbuilder-iconfiguration).

[!code-csharp[](../../../include/NonDILogger?highlight=18-26)]

---

## Get ILogger

# [DI](#tab/DI)
Use Constructor Injection is the most common and recommended approach
```csharp
    // Option 1: inject ILogger
    public class MyService {
        private readonly ILogger<MyService> _logger;
        public MyService(ILogger<MyService> logger)
        {
            _logger = logger;
        }
        public void DoWork()
        {
            _logger.LogInformation("Doing work...");
        }
    }


    // Option 2: Inject ILoggerFactory
    public class MyService {
        private readonly ILogger _logger;
        public MyService(ILoggerFactory loggerFactory)
        {
            _logger = loggerFactory.CreateLogger("MyServiceLogger");
        }
        public void DoWork()
        {
            _logger.LogInformation("Doing work...");
        }
    }
```

# [Non-DI](#tab/nonDI)

For Non-DI projects, we’ll create a static logger with the static ILoggerFactory we built.

[!code-csharp[](../../../include/NonDILogger?highlight=3-11)]

---


## Summary

Now we have completed the initialization for R9 and have the `ILogger` for emitting logs.

**Next Step**: [Instrument Data](./Instrumentation.md)