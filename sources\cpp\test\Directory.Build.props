<?xml version="1.0" encoding="utf-8"?>
<Project xmlns="http://schemas.microsoft.com/developer/msbuild/2003">

  <Import Project="$([MSBuild]::GetDirectoryNameOfFileAbove('$(MSBuildThisFileDirectory)..', 'Directory.Build.props'))\Directory.Build.props" />

  <PropertyGroup>
    <QTestAdapterPath>$(EnlistmentRoot)\packages\GoogleTestAdapter.0.18.0\build\_common\</QTestAdapterPath>
  </PropertyGroup>

  <!--This is usually imported from Sources.Build.props for projects under sources\test.
  Projects here don't resolve to 'test' GROUP. Mannually import the test props.-->
  <Import Project="$(EnlistmentRoot)\build\Extensions\Test.Sources.Build.props"/>

</Project>