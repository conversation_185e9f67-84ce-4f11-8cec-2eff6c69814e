﻿// <copyright file="UnifiedTelemetryECSClientTest.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.Configuration;
using System.Net.Sockets;
using System.Reflection;
using System.Security.Cryptography.X509Certificates;
using System.Threading;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.M365.Core.Telemetry.ECSClient;
using Microsoft.Skype.ECS.Client;
using Microsoft.Skype.ECS.Core;
using Newtonsoft.Json.Linq;
using NSubstitute;
using Xunit;
using ConfigurationBuilder = Microsoft.Extensions.Configuration.ConfigurationBuilder;

namespace Microsoft.M365.Core.Telemetry.ECSClientTest
{
    /// <summary>
    /// Tests to validate UnifiedTelemetryECSClient code.
    /// </summary>
    [Collection("Non-Parallel Collection")]
    public class UnifiedTelemetryECSClientTest
    {
        /// <summary>
        /// Test throw exception when no service name/runtime model.
        /// </summary>
        [Fact]
        public void InitECSClientNoServiceNameThrowException()
        {
            // Mock a configuration
            var mockConfiguration = new Dictionary<string, string>
            {
                { "Microsoft_m365_core_telemetry:ServiceMetadata", string.Empty }
            };

            var configuration = new ConfigurationBuilder()
                .AddInMemoryCollection(mockConfiguration)
                .Build();

            var client = new UnifiedTelemetryECSClient(configuration);
            Assert.Null(client.ServiceName);
            Assert.Null(client.RuntimeModel);

            TestUtils.SetPrivateStaticField(typeof(UnifiedTelemetryECSClient), "ecsInstance", null);
        }

        /// <summary>
        /// Test throw exception when runtime model is not supported.
        /// </summary>
        [Fact]
        public void InitECSClientWrongRuntimeModelThrowException()
        {
            // Mock a configuration
            var mockConfiguration = new Dictionary<string, string>
            {
                ["Microsoft_m365_core_telemetry:ServiceMetadata:ServiceName"] = "Pop3",
                ["Microsoft_m365_core_telemetry:ServiceMetadata:RuntimeModel"] = "TestModel"
            };

            var configuration = new ConfigurationBuilder()
                .AddInMemoryCollection(mockConfiguration)
                .Build();

            var client = new UnifiedTelemetryECSClient(configuration);
            Assert.Equal("Pop3", client.ServiceName);
            Assert.Equal("TestModel", client.RuntimeModel);

            TestUtils.SetPrivateStaticField(typeof(UnifiedTelemetryECSClient), "ecsInstance", null);
        }

        /// <summary>
        /// Test correctly initialization.
        /// </summary>
        [Fact]
        public void InitECSClientCorrectlyConfigure()
        {
            // Mock a configuration
            var mockConfiguration = new Dictionary<string, string>
            {
                ["Microsoft_m365_core_telemetry:ServiceMetadata:ServiceName"] = "Pop3",
                ["Microsoft_m365_core_telemetry:ServiceMetadata:RuntimeModel"] = "ModelB2"
            };

            var configuration = new ConfigurationBuilder()
                .AddInMemoryCollection(mockConfiguration)
                .Build();

            var client = UnifiedTelemetryECSClient.EcsClientInstance(configuration);
            IECSConfigurationRequester mockEcsRequester = Substitute.For<IECSConfigurationRequester>();
            client.EcsRequester = mockEcsRequester;
            Assert.NotNull(client.EcsRequester);
            Assert.Equal("Pop3", client.ServiceName);
            Assert.Equal("ModelB2", client.RuntimeModel);

            TestUtils.SetPrivateStaticField(typeof(UnifiedTelemetryECSClient), "ecsInstance", null);
        }

        /// <summary>
        /// Test correctly initialize InitializeECSRequester with ECS Integration.
        /// </summary>
        [Fact]
        public void TestInitializeECSRequesterIntegration()
        {
            var mockConfiguration = new Dictionary<string, string>
            {
                ["Microsoft_m365_core_telemetry:ServiceMetadata:ServiceName"] = "Pop3",
                ["Microsoft_m365_core_telemetry:ServiceMetadata:RuntimeModel"] = "ModelB2"
            };
            var configuration = new ConfigurationBuilder()
                .AddInMemoryCollection(mockConfiguration)
                .Build();

            Environment.SetEnvironmentVariable("Microsoft_M365_Core_Telemetry_ECSClient_ECSEnvironment", "Integration");
            var client = new UnifiedTelemetryECSClient(configuration);
            IECSConfigurationRequester mockEcsRequester = Substitute.For<IECSConfigurationRequester>();
            client.EcsRequester = mockEcsRequester;
            Assert.NotNull(client.EcsRequester);
            Assert.Equal("Pop3", client.ServiceName);
            var ecsEnvironment = TestUtils.GetPrivateField<string>(client, "ecsEnvironment");
            Assert.Equal("Integration", ecsEnvironment);
        }

        /// <summary>
        /// Test OnConfigurationChanged.
        /// </summary>
        [Fact]
        public void TestOnConfigurationChanged()
        {
            var mockConfiguration = new Dictionary<string, string>
            {
                ["Microsoft_m365_core_telemetry:ServiceMetadata:ServiceName"] = "Pop3",
                ["Microsoft_m365_core_telemetry:ServiceMetadata:RuntimeModel"] = "ModelB2"
            };

            var configuration = new ConfigurationBuilder()
                .AddInMemoryCollection(mockConfiguration)
                .Build();
            var passiveConfig = new PassiveR9Config(configuration);
            var tracingConfig = new R9TracingConfig(configuration);

            var args = new ConfigChangedEventArgs();
            Lazy<IEnumerable<string>> agents = new Lazy<IEnumerable<string>>(() => new string[] { "PassiveMon", "SOTELSTracing" });
            _ = agents.Value;
            TestUtils.SetPrivateField(args, "_configChangedAgents", agents);
            var ecsClient = new UnifiedTelemetryECSClient(configuration);
            ecsClient.RegularUpdateEventWrapper = new EventWrapper();
            Assert.NotNull(ecsClient.RegularUpdateEventWrapper);
            TestUtils.InvokePrivateMethod<object>(ecsClient, "OnConfigurationChanged", new object[] { null, args });

            TestUtils.SetPrivateStaticField(typeof(UnifiedTelemetryECSClient), "ecsInstance", null);
        }

        /// <summary>
        /// Test GenerateCertificate with empty name.
        /// </summary>
        [Fact]
        public void TestGenerateCertificateEmptyName()
        {
            string emptyCertName = string.Empty;
            X509Certificate2 emptyCert = TestUtils.InvokePrivateStaticMethod<X509Certificate2>(typeof(UnifiedTelemetryECSClient), "GenerateCertificate", new string[] { emptyCertName });
            Assert.Null(emptyCert);
        }

        /// <summary>
        /// Test GenerateCertificate with invalid name.
        /// </summary>
        [Fact]
        public void TestGenerateCertificateInvalidName()
        {
            string invalidCertName = "invalid-cert";
            X509Certificate2 invalidCert = TestUtils.InvokePrivateStaticMethod<X509Certificate2>(typeof(UnifiedTelemetryECSClient), "GenerateCertificate", new string[] { invalidCertName });
            Assert.Null(invalidCert);
        }

        /// <summary>
        /// Test GenerateCertificate with empty name.
        /// </summary>
        [Fact]
        public void TestGenerateConfidentialInvalidCert()
        {
            X509Certificate2 invalidCert = null;
            Action action = () => TestUtils.InvokePrivateStaticMethod<X509Certificate2>(typeof(UnifiedTelemetryECSClient), "GenerateConfidential", new object[] { invalidCert });
            Assert.Throws<TargetInvocationException>(action);
        }
    }
}
