﻿// <copyright file="TextFileLoggerOptions.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System;

namespace Microsoft.M365.Core.Telemetry.FileExporter
{
    /// <summary>
    /// File logger option for ODL NRT Pipeline. 
    /// </summary>
    public class TextFileLoggerOptions : BaseFileLoggerOptions
    {
        /// <summary>
        /// Gets or sets the formatter for <see cref="string"/>.
        /// </summary>
        /// <remarks>Default output format:
        /// </remarks>
        public Func<string, string> StringFormatter { get; set; } = (str) => DefaultStringFormatter.FormatString(str);
    }
}
