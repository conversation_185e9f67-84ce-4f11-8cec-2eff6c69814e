﻿// <copyright file="HttpClientEUIIRedactEnricherTest.netfx.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>
#if NETFRAMEWORK
using System.Diagnostics;
using System.Net;
using Xunit;

namespace Microsoft.M365.Core.Telemetry.R9.Tracing.Instrumentation.Accelerator.Tests
{
    /// <summary>
    /// HttpClientEUIIRedactEnricherTest
    /// </summary>
    public class HttpClientEUIIRedactEnricherTest
    {
        HttpWebRequest httpRequest;

        /// <summary>
        /// HttpClientEUIIRedactEnricherTest
        /// </summary>
        public HttpClientEUIIRedactEnricherTest()
        {
            this.httpRequest = WebRequest.CreateHttp("http://localhost:8080/api/values?name=foo");
        }

        /// <summary>
        /// EnrichWithDefaultRedactionStrategyValidInputSuccess
        /// </summary>
        [Fact]
        public void EnrichWithDefaultRedactionStrategyValidInputSuccess()
        {
            Activity activity = new Activity("out");
            HttpClientEUIIRedactEnricher.EnrichWithHttpRequestMessage(activity, this.httpRequest, RedactionStrategyType.Default);
            Assert.Equal("http://localhost:8080/" + Constants.RedactedPlacholder, activity.GetTagItem(Constants.HttpUrl));

            HttpClientEUIIRedactEnricher.EnrichWithHttpRequestMessage(null, this.httpRequest, RedactionStrategyType.Default);
        }
    }
}
#endif