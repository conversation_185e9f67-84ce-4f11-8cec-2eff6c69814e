﻿// <copyright file="R9MetricTest.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.Diagnostics.CodeAnalysis;
using Microsoft.Extensions.Configuration;
using Microsoft.M365.Core.Telemetry.ECSClient;
using Microsoft.M365.Core.Telemetry.R9;
using Microsoft.M365.Core.Telemetry.TestCommon;
using Microsoft.R9.Extensions.Metering;
using NSubstitute;
using Xunit;

namespace Microsoft.M365.Core.Telemetry.R9Test
{
    /// <summary>
    /// R9MetricTest
    /// </summary>
    [Collection("Do not parallel")]
    public class R9MetricTest
    {
        /// <summary>
        /// R9HistogramMetricValidTest
        /// </summary>
        [Fact]
        [Obsolete("FakeMetric is now available in .NET, please upgrade to https://learn.microsoft.com/dotnet/api/microsoft.extensions.diagnostics.metrics.testing. Consult https://aka.ms/r9_v8 for details.")]
        public void R9HistogramMetricValidTest()
        {
            AddFakeExtensions.EnableR9(null, null);
            var fakeHistogram = new R9Metric<FakeMetric>("testName", new string[] { "testDim" });
            fakeHistogram.Log(3, new string[] { "testDimVal" });
            var fakeMeter = R9Services.GetMeter<FakeMetric>() as FakeMeter;
            var hist = fakeMeter.Histograms["testName"];

            Assert.Equal("testDimVal", hist.LatestWritten.Key[0]);
            Assert.Equal(3, hist.LatestWritten.Value[0]);
        }

        /// <summary>
        /// R9HistogramTestBlockMetric
        /// </summary>
        [Fact]
        [Obsolete("FakeMetric is now available in .NET, please upgrade to https://learn.microsoft.com/dotnet/api/microsoft.extensions.diagnostics.metrics.testing. Consult https://aka.ms/r9_v8 for details.")]
        public void R9HistogramTestBlockMetric()
        {
            var blockList = Substitute.For<IBlockList>();
            blockList.ShouldBlockMetric(Arg.Any<string>(), Arg.Any<string>()).ReturnsForAnyArgs(true);
            AddFakeExtensions.EnableR9(null, blockList);
            var fakeHistogram = new R9Metric<FakeMetric>("testName", new string[] { "testDim" });
            fakeHistogram.Log(3, new string[] { "testDimVal" });
            var fakeMeter = R9Services.GetMeter<FakeMetric>() as FakeMeter;

            Assert.Equal(0, fakeMeter.Histograms.Count);
        }

        /// <summary>
        /// R9HistogramAlwaysEnableR9
        /// </summary>
        [Fact]
        [Obsolete("FakeMetric is now available in .NET, please upgrade to https://learn.microsoft.com/dotnet/api/microsoft.extensions.diagnostics.metrics.testing. Consult https://aka.ms/r9_v8 for details.")]
        public void R9HistogramAlwaysEnableR9()
        {
            var passiveConfig = Substitute.For<IPassiveR9Config>();
            passiveConfig.R9MetricEnabled.Returns(false);
            var sc = AddFakeExtensions.ConfigureServicesForR9Test(null, null, null, passiveConfig);
            R9Services.InitR9Services(sc, true);
            var fakeHistogram = new R9Metric<FakeMetric>("testName", new string[] { "testDim" });
            fakeHistogram.Log(3, new string[] { "testDimVal" });
            var fakeMeter = R9Services.GetMeter<FakeMetric>() as FakeMeter;
            var hist = fakeMeter.Histograms["testName"];

            Assert.Equal("testDimVal", hist.LatestWritten.Key[0]);
            Assert.Equal(3, hist.LatestWritten.Value[0]);
        }

        /// <summary>
        /// R9HistogramSelectivelyEnableR9
        /// </summary>
        [Fact]
        [Obsolete("FakeMetric is now available in .NET, please upgrade to https://learn.microsoft.com/dotnet/api/microsoft.extensions.diagnostics.metrics.testing. Consult https://aka.ms/r9_v8 for details.")]
        public void R9HistogramSelectivelyEnableR9()
        {
            var passiveConfig = Substitute.For<IPassiveR9Config>();
            passiveConfig.MetricEnabledForR9("Microsoft.M365.Core.Telemetry.R9Test.FakeMetricEnabled").Returns(true);
            passiveConfig.MetricEnabledForR9("Microsoft.M365.Core.Telemetry.R9Test.FakeMetricDisabled").Returns(false);
            var sc = AddFakeExtensions.ConfigureServicesForR9Test(null, null, null, passiveConfig);
            R9Services.InitR9Services(sc);
            var enabledMetric = new R9Metric<FakeMetricEnabled>("enabledMetric", new string[] { "testDim" });
            var disabledMetric = new R9Metric<FakeMetricDisabled>("disabledMetric", new string[] { "testDim" });
            enabledMetric.Log(3, new string[] { "testDimVal" });
            disabledMetric.Log(3, new string[] { "testDimVal" });

            var fakeMeterEnabled = R9Services.GetMeter<FakeMetricEnabled>() as FakeMeter;
            Assert.Equal(3, fakeMeterEnabled.Histograms["enabledMetric"].LatestWritten.Value[0]);
            var fakeMeterDisabled = R9Services.GetMeter<FakeMetricDisabled>() as FakeMeter;
            Assert.Equal(1, fakeMeterDisabled.Histograms.Count);
        }

        /// <summary>
        /// R9HistogramMetricValidTestforLibrary
        /// </summary>
        [Fact]
        [Obsolete("FakeMetric is now available in .NET, please upgrade to https://learn.microsoft.com/dotnet/api/microsoft.extensions.diagnostics.metrics.testing. Consult https://aka.ms/r9_v8 for details.")]
        public void R9HistogramMetricValidTestforLibrary()
        {
            AddFakeExtensions.EnableR9(null, null);
            var fakeHistogram = new R9Metric<FakeMetricSDK>("testName", new string[] { "testDim" });
            fakeHistogram.Log(3, new string[] { "testDimVal" });
            var fakeMeter = R9Services.GetMeter<FakeMetricSDK>() as FakeMeter;
            var hist = fakeMeter.Histograms["testName"];

            Assert.Equal("testDimVal", hist.LatestWritten.Key[0]);
            Assert.Equal(3, hist.LatestWritten.Value[0]);
        }

        /// <summary>
        /// Test GetMeterV2 functionality
        /// </summary>
        [Fact]
        [Obsolete("FakeMetric is now available in .NET, please upgrade to https://learn.microsoft.com/dotnet/api/microsoft.extensions.diagnostics.metrics.testing. Consult https://aka.ms/r9_v8 for details.")]
        public void GetMeterV2Test()
        {
            AddFakeExtensions.EnableR9(null, null);
            var mockConfiguration = new Dictionary<string, string>
            {
                ["GenevaLogging:ConnectionString"] = "EtwSession=o365PassiveMonitoringSessionR9",
                ["GenevaMetering:Protocol"] = "Etw",
                ["GenevaMetering:MonitoringAccount"] = "R9TestAccount",
                ["GenevaMetering:MonitoringNamespace"] = "R9TestNamespace"
            };
            var configuration = new ConfigurationBuilder()
                .AddInMemoryCollection(mockConfiguration)
                .Build();
            AddFakeExtensions.InjectV2ServiceProvider(configuration);
            var fakeHistogram = new R9Metric<FakeMetricSDK>("testName", new string[] { "testDim" });
            fakeHistogram.Log(3, new string[] { "testDimVal" });
        }
    }

    /// <summary>
    /// FakeMetricSDK
    /// </summary>
    [ExcludeFromCodeCoverage]
    [Obsolete("FakeMetric is now available in .NET, please upgrade to https://learn.microsoft.com/dotnet/api/microsoft.extensions.diagnostics.metrics.testing. Consult https://aka.ms/r9_v8 for details.")]
    public class FakeMetricSDK : FakeMetric
    {
        /// <summary>
        /// FakeMetricSDK
        /// </summary>
        /// <param name="name"></param>
        /// <param name="dimensionNames"></param>
        public FakeMetricSDK(string name, IReadOnlyList<string> dimensionNames) : base(name, dimensionNames)
        {
        }

        /// <summary>
        /// Clear
        /// </summary>
        public override void Clear()
        {
            this.DimensionNames.Equals(null);
        }
    }

    /// <summary>
    /// FakeMetricEnabled
    /// </summary>
    [ExcludeFromCodeCoverage]
    public class FakeMetricEnabled
    {
    }

    /// <summary>
    /// FakeMetricDisabled
    /// </summary>
    [ExcludeFromCodeCoverage]
    public class FakeMetricDisabled
    {
    }
}
