﻿// <copyright file="ODLTcpLogExporterTests.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Diagnostics.CodeAnalysis;
using System.IO;
using System.Linq;
using System.Runtime.CompilerServices;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Microsoft.M365.Core.Telemetry.Exporters.ODLTCP;
using Microsoft.M365.Core.Telemetry.Exporters.ODLTCP.Log;
using Microsoft.R9.Extensions.Logging;
using OpenTelemetry;
using OpenTelemetry.Logs;
using Xunit;
using Assert = Xunit.Assert;

namespace Microsoft.R9.Extensions.Telemetry.Exporters.ODLTcp.Test
{
    public class ODLTcpLogExporterTests
    {
        [Fact]
        public void AddODLExporter_GivenOptions_ExportToLogTypeinCustomizedFormatter()
        {
            using var exporter = new TestExporter();

            ILogger logger = GetLogger(enableLogTypeMapping: true, enableCustomerFields: false, enablePrepolulatedFields: true, testExporter: exporter, mappingType: new Dictionary<string, string>
            {
                ["*"] = "TestLogEVR",
            }, logSchema: LogSchema.Customized,
            logFormatter: (serializer, logRecord, option) => serializer.Serialize(logRecord.Body));

            var inputlist = new Dictionary<string, object>
            {
                { "message1", 1 },
                { "message2", 2U },
                { "message3", 3L },
                { "message4", 4UL },
                { "message5", (short)5.5 },
                { "message6", (ushort)6.6 },
                { "message7", (double)7.7 },
                { "message8", 8.8F },
                { "message9", false },
                { "message10", DateTimeOffset.MinValue },
                { "message11", null }
            };

            logger.Log(LogLevel.Information, eventId: 0, inputlist.ToArray(), exception: null, formatter: (_, _) => string.Empty);
        }

        [Fact]
        public void AddODLExporter_GivenOptions_ExportToLogTypeinCommonFormatter()
        {
            using var exporter = new TestExporter();

            ILogger logger = GetLogger(enableLogTypeMapping: true, enableCustomerFields: false, enablePrepolulatedFields: true, testExporter: exporter, mappingType: new Dictionary<string, string>
            {
                ["*"] = "TestLogEVR",
            }, logSchema: LogSchema.Common);

            var inputlist = new Dictionary<string, object>
            {
                { "message1", 1 },
                { "message2", 2U },
                { "message3", 3L },
                { "message4", 4UL },
                { "message5", (short)5.5 },
                { "message6", (ushort)6.6 },
                { "message7", (double)7.7 },
                { "message8", 8.8F },
                { "message9", false },
                { "message10", DateTimeOffset.MinValue },
                { "message11", null }
            };

            logger.Log(LogLevel.Information, eventId: 0, inputlist.ToArray(), exception: null, formatter: (_, _) => string.Empty);
        }

        [Fact]
        public void AddODLExporter_GivenOptions_ExportToLogType()
        {
            using var exporter = new TestExporter();

            ILogger logger = GetLogger(enableLogTypeMapping: true, enableCustomerFields: false, enablePrepolulatedFields: true, testExporter: exporter, mappingType: new Dictionary<string, string>
            {
                ["*"] = "TestLogEVR",
            });

            string logMessage = "This is testing {user}";

            var dictExpected = new Dictionary<string, object>
            {
                { "{OriginalFormat}", logMessage },
                { "user", "testUser" }
            };

            var inputlist = new Dictionary<string, object>
            {
                { "message1", "this is a string with comma," },
                { "message2", null },
                { "message3", 3 },
                { "message4", new TestProduct { Name = "TestLoggerA", Price = 999.99 } },
                { "message5", new object[] { 1, 2, "hello, world" } },
                { "message6", dictExpected }
            };

            logger.Log(LogLevel.Information, eventId: 0, inputlist.ToArray(), exception: null, formatter: (_, _) => string.Empty);
            logger.LogInformation("Log a {CustomField} and {Property}", "CustomFieldValue,hello", null);
            logger.LogCritical("Log a {CustomField} and {Property}", "CustomFieldValue,hello", null);
            logger.Log(LogLevel.Trace, 101, "Log a {CustomField} and {Property}", "CustomFieldValue,hello", null);
            logger.LogWarning("CustomFieldValue,hello");
            logger.LogDebug(logMessage, "testUser");
            Assert.True(CompareStateValues(exporter.FirstState!, dictExpected));
        }

        [Fact]
        public void AddODLExporter_GivenOptions_ExportToLogTypeWithScopeAndEnvProperties()
        {
            using var exporter = new TestExporter();

            ILogger logger = GetLogger(enableLogTypeMapping: true, enableCustomerFields: true, enablePrepolulatedFields: true, enableExceptionOutput: true, enableEnvProperties: true, enableEventIdNumber: true, testExporter: exporter, customerFiels: new string[] { "user", "ScopeKey1", "ScopeKey3", "name" }, mappingType: new Dictionary<string, string>
            {
                ["*"] = "TestLogEVR",
            }, eventNameExportMode: EventNameExportMode.ExportAsPartAName, logSchema: LogSchema.GenevaSchema4WithScope);
            string logMessage = "This is testing {user}";

            var dictExpected = new Dictionary<string, object>
            {
                { "{OriginalFormat}", logMessage },
                { "user", "testUser" }
            };

            var inputlist1 = new Dictionary<string, object>
            {
                { "Attribute1", "this is a string with comma," },
                { "Attribute2", null },
                { "name", 3 },
                { "Attribute4", new TestProduct { Name = "TestLoggerA", Price = 999.99 } },
                { "Attribute5", new object[] { 1, 2, "hello, world" } },
                { "Attribute6", dictExpected }
            };

            var inputlist2 = new Dictionary<string, object>
            {
                { "name", "name.2,3" },
                { "user", 3 },
            };

            var sourceName = GetTestMethodName();

            using var listener = new ActivityListener();    
            listener.ShouldListenTo = (activitySource) => activitySource.Name == sourceName;
            listener.Sample = (ref ActivityCreationOptions<ActivityContext> options) => ActivitySamplingResult.AllDataAndRecorded;
            ActivitySource.AddActivityListener(listener);
            using var source = new ActivitySource(sourceName);
            List<KeyValuePair<string, object>> sortedFieldValue = new ();
            sortedFieldValue.Add(new KeyValuePair<string, object>("CustomizedTestKey1", "CustomizedTestValue1"));
            sortedFieldValue.Add(new KeyValuePair<string, object>("CustomizedTestKey2", "CustomizedTestValue2"));

            using (var activity = source.StartActivity("Activity"))
            {
                // Log inside an activity to set LogRecord.TraceId and LogRecord.SpanId
                logger.Log(LogLevel.Information, eventId: 0, string.Join(",", sortedFieldValue.Select(x => x.Value).ToArray()));
                logger.Log(LogLevel.Information, eventId: 0, sortedFieldValue, exception: null, formatter: (state, ex) => string.Join(",", sortedFieldValue.Select(x => x.Value).ToArray()));
            }

            using (logger.BeginScope(new List<KeyValuePair<string, object>> { new ("ScopeKey1", "ScopeValue1"), new ("ScopeKey2", "ScopeValue2"), new ("ScopeKey3", new DateTime(2022, 6, 6)), new ("ScopeKey4", new Dictionary<int, int>()), new (string.Empty, null) }))
            {
                logger.Log(LogLevel.Information, eventId: 0, inputlist1.ToArray(), exception: null, formatter: (_, _) => string.Empty);
                logger.Log(LogLevel.Information, eventId: 0, inputlist2.ToArray(), exception: null, formatter: (_, _) => string.Empty);
                logger.Log(LogLevel.Warning, new EventId(101, "TestEventNameWithLogExtensionMethod"), "Log a {CustomField} and {Property}", "CustomFieldValue", "PropertyValue");
            }
            logger.LogError(new Exception("Invalid Argument"), logMessage, "testUser");
            logger.Log(LogLevel.Information, eventId: 0, inputlist2.ToArray(), exception: null, formatter: (_, _) => string.Empty);
            logger.LogCritical(logMessage, "testUser");
            logger.LogWarning(logMessage, "testUser");
            logger.LogDebug(logMessage, "testUser");
            Assert.True(CompareStateValues(exporter.FirstState!, dictExpected));
        }

        [Fact]
        public void AddODLExporter_SizeLimited_FallBackToDisk()
        {
            var exception = Record.Exception(() =>
            {
                using var exporter = new TestExporter();
                ILogger logger = GetLogger(enableLogTypeMapping: true, enableCustomerFields: false, enablePrepolulatedFields: true, exporter, new Dictionary<string, string>
                {
                    ["*"] = "TestLogEVR",
                }, useFormattedMessage: true, enableBatch: false, enableFallback: true);
                string logMessage = new string('a', 33000);
                logger.LogInformation(logMessage);
            });
            Assert.Null(exception);
        }

        [Fact]
        public void AddODLExporter_InvalidMapping_ExportToCategory()
        {
            var exception = Record.Exception(() =>
            {
                using var exporter = new TestExporter();
                ILogger logger = GetLogger(enableLogTypeMapping: true, enableCustomerFields: true, enablePrepolulatedFields: true, exporter, new Dictionary<string, string>
                {
                    ["Microsoft.R9.Extensions.Logging.Exporters.Tests.LogTests"] = String.Empty,
                }, customerFiels: null);
            });
            Assert.NotNull(exception);

            exception = Record.Exception(() =>
            {
                using var exporter = new TestExporter();
                ILogger logger = GetLogger(enableLogTypeMapping: true, enableCustomerFields: true, enablePrepolulatedFields: true, exporter, new Dictionary<string, string>
                {
                    ["Microsoft.R9.Extensions.Logging.Exporters.Tests.LogTests"] = null,
                });
            });
            Assert.NotNull(exception);

            exception = Record.Exception(() =>
            {
                using var exporter = new TestExporter();
                ILogger logger = GetLogger(enableLogTypeMapping: true, enableCustomerFields: true, enablePrepolulatedFields: true, exporter, new Dictionary<string, string>
                {
                    [string.Empty] = "test",
                });
            });
            Assert.NotNull(exception);
        }

        [Fact]
        public void AddODLExporter_StarMapping_ExportToLogType()
        {
            var exception = Record.Exception(() =>
            {
                using var exporter = new TestExporter();
                ILogger logger = GetLogger(true, true, true, exporter, new Dictionary<string, string>
                {
                    ["*"] = "TestLogEVR",
                });
                string logMessage = "test log";
                logger.LogInformation(logMessage);
            });
            Assert.Null(exception);
        }

        [Fact]
        public void AddODLExporter_InvalidMapping_SkipRecord()
        {
            var exception = Record.Exception(() =>
            {
                using var exporter = new TestExporter();
                ILogger logger = GetLogger(true, true, true, exporter, new Dictionary<string, string>
                {
                    ["other"] = "TestLogEVR",
                });
                string logMessage = "test log";
                logger.LogInformation(logMessage);
            });
            Assert.Null(exception);
        }

        private static ILogger GetLogger(bool enableLogTypeMapping, bool enableCustomerFields, bool enablePrepolulatedFields, TestExporter testExporter, Dictionary<string, string> mappingType, bool enableEnvProperties = false, bool enableExceptionOutput = false, bool enableEventIdNumber = false, bool useFormattedMessage = true, bool enableBatch = false, bool enableFallback = false, string[] customerFiels = null, EventNameExportMode eventNameExportMode = EventNameExportMode.None, LogSchema logSchema = LogSchema.GenevaSchema4, Func<ILogSerializer, LogRecord, ODLTcpLogExporterOptions, string> logFormatter = null)
        {
            var host = new HostBuilder()
                        .ConfigureLogging(builder => builder
                            .AddOpenTelemetryLogging(options =>
                            {
                                options.UseFormattedMessage = useFormattedMessage;
                                options.IncludeScopes = true;
                            })
                            .SetMinimumLevel(LogLevel.Debug)
                            .AddProcessor(new SimpleLogRecordExportProcessor(testExporter))
                            .AddODLTcpExporter(
                                options =>
                                {
                                    options.ExceptionStackExportMode = ExceptionStackExportMode.ExportAsString;
                                    options.EventNameExportMode = eventNameExportMode;

                                    if (enableLogTypeMapping)
                                    {
                                        options.LogTypeMappings = mappingType;
                                    }
                                    if (enableCustomerFields)
                                    {
                                        options.CustomFields = customerFiels;
                                    }
                                    if (enablePrepolulatedFields)
                                    {
                                        options.PrepopulatedFields = new Dictionary<string, object>
                                        {
                                            { "role", "testRole" }
                                        };
                                    }
                                    if (enableEnvProperties)
                                    {
                                        options.NeedEnvProperties = true;
                                    }
                                    if (enableExceptionOutput)
                                    {
                                        options.NeedExceptionOutput = true;
                                    }
                                    if (enableEventIdNumber)
                                    {
                                        options.NeedEventIdNumber = true;
                                    }
                                    if (logFormatter != null)
                                    {
                                        options.LogFormatter = logFormatter;
                                    }
                                    options.LogSchema = logSchema;
                                }, enableBatch))
                        .Build();
            return host.Services.GetRequiredService<ILoggerFactory>().CreateLogger<ODLTcpLogExporterTests>();
        }

        private static bool CompareStateValues(IReadOnlyCollection<KeyValuePair<string, object>> stateValues, Dictionary<string, object> dictExpected)
        {
            if (stateValues.Count != dictExpected.Count)
            {
                return false;
            }

            foreach (var entry in stateValues)
            {
                if (dictExpected.TryGetValue(entry.Key, out var value))
                {
                    if (entry.Value.ToString() != value.ToString())
                    {
                        return false;
                    }
                }
                else
                {
                    return false;
                }
            }

            return true;
        }

        /// <summary>
        /// Get test method name.
        /// </summary>
        /// <param name="callingMethodName"></param>
        /// <returns></returns>
        private static string GetTestMethodName([CallerMemberName] string callingMethodName = "")
        {
            return callingMethodName;
        }
    }

#nullable enable
    [ExcludeFromCodeCoverage]
    internal class TestExporter : BaseExporter<LogRecord>
    {
        internal LogRecord? FirstLogRecord { get; set; }

        internal List<KeyValuePair<string, object?>>? FirstState { get; set; }

        public override ExportResult Export(in Batch<LogRecord> batch)
        {
            foreach (var logRecord in batch)
            {
                FirstLogRecord = logRecord;
                FirstState = logRecord.Attributes is null ? null : new (logRecord.Attributes);
            }

            return ExportResult.Success;
        }
    }

    internal class TestProduct
    {
        public string? Name { get; set; }

        public double Price { get; set; }
    }
}
