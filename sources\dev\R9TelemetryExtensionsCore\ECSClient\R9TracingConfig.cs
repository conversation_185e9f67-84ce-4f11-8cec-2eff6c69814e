﻿// <copyright file="R9TracingConfig.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.Configuration;
using System.Globalization;
using System.Linq;

using Microsoft.Extensions.Configuration;
using Microsoft.M365.Core.Telemetry.Enrichment;
using Microsoft.M365.Core.Telemetry.SDKLogger;

using Newtonsoft.Json.Linq;

namespace Microsoft.M365.Core.Telemetry.ECSClient
{
    /// <summary>
    /// R9TracingConfig
    /// </summary>
    public class R9TracingConfig
    {
        /// <summary>
        /// If R9 distributed tracing is enabled.
        /// </summary>
        public bool R9DTEnabled { get; set; }

        /// <summary>
        /// Trace Sample Rate in DynamicRatioSampler.
        /// </summary>
        public float TraceSampleRate { get; set; }

        /// <summary>
        /// sampler type: RatioBased, ParentBased
        /// </summary>
        public Constants.DynamicSamplerType SamplerType { get; set; } = Constants.DynamicSamplerType.AlwaysOff;

        /// <summary>
        /// sampler type: RatioBased, AlwaysOff
        /// </summary>
        public Constants.DynamicSamplerType ParentRootSamplerType { get; set; } = Constants.DynamicSamplerType.AlwaysOff;

        /// <summary>
        /// Full ECS context
        /// </summary>
        public JObject EcsConfigRoot { get; private set; }

        /// <summary>
        /// whether config (Samplertype) changed
        /// </summary>
        public bool ConfigChanged { get; set; }

        /// <summary>
        /// The ECS context filter.
        /// </summary>
        private Dictionary<string, string> ecsContext;

        /// <summary>
        /// ecsClient instance.
        /// </summary>
        private readonly UnifiedTelemetryECSClient ecsClient;

        /// <summary>
        /// Default values for the configuration.
        /// </summary>
        private Dictionary<string, object> defaultValues = new Dictionary<string, object>();

        /// <summary>
        /// Constructor of the R9TracingConfig class.
        /// </summary>
        /// <param name="configuration"> The application configuration properties. </param>
        public R9TracingConfig(IConfiguration configuration)
        {
            try
            {
                SDKLog.Info("Initializing R9TracingConfig");

                //set default value from static settings
                SetDefaultValue(configuration);
                R9DTEnabled = (bool)defaultValues[nameof(R9DTEnabled)];
                TraceSampleRate = (float)defaultValues[nameof(TraceSampleRate)];
                SamplerType = ParseSamplerType((string)defaultValues[nameof(SamplerType)]);
                ParentRootSamplerType = ParseSamplerType((string)defaultValues[nameof(ParentRootSamplerType)]);

                this.ecsClient = UnifiedTelemetryECSClient.EcsClientInstance(configuration);

                //RuntimeModel is optional
                if (!string.IsNullOrEmpty(Environment.GetEnvironmentVariable("COSMIC_PODNAME")) && string.IsNullOrEmpty(this.ecsClient.RuntimeModel))
                {
                    this.ecsClient.RuntimeModel = "Cosmic";
                }
                if ("Cosmic".Equals(this.ecsClient.RuntimeModel, StringComparison.OrdinalIgnoreCase)
                    || "ModelD2".Equals(this.ecsClient.RuntimeModel, StringComparison.OrdinalIgnoreCase))
                {
                    ecsContext = new Dictionary<string, string>()
                    {
                        { "ServiceName", this.ecsClient.ServiceName },
                        { "Model",  this.ecsClient.RuntimeModel },
                        { "COSMIC_CLUSTER_NAME", Environment.GetEnvironmentVariable("COSMIC_CLUSTER_NAME") },
                        { "COSMIC_LOCATION",  Environment.GetEnvironmentVariable("COSMIC_LOCATION") },
                        { "COSMIC_NAMESPACE", Environment.GetEnvironmentVariable("COSMIC_NAMESPACE") },
                        { "COSMIC_NODENAME", Environment.GetEnvironmentVariable("COSMIC_NODENAME") },
                        { "COSMIC_PARTITION", Environment.GetEnvironmentVariable("COSMIC_PARTITION") },
                        { "COSMIC_PODNAME", Environment.GetEnvironmentVariable("COSMIC_PODNAME") },
                        { "COSMIC_REGION", Environment.GetEnvironmentVariable("COSMIC_REGION") },
                        { "COSMIC_RING", Environment.GetEnvironmentVariable("COSMIC_RING") },
                        { "COSMIC_VERSION", Environment.GetEnvironmentVariable("COSMIC_VERSION") }
                    };
                }
                else
                {
                    ecsContext = new Dictionary<string, string>()
                    {
                        { "DeployRing", DimensionValues.DeployRing.ToUpper(CultureInfo.InvariantCulture) },  //TDF,PDT,SDFV2,MSIT,DONMT,WW,EOPSDF,EOPMSFT
                        { "Machine", DimensionValues.Machine },  //BL0PR00MB0340
                        { "BuildVersion", DimensionValues.BuildVersion },  //16.20.6523.000
                        { "ServiceName", this.ecsClient.ServiceName },
                        { "Model",  this.ecsClient.RuntimeModel }, //ModelA,ModelB,ModelB2,ModelD,ModelD2,Cosmic
                        { "Dag",  DimensionValues.AvailabilityGroup }, //NAMPR00DG022
                        { "Forest", DimensionValues.Forest }, //NAMPRD01
                        { "Role", DimensionValues.Role }, //AD,CAMgt3,CAMgt2,MAFE,MS,MDB,MDW,MFS,MPS,MWS,BE,FE,OC...
                        { "Service", DimensionValues.Service }, //ServiceDogfood,PROD,ITAR,GALLATIN,RAPTORX,EAGLEX
                        { "Region", DimensionValues.Region } //NAM,EMEA,APAC
                    };
                }

                this.ecsClient.RegularUpdateEventWrapper.UpdateConfig += (sender, args) =>
                {
                    args.WaitHandleCounter.AddCount();
                    UpdateConfigValue(sender, args.ChangedAgents);
                    args.WaitHandleCounter.Signal();
                };
                string ecsContestMsg = string.Join(";", ecsContext.Select(x => x.Key + "=" + x.Value).ToArray());
                SDKLog.Info($"R9TracingConfig ECSContext. {ecsContestMsg}");

                // One time update config
                var changedAgents = new HashSet<string>(StringComparer.InvariantCultureIgnoreCase);
                changedAgents.Add("SOTELSTracing");
                UpdateConfigValue(default(Object), changedAgents);
            }
            catch (Exception e)
            {
                SDKLog.Error($"R9TracingConfig init exception: {e}");
            }
        }

        /// <summary>
        /// parse sample type from string
        /// </summary>
        /// <param name="samplerType"></param>
        /// <returns></returns>
        private static Constants.DynamicSamplerType ParseSamplerType(string samplerType)
        {
            foreach (Constants.DynamicSamplerType sampler in Enum.GetValues(typeof(Constants.DynamicSamplerType)))
            {
                if (sampler.ToString().Equals(samplerType, StringComparison.OrdinalIgnoreCase))
                {
                    return sampler;
                }
            }
            return Constants.DynamicSamplerType.AlwaysOff;
        }

        /// <summary>
        /// Update R9TracingConfig value.
        /// </summary>
        /// <param name="sender"> The sender. </param>
        /// <param name="changedAgents"> Agents that has been changed. </param>
        private void UpdateConfigValue(object sender, HashSet<string> changedAgents)
        {
            try
            {
                _ = sender;
                if (changedAgents == null || !changedAgents.Contains("SOTELSTracing"))
                {
                    return;
                }

                EcsConfigRoot = ECSClientUtilities.GetUnifiedTelemetryConfig("SOTELSTracing", ecsContext, ecsClient.EcsRequester);
                if (EcsConfigRoot == null)
                {
                    return;
                }

                SDKLog.Info(string.Format(CultureInfo.InvariantCulture, "SOTELSTracing refresh: {0}", EcsConfigRoot.ToString()));
                JToken res;

                // R9 default boolean value is false
                R9DTEnabled = (res = ECSClientUtilities.ParseConfig(EcsConfigRoot, Constants.R9DTEnabled)) != null ? res.ToObject<bool>() : (bool)defaultValues[nameof(R9DTEnabled)];

                // TraceSampleRate default value is 0.0
                TraceSampleRate = (res = ECSClientUtilities.ParseConfig(EcsConfigRoot, Constants.TraceSampleRate)) != null
                    ? res.ToObject<float>()
                    : (float)defaultValues[nameof(TraceSampleRate)];

                // SamplerType default value is AlwaysOff
                var tempSamplerType = ParseSamplerType(ECSClientUtilities.ParseConfig(EcsConfigRoot, Constants.SamplerType)?.ToString() ?? (string)defaultValues[nameof(SamplerType)]);
                var tempParentRootSamplerType = ParseSamplerType(ECSClientUtilities.ParseConfig(EcsConfigRoot, Constants.ParentRootSamplerType)?.ToString() ?? (string)defaultValues[nameof(ParentRootSamplerType)]);
                if (tempSamplerType != SamplerType || tempParentRootSamplerType != ParentRootSamplerType)
                {
                    ConfigChanged = true;
                }

                SamplerType = tempSamplerType;
                ParentRootSamplerType = tempParentRootSamplerType;
            }
            catch (Exception e)
            {
                SDKLog.Error($"UpdateConfigValue for SOTELSTracing failed : {e}");
            }
        }

        private void SetDefaultValue(IConfiguration configuration)
        {
            var deployRing = DimensionValues.DeployRing.ToUpper(CultureInfo.InvariantCulture);
            this.defaultValues.Add(nameof(R9DTEnabled), R9DTEnabled = bool.TryParse(configuration.GetValue<string>($"{deployRing}:{nameof(R9DTEnabled)}"), out bool temp) && temp);
            this.defaultValues.Add(nameof(TraceSampleRate), float.TryParse(configuration.GetValue<string>($"{deployRing}:{nameof(TraceSampleRate)}"), out float tempFloat) ? tempFloat : 0.0F);
            this.defaultValues.Add(nameof(SamplerType), configuration.GetValue<string>($"{deployRing}:{nameof(SamplerType)}"));
            this.defaultValues.Add(nameof(ParentRootSamplerType), configuration.GetValue<string>($"{deployRing}:{nameof(ParentRootSamplerType)}"));
        }
    }
}
