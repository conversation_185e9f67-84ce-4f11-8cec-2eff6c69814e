﻿// <copyright file="DefaultSecurityRecordFormatter.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Microsoft.Extensions.ObjectPool;
using Microsoft.R9.Extensions.SecurityTelemetry;
using Newtonsoft.Json;

namespace Microsoft.M365.Core.Telemetry.ODL.SecurityTelemetryTcpExporter
{
    /// <summary>
    /// The default formatter for <see cref="SecurityRecord"/>, this is for turning security record into strings 
    /// before packaging them into NRTRequest
    /// </summary>
    public static class DefaultSecurityRecordFormatter
    {
        private const string SchemaNameKey = "SchemaName";

        private const string ValidationStatusKey = "ValidationStatus";

        /// <summary>
        /// Formatter of <see cref="SecurityRecord"/> class into a jons string containing both static and dynamic fields
        /// </summary>
        /// <param name="securityRecord"><see cref="SecurityRecord"/>the security record</param>
        /// <param name="staticProperties">the static properties for the given <paramref name="securityRecord"/></param>
        public static string FormatSecurityRecord(in SecurityRecord securityRecord, IReadOnlyDictionary<string, object> staticProperties)
        {
            Dictionary<string, object?> completeRecord = securityRecord.Properties.ToDictionary(pair => pair.Key, pair => pair.Value);
            completeRecord[SchemaNameKey] = securityRecord.SchemaName;
            completeRecord[ValidationStatusKey] = securityRecord.ValidationStatus;

            // Each key name should be unique among both static and dynamic keys, otherwise it will be overrided by default
            foreach (var property in staticProperties)
            {
                completeRecord[property.Key] = property.Value;
            }

            return JsonConvert.SerializeObject(completeRecord);
        }
    }
}
