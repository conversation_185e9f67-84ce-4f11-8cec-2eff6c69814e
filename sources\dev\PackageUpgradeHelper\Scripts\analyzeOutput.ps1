param(
    [Parameter(Mandatory=$false)]
    [string]$ResultFilePath = ".\OutputFiles\result.txt",
    
    [Parameter(Mandatory=$false)]
    [string]$FilterPackagesFilePath = ".\OutputFiles\filterPackages.txt",
    
    [Parameter(Mandatory=$false)]
    [string]$TargetPackagesFilePath = ".\OutputFiles\targetPackages.txt",
    
    [Parameter(Mandatory=$false)]
    [string]$UpgradeOnlyListPath = ".\OutputFiles\UpgradeOnlyList.txt",
    
    [Parameter(Mandatory=$false)]
    [string]$UpgradeAddListPath = ".\OutputFiles\UpgradeAddList.txt",
    
    [Parameter(Mandatory=$false)]
    [string]$RootNewPackageMapPath = ".\OutputFiles\RootNewPackageMap.txt"
)

# Initialize dictionaries
$upgradeRequiredDict = @{}
$noDirectDependencyDict = @{}
$rootPackageDependencies = @{}

# Load filter packages
$filterPackages = Get-Content $FilterPackagesFilePath | Where-Object { $_ -match "^[^<]" } | ForEach-Object { $_.Trim() }

# Load target packages
$targetPackages = @{}
Get-Content $TargetPackagesFilePath | Where-Object { $_ -match "^\s*[^/]" } | ForEach-Object {
    if ($_ -match "^\s*([^\s]+)\s+([^\s]+)\s*$") {
        $packageName = $matches[1]
        $packageVersion = $matches[2]
        $targetPackages[$packageName] = $packageVersion
    }
}

$currentRootPackage = ""

# Function to compare semantic versions (supports versions like "9.1.0-dev")
function Compare-Version {
    param (
        [string]$version1,
        [string]$version2
    )
    try {
        $v1 = [System.Management.Automation.SemanticVersion]::Parse($version1)
        $v2 = [System.Management.Automation.SemanticVersion]::Parse($version2)
        return $v1.CompareTo($v2)
    }
    catch {
        Write-Warning "Invalid version format: '$version1' or '$version2'"
        return 0
    }
}

# Regex patterns
$upgradePattern = '- ([^\s]+) \(>= ([^\)]+)\): Upgrade required'
$noDependencyPattern = '- ([^\s]+) \(>= ([^\)]+)\): No direct dependency'
$rootPackagePattern = 'Arguments: package plan-upgrade ([^\s]+) -v ([^\s]+)'

# Read file and process lines
Get-Content $ResultFilePath | ForEach-Object {
    $line = $_
    if ($line -match $rootPackagePattern) {
        $currentRootPackage = $matches[1]
        if (-not $rootPackageDependencies.ContainsKey($currentRootPackage)) {
            $rootPackageDependencies[$currentRootPackage] = @{}
        }
    }
    elseif ($line -match $upgradePattern) {
        $packageName = $matches[1]
        $targetVersion = $matches[2]
        # Skip if package is in target packages list
        if ($targetPackages.ContainsKey($packageName)) {
            
        }
        elseif (-not $upgradeRequiredDict.ContainsKey($packageName) -or (Compare-Version $upgradeRequiredDict[$packageName] $targetVersion) -lt 0) {
            $upgradeRequiredDict[$packageName] = $targetVersion
        }
    }
    elseif ($line -match $noDependencyPattern) {
        $packageName = $matches[1]
        $targetVersion = $matches[2]
        
        # Skip if package is in filter list
        if ($filterPackages -contains $packageName) {
            
        }
        elseif (-not $noDirectDependencyDict.ContainsKey($packageName) -or (Compare-Version $noDirectDependencyDict[$packageName] $targetVersion) -lt 0) {
            $noDirectDependencyDict[$packageName] = $targetVersion
            
            # Only add packages with noDependencyPattern to rootPackageDependencies
            if ($currentRootPackage -ne "" -and -not $rootPackageDependencies[$currentRootPackage].ContainsKey($packageName)) {
                $rootPackageDependencies[$currentRootPackage][$packageName] = $targetVersion
            }
        }
    }
}

# Ensure output directories exist
$UpgradeOnlyDir = Split-Path -Parent $UpgradeOnlyListPath
if (-not (Test-Path $UpgradeOnlyDir) -and $UpgradeOnlyDir) {
    if ($PSCmdlet.ShouldProcess($UpgradeOnlyDir, "Create directory")) {
        New-Item -Path $UpgradeOnlyDir -ItemType Directory -Force | Out-Null
    }
}

$UpgradeAddDir = Split-Path -Parent $UpgradeAddListPath
if (-not (Test-Path $UpgradeAddDir) -and $UpgradeAddDir) {
    if ($PSCmdlet.ShouldProcess($UpgradeAddDir, "Create directory")) {
        New-Item -Path $UpgradeAddDir -ItemType Directory -Force | Out-Null
    }
}

$RootNewPackageDir = Split-Path -Parent $RootNewPackageMapPath
if (-not (Test-Path $RootNewPackageDir) -and $RootNewPackageDir) {
    if ($PSCmdlet.ShouldProcess($RootNewPackageDir, "Create directory")) {
        New-Item -Path $RootNewPackageDir -ItemType Directory -Force | Out-Null
    }
}

# Generate UpgradeOnlyList output
$smallresult = @()
$upgradeRequiredDict.GetEnumerator() | Sort-Object Key | ForEach-Object {
    $smallresult += "$($_.Key) $($_.Value)"
}

if ($PSCmdlet.ShouldProcess($UpgradeOnlyListPath, "Write upgrade-only list")) {
    $smallresult | Out-File -FilePath $UpgradeOnlyListPath -Force
}

# Generate UpgradeAddList output
$results = @()
$results += "Packages requiring upgrade:"

$upgradeRequiredDict.GetEnumerator() | Sort-Object Key | ForEach-Object {
    $results += "$($_.Key): $($_.Value)"
}

$results += "`nPackages to be added:"
$noDirectDependencyDict.GetEnumerator() | Sort-Object Key | ForEach-Object {
    $results += "$($_.Key): $($_.Value)"
}

if ($PSCmdlet.ShouldProcess($UpgradeAddListPath, "Write upgrade and add list")) {
    $results | Out-File -FilePath $UpgradeAddListPath -Force
}

# Filter rootPackageDependencies to only include entries with more than 1 item
$filteredRootPackageDependencies = @{}
$rootPackageDependencies.GetEnumerator() | ForEach-Object {
    $rootPackage = $_.Key
    $dependencies = $_.Value
    
    if ($dependencies.Count -gt 0) {
        $filteredRootPackageDependencies[$rootPackage] = $dependencies
    }
}

# Output summary information
Write-Host "Original root packages: $($rootPackageDependencies.Count)"
Write-Host "Filtered root packages (with dependencies): $($filteredRootPackageDependencies.Count)"

# Create RootNewPackageMap output
$outputContent = ""

# Create content in the format: RootPackage,RootVersion,newPackage,NewPackageVersion
foreach ($rootPackage in $filteredRootPackageDependencies.Keys) {
    $dependencies = $filteredRootPackageDependencies[$rootPackage]
    $rootVersion = $targetPackages[$rootPackage]
    foreach ($dependency in $dependencies.Keys) {
        $version = $dependencies[$dependency]
        $outputContent += "$rootPackage,$rootVersion,$dependency,$version`r`n"
    }
}

# Write to file
if ($PSCmdlet.ShouldProcess($RootNewPackageMapPath, "Write root-new package map")) {
    Set-Content -Path $RootNewPackageMapPath -Value $outputContent
}

Write-Host "Analysis complete. Results written to:"
Write-Host "- Upgrade-only packages: $UpgradeOnlyListPath"
Write-Host "- Upgrade and add packages: $UpgradeAddListPath"
Write-Host "- Root package dependencies: $RootNewPackageMapPath"