
## Overview
There are several methods to validate that OpenTelemetry .NET Geneva Exporter is correctly configured and setup to export metrics on a local machine. 
- Collect metrics by using **MdmMetricsExtension** and then view metrics on Jarvis dashboard. Both Windows and Linux environments are feasible.
- Collect metrics by using **MetricDog** and view metrics on local files in Windows environment.
- Collect metrics by using **PerfView** in Windows environment.
- Collect metrics by using **dotnet-trace** in Linux environment.

## Validate metrics through MdmMetricsExtension
If you want to emit metrics on local development machine and view metrics on Jarvis Dashboard. Please follow up with this wiki. [Test: See Metrics in action using your dev-box | Geneva Monitoring Docs](https://eng.ms/docs/products/geneva/metrics/metricsagentsdk/testmetricscollection)
Both Windows and Linux environments are supported.

## Validate metrics through MetricDog
If you don't want to create Geneva test account or add certificate things. An easier way to validate metrics on a **Windows** machine is using MetricDog.
[MetricDog](https://aka.ms/metricdog) is a troubleshooting/diagnostics tool which decodes metric traffic capture files and dumps it in human-readable formats. Supported protocols for metric data are `ifxMetrics`, `statsd`, `influxDB`, `otlp` and supported output file types are `csv`, `tsv` and `json`. It also supports consuming ETW events in **real-time**.

For step-by-step instructions see [Windows Local Validation for Metrics](https://eng.ms/docs/products/geneva/collect/instrument/opentelemetrydotnet/otel-sdklocalvalidation/otel-metricslocalvalidation).

## Validate metrics through PerfView
You can validate metrics through PerfView tool on a **Windows** machine too. Please follow up with this wiki. [Metrics troubleshooting using PerfView | R9 SDK](https://eng.ms/docs/experiences-devices/r9-sdk/docs/telemetry/metering/metrics-troubleshooting#:~:text=information%20see%20MetricDog-,Method%202,-You%20can%20use)

## Validate metrics though donet-trace
You can validate metrics through dotnet-trace on a **Linux** machine. Please follow up with this wiki. [Metrics troubleshooting using donet-trace | R9 SDK](https://eng.ms/docs/experiences-devices/r9-sdk/docs/telemetry/metering/metrics-troubleshooting#:~:text=using%20Windows%20containers.-,Linux,-You%20can%20use)
