﻿// <copyright file="PackageUpgradeScenario.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using PackageUpgradeHelper.ADO;
using PackageUpgradeHelper.ProjectFile;

namespace PackageUpgradeHelper
{
    /// <summary>
    /// PackageUpgradeScenario
    /// </summary>
    class PackageUpgradeScenario
    {
        /// <summary>
        /// Create a new branch
        /// </summary>
        /// <param name="newBranchName"></param>
        public static void CreateBranch(string? newBranchName)
        {
            ColorConsoleWriter.WriteLine("=======================================================================================", ConsoleColor.Cyan);
            ColorConsoleWriter.WriteLine("Step 1 Make sure target branch is existed.", ConsoleColor.Cyan);
            ColorConsoleWriter.WriteLine("=======================================================================================", ConsoleColor.Cyan);
            var name = newBranchName ?? $"user/packageupgradehelper/Upgrade{DateTime.UtcNow.Microsecond}{DateTime.UtcNow.Millisecond}";
            if (string.IsNullOrEmpty(newBranchName))
            {
                Console.WriteLine($"New branch name is not provided. A random branch {name} will be created.");
            }
            var gitSkill = new GitSkill();
            var latestCommitId = gitSkill.GetLatestCommitId("master").Result;
            try
            {
                gitSkill.CreateBranch(latestCommitId, name).Wait();
                ColorConsoleWriter.WriteLine($"Branch {name} is existed.\n", ConsoleColor.Gray);
            }
            catch
            {
                ColorConsoleWriter.WriteLine($"Warn: Branch {name} is already existed. Make sure this is your target branch", ConsoleColor.Yellow);
            }
            finally
            {
                ColorConsoleWriter.WriteLine($"[Action Needed] Please switch to {name} in your local Substrate repo before any other operations.", ConsoleColor.Yellow);
                ColorConsoleWriter.WriteLine("For demo, we mocked the opertion for user.\n", ConsoleColor.Green);
            }
        }

        /// <summary>
        /// Run Substrate CLI
        /// </summary>
        public static void RunSubstrateCLI(bool skip)
        {
            ColorConsoleWriter.WriteLine("=======================================================================================", ConsoleColor.Cyan);
            ColorConsoleWriter.WriteLine("Step 2 Run scripts to do basic upgrading", ConsoleColor.Cyan);
            ColorConsoleWriter.WriteLine("=======================================================================================", ConsoleColor.Cyan);
            // Run the one-click script
            if (!skip)
            {
                ColorConsoleWriter.WriteLine("Start to run Substrate CLI.", ConsoleColor.Cyan);
                PackageUpgradeExecutor.ExecuteUpgrade("8.11.0");
                ColorConsoleWriter.WriteLine("Substrate CLI is executed successfully.", ConsoleColor.Green);
            }
            else
            {
                ColorConsoleWriter.WriteLine("It takes about 1h to run. Skip this time for demo purpose.", ConsoleColor.Yellow);
                ColorConsoleWriter.WriteLine("In this step, we finish the following tasks:", ConsoleColor.Yellow);
                ColorConsoleWriter.WriteLine("1. Analyze package dependency of target packages", ConsoleColor.Yellow);
                ColorConsoleWriter.WriteLine("2. Update package.props and corext", ConsoleColor.Yellow);
                ColorConsoleWriter.WriteLine("3. Update binding redirects for **existing** packages", ConsoleColor.Yellow);
                ColorConsoleWriter.WriteLine("4. Update sharedBingredirects", ConsoleColor.Yellow);
                Console.WriteLine();
            }
            ColorConsoleWriter.WriteLine("[Action Needed] Please run update sharedBingredirects manually in Substrate enlistment.", ConsoleColor.Yellow);
            ColorConsoleWriter.WriteLine("[Action Needed] Push all changes to remote branch before running the next step.", ConsoleColor.Yellow);
            if (skip)
            {
                ColorConsoleWriter.WriteLine("For demo, we mocked the opertion for user.\n", ConsoleColor.Green);
            }
        }

        /// <summary>
        /// Upgrade package version
        /// </summary>
        public static void UpgradePackageVersion(string newBranchName)
        {
            var getFileContentSkill = new GetFileContentSkill();
            var searchCodeSkill = new SearchCodeSkill();
            var gitSkill = new GitSkill();
            var latestCommitId = gitSkill.GetLatestCommitId(newBranchName).Result;

            // Prepare all file changes
            var fileChanges = new List<FileChange>();

            // Read output files
            // The output files should be generated under ./OutputFiles folder
            List<string> upgradedPackages =
            [.. File.ReadAllLines("OutputFiles/UpgradedPackages.txt")];
            List<string> addedPackages =
            [.. File.ReadAllLines("OutputFiles/AddedPackages.txt")];
            List<string> filterPackages =
            [.. File.ReadAllLines("OutputFiles/filterPackages.txt")];
            List<string> rootNewPackageMap =
            [.. File.ReadAllLines("OutputFiles/RootNewPackageMap.txt")];
            List<string> targetPackages =
            [.. File.ReadAllLines("OutputFiles/targetPackages.txt")];
            GetPackageDependencySkill getPackageDependencySkill = new GetPackageDependencySkill();
            List<Tuple<DllRecord, DllRecord>> rootPackageMapping = new List<Tuple<DllRecord, DllRecord>>();
            foreach (var rootNewPackage in rootNewPackageMap)
            {
                var rootNewPackageArray = rootNewPackage.Split(",");
                var rootPackageName = rootNewPackageArray[0];
                var rootPackageVersion = rootNewPackageArray[1];
                var introducedPackageName = rootNewPackageArray[2];
                var introducedPackageVersion = rootNewPackageArray[3];
                var rootDllList = getPackageDependencySkill.GetPackageDependencySingle(rootPackageName, rootPackageVersion).Result;
                DllRecord rootNetFrameworkDll = rootDllList.FirstOrDefault(r => r.TargetFramework.Equals("NetFramework")) ?? new DllRecord();
                var introducedDllList = getPackageDependencySkill.GetPackageDependencySingle(introducedPackageName, introducedPackageVersion).Result;
                DllRecord introducedNetFrameworkDll = introducedDllList.FirstOrDefault(r => r.TargetFramework.Equals("NetFramework")) ?? new DllRecord();
                if (!string.IsNullOrEmpty(rootNetFrameworkDll.DllName))
                {
                    rootPackageMapping.Add(new Tuple<DllRecord, DllRecord>(rootNetFrameworkDll, introducedNetFrameworkDll));
                }
            }
            var distinctNewPackages = rootPackageMapping.Select(r => r.Item2).DistinctBy(r => r.DllName).ToList();
            ColorConsoleWriter.WriteLine("=======================================================================================", ConsoleColor.Cyan);
            ColorConsoleWriter.WriteLine("Step 3 Analyse output files.", ConsoleColor.Cyan);
            ColorConsoleWriter.WriteLine("=======================================================================================", ConsoleColor.Cyan);
            ColorConsoleWriter.WriteLine("After analyzing the output files, there are several new packages: ", ConsoleColor.Gray);
            foreach (var newPackage in distinctNewPackages)
            {
                ColorConsoleWriter.WriteLine($"{newPackage.PackageName}", ConsoleColor.Gray);
            }
            Console.WriteLine();

            // Add references to Binplace for new introduced packages
            // todo: need fix
            ColorConsoleWriter.WriteLine("=======================================================================================", ConsoleColor.Cyan);
            ColorConsoleWriter.WriteLine("Step 4 Start to add references to binplace for new packages.", ConsoleColor.Cyan);
            ColorConsoleWriter.WriteLine("=======================================================================================", ConsoleColor.Cyan);

            var modifyBinPlaceSkill = new ModifyBinPlaceSkill();
            string binPlacePath = "/sources/dev/common/src/BinPlaceForPackages/BinPlaceForPackages.csproj";
            string binPlaceContent = binPlaceContent = getFileContentSkill.GetFileContent(binPlacePath, newBranchName).Result;
            foreach (var addedPackage in addedPackages)
            {
                var packageInfoArray = addedPackage.Split(": ");
                var dllList = getPackageDependencySkill.GetPackageDependencySingle(packageInfoArray[0], packageInfoArray[1]).Result;              
                binPlaceContent = modifyBinPlaceSkill.ModifyBinPlace(dllList, binPlaceContent);
            }
            fileChanges.Add(new FileChange(binPlacePath, "edit", binPlaceContent));
            if (fileChanges.Count > 0)
            {
                var comment = "Add references to binplace for new introduced packages";
                latestCommitId = gitSkill.PushToRemoteBranch(newBranchName, latestCommitId, fileChanges, comment).Result;
                fileChanges.Clear();
                ColorConsoleWriter.WriteLine("Adding references to binplace is successful.\n", ConsoleColor.Green);
            }
            else
            {
                ColorConsoleWriter.WriteLine("No references need to add to binplace.\n", ConsoleColor.Gray);
            }

            // Add XMLDrop entry for new introduced packages
            ColorConsoleWriter.WriteLine("=======================================================================================", ConsoleColor.Cyan);
            ColorConsoleWriter.WriteLine("Step 5 Start to add references to xmldrop files for new packages.", ConsoleColor.Cyan);
            ColorConsoleWriter.WriteLine("=======================================================================================", ConsoleColor.Cyan);
            ModifyXMLDropSkill modifyXMLDropSkill = new ModifyXMLDropSkill();
            
            // Collect DLL information that needs to be added to each XML file
            var xmlFileModifications = new Dictionary<ADOFileInfo, List<(string rootDllName, string newDllName, string newDllPath)>>();
            foreach (var recordTuple in rootPackageMapping)
            {
                DllRecord rootNetFrameworkDll = recordTuple.Item1;
                DllRecord introducedNetFrameworkDll = recordTuple.Item2;
                List<ADOFileInfo> adoFileInfos = modifyXMLDropSkill.FindTargetXMLDropFilesAsync(rootNetFrameworkDll!.DllName).Result;
                foreach (var adoFileInfo in adoFileInfos)
                {
                    if (!xmlFileModifications.ContainsKey(adoFileInfo))
                    {
                        xmlFileModifications[adoFileInfo] = new List<(string, string, string)>();
                    }
                    xmlFileModifications[adoFileInfo].Add((rootNetFrameworkDll.DllName, introducedNetFrameworkDll.DllName, introducedNetFrameworkDll.LibraryDirectoryPath));
                }
            }

            // Apply modifications to each XML file at once
            foreach (var xmlFileModification in xmlFileModifications)
            {
                var xmlDropContent = modifyXMLDropSkill.AddXMLDropEntriesForNewDependencies(xmlFileModification.Key, xmlFileModification.Value, newBranchName);
                fileChanges.Add(new FileChange(xmlFileModification.Key.Path, "edit", xmlDropContent));
            }

            if (fileChanges.Count > 0)
            {
                var comment = "Add XMLDrop entry for new introduced packages";
                latestCommitId = gitSkill.PushToRemoteBranch(newBranchName, latestCommitId, fileChanges, comment).Result;
                fileChanges.Clear();
                ColorConsoleWriter.WriteLine("Adding XMLDrop entry for new introduced packages is successful.\n", ConsoleColor.Green);
            }
            else
            {
                ColorConsoleWriter.WriteLine("No XMLDrop entries need to add.\n", ConsoleColor.Gray);
            }

            // Add references to csproj 
            ColorConsoleWriter.WriteLine("=======================================================================================", ConsoleColor.Cyan);
            ColorConsoleWriter.WriteLine("Step 6 Start to add references to csproj files for new packages.", ConsoleColor.Cyan);
            ColorConsoleWriter.WriteLine("=======================================================================================", ConsoleColor.Cyan);

            Dictionary<DllRecord, List<DllRecord>> newPackageToRoot = new Dictionary<DllRecord, List<DllRecord>>();
            foreach ((var rootPackage, var newPackage) in rootPackageMapping)
            {
                var roots = newPackageToRoot.TryGetValue(newPackage, out var existingRoots) ? existingRoots : new List<DllRecord>();
                roots.Add(rootPackage);
                newPackageToRoot[newPackage] = roots;
            }
            foreach ((var newPackage, var roots) in newPackageToRoot)
            {
                ColorConsoleWriter.WriteLine($"Start to add references for {newPackage.PackageName}", ConsoleColor.Gray);
                List<ADOFileInfo> fileInfos = new List<ADOFileInfo>();
                foreach (var root in roots)
                {
                    var ret = searchCodeSkill.SearchCodeAsync($"ext:csproj {root.PackageName} NOT {newPackage.DllName}").Result;
                    fileInfos.AddRange(ret);
                }
                fileInfos.DistinctBy(fileInfos => fileInfos.Path);
                foreach (var fileInfo in fileInfos)
                {
                    var content = getFileContentSkill.GetFileContent(fileInfo.Path, newBranchName).Result;
                    var modifyProjectFileSkill = new ModifyProjectFileReferenceSkill();
                    var newContent = modifyProjectFileSkill.AddNewPackageReference(fileInfo, roots.Select(r => r.PackageName).ToList(), newPackage.HintPath, newBranchName);
                    if (!string.IsNullOrEmpty(newContent))
                    {
                        fileChanges.Add(new FileChange(fileInfo.Path, "edit", newContent));
                    }
                }
                if (fileChanges.Count > 0)
                {
                    var comment = $"Add references for {newPackage.PackageName}";
                    latestCommitId = gitSkill.PushToRemoteBranch(newBranchName, latestCommitId, fileChanges, comment).Result;
                    fileChanges.Clear();
                    ColorConsoleWriter.WriteLine($"Adding references for {newPackage.PackageName} is successful.", ConsoleColor.Green);
                }
                else
                {
                    ColorConsoleWriter.WriteLine($"No references need to add for {newPackage.PackageName}.", ConsoleColor.Gray);
                }
            }
            Console.WriteLine();

            // Add binding redirects for new introduced packages
            ColorConsoleWriter.WriteLine("=======================================================================================", ConsoleColor.Cyan);
            ColorConsoleWriter.WriteLine("Step 7 Start to add references to binding redirects files for new packages.", ConsoleColor.Cyan);
            ColorConsoleWriter.WriteLine("=======================================================================================", ConsoleColor.Cyan);
            var readSharedBindingRedirectsSkill = new ReadSharedBindingRedirectsSkill();
            var modifyBindingRedirectSkill = new ModifyBindingRedirectSkill();
            Dictionary<ADOFileInfo, List<string>> fileToNewAddedXML = new Dictionary<ADOFileInfo, List<string>>();
            foreach ((var rootPackage, var newPackage) in rootPackageMapping)
            {
                var bindingRedirectsXML = readSharedBindingRedirectsSkill.ReadAssemblyXMLRemotely(newPackage.DllName, newBranchName);
                var searchText = $"ext:config {rootPackage.DllName}";
                var bindingRedirectsFiles = searchCodeSkill.SearchCodeAsync(searchText).Result;

                foreach (var fileInfo in bindingRedirectsFiles)
                {
                    var list = fileToNewAddedXML.TryGetValue(fileInfo, out var existingList) ? existingList : new List<string>();
                    list.Add(bindingRedirectsXML);
                    fileToNewAddedXML[fileInfo] = list;
                }
            }
            foreach ((var fileInfo, var list) in fileToNewAddedXML)
            {
                var distinctedList = list.Distinct().ToList();
                var content = modifyBindingRedirectSkill.AddAssemblyBindingRedirect(fileInfo, distinctedList, newBranchName);
                fileChanges.Add(new FileChange(fileInfo.Path, "edit", content));
            }
            if (fileChanges.Count > 0)
            {
                var comment = "Add binding redirects for new introduced packages";
                latestCommitId = gitSkill.PushToRemoteBranch(newBranchName, latestCommitId, fileChanges, comment).Result;
                fileChanges.Clear();
                ColorConsoleWriter.WriteLine("Adding binding redirects for new introduced packages is successful\n", ConsoleColor.Gray);
            }
            else
            {
                ColorConsoleWriter.WriteLine("No binding redirects need to add\n", ConsoleColor.Gray);
            }

            ColorConsoleWriter.WriteLine("All steps are done.", ConsoleColor.Green);
            ColorConsoleWriter.WriteLine("Please check the changes in the remote branch.", ConsoleColor.Gray);
        }
    }
}
