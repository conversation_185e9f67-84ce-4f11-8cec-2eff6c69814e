// <copyright file="MatcherString.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Microsoft.M365.Core.Telemetry.R9.Logging.Substrate.Sampler
{
    /// <summary>
    /// String related methods for matcher.
    /// <see cref="TryMatch{TState}(in Extensions.Logging.Abstractions.LogEntry{TState})"/> for matching logic.
    /// </summary>
    internal partial class Matcher
    {
        private static bool CompareStringValues(IReadOnlyList<KeyValuePair<string, object?>> state, string fieldName, string fieldValue, Func<string, string, bool> compareFunc)
        {
            var fieldKVP = state.FirstOrDefault(kv => kv.Key == fieldName);
            if (fieldKVP.Value == null)
            {
                return false;
            }

            var logValue = fieldKVP.Value.ToString();
            if (logValue == null)
            {
                return false;
            }

            return compareFunc(logValue, fieldValue);
        }

        private static MatchFunc GenerateStringMatchFunc(Constraint constraint)
        {
            var fieldName = constraint.Field;
            var fieldValue = constraint.Value;

            return constraint.RuleOperator switch
            {
                OperatorType.StartsWith => (_, _, state) =>
                    CompareStringValues(state, fieldName, fieldValue, (s, v) => s.StartsWith(v, StringComparison.Ordinal)),

                OperatorType.EndsWith => (_, _, state) =>
                    CompareStringValues(state, fieldName, fieldValue, (s, v) => s.EndsWith(v, StringComparison.Ordinal)),

                OperatorType.StringEquals => (_, _, state) =>
                    CompareStringValues(state, fieldName, fieldValue, (s, v) => s == v),

                OperatorType.NotStartsWith => (_, _, state) =>
                    CompareStringValues(state, fieldName, fieldValue, (s, v) => !s.StartsWith(v, StringComparison.Ordinal)),

                OperatorType.NotEndsWith => (_, _, state) =>
                    CompareStringValues(state, fieldName, fieldValue, (s, v) => !s.EndsWith(v, StringComparison.Ordinal)),

                OperatorType.StringNotEquals => (_, _, state) =>
                    CompareStringValues(state, fieldName, fieldValue, (s, v) => s != v),

                _ => throw new NotImplementedException($"Unsupported operator type: {constraint.RuleOperator}") // This should never happen, we should have validated the constraints before.
            };
        }
    }
}
