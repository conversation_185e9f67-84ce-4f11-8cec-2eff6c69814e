{"options": [{"enabled": false, "definition": {"id": "a9db38f9-9fdc-478c-b0f9-464221e58316"}, "inputs": {"workItemType": "399868", "assignToRequestor": "false", "additionalFields": "{}"}}, {"enabled": false, "definition": {"id": "5d58cc01-7c75-450c-be18-a388ddb129ec"}, "inputs": {"branchFilters": "[\"+refs/heads/*\"]", "additionalFields": "{}"}}], "variables": {"build.cloudbuildqueue": {"value": "OSS_{{RepositoryName}}_Retail_Git"}, "build.pipeline": {"value": "{{RepositoryName}}"}, "system.debug": {"value": "false", "allowOverride": true}}, "retentionRules": [{"branches": ["+refs/heads/*"], "artifacts": ["build.SourceLabel"], "artifactTypesToDelete": [], "daysToKeep": 10, "minimumToKeep": 1, "deleteBuildRecord": true, "deleteTestResults": true}], "tags": [], "jobAuthorizationScope": 1, "jobTimeoutInMinutes": 60, "jobCancelTimeoutInMinutes": 5, "process": {"phases": [{"steps": [{"environment": {}, "enabled": true, "continueOnError": true, "alwaysRun": true, "displayName": "MSBR: Precheckin Retail Agentless $(build.cloudbuildqueue)", "timeoutInMinutes": 0, "condition": "succeededOrFailed()", "task": {"id": "81d4d6ba-2202-4bd8-a832-26eae5ea9b73", "versionSpec": "1.*", "definitionType": "metaTask"}, "inputs": {"build_cloudBuildQueue": "$(build.cloudbuildqueue)", "DropMetadata": ""}}], "name": "Retail", "refName": "Retail", "condition": "succeeded()", "target": {"executionOptions": {"type": 0}, "type": 2}, "jobAuthorizationScope": 1}], "type": 1}, "repository": {"properties": {"labelSources": "0", "reportBuildStatus": "false", "fetchDepth": "0", "gitLfsSupport": "false", "skipSyncSource": "true", "cleanOptions": "0", "checkoutNestedSubmodules": "false", "labelSourcesFormat": "$(build.buildNumber)"}, "id": "{{RepositoryId}}", "type": "TfsGit", "name": "{{RepositoryName}}", "defaultBranch": "refs/heads/master", "clean": "true", "checkoutSubmodules": false}, "processParameters": {}, "quality": 1, "drafts": [], "queue": {"id": 26, "name": "Official", "url": "https://o365exchange.visualstudio.com/_apis/build/Queues/26", "pool": {"id": 11, "name": "Official"}}, "name": "{{RepositoryName}} Precheckin_Retail", "path": "\\{{RepositoryName}}", "type": 2}