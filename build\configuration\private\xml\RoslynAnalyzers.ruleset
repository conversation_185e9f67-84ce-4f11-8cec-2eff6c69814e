﻿<?xml version="1.0" encoding="utf-8"?>
<RuleSet Name="Security Rules with default action" Description="All Security Rules with default action. Rules with IsEnabledByDefault = false or from a different category are disabled." ToolsVersion="15.0">
  <Rules AnalyzerId="Microsoft.CodeAnalysis.CSharp" RuleNamespace="Microsoft.CodeAnalysis.CSharp">
    <Rule Id="AD0001" Action="None" />
    <Rule Id="IDE1002" Action="None" />
  </Rules>
  <Rules AnalyzerId="Internal.Analyzers" RuleNamespace="Internal.Analyzers">
    <Rule Id="IA2992" Action="Warning" />
    <Rule Id="IA2993" Action="Warning" />
    <Rule Id="IA2994" Action="Warning" />
    <Rule Id="IA2995" Action="Warning" />
    <Rule Id="IA2996" Action="Warning" />
    <Rule Id="IA2997" Action="Warning" />
    <Rule Id="IA2998" Action="Warning" />
    <Rule Id="IA2999" Action="Warning" />
  </Rules>
  <Rules AnalyzerId="Microsoft.CodeQuality.Analyzers" RuleNamespace="Microsoft.CodeQuality.Analyzers">
    <Rule Id="Async001" Action="Warning" />
    <Rule Id="Async002" Action="Warning" />
    <Rule Id="Async003" Action="Warning" />
    <Rule Id="Async004" Action="Warning" />
    <Rule Id="Async005" Action="Warning" />
    <Rule Id="Async006" Action="Warning" />
    <Rule Id="CA1000" Action="None" /> <!-- Do not declare static members on generic types -->
    <Rule Id="CA1031" Action="None" /> <!-- Modify to catch a more specific exception type. -->
    <Rule Id="CA1052" Action="None" /> <!-- A public or protected, non-abstract type contains only static members and is not declared with the sealed (NotInheritable) modifier. -->
    <Rule Id="CA1054" Action="None" /> <!-- URI parameters should not be strings -->
    <Rule Id="CA1056" Action="None" /> <!-- URI properties should not be strings -->
    <Rule Id="CA1062" Action="None" /> <!-- Validate arguments of public methods -->
    <Rule Id="CA1812" Action="None" /> <!-- Avoid uninstantiated internal classes -->
  </Rules>
  <Rules AnalyzerId="Microsoft.CodeQuality.CSharp.Analyzers" RuleNamespace="Microsoft.CodeQuality.CSharp.Analyzers">
    <Rule Id="Async001" Action="Warning" />
    <Rule Id="Async002" Action="Warning" />
    <Rule Id="Async003" Action="Warning" />
    <Rule Id="Async004" Action="Warning" />
    <Rule Id="Async005" Action="Warning" />
    <Rule Id="Async006" Action="Warning" />
  </Rules>
  <Rules AnalyzerId="Microsoft.CodeQuality.VisualBasic.Analyzers" RuleNamespace="Microsoft.CodeQuality.VisualBasic.Analyzers">
    <Rule Id="Async001" Action="Warning" />
    <Rule Id="Async002" Action="Warning" />
    <Rule Id="Async003" Action="Warning" />
    <Rule Id="Async004" Action="Warning" />
    <Rule Id="Async005" Action="Warning" />
    <Rule Id="Async006" Action="Warning" />
    <Rule Id="CA1001" Action="None" />
    <Rule Id="CA1032" Action="None" />
    <Rule Id="CA1065" Action="None" />
    <Rule Id="CA1200" Action="None" />
    <Rule Id="CA1507" Action="None" />
    <Rule Id="CA1821" Action="None" />
    <Rule Id="CA2234" Action="None" />
  </Rules>
  <Rules AnalyzerId="Microsoft.NetCore.Analyzers" RuleNamespace="Microsoft.NetCore.Analyzers">
    <Rule Id="CA1303" Action="None" /> <!-- Do not pass literals as localized parameters -->
    <Rule Id="CA2000" Action="None" /> <!-- Dispose objects before losing scope -->
  </Rules>
  <Rules AnalyzerId="Microsoft.NetCore.CSharp.Analyzers" RuleNamespace="Microsoft.NetCore.CSharp.Analyzers">
    <Rule Id="CA2215" Action="Warning" />
  </Rules>
  <Rules AnalyzerId="Microsoft.NetCore.VisualBasic.Analyzers" RuleNamespace="Microsoft.NetCore.VisualBasic.Analyzers">
    <Rule Id="CA1825" Action="Warning" />
    <Rule Id="CA2010" Action="Warning" />
    <Rule Id="CA2215" Action="Warning" />
    <Rule Id="CA5350" Action="Warning" />
    <Rule Id="CA5351" Action="Warning" />
  </Rules>
  <Rules AnalyzerId="Microsoft.NetFramework.VisualBasic.Analyzers" RuleNamespace="Microsoft.NetFramework.VisualBasic.Analyzers">
    <Rule Id="CA3076" Action="Warning" />
    <Rule Id="CA3077" Action="Warning" />
    <Rule Id="CA5350" Action="Warning" />
    <Rule Id="CA5351" Action="Warning" />
  </Rules>
  <Rules AnalyzerId="StyleCop.Analyzers" RuleNamespace="StyleCop.Analyzers">
    <!-- Currently disabled rules in StyleCop.Strict -->
    <Rule Id="SA1648" Action="None" />
    <Rule Id="SA1649" Action="None" />
    <Rule Id="SA1650" Action="None" />
    <Rule Id="SA1602" Action="None" />
    <Rule Id="SA1620" Action="None" />
    <Rule Id="SA1617" Action="None" />
    <Rule Id="SA1623" Action="None" />
    <Rule Id="SA1644" Action="None" />
    <Rule Id="SA1517" Action="None" />
    <Rule Id="SA1518" Action="None" />
    <Rule Id="SA1501" Action="None" />
    <Rule Id="SA1126" Action="None" />
    <Rule Id="SA1125" Action="None" />
    <Rule Id="SA1117" Action="None" />
    <Rule Id="SA1108" Action="None" />
    <Rule Id="SA1118" Action="None" />
    <Rule Id="SA1131" Action="None" />
    <Rule Id="SA1311" Action="None" />
    <Rule Id="SA1305" Action="None" />
    <Rule Id="SA1411" Action="None" />
    <Rule Id="SA1410" Action="None" />
    <Rule Id="SA1203" Action="None" />

    <!-- Begin StyleCop.Analyzer specific rules -->
    <Rule Id="SA0001" Action="None" />
    <Rule Id="SA1028" Action="None" />

    <!-- SA1127 and above are new to StyleCop analyzers -->
    <Rule Id="SA1124" Action="None" />
    <Rule Id="SA1135" Action="None" />
    <Rule Id="SA1127" Action="None" />
    <Rule Id="SA1128" Action="None" />
    <Rule Id="SA1129" Action="None" />

    <Rule Id="SA1314" Action="None" />
    <Rule Id="SA1413" Action="None" />

    <Rule Id="SA1629" Action="None" />
    
    <!-- Current StyleCop.Standard overrides -->
    <Rule Id="SA1601" Action="None" />
    <Rule Id="SA1604" Action="None" />
    <Rule Id="SA1605" Action="None" />
    <Rule Id="SA1606" Action="None" />
    <Rule Id="SA1607" Action="None" />
    <Rule Id="SA1614" Action="None" />
    <Rule Id="SA1615" Action="None" />
    <Rule Id="SA1616" Action="None" />
    <Rule Id="SA1630" Action="None" />
    <Rule Id="SA1631" Action="None" />
    <Rule Id="SA1632" Action="None" />
    <Rule Id="SA1642" Action="None" />
    <Rule Id="SA1625" Action="None" />
    <Rule Id="SA1504" Action="None" />
    <Rule Id="SA1513" Action="None" />
    <Rule Id="SA1400" Action="None" />
    <Rule Id="SA1401" Action="None" />
    <Rule Id="SA1402" Action="None" />
    <Rule Id="SA1301" Action="None" />
    <Rule Id="SA1308" Action="None" />
    <Rule Id="SA1310" Action="None" />
    <Rule Id="SA1214" Action="None" />
    <Rule Id="SA1215" Action="None" />
    <Rule Id="SA1200" Action="None" />
    <Rule Id="SA1201" Action="None" />
    <Rule Id="SA1202" Action="None" />
    <Rule Id="SA1120" Action="None" />
    <Rule Id="SA1101" Action="None" />
    <Rule Id="SA1121" Action="None" />
    <Rule Id="SA1005" Action="None" />
    <Rule Id="SA1008" Action="None" />
    <Rule Id="SA1009" Action="None" />
  </Rules>
</RuleSet>