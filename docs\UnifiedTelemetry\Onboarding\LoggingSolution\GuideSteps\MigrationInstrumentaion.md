# Migrating from Existing Events

If you are transitioning from legacy Telemetry SDKs such as IFx and PassiveMon, it is necessary to locate all original log API calls and replace them with the newly defined methods.

The usage of Ifx usually follows this pattern:
```csharp
/// Ifx logging
Event<EventName>.Log()
```

In most cases, logs were emitted as objects.
Find the lines where the origin Ifx events were logged, and call new logging method
```csharp
/// Logging Original event
Event<CustomizedEvent>.Log(event)
```

For example:
```csharp
/// <summary>
/// Original method calls the logging function
/// </summary>
/// <param name="originalEvent">Original event to be logged</param>
private static void LogOrignalEvent(OriginalEvent event)
{
	/// some processing logic

	Event<OriginalEvent>.Log(event);
}
```

# [DI](#tab/DI)
### For DI projects, use the ilogger created in constructor:
Pass the ILogger configured in your service collection to the logging method.
```csharp
/// <summary>
/// Original method calls the logging function
/// </summary>
/// <param name="originalEvent">Original event to be logged</param>
private static void LogOrignalEvent(ILogger iLogger, OriginalEvent event)
{
	/// some processing logic

	Event<OriginalEvent>.Log(event);
	CustomizedR9Event.Log(iLogger, event);
}
```
# [Non-DI](#tab/nonDI)
### For Non-DI projects, use the static logger
```csharp
/// <summary>
/// Original method calls the logging function
/// </summary>
/// <param name="originalEvent">Original event to be logged</param>
private static void LogOrignalEvent(OriginalEvent event)
{
	/// some processing logic

	Event<OriginalEvent>.Log(event);
	CustomizedR9Event.Log(LoggingConfiguration.Logger.Value, event);
}
```
---
