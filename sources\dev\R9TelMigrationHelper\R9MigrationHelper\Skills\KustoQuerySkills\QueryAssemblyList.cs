﻿// <copyright file="QueryAssemblyList.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System.Data;
using System.Xml.Linq;
using R9MigrationHelper.Model;

namespace R9MigrationHelper.Skills.KustoQuerySkills
{
    /// <summary>
    /// QueryAssemblyList.
    /// </summary>
    public class QueryAssemblyList
    {
        QueryKustoTableSkill queryKustoTableSkill = new QueryKustoTableSkill();

        /// <summary>
        /// Choose version, package info by Source Path
        /// </summary>
        /// <param name="assembly">Assembly</param>
        public static void PrintAssembly(AssemblyModel assembly)
        {
            Console.ForegroundColor = ConsoleColor.DarkYellow;
            Console.WriteLine($"Assembly Name = {assembly.Name}; SourcePath = {assembly.SourcePath}; PossibleSourcePaths = <{assembly.PossibleSourcePaths}>;\nSourcePathCandidatesInfo:");
            foreach (string sp in assembly.SourcePathCandidatesInfo.Keys)
            {
                Console.WriteLine($"\tSourcePath = {sp}; ");
                foreach (SourcePathRelatedInfo spri in assembly.SourcePathCandidatesInfo[sp])
                {
                    Console.WriteLine($"\t\t<AssemblyVersion = {spri.AssemblyVersion}; PackageName = {spri.PackageName}; PackageVersion = {spri.PackageVersion};>");
                }
            }
            Console.WriteLine(" ");
            Console.ResetColor();
        }

        /// <summary>
        /// GetAllPackageList.
        /// </summary>
        public async Task<List<string>> GetAllPackageList()
        {
            string query = @"cluster('resourcemanagement.westus2.kusto.windows.net').database('adhoc').BindingRedirectAssembly | distinct PackageName";

            List<string> packageList = new List<string>();

            using (IDataReader reader = await queryKustoTableSkill.GetKustoDataAsync(query).ConfigureAwait(true))
            {
                while (reader.Read())
                {
                    packageList.Add(reader.GetString(0));
                }
            }
            return packageList;
        }

        /// <summary>
        /// Get source paths from binplace csproj file and exsiting xmldrop file.
        /// </summary>
        /// <param name="dllList"></param>
        /// <param name="binPlaceCsProjFileContent"></param>
        /// <returns></returns>
        public async Task GetUsedSourcePath(List<AssemblyModel> dllList, string binPlaceCsProjFileContent)
        {
            HashSet<string> importedPaths = new HashSet<string>();
            try
            {
                var projDoc = XDocument.Parse(binPlaceCsProjFileContent);
                var ns = projDoc.Root!.Name.Namespace;
                importedPaths = (
                        from pkgRef in projDoc.Element(ns + "Project")!.Descendants(ns + "BinPlaceNetFramework")
                        let includeValue = pkgRef.Attribute("Include")?.Value
                        where !string.IsNullOrEmpty(includeValue)
                        select includeValue).ToHashSet(StringComparer.OrdinalIgnoreCase);
            }
            catch (Exception e)
            {
                Console.WriteLine($"Not able to extract imported package from project file, error: {e.Message}");
            }

            Dictionary<string, string> binPlacePaths = new Dictionary<string, string>();
            foreach (string path in importedPaths)
            {
                string[] pathParts = path.Split('\\');
                if (pathParts.Length == 2)
                {
                    continue;
                }
                string dllName = pathParts[pathParts.Length - 1];
                string pkgName = pathParts[0];
                if (dllName == "*.*")
                {
                    continue;
                }
                string dllPath = path.Substring(pkgName.Length + 1, path.Length - dllName.Length - pkgName.Length - 2);
                binPlacePaths.Add(dllName, dllPath);
            }

            string dlls = String.Join(",\n", dllList.Select(x => $"\"{x.Name}\""));
            string query = @$"let dlls = datatable(AssemblyName:string)
[
{dlls}
];
cluster('chronicledev.northeurope.kusto.windows.net').database('essdata_dev').XMLPkgDropDestinations
| where RepositoryName =~ 'substrate' and isnotempty(source)
| distinct tostring(filename), tostring(source)
| project AssemblyName = filename, Source = source
| join kind = rightouter dlls on AssemblyName
| project AssemblyName, Source";

            Dictionary<string, List<string>> xmlDropPath = new Dictionary<string, List<string>>();
            using (IDataReader reader = await queryKustoTableSkill.GetKustoDataAsync(query).ConfigureAwait(true))
            {
                while (reader.Read())
                {
                    string dllName = reader.GetString(0);
                    string sourcePath = reader.GetString(1);
                    if (xmlDropPath.ContainsKey(dllName))
                    {
                        xmlDropPath[dllName].Add(sourcePath);
                    }
                    else
                    {
                        xmlDropPath.Add(dllName, new List<string>() { sourcePath });
                    }
                }
            }

            foreach (AssemblyModel dll in dllList)
            {
                if (xmlDropPath.ContainsKey(dll.Name))
                {
                    dll.ExsitedDropSourcePaths = xmlDropPath[dll.Name];
                }
                if (binPlacePaths.ContainsKey(dll.Name))
                {
                    dll.BinPlaceSourcePath = binPlacePaths[dll.Name];
                }
            }
        }

        /// <summary>
        /// GetAssemblyList.
        /// </summary>
        /// <param name="fullDestination">XML full destination list.</param>
        /// <param name="additionalTopPackages">Addtional top level packages list.</param>
        public async Task<List<AssemblyModel>> GetAndFilterAssemblyList(List<XmlFullDestination> fullDestination, List<string> additionalTopPackages)
        {
            if (fullDestination.Count == 0)
            {
                throw new Exception("No destination to drop!");
            }

            //if root DATACENTER and PRODUCT both exist, then choose DATACENTER
            Func<string, string> dedup = x => { return (x != "PRODUCT") ? x : "DATACENTER"; };
            string xmlContent = String.Join(",\n", fullDestination.Select(x => $"\"{dedup(x.Root)}\", \"{x.Destination.Replace(@"\", @"\\", StringComparison.CurrentCulture)}\""));

            //            string query = @$"let xml = datatable(Root:string, Destination:string)
            //[
            //{xmlContent}
            //];
            //let topPackages = datatable(PackageName:string)
            //[
            //{additionalTopPackagesContent}
            //];
            //topPackages
            //| join kind=leftouter (cluster('resourcemanagement.westus2.kusto.windows.net').database('adhoc').BindingRedirectAssembly) on $left.PackageName == $right.TopPackageName
            //| where isnotempty(AssemblyName)
            //| distinct AssemblyName, AssemblyVersion, SourcePath, PackageName, PackageVersion
            //| extend placeholder = 1
            //| join kind=inner (xml | extend placeholder = 1 ) on placeholder
            //| join kind=leftouter (cluster('resourcemanagement.westus2.kusto.windows.net').database('adhoc').ExistingXMLDropDestinations | project AssemblyName, Root = iff(Root != 'PRODUCT', Root, 'DATACENTER'), Destination) on AssemblyName and Root and Destination
            //| where isempty(AssemblyName1)
            //| distinct AssemblyName, AssemblyVersion, Root, Destination, SourcePath
            //| order by AssemblyName asc, AssemblyVersion";
            string query;
            if (additionalTopPackages.Count == 0)
            {
                query = @$"let xml = datatable(Root:string, Destination:string)
[
{xmlContent}
];
cluster('resourcemanagement.westus2.kusto.windows.net').database('adhoc').BindingRedirectAssembly
| where isnotempty(AssemblyName)
| distinct AssemblyName, AssemblyVersion, SourcePath, PackageName, PackageVersion
| extend placeholder = 1
| join kind=inner (xml | extend placeholder = 1 ) on placeholder
| join kind=leftouter (cluster('chronicledev.northeurope.kusto.windows.net').database('essdata_dev').XMLPkgDropDestinations
| where RepositoryName =~ 'substrate'
| distinct tostring(filename), DeliverableType, tostring(Destination)
| project AssemblyName = filename, Root = iff(DeliverableType != 'PRODUCT', DeliverableType, 'DATACENTER'), Destination) on AssemblyName and Root and Destination
| where isempty(AssemblyName1)
| distinct AssemblyName, AssemblyVersion, Root, Destination, SourcePath, PackageName, PackageVersion
| order by AssemblyName asc, SourcePath asc";
            }
            else
            {
                string additionalTopPackagesContent = String.Join(",\n", additionalTopPackages.Select(x => $"\"{x}\""));
                query = @$"let xml = datatable(Root:string, Destination:string)
[
{xmlContent}
];
let topPackages = datatable(PackageName:string)
[
{additionalTopPackagesContent}
];
topPackages
| project TopPackageName = PackageName
| join kind=leftouter (cluster('resourcemanagement.westus2.kusto.windows.net').database('adhoc').BindingRedirectAssembly) on $left.TopPackageName == $right.TopPackageName
| distinct AssemblyName, AssemblyVersion, SourcePath, PackageName, PackageVersion
| extend placeholder = 1
| join kind=inner (xml | extend placeholder = 1 ) on placeholder
| join kind=leftouter (cluster('chronicledev.northeurope.kusto.windows.net').database('essdata_dev').XMLPkgDropDestinations
| where RepositoryName =~ 'substrate'
| distinct tostring(filename), DeliverableType, tostring(Destination)
| project AssemblyName = filename, Root = iff(DeliverableType != 'PRODUCT', DeliverableType, 'DATACENTER'), Destination) on AssemblyName and Root and Destination
| where isempty(AssemblyName1)
| distinct AssemblyName, AssemblyVersion, Root, Destination, SourcePath, PackageName, PackageVersion
| order by AssemblyName asc, SourcePath asc";
            }

            List<AssemblyModel> assemblyList = new List<AssemblyModel>();

            using (IDataReader reader = await queryKustoTableSkill.GetKustoDataAsync(query))
            {
                while (reader.Read())
                {
                    string name = reader.GetString(0);
                    string version = reader.GetString(1);
                    string root = reader.GetString(2);
                    string destination = reader.GetString(3);
                    string sourcePath = reader.GetString(4);
                    string packageName = reader.GetString(5);
                    string packageVersion = reader.GetString(6);

                    ////dtermine DLL by name and version
                    //if (assemblyList.Count > 0 && assemblyList.Last().Name == name && assemblyList.Last().Version == version)
                    //{
                    //    assemblyList.Last().DropDestinations.Add(new XmlFullDestination() { Root = root, Destination = destination });
                    //    assemblyList.Last().SourcePathCandidates.Add(sourcePath);
                    //}
                    //else
                    //{
                    //    assemblyList.Add(new AssemblyModel() 
                    //    { 
                    //        Name = name, 
                    //        Version = version, 
                    //        DropDestinations = new HashSet<XmlFullDestination>() { new XmlFullDestination() { Root = root, Destination = destination } },
                    //        SourcePathCandidates = new HashSet<string>() { sourcePath }
                    //    });
                    //}
                    //determine DLL by name
                    if (assemblyList.Count > 0 && assemblyList.Last().Name == name)
                    {
                        assemblyList.Last().DropDestinations.Add(new XmlFullDestination() { Root = root, Destination = destination });
                        if (assemblyList.Last().SourcePathCandidatesInfo.ContainsKey(sourcePath))
                        {
                            assemblyList.Last().SourcePathCandidatesInfo[sourcePath].Add(new SourcePathRelatedInfo()
                            {
                                PackageName = packageName,
                                PackageVersion = packageVersion,
                                AssemblyVersion = version
                            });
                        }
                        else
                        {
                            assemblyList.Last().SourcePathCandidatesInfo.Add(sourcePath, new HashSet<SourcePathRelatedInfo>()
                            {
                                new SourcePathRelatedInfo()
                                {
                                    PackageName = packageName,
                                    PackageVersion = packageVersion,
                                    AssemblyVersion = version
                                }
                            });
                        }
                    }
                    else
                    {
                        assemblyList.Add(new AssemblyModel()
                        {
                            Name = name,
                            DropDestinations = new HashSet<XmlFullDestination>() { new XmlFullDestination() { Root = root, Destination = destination } },
                            SourcePathCandidatesInfo = new Dictionary<string, HashSet<SourcePathRelatedInfo>>()
                            {
                                { sourcePath, new HashSet<SourcePathRelatedInfo>() { new SourcePathRelatedInfo() { PackageName = packageName, PackageVersion = packageVersion, AssemblyVersion = version } } }
                            }
                        });
                    }
                }
            }

            Console.WriteLine($"\n{assemblyList.Count} DLLs in this stage\n");

            for (int i = 0; i < (assemblyList.Count < 10 ? assemblyList.Count : 10); i++)
            {
                Console.Write($"{assemblyList[i].Name}:\n\t");
                foreach (XmlFullDestination xml in assemblyList[i].DropDestinations)
                {
                    Console.Write($"<{xml.Root}, {xml.Destination}>; ");
                }
                Console.WriteLine(" ");
                PrintAssembly(assemblyList[i]); // TBD
                Console.WriteLine($"\n");
            }

            return assemblyList;
        }

        /// <summary>
        /// Choose version, package info by Source Path
        /// </summary>
        /// <param name="assembly">Assembly</param>
        public async Task<AssemblyModel> ChooseOtherInfoBySourcePath(AssemblyModel assembly)
        {
            if (string.IsNullOrEmpty(assembly.SourcePath) || !assembly.SourcePathCandidatesInfo.ContainsKey(assembly.SourcePath))
            {
                PrintAssembly(assembly); // TBD

                throw new Exception("Unexpected Source Path!");
            }
            HashSet<SourcePathRelatedInfo> sourcePathRelatedInfoList = assembly.SourcePathCandidatesInfo[assembly.SourcePath];
            if (sourcePathRelatedInfoList == null || sourcePathRelatedInfoList.Count == 0)
            {
                PrintAssembly(assembly); // TBD

                throw new Exception("Wrong Source Path!");
            }
            if (sourcePathRelatedInfoList.Count == 1)
            {
                assembly.Version = sourcePathRelatedInfoList.First().AssemblyVersion;
                assembly.PackageName = sourcePathRelatedInfoList.First().PackageName;
                assembly.PackageVersion = sourcePathRelatedInfoList.First().PackageVersion;
                return assembly;
            }
            List<string> rootPackages = new List<string>();
            string query = "TopPackageName | distinct PackageName";
            using (IDataReader reader = await queryKustoTableSkill.GetKustoDataAsync(query))
            {
                while (reader.Read())
                {
                    rootPackages.Add(reader.GetString(0));
                }
            }

            foreach (SourcePathRelatedInfo sourcePathRelatedInfo in sourcePathRelatedInfoList)
            {
                if (sourcePathRelatedInfo.PackageName + ".dll" == assembly.Name)
                {
                    assembly.Version = sourcePathRelatedInfo.AssemblyVersion;
                    assembly.PackageName = sourcePathRelatedInfo.PackageName;
                    assembly.PackageVersion = sourcePathRelatedInfo.PackageVersion;
                    return assembly;
                }
                else if (rootPackages.Contains(sourcePathRelatedInfo.PackageName))
                {
                    assembly.Version = sourcePathRelatedInfo.AssemblyVersion;
                    assembly.PackageName = sourcePathRelatedInfo.PackageName;
                    assembly.PackageVersion = sourcePathRelatedInfo.PackageVersion;
                }
            }
            if (string.IsNullOrEmpty(assembly.PackageName))
            {
                assembly.Version = sourcePathRelatedInfoList.First().AssemblyVersion;
                assembly.PackageName = sourcePathRelatedInfoList.First().PackageName;
                assembly.PackageVersion = sourcePathRelatedInfoList.First().PackageVersion;
            }
            return assembly;
        }
    }
}
