#include "LogFileExporterRecordable.h"

#include <nlohmann/json.hpp>
#include <opentelemetry/logs/severity.h>
#include <opentelemetry/nostd/span.h>
#include <opentelemetry/nostd/string_view.h>
#include <opentelemetry/nostd/variant.h>
#include <opentelemetry/sdk/common/attribute_utils.h>
#include <opentelemetry/sdk/instrumentationscope/instrumentation_scope.h>
#include <opentelemetry/sdk/resource/resource.h>
#include <opentelemetry/trace/span_id.h>
#include <opentelemetry/trace/trace_flags.h>
#include <opentelemetry/trace/trace_id.h>

#include <chrono>
#include <ctime>
#include <string>

namespace
{
using namespace opentelemetry;
//
// See `attribute_value.h` for details.
//
const int kAttributeValueSize = 16;

void PopulateAttribute(nlohmann::json &attribute,
                       opentelemetry::nostd::string_view key,
                       const opentelemetry::common::AttributeValue &value)
{
    // ...existing code...
    // Assert size of variant to ensure that this method gets updated if the variant
    // definition changes
    static_assert(nostd::variant_size<common::AttributeValue>::value == kAttributeValueSize,
                  "AttributeValue contains unknown type");

    if (nostd::holds_alternative<bool>(value))
    {
        attribute[key.data()] = nostd::get<bool>(value);
    }
    else if (nostd::holds_alternative<int>(value))
    {
        attribute[key.data()] = nostd::get<int>(value);
    }
    else if (nostd::holds_alternative<int64_t>(value))
    {
        attribute[key.data()] = nostd::get<int64_t>(value);
    }
    else if (nostd::holds_alternative<unsigned int>(value))
    {
        attribute[key.data()] = nostd::get<unsigned int>(value);
    }
    else if (nostd::holds_alternative<uint64_t>(value))
    {
        attribute[key.data()] = nostd::get<uint64_t>(value);
    }
    else if (nostd::holds_alternative<double>(value))
    {
        attribute[key.data()] = nostd::get<double>(value);
    }
    else if (nostd::holds_alternative<const char *>(value))
    {
        attribute[key.data()] = nostd::get<const char *>(value);
    }
    else if (nostd::holds_alternative<nostd::string_view>(value))
    {
        attribute[key.data()] = nostd::string_view(nostd::get<nostd::string_view>(value).data(),
                                                   nostd::get<nostd::string_view>(value).size());
    }
    else if (nostd::holds_alternative<nostd::span<const uint8_t>>(value))
    {
        attribute[key.data()] = {};
        for (const auto &val : nostd::get<nostd::span<const uint8_t>>(value))
        {
            attribute[key.data()].push_back(val);
        }
    }
    else if (nostd::holds_alternative<nostd::span<const bool>>(value))
    {
        attribute[key.data()] = {};
        for (const auto &val : nostd::get<nostd::span<const bool>>(value))
        {
            attribute[key.data()].push_back(val);
        }
    }
    else if (nostd::holds_alternative<nostd::span<const int>>(value))
    {
        attribute[key.data()] = {};
        for (const auto &val : nostd::get<nostd::span<const int>>(value))
        {
            attribute[key.data()].push_back(val);
        }
    }
    else if (nostd::holds_alternative<nostd::span<const int64_t>>(value))
    {
        attribute[key.data()] = {};
        for (const auto &val : nostd::get<nostd::span<const int64_t>>(value))
        {
            attribute[key.data()].push_back(val);
        }
    }
    else if (nostd::holds_alternative<nostd::span<const unsigned int>>(value))
    {
        attribute[key.data()] = {};
        for (const auto &val : nostd::get<nostd::span<const unsigned int>>(value))
        {
            attribute[key.data()].push_back(val);
        }
    }
    else if (nostd::holds_alternative<nostd::span<const uint64_t>>(value))
    {
        attribute[key.data()] = {};
        for (const auto &val : nostd::get<nostd::span<const uint64_t>>(value))
        {
            attribute[key.data()].push_back(val);
        }
    }
    else if (nostd::holds_alternative<nostd::span<const double>>(value))
    {
        attribute[key.data()] = {};
        for (const auto &val : nostd::get<nostd::span<const double>>(value))
        {
            attribute[key.data()].push_back(val);
        }
    }
    else if (nostd::holds_alternative<nostd::span<const nostd::string_view>>(value))
    {
        attribute[key.data()] = {};
        for (const auto &val : nostd::get<nostd::span<const nostd::string_view>>(value))
        {
            attribute[key.data()].push_back(std::string(val.data(), val.size()));
        }
    }
}
}  // namespace

namespace Microsoft {
namespace M365 {
namespace Exporters {

LogFileExporterRecordable::LogFileExporterRecordable() noexcept : opentelemetry::sdk::logs::Recordable()
{
}

nlohmann::json LogFileExporterRecordable::GetJSON() noexcept
{
    return json_;
}

void LogFileExporterRecordable::SetTimestamp(
    opentelemetry::common::SystemTimestamp timestamp) noexcept
{
    const std::chrono::system_clock::time_point timePoint{timestamp};
    json_["@timestamp"] = std::to_string(std::chrono::duration_cast<std::chrono::microseconds>(timestamp.time_since_epoch()).count());
}

void LogFileExporterRecordable::SetObservedTimestamp(
    opentelemetry::common::SystemTimestamp timestamp) noexcept
{
    json_["observedtimestamp"] = timestamp.time_since_epoch().count();
}

void LogFileExporterRecordable::SetSeverity(opentelemetry::logs::Severity severity) noexcept
{
    auto &severityField = json_["log"]["level"];

    // Convert the severity enum to a string
    std::uint32_t severity_index = static_cast<std::uint32_t>(severity);
    if (severity_index >= std::extent<decltype(opentelemetry::logs::SeverityNumToText)>::value)
    {
        severityField =
            std::string("Invalid severity(").append(std::to_string(severity_index)).append(")");
    }
    else
    {
        severityField = opentelemetry::logs::SeverityNumToText[severity_index];
    }
}

void LogFileExporterRecordable::SetBody(const opentelemetry::common::AttributeValue &message) noexcept
{
    PopulateAttribute(json_, "message", message);
}

void LogFileExporterRecordable::SetEventId(int64_t id , opentelemetry::nostd::string_view name) noexcept
{
    json_["event_id"] = id;
    json_["event_name"] = std::string(name);
}

void LogFileExporterRecordable::SetTraceId(const opentelemetry::trace::TraceId &trace_id) noexcept
{
    if (trace_id.IsValid())
    {
        char trace_buf[opentelemetry::trace::TraceId::kSize * 2];
        trace_id.ToLowerBase16(trace_buf);
        json_["traceid"] = std::string(trace_buf, sizeof(trace_buf));
    }
    else
    {
        json_.erase("traceid");
    }
}

void LogFileExporterRecordable::SetSpanId(const opentelemetry::trace::SpanId &span_id) noexcept
{
    if (span_id.IsValid())
    {
        char span_buf[opentelemetry::trace::SpanId::kSize * 2];
        span_id.ToLowerBase16(span_buf);
        json_["spanid"] = std::string(span_buf, sizeof(span_buf));
    }
    else
    {
        json_.erase("spanid");
    }
}

void LogFileExporterRecordable::SetTraceFlags(
    const opentelemetry::trace::TraceFlags &trace_flags) noexcept
{
    char flag_buf[2];
    trace_flags.ToLowerBase16(flag_buf);
    json_["traceflags"] = std::string(flag_buf, sizeof(flag_buf));
}

void LogFileExporterRecordable::SetAttribute(
    opentelemetry::nostd::string_view key,
    const opentelemetry::common::AttributeValue &value) noexcept
{
    PopulateAttribute(json_, key, value);
}

void LogFileExporterRecordable::SetResource(
    const opentelemetry::sdk::resource::Resource &resource) noexcept
{
    for (const auto &attribute : resource.GetAttributes())
    {
        // TODO(jiayiwang): Implement.
    }
}

void LogFileExporterRecordable::SetInstrumentationScope(
    const opentelemetry::sdk::instrumentationscope::InstrumentationScope
        &instrumentation_scope) noexcept
{
    json_["log"]["logger"] = instrumentation_scope.GetName();
}

} // namespace Exporters
} // namespace M365
} // namespace Microsoft