﻿// <copyright file="ChooseSourcePath.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Diagnostics.CodeAnalysis;
using System.Linq;
using System.Net.Sockets;
using System.Reflection;
using System.Text;
using System.Threading.Tasks;
using Azure.Identity;
using Azure.Security.KeyVault.Secrets;
using Kusto.Cloud.Platform.Utils;
using R9MigrationHelper.Implementations;
using R9MigrationHelper.Model;
using R9MigrationHelper.Skills.KustoQuerySkills;

namespace R9MigrationHelper.Skills.LLMSkills
{
    /// <summary>
    /// CreatePullRequestSkill.
    /// </summary>
    [ExcludeFromCodeCoverage]
    public class ChooseSourcePath
    {
        /// <summary>
        /// Choose Source Path by LLM
        /// </summary>
        /// <param name="assembly"></param>
        /// <param name="serviceFramework"></param>
        public async Task<AssemblyModel> ChooseSourcePathByLLM(AssemblyModel assembly, string serviceFramework)
        {
            List<string> sourcePathCandidates = assembly.SourcePathCandidatesInfo.Keys.ToList();
            if (sourcePathCandidates.Count == 0)
            {
                throw new Exception("No source path candidates!");
            }
            if (sourcePathCandidates.Count == 1)
            {
                assembly.SourcePath = sourcePathCandidates.First();
                return assembly;
            }

            string sourcePathCandidatesString = String.Join(";", sourcePathCandidates);
            string instruction =
                $"My C# project's target framework is {serviceFramework} and runs in a x64 machine, please select the most possible source path of its assemblies from the given source path candidate list.\n" +
                $"Instructions:\n" +
                $"1.You should only choose the most possible one source path.\n" +
                $"2.Consider a source path more likely the best choice if it starts \"lib\".\n" +
                $"3.Consider a source path more likely when its pattern matches the project target framework.\n" +
                $"4.You will be given a source path candidate list, every source path separated bt ';', and you can only choose from the list.\n" +
                $"5.Give anwser with the most possible source path and nothing more: " +
                $"For example, if the framework is 'net472' and you are given list: 'lib\\net461; lib\\net5.0;lib\\net6.0;lib\\netstandard2.0;lib\\netcoreapp3.1', you will output 'lib\\net461'.\n";

            string question = $"My C# project's target framework is {serviceFramework} and runs in a x64 machine, please select the most possible source path of its assemblies from following source path candidate: {sourcePathCandidatesString}";

            //Console.WriteLine($"{instruction}\n{question}");
            const string secretName = "GPTApiKey";
            var keyVaultName = "SotelsKeys";
            var kvUri = $"https://{keyVaultName}.vault.azure.net";
            string tenantId = "cdc5aeea-15c5-4db6-b079-fcadd2505dc2";
            string appId = "bad6c3f0-aa3c-4464-93e6-1560e8b43d4e";
            string appKey = UserInput.AppKey;
            var credentials = new ClientSecretCredential(tenantId, appId, appKey);
            var client = new SecretClient(new Uri(kvUri), credentials);
            string apiKey = client.GetSecret(secretName).Value.Value;

            OpenAIService openAIService = new OpenAIService("https://sotels-openai.openai.azure.com/", apiKey, "GPT4");

            string answer = await openAIService.AzureGPTConversation(instruction, question).ConfigureAwait(false);

            Console.ForegroundColor = ConsoleColor.DarkYellow;
            Console.WriteLine($"GPT choose '{answer}' out of '{sourcePathCandidatesString}' for {serviceFramework} service");
            Console.ResetColor();

            if (sourcePathCandidates.Contains(answer))
            {
                assembly.SourcePath = answer;

                QueryAssemblyList.PrintAssembly(assembly); // TBD
                return assembly;
            }

            Console.ForegroundColor = ConsoleColor.Red;
            Console.WriteLine($"GPT gave an unexpected answer: '{answer}' out of '{sourcePathCandidates}', use traditional way");
            Console.ResetColor();
            return ChooseSourcePathByTradition(assembly, serviceFramework);
        }

        /// <summary>
        /// Choose Source Path by LLM
        /// </summary>
        /// <param name="assemblyList"></param>
        /// <param name="serviceFramework"></param>
        public async Task<List<AssemblyModel>> ChooseSourcePathByLLMBatch(List<AssemblyModel> assemblyList, string serviceFramework)
        {
            HashSet<string> possibleCombinations = new HashSet<string>();
            foreach (AssemblyModel assembly in assemblyList)
            {
                if (assembly.SourcePathCandidatesInfo.Keys.Count == 0)
                {
                    throw new Exception("No source path candidates!");
                }
                if (assembly.SourcePathCandidatesInfo.Keys.Count == 1)
                {
                    assembly.SourcePath = assembly.SourcePathCandidatesInfo.Keys.First();
                    continue;
                }
                string sourcePathCandidatesString = String.Join(";", assembly.SourcePathCandidatesInfo.Keys);
                assembly.PossibleSourcePaths = sourcePathCandidatesString;
                possibleCombinations.Add(sourcePathCandidatesString);
            }

            Console.WriteLine($"{possibleCombinations.Count} Possible Combinations\n");

            string instruction =
                $"My C# project's target framework is {serviceFramework} and runs in a x64 machine, please select the most possible source path of its assemblies in each group of given list.\n" +
                $"Instructions:\n" +
                $"1.You should only choose the most possible one source path for each group of candidates.\n" +
                $"2.Consider a source path more likely the best choice if it starts \"lib\".\n" +
                $"3.Consider a source path more likely when its pattern matches the project target framework.\n" +
                $"4.You will be given a list of group of source path candidates, every group is separated by '|', and every source paths in one group separated by ';', and you can only choose one from each group.\n" +
                $"5.Give anwser with a list of the most possible source path in each group, separated by '|' in given order, and nothing more." +

                //$"6.How many groups in given list, how many source paths in answer" +
                $"6.The answer should contain source paths of exact quantity of number of groups in given list" +
                $"For example, if the framework is 'net472' and you are given list: 'lib\\net461;lib\\net5.0;lib\\net6.0;lib\\netstandard2.0;lib\\netcoreapp3.1|lib\\net462;lib\\net6.0;lib\\netcoreapp3.1', you will output 'lib\\net461|lib\\net462'.\n";

            string question = $"My C# project's target framework is {serviceFramework} and runs in a x64 machine, there are {possibleCombinations.Count} groups of source path, so answer must conatin {possibleCombinations.Count} source paths, please select the most possible source path of its assemblies in each group from following list: {String.Join("|", possibleCombinations)}";

            Console.WriteLine($"{instruction}\n{question}");
            OpenAIService openAIService = new OpenAIService("<OpenAiEndPoint>", "<Key>", "<version>");

            string answer = await openAIService.AzureGPTConversation(instruction, question).ConfigureAwait(false);

            Console.ForegroundColor = ConsoleColor.DarkYellow;
            Console.WriteLine($"GPT gave answer '{answer}'");
            Console.ResetColor();

            List<string> answers = answer.Split('|').ToList();
            if (answers.Count != possibleCombinations.Count)
            {
                Console.WriteLine($"answers.Count = {answers.Count}, possibleCombinations.Count = {possibleCombinations.Count}");
                throw new Exception("Count not match");
            }

            Console.ForegroundColor = ConsoleColor.DarkYellow;
            Dictionary<string, string> match = new Dictionary<string, string>();
            for (int index = 0; index < answers.Count; index++)
            {
                match.Add(possibleCombinations.ToList()[index], answers[index]);
                Console.WriteLine($"{answers[index]} \t<= {possibleCombinations.ToList()[index]}");
            }
            Console.ResetColor();
            foreach (AssemblyModel assembly in assemblyList)
            {
                if (!string.IsNullOrEmpty(assembly.PossibleSourcePaths))
                {
                    assembly.SourcePath = match[assembly.PossibleSourcePaths];
                }
            }
            return assemblyList;
        }

        /// <summary>
        /// Choose Source Path by LLM
        /// </summary>
        /// <param name="assemblyList"></param>
        /// <param name="serviceFramework"></param>
        /// <param name="batchNumber"></param>
        public async Task<List<AssemblyModel>> ChooseSourcePathByLLMBatch(List<AssemblyModel> assemblyList, string serviceFramework, int batchNumber)
        {
            HashSet<string> possibleCombinations = new HashSet<string>();
            foreach (AssemblyModel assembly in assemblyList)
            {
                if (assembly.SourcePathCandidatesInfo.Keys.Count == 0)
                {
                    throw new Exception("No source path candidates!");
                }
                if (assembly.SourcePathCandidatesInfo.Keys.Count == 1)
                {
                    assembly.SourcePath = assembly.SourcePathCandidatesInfo.Keys.First();
                    continue;
                }
                string sourcePathCandidatesString = String.Join(";", assembly.SourcePathCandidatesInfo.Keys);
                assembly.PossibleSourcePaths = sourcePathCandidatesString;
                possibleCombinations.Add(sourcePathCandidatesString);
            }

            Console.WriteLine($"{possibleCombinations.Count} Possible Combinations\n");

            Dictionary<string, string> match = new Dictionary<string, string>();
            Dictionary<string, string> matchMethod = new Dictionary<string, string>();
            List<string> possibleCombinationsList = possibleCombinations.ToList();
            List<string> answers;
            for (int i = 0; i < possibleCombinationsList.Count; i = i + batchNumber)
            {
                int len = batchNumber;
                if (i + batchNumber >= possibleCombinationsList.Count)
                {
                    len = possibleCombinationsList.Count - i;
                }
                string answer = await ChooseSourcePathHelper(string.Join('|', possibleCombinationsList.GetRange(i, len)), serviceFramework, len).ConfigureAwait(false);
                Console.ForegroundColor = ConsoleColor.DarkYellow;
                Console.WriteLine(answer);
                Console.ResetColor();
                answers = answer.Split('|').ToList();
                if (answers.Count != len)
                {
                    Console.WriteLine($"answers.Count = {answers.Count}, len = {len}, answer = {answer}");
                    throw new Exception("Count not match");
                }

                for (int index = 0; index < len; index++)
                {
                    if (possibleCombinationsList[i + index].Split(';').Contains(answers[index]))
                    {
                        match.Add(possibleCombinationsList[index + i], answers[index]);
                        matchMethod.Add(possibleCombinationsList[index + i], "LLM");
                        Console.WriteLine($"{answers[index]} \t<= <{possibleCombinationsList[index + i]}>");
                    }
                    else
                    {
                        Console.WriteLine($"Else: {answers[index]} not in {possibleCombinationsList[i + index]}"); //TBD
                        match.Add(possibleCombinationsList[index + i], ChooseSourcePathByTradition(possibleCombinationsList[i + index].Split(';').ToList(), serviceFramework));
                        matchMethod.Add(possibleCombinationsList[index + i], "Traditional");
                        Console.WriteLine($"{answers[index]} \t<=Traditional= <{possibleCombinationsList[index + i]}>");
                    }
                }
            }

            Console.ForegroundColor = ConsoleColor.DarkYellow;
            Console.WriteLine("match:");
            foreach (string s in match.Keys)
            {
                Console.WriteLine($"<{s}> => {match[s]}");
            }
            Console.ResetColor();

            foreach (AssemblyModel assembly in assemblyList)
            {
                if (!string.IsNullOrEmpty(assembly.PossibleSourcePaths))
                {
                    assembly.SourcePath = match[assembly.PossibleSourcePaths];
                    if (matchMethod[assembly.PossibleSourcePaths] == "LLM")
                    {
                        if (string.IsNullOrEmpty(assembly.BinPlaceSourcePath) && assembly.BinPlaceSourcePath != assembly.BinPlaceSourcePath)
                        {
                            Console.ForegroundColor = ConsoleColor.Red;
                            Console.WriteLine($"Source path for {assembly.Name} in Binplace is not the same with LLM result!");
                            Console.WriteLine($"Binplace: {assembly.BinPlaceSourcePath}, LLM: {assembly.SourcePath}");
                            Console.ResetColor();
                            assembly.SourcePath = assembly.BinPlaceSourcePath;
                        }
                        else if (assembly.ExsitedDropSourcePaths.Count == 1 && assembly.ExsitedDropSourcePaths[0] != assembly.SourcePath)
                        {
                            Console.ForegroundColor = ConsoleColor.Red;
                            Console.WriteLine($"Source path for {assembly.Name} in existed XmlDrop files is not the same with LLM result!");
                            Console.WriteLine($"Existed XmlDrop: {assembly.BinPlaceSourcePath}, LLM: {assembly.SourcePath}");
                            Console.ResetColor();
                            assembly.SourcePath = assembly.ExsitedDropSourcePaths[0];
                        } 
                    }
                    Console.WriteLine($"{assembly.Name}: {assembly.SourcePath} <= {assembly.PossibleSourcePaths}");
                }
            }
            return assemblyList;
        }

        /// <summary>
        /// Choose Source Path by LLM
        /// </summary>
        /// <param name="sourcePathList"></param>
        /// <param name="serviceFramework"></param>
        /// <param name="batchNumber"></param>
        public async Task<string> ChooseSourcePathHelper(string sourcePathList, string serviceFramework, int batchNumber)
        {
            string instruction =
                $"My C# project's target framework is {serviceFramework} and runs in a x64 machine, please select the most possible source path of its assemblies in each group of given list.\n" +
                $"Instructions:\n" +
                $"1.You should only choose the most possible one source path for each group of candidates.\n" +
                $"2.Consider a source path more likely the best choice if it starts \"lib\".\n" +
                $"3.Consider a source path more likely when its pattern matches the project target framework.\n" +
                $"4.You will be given a list of group of source path candidates, every group is separated by '|', and every source paths in one group separated by ';', and you can only choose one from each group.\n" +
                $"5.Give anwser with a list of the most possible source path in each group, separated by '|' in given order, and nothing more." +

                //$"6.How many groups in given list, how many source paths in answer" +
                $"6.The answer should contain source paths of exact quantity of number of groups in given list" +
                $"For example, if the framework is 'net472' and you are given list: 'lib\\net461;lib\\net5.0;lib\\net6.0;lib\\netstandard2.0;lib\\netcoreapp3.1|lib\\net462;lib\\net6.0;lib\\netcoreapp3.1', you will output 'lib\\net461|lib\\net462'.\n";

            string question = $"My C# project's target framework is {serviceFramework} and runs in a x64 machine, there are {batchNumber} groups of source path, so answer must conatin {batchNumber} source paths, please select the most possible source path of its assemblies in each group from following list: {sourcePathList}";

            Console.WriteLine($"{instruction}\n{question}");
            OpenAIService openAIService = new OpenAIService("<OpenAiEndPoint>", "<Key>", "<version>");

            return await openAIService.AzureGPTConversation(instruction, question).ConfigureAwait(false);
        }

        /// <summary>
        /// Choose Source Path by Tradition
        /// </summary>
        /// <param name="assembly">Assembly</param>
        /// <param name="serviceFramework">Service Framework</param>
        public AssemblyModel ChooseSourcePathByTradition(AssemblyModel assembly, string serviceFramework)
        {
            Dictionary<string, int> versionOrder = new Dictionary<string, int>()
                {
                    { @"lib\net462", 5 },
                    { @"lib\net461", 4 },
                    { @"lib\net46", 3 },
                    { @"lib\net45", 2 },
                    { @"lib\netstandard2.0", 1 }
                };
            List<string> sourcePathCandidates = assembly.SourcePathCandidatesInfo.Keys.ToList();
            int maxWeight = 0;
            foreach (string candidate in sourcePathCandidates)
            {
                int currentWeight = 0;
                if (candidate.StartsWith(@"lib\", StringComparison.InvariantCulture))
                {
                    currentWeight += 10;
                }
                if (candidate.EndsWith(serviceFramework, StringComparison.InvariantCulture))
                {
                    currentWeight += 100;
                }
                else
                {
                    if (versionOrder.ContainsKey(candidate))
                    {
                        currentWeight += versionOrder[candidate];
                    }
                }
                if (currentWeight >= maxWeight)
                {
                    maxWeight = currentWeight;
                    assembly.SourcePath = candidate;
                }
            }

            Console.WriteLine($"Non-LLM method choose: '{assembly.SourcePath}'");
            return assembly;
        }

        /// <summary>
        /// Choose Source Path by Tradition
        /// </summary>
        /// <param name="sourcePathCandidates">Assembly</param>
        /// <param name="serviceFramework">Service Framework</param>
        public string ChooseSourcePathByTradition(List<string> sourcePathCandidates, string serviceFramework)
        {
            Dictionary<string, int> versionOrder = new Dictionary<string, int>()
                {
                    { @"lib\net462", 5 },
                    { @"lib\net461", 4 },
                    { @"lib\net46", 3 },
                    { @"lib\net45", 2 },
                    { @"lib\netstandard2.0", 1 }
                };

            //List<string> sourcePathCandidates = assembly.SourcePathCandidatesInfo.Keys.ToList();
            int maxWeight = 0;
            string sourcePath = string.Empty;
            foreach (string candidate in sourcePathCandidates)
            {
                int currentWeight = 0;
                if (candidate.StartsWith(@"lib\", StringComparison.InvariantCulture))
                {
                    currentWeight += 10;
                }
                if (candidate.EndsWith(serviceFramework, StringComparison.InvariantCulture))
                {
                    currentWeight += 100;
                }
                else
                {
                    if (versionOrder.ContainsKey(candidate))
                    {
                        currentWeight += versionOrder[candidate];
                    }
                }
                if (currentWeight >= maxWeight)
                {
                    maxWeight = currentWeight;
                    sourcePath = candidate;
                }
            }

            Console.WriteLine($"Non-LLM method choose: '{sourcePath}'");
            return sourcePath;
        }
    }
}
