
# Tcp Exporter For Security Telemetry

An TCP-based exporter implementation to export `SecurityRecord` to local ODL TCP Server.

## Configurable Exporter Options

- `RecordCountPerRequest`: Count of security records per Tcp request.
- `ScheduledDelayMilliseconds`: Refer to [SecurityTelemetry `BatchExporter`](https://domoreexp.visualstudio.com/R9/_git/SDK?path=/src/Extensions/SecurityTelemetry/BatchExporter.cs&version=GBmain&_a=contents).
- `ExporterTimeoutMilliseconds`: Refer to [SecurityTelemetry `BatchExporter`](https://domoreexp.visualstudio.com/R9/_git/SDK?path=/src/Extensions/SecurityTelemetry/BatchExporter.cs&version=GBmain&_a=contents).
- `SecurityRecordFormatter`: The formatter used to convert `SecurityRecord` to json string.
- `ConnectionHealthCheckInterval`: The interval to check the aliveness of connection to ODL server in milliseconds.
- `SendTimeout`: Timeout for sending data/build connection to server in milliseconds.
- `UseAdhocLogger`: Whether use the windows event log and <PERSON>sole as the adhoc logger, default to true.
- `<PERSON><PERSON>erSize`: The BufferSize of data buffer for carrying serilized tcp request, typically, it should be set to RecordCountPerRequest*(Size of each SecurityRecord in bytes).
- `BatchSizeForSendingMetric`: the batch size for sending metric of TCP client.
- `SendMetricInterval`: Time interval for sending metric of TCP client in milliseconds.
- `EnableCleanUpArchivedFiles`: Enable clean up files.
- `RetentionInterval`: Time interval to keep archived files.
- `RetentionCount`: Number of archived files to keep.
- `MaxQueueSize`: Refer to [SecurityTelemetry `BatchExporter`](https://domoreexp.visualstudio.com/R9/_git/SDK?path=/src/Extensions/SecurityTelemetry/BatchExporter.cs&version=GBmain&_a=contents).
- `ScheduledDelay`: Refer to [SecurityTelemetry `BatchExporter`](https://domoreexp.visualstudio.com/R9/_git/SDK?path=/src/Extensions/SecurityTelemetry/BatchExporter.cs&version=GBmain&_a=contents).
- `ExporterTimeout`: Refer to [SecurityTelemetry `BatchExporter`](https://domoreexp.visualstudio.com/R9/_git/SDK?path=/src/Extensions/SecurityTelemetry/BatchExporter.cs&version=GBmain&_a=contents).
- `MaxExportBatchSize`: Refer to [SecurityTelemetry `BatchExporter`](https://domoreexp.visualstudio.com/R9/_git/SDK?path=/src/Extensions/SecurityTelemetry/BatchExporter.cs&version=GBmain&_a=contents).

## How to use

```c#
            IHostBuilder hostbuilder = new HostBuilder()
                .ConfigureServices((context, services) =>
                {
                    _ = services.AddSecurityTelemetry(builder =>
                    {
                        _ = services.Configure<SecurityTelemetryOptions>(option =>
                        {
                            option.PrepopulatedFields = new Dictionary<string, object>
                            {
                                { "ComputerName", "TestComputerName" },
                                { "ClusterId", "TestClusterId" }
                            };
                        });
                        _ = services.Configure<SecurityTelemetryTcpExporterOptions>(option =>
                        {
                            option.ConnectionHealthCheckInterval = 30000;
                            option.RecordCountPerRequest = 20;
                            option.SendTimeout = 2000;
                            option.BufferSize = 30 * 4 * 1024
                        });
                        _ = builder.WithExporter<SecurityTelemetryExporter>();
                    });
                });
```

## Fallback to disk 

When exporter fail to export security records to ODL server via TCP, it persist the records to local disk files with a default folder name of `D:\OfficeDataLoader\Dump\NRTLoggingSdk\{Record Schema Name}` if there is disk D on the host, on ODL side, the files will be picked up periodically and uploaded to the related destination 
based on the corresponding Logtype configuration
