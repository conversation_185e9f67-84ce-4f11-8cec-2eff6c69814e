<?xml version="1.0" encoding="utf-8"?>
<!--Cpp build environment is significantly different from c#. Adding subdir level Directory.Build.props
and Directory.Build.Targets here to limit the build environment change to vcxproj. Otherwise we'll have many
Condition="'$(MSBuildProjectExtension)' != '.vcxproj'" blocks here and there, increasing maintenance
complexity.-->
<Project xmlns="http://schemas.microsoft.com/developer/msbuild/2003">

  <PropertyGroup>
    <MSBuildAllProjects>$(MSBuildAllProjects);$(MSBuildThisFileFullPath)</MSBuildAllProjects>
  </PropertyGroup>

  <Import Project="$([MSBuild]::GetDirectoryNameOfFileAbove('$(MSBuildThisFileDirectory)..', 'Directory.Build.props'))\Directory.Build.props" />

  <PropertyGroup>
    <OutputPath>$(DistribRoot)\$(Configuration)\$(Platform)\$(MSBuildProjectName)\</OutputPath>
    <OutDir>$(OutputPath)</OutDir>
    <PkgVisualCppTools>$(EnlistmentRoot)\packages\VisualCppTools.14.36.32535\</PkgVisualCppTools>
    <PkgMicrosoft_Windows_SDK_CPP>$(EnlistmentRoot)\packages\Microsoft.Windows.SDK.CPP.10.0.20348.19\</PkgMicrosoft_Windows_SDK_CPP>
    <PkgMicrosoft_Windows_SDK_CPP_x64>$(EnlistmentRoot)\packages\Microsoft.Windows.SDK.CPP.x64.10.0.20348.19\</PkgMicrosoft_Windows_SDK_CPP_x64>
  </PropertyGroup>

  <PropertyGroup Condition="Exists('$(PkgVisualCppTools)\lib\native')">
    <OutIncludePath>$(OutputPath)include</OutIncludePath>
    <VCInstallDir>$(PkgVisualCppTools)\lib\native</VCInstallDir>
    <VCToolsInstallDir>$(VCInstallDir)</VCToolsInstallDir>
    <VCInstallDir_170>$(VCInstallDir)</VCInstallDir_170>
    <VCToolsInstallDir_170>$(VCInstallDir)</VCToolsInstallDir_170>
  </PropertyGroup>

  <!--
    Use the Windows SDK from the "Microsoft.Windows.SDK.CPP" NuGet package
    This NuGet package is officially supported by Windows and MsBuild / Visual Studio
    See http://aka.ms/WinSdkNuget for more info
  -->
  <Import Project="$(PkgMicrosoft_Windows_SDK_CPP)\build\native\Microsoft.Windows.SDK.CPP.props" Condition="'$(MSBuildProjectExtension)' == '.vcxproj' And Exists('$(PkgMicrosoft_Windows_SDK_CPP)\build\native\Microsoft.Windows.SDK.CPP.props')"/>
  <Import Project="$(PkgMicrosoft_Windows_SDK_CPP_x64)\build\native\Microsoft.Windows.SDK.CPP.x64.props" Condition="'$(MSBuildProjectExtension)' == '.vcxproj' And Exists('$(PkgMicrosoft_Windows_SDK_CPP_x64)\build\native\Microsoft.Windows.SDK.CPP.x64.props')"/>


  <!-- Use the C++ Build Tools from the VisualCppTools package -->
  <!-- Officially supported by the Visual Studio C++ team -->
  <Import Project="$(PkgVisualCppTools)\build\native\VisualCppTools.props" Condition="Exists('$(PkgVisualCppTools)\build\native\VisualCppTools.props')" />

  <PropertyGroup>
    <UniversalCRTSdkDir_10>$(UCRTContentRoot)</UniversalCRTSdkDir_10>
  </PropertyGroup>

  <Sdk Name="Microsoft.Build.Vcpkg" />

  <PropertyGroup>
    <VcpkgHostTriplet>tc</VcpkgHostTriplet>
    <VcpkgTriplet>tc</VcpkgTriplet>
    <VcpkgAdditionalInstallOptions>$(VcpkgAdditionalInstallOptions) --overlay-triplets "$(EnlistmentRoot)\build\vcpkg\triplets"</VcpkgAdditionalInstallOptions>
    <VcpkgAdditionalInstallOptions>$(VcpkgAdditionalInstallOptions) --overlay-ports "$(EnlistmentRoot)\build\vcpkg\ports"</VcpkgAdditionalInstallOptions>
    <!-- Add the TerrapinRetrievalTool for vcpkg source retrieval -->
    <VcpkgAdditionalInstallOptions>$(VcpkgAdditionalInstallOptions) --x-asset-sources "x-script,$(TerrapinRetrievalToolPath) -b https://vcpkg.storage.devpackages.microsoft.io/artifacts/ -a true -p {url} -s {sha512} -d {dst};x-block-origin"</VcpkgAdditionalInstallOptions>

    <!--Some useful paths.-->
    <VcpkgInstallDir>$(EnlistmentRoot)\vcpkg_installed\$(VcpkgHostTriplet)\$(VcpkgTriplet)</VcpkgInstallDir>
    <VcpkgIncludeDir>$(VcpkgInstallDir)\include</VcpkgIncludeDir>
    <VcpkgDebugLibDir>$(VcpkgInstallDir)\debug\lib</VcpkgDebugLibDir>
    <VcpkgReleaseLibDir>$(VcpkgInstallDir)\lib</VcpkgReleaseLibDir>
  </PropertyGroup>

  <ItemDefinitionGroup>
    <ClCompile>
      <LanguageStandard>stdcpp20</LanguageStandard>
      <PreprocessorDefinitions>OPENTELEMETRY_STL_VERSION=2020;_SILENCE_ALL_CXX20_DEPRECATION_WARNINGS;BOOST_ASIO_DISABLE_CO_AWAIT;%(PreprocessorDefinitions)</PreprocessorDefinitions>
    </ClCompile>
  </ItemDefinitionGroup>

</Project>