﻿// <copyright file="BuildVersionEnricherTest.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Text.RegularExpressions;
using System.Xml.Linq;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.M365.Core.Telemetry.R9.Tracing.Instrumentation.Accelerator;
using Microsoft.M365.Core.Telemetry.R9.Tracing.Instrumentation.Accelerator.Utilities;
using Microsoft.R9.Extensions.Enrichment;
using Xunit;
#if NETSTANDARD || NETCOREAPP || NET6_0
using Microsoft.M365.Core.Portable.Registry;
#else
using Microsoft.Win32;
#endif

namespace Microsoft.M365.Core.Telemetry.R9.Tracing.Instrumentation.Accelerator.Tests
{
    /// <summary>
    /// BuildVersionEnricher Test
    /// </summary>
    public class BuildVersionEnricherTest
    {
        /// <summary>
        /// Enrich build version test
        /// </summary>
        [Fact]
        public void EnrichBuildVersionTest()
        {
            var services = new ServiceCollection();

            var configBuilder = new ConfigurationBuilder().AddInMemoryCollection(new Dictionary<string, string>
            {
                { "ServiceName", "name" },
                { "RuntimeModel", "ModelA" }
            });
            services.Configure<ServiceMetaDataOptions>(configBuilder.Build());
            services.AddSingleton<ITraceEnricher, BuildVersionEnricher>();
            var enricher = services.BuildServiceProvider().GetRequiredService<ITraceEnricher>();
            Assert.NotNull(enricher);

            var activity = new Activity("test");
            Assert.Null(activity.GetTagItem(Constants.BuildVersion));

            enricher.Enrich(activity);
            string buildVersion = (string)activity.GetTagItem(Constants.BuildVersion);

            Assert.NotNull(buildVersion);
            Match match = Regex.Match(buildVersion, @"^\d+\.\d{2}\.\d{4}\.\d{3}$"); // Follow pattern like "0.00.0000.000"
            Assert.True(match.Success);
        }

        /// <summary>
        /// Erich build vision mock test
        /// </summary>
        [Fact]
        public void ErichBuildVisionMockTest()
        {
            TracerStartupExtensions.BuildVersionCache = string.Empty;
            FakeRegistryKey testRegistryKey = new FakeRegistryKey(string.Empty);

            string buildVersion = BuildVersionEnricher.GetBuildVersion(testRegistryKey);
            Assert.Equal("15.20.5678.000", buildVersion);

            TracerStartupExtensions.BuildVersionCache = "4.33.2222.001";
            buildVersion = BuildVersionEnricher.GetBuildVersion(testRegistryKey);
            Assert.Equal("4.33.2222.001", buildVersion);

            TracerStartupExtensions.BuildVersionCache = string.Empty;
            FakeRegistryKey testRegistryKey_2 = new FakeRegistryKey();
            testRegistryKey_2.SetRegistries(new Dictionary<string, object>()
            {
                { @"MsiProductMajor", 1 },
                { @"MsiProductMinor", 22 },
                { @"MsiBuildMajor", 3333 },
                { @"MsiBuildMinor", 4 },
            });

            buildVersion = BuildVersionEnricher.GetBuildVersion(testRegistryKey_2);
            Assert.Equal("1.22.3333.004", buildVersion);
        }

        /// <summary>
        /// Windows registry key test
        /// </summary>
        [Fact]
        public void WindowsRegistryKeyTest()
        {
            IRegistryKey registryKey = new WindowsRegistryKey();
            IRegistryKey notExist = registryKey.OpenSubKey(@"SOFTWARE\NotExist");
            Assert.Null(notExist);
            IRegistryKey setupKey = registryKey.OpenSubKey(@"SOFTWARE\Microsoft\ExchangeServer\v15\Setup");
            Assert.Null(setupKey?.GetValue("NotExist"));
        }
    }
}
