﻿// <copyright file="CommonSettings.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Diagnostics.CodeAnalysis;
using System.IO;
using System.Linq;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.M365.ODL.NrtTcpClient;
using LogLevel = Microsoft.M365.ODL.NrtTcpClient.LogLevel;

namespace Microsoft.M365.ODL.NrtTcpClient
{
    /// <summary>
    /// common settings, copied from the Substrate ODL code repo but with Microsoft.Extensions.Configuration
    /// </summary>
    [ExcludeFromCodeCoverage]
    public sealed class CommonSettings
    {
        /// <summary>
        /// The logger
        /// </summary>
        private static IOdlLogger logger = OdlLogger.Instance;

        /// <summary>
        /// the product version
        /// </summary>
        public static readonly string? ProductVersion;

        /// <summary>
        /// Instance name
        /// </summary>
        public static readonly string InstanceName = "ODLTcpClient";

        /// <summary>
        /// ODL dump folder root. Env variables are supported (eg: %WinDir%).
        /// </summary>
        public const string OdlOfflineLogRoot = "OdlOfflineLogRoot";

        /// <summary>
        /// Event source name and dump leaf folder
        /// </summary>
        public const string EventSourceName = "NRTLoggingSdk";

        /// <summary>
        /// Dynamic settings
        /// </summary>
        public static readonly Dictionary<string, string> Configurations;

        /// <summary>
        /// Initializes static members of the <see cref="CommonSettings" /> class.
        /// </summary>
        static CommonSettings()
        {
            Configurations = new Dictionary<string, string>();

            // Customized OdlOfflineLogRoot through appsettings.json
            try
            {
                string configPath = Path.Combine(Directory.GetCurrentDirectory(), "appsettings.json");
                var builder = new ConfigurationBuilder()
                    .SetBasePath(Directory.GetCurrentDirectory())
                    .AddJsonFile(configPath, optional: true, reloadOnChange: true);

                IConfigurationRoot configuration = builder.Build();
                var configSettings = configuration.GetSection("AppSettings").GetChildren().ToDictionary(x => x.Key, x => x.Value);

                if (configSettings.Count > 0 && configSettings.ContainsKey(OdlOfflineLogRoot))
                {
                    string logRootPath = configSettings[OdlOfflineLogRoot];

                    if (!string.IsNullOrWhiteSpace(logRootPath))
                    {
                        string pathRoot = Path.GetPathRoot(logRootPath);
                        var customizedDriver = DriveInfo.GetDrives().SingleOrDefault(d => d.Name == pathRoot);
                        if (customizedDriver != null && customizedDriver.DriveType == DriveType.Fixed)
                        {
                            Configurations.Add(OdlOfflineLogRoot, logRootPath);
                        }
                    }
                }

                var assemblyLocation = Assembly.GetExecutingAssembly().Location;
                ProductVersion = FileVersionInfo.GetVersionInfo(assemblyLocation).ProductVersion ?? string.Empty;
            }
            catch (Exception e)
            {
                logger.Log(LogLevel.Error, LogEventId.TcpClientCommonSettingError, $"read logRootPath from config file failed. {e}");
            }

            // fall back to default value
            var driveD = DriveInfo.GetDrives().SingleOrDefault(d => d.Name == @"D:\");
            string defaultRootPath = driveD != null && driveD.DriveType == DriveType.Fixed
                ? @"D:\OfficeDataLoader\Dump"
                : @"%SystemDrive%\OfficeDataLoader\Dump";
            if (!Configurations.TryGetValue(OdlOfflineLogRoot, out string curVal) || string.IsNullOrEmpty(curVal))
            {
                Configurations[OdlOfflineLogRoot] = defaultRootPath;
            }
        }

        /// <summary>
        /// Get dump folder for logtype, copy from ODL repo
        /// </summary>
        /// <param name="logtype">Log type</param>
        /// <returns>Dump folder of logtype</returns>
        public static string GetDumpFolderofLogtype(string logtype)
        {
            return Path.Combine(
                Environment.ExpandEnvironmentVariables(Configurations[OdlOfflineLogRoot]), // ROOT
                EventSourceName,
                logtype);
        }
    }
}
