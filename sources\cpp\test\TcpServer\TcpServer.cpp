#include "TcpServer.h"
#include "AsioContext.h"

#include <iostream>
#include <ctime>
#include <thread>
#include <windows.h>
#include <psapi.h>

namespace Microsoft {
namespace M365 {
namespace Exporters {

constexpr int kPrintStatsPeriod = 10; // seconds

TcpServer::TcpServer(short port)
    : io_context_(AsioContext::GetIoContext()),
      port_(port),
      logger_(opentelemetry::logs::Provider::GetLoggerProvider()->GetLogger("TestServerLogger")),
      stats_timer_(io_context_),
      read_timer_(io_context_)
{
    Start();
}

void TcpServer::ShutDown()
{
    std::lock_guard<std::mutex> lock(mutex_);
    try {
        acceptor_.reset();
        for (auto& socket : active_sockets_)
        {
            close_socket(socket);
        }
        is_live_ = false;
        active_sockets_.clear();
        stats_timer_.cancel();
    }
    catch (const std::exception& e)
    {
        logger_->Log(opentelemetry::logs::Severity::kError, "Server: ShutDown error: " + std::string(e.what()));
    }
}

void TcpServer::Start()
{
    std::lock_guard<std::mutex> lock(mutex_);
    acceptor_.reset();
    acceptor_ = std::make_unique<boost::asio::ip::tcp::acceptor>(io_context_, boost::asio::ip::tcp::endpoint(boost::asio::ip::tcp::v4(), port_));
    is_live_ = true;
    start_accept();
    start_stats_timer();
}

TcpServer::~TcpServer()
{
    ShutDown();
    std::lock_guard<std::mutex> lock(mutex_);
    stats_timer_.cancel();
    read_timer_.cancel();
}

void TcpServer::start_accept()
{
    if (!is_live_) {
        return;
    }
    auto socket = std::make_shared<boost::asio::ip::tcp::socket>(io_context_);
    acceptor_->async_accept(*socket, [this, socket](const boost::system::error_code& error)
    {
        handle_accept(error, socket);
    });
}

void TcpServer::handle_accept(const boost::system::error_code& error, std::shared_ptr<boost::asio::ip::tcp::socket> socket)
{
    std::lock_guard<std::mutex> lock(mutex_);
    if (!is_live_) {
        return;
    }
    if (!error)
    {
        logger_->Log(opentelemetry::logs::Severity::kInfo, "Server: Accepted connection.");
        active_sockets_.insert(socket);
        read(socket);
    }
    else if (error.value() == 10009) {
        // Message: The file handle supplied is not valid.
        // No incomming connection. Normal case.
    }
    else {
        logger_->Log(opentelemetry::logs::Severity::kError, "Server: Accept error: " + error.message() + ", code: " + std::to_string(error.value()) + ", category: " + error.category().name());
    }
    start_accept();
}

void TcpServer::read(std::shared_ptr<boost::asio::ip::tcp::socket> socket) {
    if (!is_live_) {
        return;
    }
    socket->async_read_some(boost::asio::buffer(buffer_),
        [this, socket](const boost::system::error_code& error, std::size_t bytes_transferred)
        {
            handle_read(error, bytes_transferred, socket);
        });
}

void TcpServer::handle_read(const boost::system::error_code& error, std::size_t bytes_transferred, std::shared_ptr<boost::asio::ip::tcp::socket> socket)
{
    std::lock_guard<std::mutex> lock(mutex_);
    if (!is_live_) {
        return;
    }
    if (!error)
    {
        // TODO(jiayiwang): According to PRAssistant, this is when client close the connection. Test.
        if (bytes_transferred == 0) {
            logger_->Log(opentelemetry::logs::Severity::kInfo, "Server: Connection closed by client.");
            remove_active_socket(socket);
            close_socket(socket);
            start_accept();
            return;
        }
        std::string message(buffer_, bytes_transferred);
        if (buffer_[0] != '\x01') {
            logger_->Log(opentelemetry::logs::Severity::kError, "Server: Invalid magic byte.");
        }
        if (bytes_transferred < 5) {
            logger_->Log(opentelemetry::logs::Severity::kError, "Server: Message too short.");
        }
        // Convert buffer_[1..4] to unsigned int. The bytes are in reverse order, according to C# BitConverter.
        int value = 0;
        for (int i = 1; i <= 4; ++i) {
            value = (value << 8) + (static_cast<unsigned char>(buffer_[5 - i]));
        }
        if (value != bytes_transferred - 5) {
            logger_->Log(opentelemetry::logs::Severity::kError, "Server: Message size and content mismatch. Expected size: " + std::to_string(value) + ", actual size: " + std::to_string(bytes_transferred - 5) + ", diff: " + std::to_string(bytes_transferred - 5 - value));
        }


        count_++;
        size_ += bytes_transferred;
        // Save the data in buffer_[5:] to last_message_
        last_message_ = std::string(buffer_ + 5, bytes_transferred - 5);

        // Continue reading from the socket
        read(socket);
    }
    else if (error == boost::asio::error::eof)
    {
        logger_->Log(opentelemetry::logs::Severity::kInfo, "Server: No data. Retry in 100ms.");
        read_timer_.expires_after(std::chrono::milliseconds(100));
        read_timer_.async_wait([this, socket](const boost::system::error_code& error)
        {
            read(socket);
        });
    }
    else
    {
        logger_->Log(opentelemetry::logs::Severity::kError, "Server: Read error: " + error.message() + ", code: " + std::to_string(error.value()) + ", category: " + error.category().name());
        remove_active_socket(socket);
        close_socket(socket);
        start_accept();
    }
}

void TcpServer::start_stats_timer()
{
    stats_timer_.expires_after(std::chrono::seconds(kPrintStatsPeriod));
    stats_timer_.async_wait([this](const boost::system::error_code& error)
    {
        handle_stats_timer(error);
    });
}

void TcpServer::handle_stats_timer(const boost::system::error_code& error)
{
    std::lock_guard<std::mutex> lock(mutex_);
    if (!error)
    {
        print_stats();
    }
    else if (error.value() == 995) {
        // Message: The I/O operation has been aborted because of either a thread exit or an application request.
        // Hard stop. The TcpServer object may not be valid anymore.
        return;
    }
    else
    {
        logger_->Log(opentelemetry::logs::Severity::kError, "Server: Stats timer error: " + error.message() + ", code: " + std::to_string(error.value()) + ", category: " + error.category().name());
    }
    start_stats_timer(); // Restart the timer
}

void TcpServer::print_stats()
{
    logger_->Log(opentelemetry::logs::Severity::kInfo, "Server: Number of received messages: " + std::to_string(count_) + ", total size: " + std::to_string(size_));

    PROCESS_MEMORY_COUNTERS_EX pmc;
    if (GetProcessMemoryInfo(GetCurrentProcess(), (PROCESS_MEMORY_COUNTERS*)&pmc, sizeof(pmc)))
    {
        logger_->Log(opentelemetry::logs::Severity::kInfo, "Server: Current memory usage: " + std::to_string(pmc.WorkingSetSize / 1024 / 1024) + " MB");
    }
    else
    {
        logger_->Log(opentelemetry::logs::Severity::kError, "Server: Failed to get memory usage.");
    }
}

void TcpServer::close_socket(std::shared_ptr<boost::asio::ip::tcp::socket> socket)
{
    if (!is_live_) {
        return;
    }
    try {
        socket->close();
    }
    catch (const std::exception& e)
    {
        logger_->Log(opentelemetry::logs::Severity::kError, "Server: ShutDown error: " + std::string(e.what()));
    }
}

void TcpServer::remove_active_socket(std::shared_ptr<boost::asio::ip::tcp::socket> socket)
{
    if (!is_live_) {
        return;
    }
    active_sockets_.erase(socket);
}

} // namespace Exporters
} // namespace M365
} // namespace Microsoft