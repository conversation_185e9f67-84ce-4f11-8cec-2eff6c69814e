﻿// <copyright file="MDSLogger.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System;
using System.Diagnostics.CodeAnalysis;
using System.Diagnostics.Tracing;

using Microsoft.Extensions.Logging;

namespace Microsoft.M365.Core.Telemetry.SDKLogger
{
    /// <summary>
    /// A proxy class of MDS local agent, singleton mode.
    /// Send the logs to MDS through ETW using EventSource V2.
    /// </summary>
    [ExcludeFromCodeCoverage]
    internal class MDSLogger
    {
        /// <summary>
        /// The name of the event.
        /// </summary>
        private const string EventName = "PassiveMonitoringSDK";

        /// <summary>
        /// Single instance of MDSLogger
        /// </summary>
        private static readonly MDSLogger instance = new MDSLogger();

        /// <summary>
        /// Flag to indicate whether Geneva MDS logging is initialized
        /// </summary>
        private bool mdsInitialized;

        /// <summary>
        /// EventSource V2 (TraceLogging)
        /// </summary>
        private EventSource traceLogger;

        /// <summary>
        /// Prevents a default instance of the <see cref="MDSLogger" /> class from being created.
        /// </summary>
        private MDSLogger()
        {
            try
            {
                this.traceLogger = new EventSource(EventName);
                this.mdsInitialized = true;
            }
            catch (Exception e)
            {
                SDKLog.Info($"Error happens in MDSLogger Initialization:{e}");
            }
        }

        /// <summary>
        /// Log message with format string.
        /// </summary>
        /// <param name="severity">the log severity</param>
        /// <param name="traceId">the trace ID</param>
        /// <param name="content">log content</param>
        /// <param name="args">format arguments</param>
        public static void TraceLog(LogLevel severity, int traceId, string content, params object[] args)
        {
#pragma warning disable CA1305 // Specify IFormatProvider
            MDSLogger.instance.TraceLogImpl(severity, traceId, string.Format(content, args));
#pragma warning restore CA1305 // Specify IFormatProvider
        }

        /// <summary>
        /// Log message.
        /// </summary>
        /// <param name="severity">the log severity</param>
        /// <param name="traceId">the trace ID</param>
        /// <param name="message">the log message</param>
        public static void TraceLog(LogLevel severity, int traceId, string message)
        {
            MDSLogger.instance.TraceLogImpl(severity, traceId, message);
        }

        /// <summary>
        /// Implementation of writing trace logs.
        /// </summary>
        /// <param name="severity">the log severity</param>
        /// <param name="traceId">the trace ID</param>
        /// <param name="message">log content</param>
        private void TraceLogImpl(LogLevel severity, int traceId, string message)
        {
            if (!this.mdsInitialized)
            {
                return;
            }

            try
            {
                this.traceLogger.Write(
                    "PassiveMonitoringSDKTraceLogs",
                    new
                    {
                        Level = severity,
                        TraceId = traceId,
                        Message = message,
                    });
            }
            catch (Exception e)
            {
                SDKLog.Info($"Error happens in TraceLogImpl:{e}");
            }
        }
    }
}
