<?xml version="1.0" encoding="utf-8"?>
<Project xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  
  <PropertyGroup>
    <MSBuildAllProjects>$(MSBuildAllProjects);$(MSBuildThisFileFullPath)</MSBuildAllProjects>

    <StrongNamePublicKeysPath>$(EnlistmentRoot)\Secrets\StrongNamePublicKeys</StrongNamePublicKeysPath>

    <DelaySign>true</DelaySign>
    <SignAssembly>true</SignAssembly>
    <Project_IsSigned>true</Project_IsSigned>
  </PropertyGroup>

  <PropertyGroup Condition = "'$(ENABLE_CLOUDSIGN)' == '1'">
    <AssemblyOriginatorKeyFile>$(StrongNamePublicKeysPath)\55490_CloBuiStrongName_QSIGN_2015-06-29.snk</AssemblyOriginatorKeyFile>
    <StrongNameScenarioToken>-5</StrongNameScenarioToken>
    <AssemblyCertificates>-5;-6</AssemblyCertificates>
  </PropertyGroup>

  <PropertyGroup Condition = "'$(ENABLE_EPRS)' == '1'">
    <AssemblyOriginatorKeyFile>$(StrongNamePublicKeysPath)\MSSharedLibSN1024.snk</AssemblyOriginatorKeyFile>
    <AssemblyCertificates>10024;400</AssemblyCertificates>
  </PropertyGroup>
  
  <ItemGroup>
    <QCustomInput Include="$(AssemblyOriginatorKeyFile)">
      <Visible>false</Visible>
    </QCustomInput>
  </ItemGroup>
</Project>