<Project>
  <PropertyGroup>
    <!-- Enables Central Package Management
      See https://learn.microsoft.com/nuget/consume-packages/central-package-management for more details.
    -->
    <ManagePackageVersionsCentrally>true</ManagePackageVersionsCentrally>
    <!-- We temporarily allow the use of overriding the package versions defined in this file.
      See
    https://learn.microsoft.com/nuget/consume-packages/Central-Package-Management#overriding-package-versions
    for more details.
    -->
    <CentralPackageVersionOverrideEnabled>true</CentralPackageVersionOverrideEnabled>
  </PropertyGroup>
  <PropertyGroup>
    <R9Version>8.11.1</R9Version>
    <MicrosoftExtensionsVersion>8.0.1</MicrosoftExtensionsVersion>
  </PropertyGroup>
  <ItemGroup>
    <PackageVersion Include="BenchmarkDotNet" Version="0.13.12" />
    <PackageVersion Include="Bond.Core.CSharp" Version="13.0.0" />
    <PackageVersion Include="CoreWCF.Http" Version="1.5.0-wsrs.1.g14d7d00798" />
    <PackageVersion Include="CoreWCF.Primitives" Version="1.5.0-wsrs.1.g14d7d00798" />
    <PackageVersion Include="coverlet.collector" Version="1.2.0" />
    <PackageVersion Include="CredSMARTSdk" Version="16.1.3237" />
    <PackageVersion Include="DotNetFxTools.Corext" Version="4.0.0" />
    <PackageVersion Include="Exchange.Focus.BuildTasks" Version="1.1.10" />
    <PackageVersion Include="Exchange.GitHooks" Version="1.0.13" />
    <PackageVersion Include="FluentAssertions" Version="5.6.0" />
    <PackageVersion Include="Microsoft.AspNetCore" Version="2.2.0" />
    <PackageVersion Include="Microsoft.Azure.CredentialScanner" Version="1.0.23" />
    <PackageVersion Include="Microsoft.CodeAnalysis.FxCopAnalyzers" Version="2.9.8" />
    <PackageVersion Include="Microsoft.Internal.Analyzers" Version="2.6.6" />
    <PackageVersion Include="Microsoft.Internal.CodeSign.CloudBuild" Version="7.3.0" />
    <PackageVersion Include="Microsoft.M365.Core.Portable.Registry" Version="0.1.6" />
    <PackageVersion Include="Microsoft.M365.Core.Telemetry.OpenTelemetry.Exporter.Filters" Version="1.1.1" />
    <PackageVersion Include="Microsoft.NET.Test.Sdk" Version="17.7.0" />
    <PackageVersion Include="Microsoft.Extensions.Configuration.Ecs" Version="16.1.0.564" />
    <PackageVersion Include="Microsoft.Extensions.Configuration.Ecs.InternalizedDependencies" Version="20.6.2.1208" />
    <PackageVersion Include="Microsoft.Extensions.Configuration.Ecs.Authentication.InternalizedDependencies" Version="20.6.2.1208" />
    <PackageVersion Include="Microsoft.Extensions.Configuration.Json" Version="$(MicrosoftExtensionsVersion)" />
    <PackageVersion Include="Microsoft.Extensions.DependencyInjection" Version="$(MicrosoftExtensionsVersion)" />
    <PackageVersion Include="Microsoft.Extensions.Diagnostics.Testing" Version="8.2.0" />
    <PackageVersion Include="Microsoft.Extensions.Hosting" Version="$(MicrosoftExtensionsVersion)" />
    <PackageVersion Include="Microsoft.Extensions.Logging" Version="$(MicrosoftExtensionsVersion)" />
    <PackageVersion Include="Microsoft.Extensions.Telemetry" Version="9.3.0" />
    <PackageVersion Include="Microsoft.Identity.Client" Version="4.68.0" />
    <PackageVersion Include="Microsoft.R9.Extensions.ClusterMetadata.Cosmic" Version="$(R9Version)" />
    <PackageVersion Include="Microsoft.R9.Extensions.Enrichment.Abstractions" Version="$(R9Version)" />
    <PackageVersion Include="Microsoft.R9.Extensions.Enrichment.Cosmic" Version="$(R9Version)" />
    <PackageVersion Include="Microsoft.R9.Extensions.Essentials" Version="$(R9Version)" />
    <PackageVersion Include="Microsoft.R9.Extensions.HttpClient.Tracing" Version="$(R9Version)" />
    <PackageVersion Include="Microsoft.R9.Extensions.Logging" Version="$(R9Version)" />
    <PackageVersion Include="Microsoft.R9.Extensions.Logging.Abstractions" Version="$(R9Version)" />
    <PackageVersion Include="Microsoft.R9.Extensions.Logging.Exporters.Console" Version="$(R9Version)" />
    <PackageVersion Include="Microsoft.R9.Extensions.Logging.Exporters.Geneva" Version="$(R9Version)" />
    <PackageVersion Include="Microsoft.R9.Extensions.Logging.Fakes" Version="$(R9Version)" />
    <PackageVersion Include="Microsoft.R9.Extensions.Metering" Version="$(R9Version)" />
    <PackageVersion Include="Microsoft.R9.Extensions.Metering.Abstractions" Version="$(R9Version)" />
    <PackageVersion Include="Microsoft.R9.Extensions.Metering.Exporters.Geneva" Version="$(R9Version)" />
    <PackageVersion Include="Microsoft.R9.Extensions.Metering.Fakes" Version="$(R9Version)" />
    <PackageVersion Include="Microsoft.R9.Extensions.Metering.Geneva" Version="$(R9Version)" />
    <PackageVersion Include="Microsoft.R9.Extensions.Options.Validation" Version="$(R9Version)" />
    <packageVersion Include="Microsoft.R9.Extensions.Redaction" Version="$(R9Version)" />
    <PackageVersion Include="Microsoft.R9.Extensions.Tracing" Version="$(R9Version)" />
    <PackageVersion Include="Microsoft.R9.Extensions.Tracing.Exporters.Geneva" Version="$(R9Version)" />
    <PackageVersion Include="Microsoft.R9.Extensions.Tracing.Http" Version="$(R9Version)" />
    <PackageVersion Include="Microsoft.R9.Generators" Version="1.25.0" />
    <PackageVersion Include="Microsoft.SecurityCop" Version="1.0.0.4" />
    <PackageVersion Include="Microsoft.Skype.ECS.Client" Version="16.1.0.602" />
    <PackageVersion Include="Microsoft.Skype.ECS.Client.InternalizedDependencies" Version="20.6.2.1208" />
    <PackageVersion Include="Microsoft.Skype.ECS.Client.Authentication" Version="16.1.0.600" />
    <PackageVersion Include="Microsoft.Skype.ECS.Client.Authentication.InternalizedDependencies" Version="20.6.2.1208" />
    <PackageVersion Include="Microsoft.Win32.Registry" Version="5.0.0" />
    <PackageVersion Include="Moq" Version="4.16.1" />
    <PackageVersion Include="MSTest.TestAdapter" Version="2.2.8" />
    <PackageVersion Include="MSTest.TestFramework" Version="2.2.8" />
    <PackageVersion Include="Newtonsoft.Json" version="13.0.3" />
    <PackageVersion Include="NSubstitute" Version="4.0.0" />
    <PackageVersion Include="NuGet.Protocol" Version="6.10.1" />
    <PackageVersion Include="NuProj.Common" Version="0.11.30" />
    <PackageVersion Include="OpenTelemetry.Api" Version="1.9.0" />
    <PackageVersion Include="OpenTelemetry.Exporter.Console" Version="1.8.1" />
    <PackageVersion Include="OpenTelemetry.Exporter.Geneva" Version="1.9.0" />
    <PackageVersion Include="OpenTelemetry.Exporter.InMemory" Version="1.7.0" />
    <PackageVersion Include="OpenTelemetry.Instrumentation.AspNet" Version="1.9.0-beta.1" />
    <PackageVersion Include="OpenTelemetry.Instrumentation.Http" Version="1.8.1" />
    <PackageVersion Include="OpenTelemetry.Instrumentation.wcf" Version="1.0.0-rc.15" />
    <PackageVersion Include="OSS.Build.ExchangeTestStudio" Version="1.0.43" />
    <PackageVersion Include="OSS.RoslynAnalysis" Version="1.4.3" />
    <PackageVersion Include="OSS.ScopeIdentifier.Analyzer" Version="1.2.2" />
    <PackageVersion Include="PortableGit" Version="2.17.1" />
    <PackageVersion Include="StyleCop.Analyzers" Version="1.1.118" />
    <PackageVersion Include="System.IO.Hashing" Version="9.0.2" />
    <PackageVersion Include="System.DirectoryServices.Protocols" Version="6.0.1" />
    <PackageVersion Include="System.Drawing.Common" Version="4.7.2" />
    <PackageVersion Include="System.Runtime.Loader" Version="4.3.0" />
    <PackageVersion Include="System.ServiceModel.Http" Version="4.10.3" />
    <PackageVersion Include="System.ServiceProcess.ServiceController" Version="4.7.0" />
    <PackageVersion Include="System.Text.Json" Version="8.0.5" />
    <PackageVersion Include="xunit" Version="2.4.1" />
    <PackageVersion Include="Xunit.Combinatorial" Version="2.0.24" />
    <PackageVersion Include="xunit.runner.visualstudio" Version="3.0.2" />
    <PackageVersion Include="xunit.v3" Version="2.0.1" />
    <PackageVersion Include="xunit.v3.core" Version="2.0.1" />
  </ItemGroup>
</Project>