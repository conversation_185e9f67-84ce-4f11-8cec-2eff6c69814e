﻿// <copyright file="Program.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using System.Xml.Linq;
using Kusto.Cloud.Platform.Utils;
using Kusto.Data;
using Microsoft.TeamFoundation.Build.WebApi;
using Microsoft.TeamFoundation.SourceControl.WebApi;
using Microsoft.VisualStudio.Services.Organization.Client;
using R9MigrationHelper.Model;
using R9MigrationHelper.Skills.BindingRedirectSkills.KustoQuerySkills;
using R9MigrationHelper.Skills.BindingRedirectSkills.StringParseSkills;
using R9MigrationHelper.Skills.GitSkills;
using R9MigrationHelper.Skills.KustoQuerySkills;
using R9MigrationHelper.Skills.LLMSkills;
using R9MigrationHelper.Skills.StringParseSkills;

namespace R9MigrationHelper
{
    /// <summary>
    /// This is a console app that uses SK with LLM's API.
    /// </summary>
    public static class Program
    {
        //private const string ClientId = "e9f5cd8a-622c-4957-a1df-5a4d271e2f52"; // https://ms.portal.azure.com/#view/Microsoft_AAD_RegisteredApps/ApplicationMenuBlade/~/Overview/appId/e9f5cd8a-622c-4957-a1df-5a4d271e2f52/isMSAApp~/false
        //private const string TenantId = "72f988bf-86f1-41af-91ab-2d7cd011db47"; // This is the Microsoft Tenant
        //private const string Scope = "api://e9f5cd8a-622c-4957-a1df-5a4d271e2f52/access";

        // Prod LLM from https://msasg.visualstudio.com/QAS/_wiki/wikis/QAS.wiki/134728/Getting-Started-with-Substrate-LLM-API?anchor=endpoints
        //private const string EndpointUrl = "https://fe-26.qas.bing.net/completions";

        // This GUID was created using VS's "Create Guid" command. Each use of the LLM API should create a new GUID and not re-use.
        //private static readonly Guid ScenarioGuid = new Guid("E95CF217-7975-43AC-B5B4-88EFD6DEACAD");

        /// <summary>
        /// Main function.
        /// </summary>
        public static async Task Main()
        {
            //UserInput userInput_BinPlace = new UserInput()
            //{
            //    ServiceFramework = "NetFramework",
            //    BinPlaceForPackageFilePath = "/sources/dev/common/src/BinPlaceForPackages/BinPlaceForPackages.csproj",
            //    ServiceConfigFilePath = "/sources/dev/services/src/Shadow/Service/Web.config",
            //    ServiceCsprojFilePath = "/sources/dev/services/src/Shadow/Service/Microsoft.Exchange.Shadow.csproj"
            //};
            AnalyseManifestSkill analyseManifestSkill = new ();
            string manifestFilePath = "manifest.json";
            UserInput userInput_XMLDrop = new UserInput()
            {
                ServiceName = "TokenIssuerServer",
                ServiceFramework = "net472",
                XMLDropFilePath = "/sources/dev/Security/src/SubstrateTokenIssuer/TokenIssuerServer/XmlDrop.xml",
                ServiceConfigFilePath = "/sources/dev/Security/src/SubstrateTokenIssuer/TokenIssuerServer/Microsoft.Exchange.Security.TokenIssuer.exe.config",
                ServiceCsprojFilePath = "/sources/dev/Security/src/SubstrateTokenIssuer/TokenIssuerServer/Microsoft.Exchange.Security.TokenIssuer.csproj",
                UseBinplaceDrop = false,

                WorkingPullRequestUrl = "https://o365exchange.visualstudio.com/O365%20Core/_git/Substrate/pullrequest/3073795",
                SelectedDestinations = analyseManifestSkill.GetXmlFullDestinations(manifestFilePath),
                SelectedTopPackages = new List<string>() { "System.Diagnostics.DiagnosticSource" }
            };
            
            UserInput.AppKey = "AppKey";

            //userInput_XMLDrop.AppendTopPackages(new List<string>() { "OpenTelemetry.Instrumentation.Wcf", "OpenTelemetry.Instrumentation.GrpcNetClient" });
            var dllList = await AssembleSkillsXMLDrop(userInput_XMLDrop).ConfigureAwait(true);
            await AssembleSkillsBindingRedirect(userInput_XMLDrop, dllList).ConfigureAwait(true);

            //GetPackageDependencySkill getPackageDependencySkill = new GetPackageDependencySkill();
            //ModifyBinPlaceSkill modifyBinPlaceSkill = new ModifyBinPlaceSkill();
            //string dllList = await getPackageDependencySkill.GetPackageDependencySingle("Microsoft.R9.Extensions.Logging", "8.11.0");
            //string binPlace = modifyBinPlaceSkill.ModifyBinPlace(dllList);
        }

        /// <summary>
        /// AssembleSkills for Binding Redirect.
        /// </summary>
        /// <param name="userInput">userInput.</param>
        /// <param name="dllList">dllList.</param>
        public static async Task AssembleSkillsBindingRedirect(UserInput userInput, List<AssemblyModel>? dllList = null)
        {
            Console.WriteLine("Test for non-LLM Binding Redirect solution\n");

            if (userInput == null || !userInput.Validate())
            {
                Console.WriteLine("Invalid user input");
                return;
            }

            ReadADOFileSkill readADOFileSkill = new ReadADOFileSkill();
            QueryAssemblyTable queryAssemblyTable = new QueryAssemblyTable();
            DetermineSharedBingRedirectSkill determineSharedBingRedirectSkill = new DetermineSharedBingRedirectSkill();
            GetAssemblySkill getAssemblySkill = new GetAssemblySkill();
            ModifyServiceConfigSkill modifyServiceConfigSkill = new ModifyServiceConfigSkill();
            CreatePullRequestSkill createPullRequestSkill = new CreatePullRequestSkill();
            PushToPRSkill createPullRequestToExistingSkill = new PushToPRSkill();

            string organization = userInput.Organization; // "o365exchange"
            string project = userInput.Project; // "O365 Core"
            string repository = userInput.Repository; // "Substrate"
            string alias = String.Empty;

            if (userInput.SelectedDestinations.Count != 0 && !string.IsNullOrEmpty(userInput.WorkingPullRequestUrl) && dllList != null)
            {
                var files = File.ReadAllLines("SelectedProjConfigFiles.txt");
                string sharedBindingRedirectContent = await readADOFileSkill.ReadADOFile(organization, project, repository, UserInput.SharedBindingRedirectPath).ConfigureAwait(true);
                getAssemblySkill.GetAssemblyFromSharedBindingRedirect(dllList, sharedBindingRedirectContent);
                List<FileChange> changes = new List<FileChange>();
                foreach (var file in files)
                {
                    string serviceConfigFileContent = await readADOFileSkill.ReadADOFile(organization, project, repository, file).ConfigureAwait(true);
                    string modifiedContent = modifyServiceConfigSkill.ModifyServiceConfig(serviceConfigFileContent, dllList);
                    changes.Add(new FileChange(modifiedContent, file));
                }
                string pullRequestComments = "Update binding redirect files";
                await createPullRequestToExistingSkill.PushToPRAsync(organization, project, repository, changes, pullRequestComments, userInput.WorkingPullRequestUrl).ConfigureAwait(true);
            }
            else if (userInput.SelectedDestinations.Count == 0)
            {
                if (string.IsNullOrEmpty(alias))
                {
                    alias = "haotianzhu"; // TBD
                }
                string branchName = $"refs/heads/users/{alias}/BindingRedirectCopilotUpdate{DateTime.UtcNow.Ticks}";

                Console.WriteLine("------------------------------------------");
                Console.WriteLine($"Manually defined user input:\n\nOrganization: \t{organization}\nProject: \t{project}\nRepository: \t{repository}");
                Console.WriteLine($"ServiceCsprojFilePath: \t{userInput.ServiceCsprojFilePath}\n");

                int stepCount = 1;
                stepCount = PrintStep(stepCount, "Read config file");

                //string serviceCsprojFilePathContent = await readADOFileSkill.ReadADOFile(organization, project, repository, userInput.ServiceCsprojFilePath);
                string serviceConfigFileContent = await readADOFileSkill.ReadADOFile(organization, project, repository, userInput.ServiceConfigFilePath).ConfigureAwait(true);

                //stepCount = PrintStep(stepCount, "Get Assembly from csproj file");
                //HashSet<string> packageReference = getAssemblySkill.GetPackageList(serviceCsprojFilePathContent);
                //Console.WriteLine($"{packageReference.Count} packages in total");
                //Console.ForegroundColor = ConsoleColor.DarkYellow;
                //foreach (string pkg in packageReference)
                //{
                //    Console.WriteLine(pkg);
                //}
                //Console.ResetColor();

                //stepCount = PrintStep(stepCount, "Query Assembly Table");
                //List<AssemblyModel> assemblyModels = await queryAssemblyTable.GetAssemblyList(packageReference);
                stepCount = PrintStep(stepCount, "Query to get all Assembly ");

                List<AssemblyModel> assemblyModels = await queryAssemblyTable.GetAllAssemblyList().ConfigureAwait(true);

                Console.WriteLine($"{assemblyModels.Count} assemblies in total");

                stepCount = PrintStep(stepCount, "Determine if use Shared Bing Redirect");
                if (determineSharedBingRedirectSkill.DetermineSharedBingRedirect(serviceConfigFileContent))
                {
                    Console.WriteLine("This Service uses Shared Bing Redirect");
                    return;
                }
                else { Console.WriteLine("This Service does NOT use Shared Bing Redirect"); }

                if (assemblyModels.Count == 0)
                {
                    Console.WriteLine("No assembly needs to be modified");
                    return;
                }

                Console.WriteLine($"{assemblyModels.Count} Assemblies filtered to add");
                Console.ForegroundColor = ConsoleColor.DarkYellow;
                foreach (AssemblyModel assemblyModel in assemblyModels) { Console.WriteLine($"{assemblyModel.Name}\t{assemblyModel.PublicKeyToken}\t{assemblyModel.Version}\t0.0.0.0-{assemblyModel.OldVersion}"); }
                Console.ResetColor();

                stepCount = PrintStep(stepCount, "Modify Service Config");
                string updatedServiceConfigFileContent = modifyServiceConfigSkill.ModifyServiceConfig(serviceConfigFileContent, assemblyModels);

                //Console.WriteLine("--TBD: have not push to the same branch yet----------------------------------------");
                _ = PrintStep(stepCount, "Push to Bingding Redirect PR");

                string pullRequestTitle = "[R9MigrationHelper] Binding Redirect For Service";
                string pullRequestDescription = "Update binding redirect files";
                string pullRequestComments = $"Update {userInput.ServiceConfigFilePath} for service";
                string pullRequestUrl = await createPullRequestSkill.CreatePullRequestAsync(organization, project, repository, updatedServiceConfigFileContent, userInput.ServiceConfigFilePath, branchName, pullRequestTitle, pullRequestDescription, pullRequestComments, true).ConfigureAwait(true);
                Console.WriteLine($"Successfully created pull request at {pullRequestUrl}");
            }
        }

        /// <summary>
        /// AssembleSkills for XMLDrop.
        /// </summary>
        /// <param name="userInput">userInput.</param>
        public static async Task<List<AssemblyModel>> AssembleSkillsXMLDrop(UserInput userInput)
        {
            Console.WriteLine("Test for non-LLM solution\n");

            if (userInput == null || !userInput.Validate())
            {
                throw new Exception("Invalid user input!");
            }

            DetermineFrameworkSkill determineFrameworkSkill = new DetermineFrameworkSkill();
            ReadADOFileSkill readADOFileSkill = new ReadADOFileSkill();
            DetermineBinPlaceSkill determineBinPlaceSkill = new DetermineBinPlaceSkill();
            QueryAssemblyList queryAssemblyList = new QueryAssemblyList();
            QueryDependencyTable queryDependencyTable = new QueryDependencyTable();
            ChooseSourcePath chooseSourcePath = new ChooseSourcePath();
            XMLDropParseSkill xMLDropParseSkill = new XMLDropParseSkill();
            ModifyXMLSkill modifyXMLSkill = new ModifyXMLSkill();
            CreatePullRequestSkill createPullRequestSkill = new CreatePullRequestSkill();
            SearchCodeSkill searchCodeSkill = new SearchCodeSkill("O365Exchange", "O365 Core", new List<string>() { "Substrate" }, new List<string>() { "master" }, new List<string>() { "/" });
            AddReferenceSkill addReferenceSkill = new AddReferenceSkill();

            ModifyProjectFileSkill modifyProjectFileSkill = new ModifyProjectFileSkill();
            PushToPRSkill createPullRequestToExistingSkill = new PushToPRSkill();

            bool destinationOnlyMode = userInput.SelectedDestinations.Count > 0;
            string organization = "o365exchange";
            string project = "O365 Core";
            string repository = "Substrate";

            string alias = String.Empty;
            if (string.IsNullOrEmpty(alias))
            {
                alias = "yaomingwen"; // TBD
            }
            string branchName = $"refs/heads/users/{alias}/XMLDropCopilotUpdate{DateTime.UtcNow.Ticks}";

            Console.WriteLine("------------------------------------------");
            Console.WriteLine($"Manually defined user input:\n\nOrganization: \t{organization}\nProject: \t{project}\nRepository: \t{repository}");
            Console.WriteLine($"ServiceFramework: \t{userInput.ServiceFramework}\nServiceConfigFilePath: \t{userInput.ServiceConfigFilePath}\nXMLDropFilePath: \t{userInput.XMLDropFilePath}\nBinPlaceForPaackageFilePath: \t{userInput.BinPlaceForPackageFilePath}\n");
            Console.WriteLine("------------------------------------------\n");

            int stepCount = 1;
            List<AssemblyModel> dllList = new List<AssemblyModel>();

            if (destinationOnlyMode)
            {
                stepCount = PrintStep(stepCount, "Query And Filter deduplicate Assembly List");
                List<XmlFullDestination> destinationFullPathList = userInput.SelectedDestinations;
                foreach (XmlFullDestination destination in destinationFullPathList) { Console.WriteLine($"{destination.Root} \t {destination.Destination}\n"); }
                List<AssemblyModel> dllDropList = await queryAssemblyList.GetAndFilterAssemblyList(destinationFullPathList, userInput.SelectedTopPackages).ConfigureAwait(true);
                Console.WriteLine($"\n{dllDropList.Count} DLLs after filter");
                stepCount = PrintStep(stepCount, "LLM choose source path");
                string dllListString = string.Empty;
                int count = 0;
                string binplaceProjFileContent = await readADOFileSkill.ReadADOFile(organization, project, repository, UserInput.BinplaceCsProjFilePath).ConfigureAwait(true);
                await queryAssemblyList.GetUsedSourcePath(dllDropList, binplaceProjFileContent).ConfigureAwait(true);
                List<AssemblyModel> newDllList = await chooseSourcePath.ChooseSourcePathByLLMBatch(dllDropList, userInput.ServiceFramework, 10).ConfigureAwait(true);
                foreach (AssemblyModel dll in newDllList)
                {
                    AssemblyModel new_dll = await queryAssemblyList.ChooseOtherInfoBySourcePath(dll).ConfigureAwait(true);
                    Console.WriteLine($"[{count++}/{dllDropList.Count}] DLL: {dll.Name} \t{dll.SourcePath} \t{dll.Version} \t{dll.PackageName}\t");
                    dllList.Add(new_dll);
                    dllListString += $"{new_dll.Name},{new_dll.PackageName},{new_dll.SourcePath};";
                }

                Console.WriteLine($"\n{dllList.Count} DLLs in total to add");
                if (dllList.Count == 0) { throw new Exception("No DLL to drop!"); }

                stepCount = PrintStep(stepCount, "Modify XmlDrop.xml");
                Console.WriteLine($"DLLListString: {dllListString}\n");

                string independentXMLDropFileContent = await readADOFileSkill.ReadADOFile(organization, project, repository, UserInput.IndependentXMLDropFilePath).ConfigureAwait(true);
                string updatedXMLDropFileContent = modifyXMLSkill.ModifyXML(independentXMLDropFileContent, dllList);

                stepCount = PrintStep(stepCount, "Create Pull Request, add package reference to XmlDrop project");
                string independentProjFileContent = await readADOFileSkill.ReadADOFile(organization, project, repository, UserInput.IndependentCsProjFilePath).ConfigureAwait(true);
                List<string> packageList = dllList.Select(x => x.PackageName).ToList();
                string modifiedProjectFile = modifyProjectFileSkill.AddPackageReference(independentProjFileContent, packageList);
                string pullRequestUrl = string.Empty;
                string pullRequestComments = string.Empty;
                if (string.IsNullOrEmpty(userInput.WorkingPullRequestUrl))
                {
                    string pullRequestTitle = $"[R9MigrationHelper] XML Drop For Service {userInput.ServiceName}";
                    string pullRequestDescription = "Add reference to Substrate";
                    pullRequestComments = $"Update {UserInput.IndependentCsProjFilePath} for service";
                    pullRequestUrl = await createPullRequestSkill.CreatePullRequestAsync(organization, project, repository, modifiedProjectFile, UserInput.IndependentCsProjFilePath, branchName, pullRequestTitle, pullRequestDescription, pullRequestComments, true).ConfigureAwait(true);
                    Console.WriteLine($"Successfully created pull request at {pullRequestUrl}");
                }
                else
                {
                    pullRequestComments = "Update XMLDrop file content";
                    pullRequestUrl = userInput.WorkingPullRequestUrl;
                    await createPullRequestToExistingSkill.PushToPRAsync(organization, project, repository, modifiedProjectFile, UserInput.IndependentCsProjFilePath, pullRequestComments, pullRequestUrl).ConfigureAwait(true);
                    Console.WriteLine($"Using existing pull request at {pullRequestUrl}");
                }

                stepCount = PrintStep(stepCount, "Commit XmlDrop and csproj changes");
                var changes = new List<FileChange>() { new FileChange(UserInput.IndependentXMLDropFilePath, updatedXMLDropFileContent) };
                var searchText = "((\"Microsoft.Exchange.Data.Directory\" OR \"Microsoft.Exchange.Data.StoreObjects\" OR \"Microsoft.Exchange.OAuth.UserMode\") NOT System.Diagnostics.DiagnosticSource) ext:csproj";
                var csprojFiles = await searchCodeSkill.ParseCsprojFiles(searchText).ConfigureAwait(false);
                Console.WriteLine("Finish searching matched csproj files");
                csprojFiles = csprojFiles.Where(f => f.Style != Style.SDKSTYLE)
                    .Where(f => f.RelatedReferences.Count > 0).ToList();
                Console.WriteLine("Number of matched non-sdk-style csproj files: " + csprojFiles.Count);
                if (csprojFiles.Count == 0)
                {
                    Console.WriteLine("There is no csproj file need to add reference.");
                }
                else
                {
                    foreach (var csprojFile in csprojFiles)
                    {
                        if (csprojFile.UseScriptSharp)
                        {
                            addReferenceSkill.AddScriptSharpReference(csprojFile, @"$(PkgSystem_Diagnostics_DiagnosticSource)\lib\net462\System.Diagnostics.DiagnosticSource.dll");
                            addReferenceSkill.AddScriptSharpReference(csprojFile, @"$(PkgSystem_Runtime_CompilerServices_Unsafe)\lib\netstandard2.0\System.Runtime.CompilerServices.Unsafe.dll");
                        }
                        else
                        {
                            if (csprojFile.Style == Style.SDKSTYLE)
                            {
                                addReferenceSkill.AddPackageReference(csprojFile, "System.Diagnostics.DiagnosticSource");
                            }
                            else
                            {
                                addReferenceSkill.AddReference(csprojFile, @"$(PkgSystem_Diagnostics_DiagnosticSource)\lib\net462\System.Diagnostics.DiagnosticSource.dll");
                                addReferenceSkill.AddReference(csprojFile, @"$(PkgSystem_Runtime_CompilerServices_Unsafe)\lib\netstandard2.0\System.Runtime.CompilerServices.Unsafe.dll");
                            }
                        }
                        var content = csprojFile.ToString();
                        changes.Add(new FileChange(content, csprojFile.Path));
                    }
                    Console.WriteLine("Finish adding reference to all matched csproj files");
                }
                pullRequestComments = "Add dependencies in csproj";
                await createPullRequestToExistingSkill.PushToPRAsync(organization, project, repository, updatedXMLDropFileContent, UserInput.IndependentXMLDropFilePath, pullRequestComments, pullRequestUrl).ConfigureAwait(true);
                Console.WriteLine($"Successfully committed XmlDrop change to {pullRequestUrl}");
            }
            else
            {
                stepCount = PrintStep(stepCount, "Read Service Config");
                string serviceConfigFileContent = await readADOFileSkill.ReadADOFile(organization, project, repository, userInput.ServiceConfigFilePath).ConfigureAwait(true);

                stepCount = PrintStep(stepCount, "Determine if BinPlace");
                bool isBinPlace = userInput.UseBinplaceDrop || determineBinPlaceSkill.DetermineBinPlace(serviceConfigFileContent);
                bool isNetCore = determineFrameworkSkill.IsNetCore(userInput.ServiceFramework);
                if (isNetCore || isBinPlace)
                {
                    Console.WriteLine("This service is NetCore project or using BinPlace, no need to do XML drop");
                }
                else
                {
                    Console.WriteLine("Not BinPlace,  go to XML Drop path!");

                    stepCount = PrintStep(stepCount, "Read XmlDrop.xml");
                    string xMLDropFileContent = await readADOFileSkill.ReadADOFile(organization, project, repository, userInput.XMLDropFilePath).ConfigureAwait(true);

                    stepCount = PrintStep(stepCount, "Parse XmlDrop.xml");

                    List<XmlFullDestination> destinationFullPathList = xMLDropParseSkill.GetFullDestinationPath(xMLDropFileContent);
                    Console.WriteLine($"destinationFullPathList: \n");
                    foreach (XmlFullDestination destination in destinationFullPathList) { Console.WriteLine($"{destination.Root} \t {destination.Destination}\n"); }

                    stepCount = PrintStep(stepCount, "Query And Filter deduplicate Assembly List");
                    List<AssemblyModel> dllDropList = await queryAssemblyList.GetAndFilterAssemblyList(destinationFullPathList, userInput.SelectedTopPackages).ConfigureAwait(true);
                    Console.WriteLine($"\n{dllDropList.Count} DLLs after filter");

                    stepCount = PrintStep(stepCount, "LLM choose source path");
                    string dllListString = string.Empty;
                    int count = 0;

                    //foreach (AssemblyModel dll in dllDropList)
                    //{
                    //    //AssemblyModel new_dll = await queryDependencyTable.GetAssemblyDataAsync(dll, userInput.ServiceFramework);
                    //    //AssemblyModel new_dll = await queryDependencyTable.GetAssemblyDataAsync(dll);
                    //    //Console.WriteLine($"[{count++}/{dllDropList.Count}] DLL: {new_dll.Name} \t{new_dll.Version} \t{new_dll.PackageName}\t");
                    //    Console.WriteLine($"[{count++}/{dllDropList.Count}] DLL: {dll.Name} \t{dll.Version} \t{dll.PackageName}\t");

                    //    AssemblyModel new_dll = await chooseSourcePath.ChooseSourcePathByLLM(dll, userInput.ServiceFramework);
                    //    new_dll = await queryAssemblyList.ChooseOtherInfoBySourcePath(new_dll);

                    //    dllList.Add(new_dll);
                    //    dllListString += $"{new_dll.Name},{new_dll.PackageName},{new_dll.SourcePath};";
                    //}
                    string binplaceProjFileContent = await readADOFileSkill.ReadADOFile(organization, project, repository, UserInput.BinplaceCsProjFilePath).ConfigureAwait(true);
                    await queryAssemblyList.GetUsedSourcePath(dllDropList, binplaceProjFileContent).ConfigureAwait(true);
                    List<AssemblyModel> newDllList = await chooseSourcePath.ChooseSourcePathByLLMBatch(dllDropList, userInput.ServiceFramework, 10).ConfigureAwait(true);
                    foreach (AssemblyModel dll in newDllList)
                    {
                        AssemblyModel new_dll = await queryAssemblyList.ChooseOtherInfoBySourcePath(dll).ConfigureAwait(true);
                        Console.WriteLine($"[{count++}/{dllDropList.Count}] DLL: {dll.Name} \t{dll.SourcePath} \t{dll.Version} \t{dll.PackageName}\t");
                        dllList.Add(new_dll);
                        dllListString += $"{new_dll.Name},{new_dll.PackageName},{new_dll.SourcePath};";
                    }

                    Console.WriteLine($"\n{dllList.Count} DLLs in total to add");
                    if (dllList.Count == 0) { throw new Exception("No DLL to drop!"); }

                    stepCount = PrintStep(stepCount, "Modify XmlDrop.xml");
                    Console.WriteLine($"DLLListString: {dllListString}\n");

                    //string updatedXMLDropFileContent = modifyXMLSkill.ModifyXML(xMLDropFileContent, dllList);
                    string independentXMLDropFileContent = await readADOFileSkill.ReadADOFile(organization, project, repository, UserInput.IndependentXMLDropFilePath).ConfigureAwait(true);
                    string updatedXMLDropFileContent = modifyXMLSkill.ModifyXML(independentXMLDropFileContent, dllList);

                    stepCount = PrintStep(stepCount, "Create Pull Request, add package reference to XmlDrop project");
                    string independentProjFileContent = await readADOFileSkill.ReadADOFile(organization, project, repository, UserInput.IndependentCsProjFilePath).ConfigureAwait(true);
                    List<string> packageList = await queryAssemblyList.GetAllPackageList().ConfigureAwait(true);
                    string modifiedProjectFile = modifyProjectFileSkill.AddPackageReference(independentProjFileContent, packageList);

                    string pullRequestTitle = $"[R9MigrationHelper] XML Drop For Service {userInput.ServiceName}";
                    string pullRequestDescription = "Update XMLDrop file content";
                    string pullRequestComments = $"Update {UserInput.IndependentCsProjFilePath} for service";
                    string pullRequestUrl = await createPullRequestSkill.CreatePullRequestAsync(organization, project, repository, modifiedProjectFile, UserInput.IndependentCsProjFilePath, branchName, pullRequestTitle, pullRequestDescription, pullRequestComments, true).ConfigureAwait(true);
                    Console.WriteLine($"Successfully created pull request at {pullRequestUrl}");

                    stepCount = PrintStep(stepCount, "Commit XmlDrop change");
                    Dictionary<string, string> updatedPathToContent = new Dictionary<string, string>() { { UserInput.IndependentXMLDropFilePath, updatedXMLDropFileContent } };
                    await createPullRequestToExistingSkill.PushToPRAsync(organization, project, repository, updatedXMLDropFileContent, UserInput.IndependentXMLDropFilePath, pullRequestComments, pullRequestUrl).ConfigureAwait(true);
                    Console.WriteLine($"Successfully committed XmlDrop change to {pullRequestUrl}");
                }
            }
            return dllList;
        }

        private static int PrintStep(int stepCount, string stepName)
        {
            Console.ForegroundColor = ConsoleColor.Green;
            Console.WriteLine($"\n==Step {stepCount}==================================\n{stepName}");
            Console.ResetColor();
            return stepCount + 1;
        }

        private static string ToStringWithDeclaration(this XDocument doc)
        {
            if (doc == null)
            {
                throw new ArgumentNullException("doc");
            }

            StringBuilder builder = new StringBuilder();
            using (TextWriter writer = new Utf8Writer(builder))
            {
                doc.Save(writer);
            }
            return builder.ToString();
        }

        private sealed class Utf8Writer : StringWriter
        {
#pragma warning disable CA1305 // Specify IFormatProvider
            public Utf8Writer(StringBuilder sb) : base(sb)
            {
            }
#pragma warning restore CA1305 // Specify IFormatProvider

            public override Encoding Encoding => Encoding.UTF8;
        }
    }
}