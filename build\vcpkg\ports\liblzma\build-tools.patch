--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -162,7 +162,8 @@ endif()
 #
 # Avoid set(CMAKE_C_STANDARD_REQUIRED ON) because it's fine to decay
 # to C99 if C11 isn't supported.
-set(CMAKE_C_STANDARD 11)
+# We use c20, conflict with c11.
+#set(CMAKE_C_STANDARD 20)
 
 # Support 32-bit x86 assembly files.
 if(NOT MSVC)
@@ -1562,7 +1563,7 @@ function(my_install_man COMPONENT SRC_FILE LINK_NAMES)
     endif()
 endfunction()
 
-
+if(BUILD_TOOLS)
 #############################################################################
 # libgnu (getopt_long)
 #############################################################################
@@ -2071,6 +2072,7 @@ if(UNIX)
     my_install_man(scripts_Documentation src/scripts/xzless.1 "${XZLESS_LINKS}")
 endif()
 
+endif()
 
 #############################################################################
 # Documentation
