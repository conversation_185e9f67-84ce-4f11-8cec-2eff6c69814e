﻿// <copyright file="LogTests.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System.Diagnostics.Tracing;
using System.Linq;
using Microsoft.Extensions.Logging;
using Microsoft.R9.Extensions.Telemetry.Exporters.Base;
using Xunit;

namespace Microsoft.R9.Extensions.Telemetry.Exporters.ODL.Test
{
    public class LogTests
    {
        [Fact]
        public void LogInternalError_CatchExceptionAndLogCrital()
        {
            using var listener = new TestEventListener();
            listener.EnableEvents(ExporterLogger.Log, EventLevel.Verbose, EventKeywords.All);

            listener.ClearMessages();
            var exception = Record.Exception(() =>
            {
                ServiceDependentLogger logger = new ServiceDependentLogger(true);
                logger.Log(LogLevel.Error, string.Empty, "test", "test message", null);
                logger.Log(LogLevel.Debug, string.Empty, "test", "test message", null);
                logger.Log(LogLevel.Information, string.Empty, "test", "test message", null);
                logger.Log(LogLevel.Warning, string.Empty, "test", "test message", null);
                logger.Log(LogLevel.Critical, string.Empty, "test", "test message", null);
            });

            EventWrittenEventArgs actualEvent = listener.Messages.Last();
            Assert.Equal("CriticalLogs", actualEvent.EventName);
            Assert.Null(exception);
            listener.ClearMessages();
        }

        [Fact]
        public void Dispose_CoverDispose()
        {
            ServiceDependentLogger logger = new ServiceDependentLogger(true);
            logger.Log(LogLevel.Error, "testEvr", "test", "test message", null);
            logger.Dispose();
        }
    }
}
