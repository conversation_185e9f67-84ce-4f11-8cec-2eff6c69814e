﻿// <copyright file="PoolFactory.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System.Text;
using Microsoft.Extensions.ObjectPool;

namespace Microsoft.M365.Core.Telemetry.FileExporter
{
    /// <summary>
    /// A factory of object pools.
    /// </summary>
    /// <remarks>
    /// This class makes it easy to create efficient object pools used to improve performance by reducing
    /// strain on the garbage collector.
    /// </remarks>
    internal class StringBuilderPoolFactory
    {
        private const int DefaultCapacity = 1024;
        private const int InitialStringBuilderCapacity = 128;

        private static readonly IPooledObjectPolicy<StringBuilder> defaultStringBuilderPolicy = new StringBuilderPooledObjectPolicy
        {
            InitialCapacity = InitialStringBuilderCapacity,
            MaximumRetainedCapacity = DefaultCapacity
        };

        /// <summary>
        /// Creates a pool of <see cref="StringBuilder"/> instances.
        /// </summary>
        /// <param name="maxCapacity">The maximum number of items to keep in the pool. This defaults to 1024. This value is a recommendation, the pool may keep more objects than this.</param>
        /// <returns>The pool.</returns>
        public static ObjectPool<StringBuilder> CreateStringBuilderPool()
        {
            return MakePool(defaultStringBuilderPolicy, DefaultCapacity);
        }

        private static DefaultObjectPool<T> MakePool<T>(IPooledObjectPolicy<T> policy, int maxRetained)
        where T : class
        => new (policy, maxRetained);
    }
}
