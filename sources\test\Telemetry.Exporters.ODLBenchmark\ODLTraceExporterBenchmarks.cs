﻿// <copyright file="ODLTraceExporterBenchmarks.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System.Collections.Generic;
using System.Diagnostics;
using BenchmarkDotNet.Attributes;
using Microsoft.Extensions.Options;
using Microsoft.R9.Extensions.Tracing.Exporters;
using OpenTelemetry;
using OpenTelemetry.Trace;

namespace Microsoft.M365.Core.Telemetry.Exporters.ODL.Benchmark
{
    public class ODLTraceExporterBenchmarks
    {
        private readonly Activity activity;
        private readonly Batch<Activity> batch;
        private readonly ODLTraceExporter exporter;
        private readonly ActivitySource activitySource = new ActivitySource("OpenTelemetry.Exporter.ODL.Benchmark");

 //       |            Method |     Mean |     Error |    StdDev |
 //       |------------------ |---------:|----------:|----------:|
 //       |    ExportActivity | 1.707 us | 0.0153 us | 0.0143 us |
 //       |SerializeActivity  | 1.452 us | 0.0101 us | 0.0084 us |

        public ODLTraceExporterBenchmarks()
        {
            Activity.DefaultIdFormat = ActivityIdFormat.W3C;

            this.batch = this.CreateBatch();

            using var activityListener = new ActivityListener
            {
                ActivityStarted = null,
                ActivityStopped = null,
                ShouldListenTo = (activitySource) => activitySource.Name == this.activitySource.Name,
                Sample = (ref ActivityCreationOptions<ActivityContext> options) => ActivitySamplingResult.AllDataAndRecorded,
            };

            ActivitySource.AddActivityListener(activityListener);

            using (var testActivity = this.activitySource.StartActivity("Benchmark"))
            {
                this.activity = testActivity;
                this.activity?.SetTag("tagString", "value");
                this.activity?.SetTag("tagInt", 100);
                this.activity?.SetStatus(Status.Error);
            }

            this.exporter = new ODLTraceExporter(Options.Create(new ODLTraceExporterOptions()
            {
                EnableFallBack = false,
                PrepopulatedFields = new Dictionary<string, string>
                {
                    ["cloud.role"] = "BusyWorker",
                    ["cloud.roleInstance"] = "CY1SCH030021417",
                    ["cloud.roleVer"] = "9.0.15289.2",
                },
            }));
        }

        [Benchmark]
        public void ExportActivity()
        {
            this.exporter.Export(this.batch);
        }

        [Benchmark]
        public void SerializeActivity()
        {
            this.exporter.SerializeActivity(this.activity);
        }

        [GlobalCleanup]
        public void Cleanup()
        {
            this.activity.Dispose();
            this.activitySource.Dispose();
            this.batch.Dispose();
            this.exporter.Dispose();
        }

        private Batch<Activity> CreateBatch()
        {
            using var batchGeneratorExporter = new BatchGeneratorExporter();
            using var tracerProvider = Sdk.CreateTracerProviderBuilder()
                .SetSampler(new AlwaysOnSampler())
                .AddSource(this.activitySource.Name)
                .AddProcessor(new SimpleActivityExportProcessor(batchGeneratorExporter))
                .Build();

            using (var activity = this.activitySource.StartActivity("Benchmark"))
            {
                activity.SetTag("tagString", "value");
                activity.SetTag("tagInt", 100);
                activity.SetStatus(Status.Error);
            }

            return batchGeneratorExporter.Batch;
        }

        private class BatchGeneratorExporter : BaseExporter<Activity>
        {
            public Batch<Activity> Batch { get; set; }

            public override ExportResult Export(in Batch<Activity> batch)
            {
                this.Batch = batch;
                return ExportResult.Success;
            }
        }
    }
}
