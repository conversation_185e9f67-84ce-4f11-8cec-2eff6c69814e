﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Microsoft.M365.Core.Telemetry.FileExporter;
using Microsoft.R9.Extensions.SecurityTelemetry;

namespace Microsoft.M365.Core.Telemetry.ODL.SecurityTelemetryTcpExporter
{
    /// <summary>
    /// Extensions for the <see cref="SecurityTelemetryTcpExporter"/> class.
    /// </summary>
    public static class SecurityTelemetryTcpExporterExtensions
    {
        /// <summary>
        /// Create a <see cref="SecurityTelemetryTcpExporter"/> instance for SecurityRecord
        /// </summary>
        /// <param name="builder"></param>
        /// <returns></returns>
        public static ISecurityTelemetryPipelineBuilder AddSecurityTelemetryTcpExporter(this ISecurityTelemetryPipelineBuilder builder)
        {
            _ = builder ?? throw new ArgumentNullException(nameof(builder));
            return builder.WithExporter<SecurityTelemetryExporter>();
        }
    }
}
