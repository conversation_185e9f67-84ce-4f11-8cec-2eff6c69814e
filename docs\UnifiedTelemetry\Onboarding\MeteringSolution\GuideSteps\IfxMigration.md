# IFx Metric Migration

This page is to help users migrating from [IFx Metric](https://eng.ms/docs/products/geneva/collect/references/ifxref/ifxmetrics) to our R9 Metering Solution.

We will demonstrate several usages, show the migrated code and explain the differiences.

> [!Note]
> This page is only for [Instrumentation](./Instrumentation.md) part.
> R9 Telemetry needs to be initilized before using. It's the same to [Initialize R9 Telemetry](./InitR9.md).


## Basic Metrics w/o dimensions

When using our initialization method introduced in [Initialize R9 Telemetry](./InitR9.md), all R9 metrics will be enriched with some common schema, which aligns with [Passive Monitoring Common Schema](https://eng.ms/docs/products/m365-developer-platform/substrate-docs/monitoring/get-started-with-app-monitoring/passive-monitoring-of-your-app/using-the-passive-monitoring-sdk#common-schema).

# [IFx](#tab/IFx)

```c#
var metric = MeasureMetric.Create("PassiveMonitoringTest", "TestCardinality", "TestIfx");

metric.LogValue(10);
```

# [R9](#tab/R9)

```c#
var meter = new Meter("R9Meter");

var histogram = meter.CreateHistogram<long>("IfxMetric");
histogram.Record(10);
```

# [Sample Output](#tab/out)

Common Schemas of R9 Metric are mostly omitted to keep the result simple.

| MetricName | Value | DimensionsCount | Dimensions                               |
|------------|-------|-----------------|------------------------------------------|
| TestIfx    | 10    | 0               |                                          |
| TestIfx    | 10    | 0               |                                          |
| IfxMetric  | 10    | 9               | AvailabilityGroup:FooDAG; ...; IsR9:True |
| IfxMetric  | 10    | 9               | AvailabilityGroup:FooDAG; ...; IsR9:True |

---

## Metric level Customized Dimensions

IFx support defining metrics with 0~10 dimensions.
It's created by `MeasureMetric{N}D`, like `MeasureMetric2D`.

With R9 Metering, we can use [FastMetering](./Instrumentation.md#fast-metering) to define metrics with pre-set dimensions.

Same as above, all R9 metrics will be enriched with Passive Monitoring Common Schema.

# [IFx](#tab/IFx)

```c#
// Create a metric with 2 dimensions
var metric = MeasureMetric2D.Create("PassiveMonitoringTest", "TestCardinality", "TestIfx", "Color", "Name");

metric.LogValue(10, "red", "apple");
```

# [R9](#tab/R9)

```c#
public class IfxMetric(string color, string name)
{
    [TagName("Color")]
    public string MainColor { get; set; } = color;
    public string Name { get; set; } = name;
}
partial class Metric
{
    [Histogram(typeof(IfxMetric), Name = "MigratedIfx")]
    public static partial IfxHistogram CreateIfxHistogram(Meter meter);
}
var meter = new Meter("R9Meter")
var ifxCounter = Metric.CreateIfxHistogram(meter);

ifxCounter.Record(10, new IfxMetric("red", "apple"));
```

# [Sample Output](#tab/out)

Common Schemas of R9 Metric are mostly omitted to keep the result simple.

| MetricName | Value | DimensionsCount | Dimensions                            |
|------------|-------|-----------------|---------------------------------------|
| TestIfx    | 10    | 2               | Color:red; Name:apple                 |
| TestIfx    | 10    | 2               | Color:red; Name:apple                 |
| IfxMetric  | 10    | 11              | Color:red; Name:apple; ..., IsR9:true |
| IfxMetric  | 10    | 11              | Color:red; Name:apple; ..., IsR9:true |

---

## Shared Common Dimenions (Passive Monitoring Common Schema)

Passive Monitoring SDK provides some [Common Schema](https://eng.ms/docs/products/m365-developer-platform/substrate-docs/monitoring/get-started-with-app-monitoring/passive-monitoring-of-your-app/using-the-passive-monitoring-sdk#common-schema) which are automatically populated.

In our R9 Metering Solution, we have added all these dimensions as a default enricher. No other actions are needed.

# [IFx](#tab/IFx)

```c#
// Add default dimensions from Passive Monitoring SDK
IReadOnlyDictionary<string, string> defaultDimensions = PassiveConfiguration.DefaultDimensions;
DefaultConfiguration.SetDefaultDimensionNamesValues(
    ref errorContext,
    (uint)defaultDimensions.Count,
    defaultDimensions.Keys.ToArray(),
    defaultDimensions.Values.ToArray());

var metric = MeasureMetric.Create("PassiveMonitoringTest", "TestCardinality", "TestIfx", addDefaultDimension: true);

metric.LogValue(10);
```

# [R9](#tab/R9)

```c#
var meter = new Meter("R9Meter");

var histogram = meter.CreateHistogram<long>("IfxMetric");
histogram.Record(10);
```

# [Sample Output](#tab/out)

Common Schemas of R9 Metric are mostly omitted to keep the result simple.

R9 Metrics will be enriched with one more dimension `IsR9` to indicate this record is emitted from R9. The value is always `True`.

| MetricName | Value | DimensionsCount | Dimensions                               |
|------------|-------|-----------------|------------------------------------------|
| TestIfx    | 10    | 8               | AvailabilityGroup:FooDAG; ...            |
| TestIfx    | 10    | 8               | AvailabilityGroup:FooDAG; ...            |
| IfxMetric  | 10    | 9               | AvailabilityGroup:FooDAG; ...; IsR9:True |
| IfxMetric  | 10    | 9               | AvailabilityGroup:FooDAG; ...; IsR9:True |

---

## Customized Default Dimensions


# [IFx](#tab/IFx)

```c#
// Add customized default dimensions
DefaultConfiguration.SetDefaultDimensionNamesValues(
    ref errorContext,
    (uint)defaultDimensions.Count,
    ["Id"],
    ["Id-1"]);

var metric = MeasureMetric2D.Create("PassiveMonitoringTest", "TestCardinality", "TestIfx", "Color", "Name");

metric.LogValue(10, "red", "apple");
```

# [R9](#tab/R9)

```c#
public static class CommonValues
{
    public static string ValueA = "id - 1";
}
// A Base Scenario to store reused dimensions
public class IfxGlobalDimension
{
    public string Id { get; set; } = CommonValues.ValueA;
}
public class IfxMetric(string color, string name): IfxGlobalDimension
{
    public string Color { get; set; } = color;
    public string Name { get; set; } = name;
}

public partial class Metric
{
    [Histogram(typeof(IfxMetric), Name = "IfxMetric")]
    public static partial IfxHistogram CreateIfxHistogram(Meter meter);
}

var ifxHistogram = Metric.CreateIfxHistogram(meter);

ifxHistogram.Record(10, new IfxMetric("red", "apple"));

```

# [Sample Output](#tab/out)

Common Schemas of R9 Metric are mostly omitted to keep the result simple.

| MetricName | Value | DimensionsCount | Dimensions                                   |
|------------|-------|-----------------|----------------------------------------------|
| TestIfx    | 10    | 3               | Color:red; Id:id - 1; Name:apple             |
| TestIfx    | 10    | 3               | Color:red; Id:id - 1; Name:apple             |
| IfxMetric  | 10    | 12              | Color:red; Id:id - 1; Name:apple; ...; IsR9:True |
| IfxMetric  | 10    | 12              | Color:red; Id:id - 1; Name:apple; ...; IsR9:True |

---