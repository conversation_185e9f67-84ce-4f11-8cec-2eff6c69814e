<?xml version="1.0" encoding="utf-8"?>
<Project>
    <ItemGroup>
      <PackageReference Include="DotNetFxTools.Corext" GeneratePathProperty="true" PrivateAssets="All" />
    </ItemGroup>

    <Target Name="GenerateResourcesFromResx" BeforeTargets="Build">
        <!-- Runs resgen to produce resources-->
        <ItemGroup>
          <ResxFileLocation Include="Resx\**\*.resx" />
        </ItemGroup>
        <Message Text="Begin GenerateResource" Importance="high" />
        <GenerateResource Sources="@(ResxFileLocation)" SdkToolsPath="$(PkgDotNetFxTools_Corext)" OutputResources="Resx\%(RecursiveDir)\$(AssemblyName).%(Filename).$([System.String]::new('%(RecursiveDir)').TrimEnd('\')).resources">
          <Output TaskParameter="OutputResources" ItemName="Resources" />
        </GenerateResource>
        <Message Text="End GenerateResource" Importance="high" ContinueOnError="true" />
    </Target>
  <Target Name="RunsAssemblyLinker" AfterTargets="Build">
        <!-- Runs al.exe to produce satellite assemblies-->
        <Message Text="Begin AssemblyLinker" Importance="high" ContinueOnError="true" />
        <ItemGroup>
          <ResourcesSource Include="Resx\**\*.resources" />
        </ItemGroup>
        <AL EmbedResources="@(ResourcesSource)" Culture="$([System.String]::new('%(RecursiveDir)').TrimEnd('\'))" TemplateFile="$(OutDir)\$(MSBuildProjectName).dll" KeyFile="$(TrustedKey)" DelaySign="$(DelaySign)" OutputAssembly="Resx\%(RecursiveDir)\$(AssemblyName).dll">
          <Output TaskParameter="OutputAssembly" ItemName="SatelliteAssemblies" />
        </AL>
        <Message Text="End AssemblyLinker" Importance="high" ContinueOnError="true" />
  </Target>
</Project>