﻿// <copyright file="ODLTcpTraceExporterExtensions.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System;
using System.Diagnostics;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Options;
using Microsoft.M365.Core.Telemetry.Exporters.ODLTCP.Trace;
using Microsoft.R9.Extensions.Diagnostics;
using OpenTelemetry.Exporter.Filters;
using OpenTelemetry.Trace;

namespace Microsoft.R9.Extensions.Telemetry.Exporter.Filters;

/// <summary>
/// ODL Tcp NRT exporter extension.
/// </summary>
public static class ODLTcpTraceExporterExtensions
{
    /// <summary>
    /// Adds ODL TCP exporter as a configuration to the OpenTelemetry ILoggingBuilder.
    /// </summary>
    /// <param name="builder">Logging builder where the exporter will be added.</param>
    /// <param name="configure">ODL exporter extended options to be configured.</param>
    /// <param name="batchExport">determine whether to use batchLogRecordExportProcessor</param>
    /// <param name="filter">filter for exporter</param>
    /// <returns>The instance of <see cref="ILoggingBuilder"/> to chain the calls.</returns>
    public static TracerProviderBuilder AddODLTcpExporter(this TracerProviderBuilder builder, Action<ODLTcpTraceExporterOptions> configure, bool batchExport, BaseFilter<Activity> filter)
    {
        _ = Throws.IfNull(builder);
        _ = Throws.IfNull(configure);
        _ = builder.ConfigureServices(services => services.Configure(configure));
        return builder.AddTcpProcessor(batchExport, filter);
    }

    /// <summary>
    /// Adds ODL TCP exporter as a configuration to the OpenTelemetry ILoggingBuilder.
    /// </summary>
    /// <param name="builder">Logging builder where the exporter will be added.</param>
    /// <param name="configure">ODL exporter extended options to be configured.</param>
    /// <param name="batchExport">determine whether to use batchLogRecordExportProcessor</param>
    /// <param name="sampler">sampler for exporter</param>
    /// <returns>The instance of <see cref="ILoggingBuilder"/> to chain the calls.</returns>
    public static TracerProviderBuilder AddODLTcpExporter(this TracerProviderBuilder builder, Action<ODLTcpTraceExporterOptions> configure, bool batchExport, Sampler sampler)
    {
        _ = Throws.IfNull(builder);
        _ = Throws.IfNull(configure);
        _ = builder.ConfigureServices(services => services.Configure(configure));
        return builder.AddTcpProcessor(batchExport, new SamplerFilter(sampler));
    }

    /// <summary>
    /// Extension method to add ODL tcp exporter.
    /// </summary>
    /// <param name="builder">Logging builder where the exporter will be added.</param>
    /// <param name="section">Configuration section that contains <see cref="ODLLogExporterOptions"/>.</param>
    /// <param name="batchExport">determine whether to use batchLogRecordExportProcessor</param>
    /// <param name="filter">filter for exporter</param>
    /// <returns>The instance of <see cref="ILoggingBuilder"/>.</returns>
    /// <exception cref="ArgumentNullException">When <paramref name="builder"/> or <paramref name="section"/> is <see langword="null" />.</exception>
    public static TracerProviderBuilder AddODLTcpExporter(this TracerProviderBuilder builder, IConfigurationSection section, bool batchExport, BaseFilter<Activity> filter)
    {
        _ = Throws.IfNull(builder);
        _ = Throws.IfNull(section);
        _ = builder.ConfigureServices(services => services.Configure<ODLTcpTraceExporterOptions>(section));
        return builder.AddTcpProcessor(batchExport, filter);
    }

    /// <summary>
    /// Extension method to add ODL tcp exporter.
    /// </summary>
    /// <param name="builder">Logging builder where the exporter will be added.</param>
    /// <param name="section">Configuration section that contains <see cref="ODLLogExporterOptions"/>.</param>
    /// <param name="batchExport">determine whether to use batchLogRecordExportProcessor</param>
    /// <param name="sampler">filter for exporter</param>
    /// <returns>The instance of <see cref="ILoggingBuilder"/>.</returns>
    /// <exception cref="ArgumentNullException">When <paramref name="builder"/> or <paramref name="section"/> is <see langword="null" />.</exception>
    public static TracerProviderBuilder AddODLTcpExporter(this TracerProviderBuilder builder, IConfigurationSection section, bool batchExport, Sampler sampler)
    {
        _ = Throws.IfNull(builder);
        _ = Throws.IfNull(section);
        _ = builder.ConfigureServices(services => services.Configure<ODLTcpTraceExporterOptions>(section));
        return builder.AddTcpProcessor(batchExport, new SamplerFilter(sampler));
    }

    /// <summary>
    /// Extension method to add processor.
    /// </summary>
    /// <param name="builder"></param>
    /// <param name="batchExport"></param>
    /// <param name="filter"></param>
    /// <returns>The instance of <see cref=">TracerProviderBuilder"/></returns>
    internal static TracerProviderBuilder AddTcpProcessor(this TracerProviderBuilder builder, bool batchExport, BaseFilter<Activity> filter)
    {
        _ = builder.AddProcessor(sp =>
        {
            var options = sp.GetRequiredService<IOptions<ODLTcpTraceExporterOptions>>();
            if (batchExport)
            {
                return new BatchActivityExportProcessorWithFilter(new ODLTcpTraceExporter(options), filter, options.Value.MaxQueueSize, options.Value.ScheduledDelayMilliseconds, options.Value.ExporterTimeoutMilliseconds, options.Value.MaxExportBatchSize);
            }
            else
            {
                return new BatchActivityExportProcessorWithFilter(new ODLTcpTraceExporter(options), filter);
            }
        });
        return builder;
    }
}
