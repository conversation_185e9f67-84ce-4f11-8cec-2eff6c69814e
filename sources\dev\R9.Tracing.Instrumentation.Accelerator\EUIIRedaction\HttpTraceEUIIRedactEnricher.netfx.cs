﻿// <copyright file="HttpTraceEUIIRedactEnricher.netfx.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

#if NETFRAMEWORK
namespace Microsoft.M365.Core.Telemetry.R9.Tracing.Instrumentation.Accelerator
{
    using System;
    using System.Diagnostics;
    using System.Globalization;
    using System.Web;

    /// <summary>
    /// HttpTraceEUIIRedactEnricher
    /// </summary>
    internal class HttpTraceEUIIRedactEnricher
    {
        /// <summary>
        /// Default redaction strategy for ingress
        /// </summary>
        /// <param name="activity">Activity</param>
        /// <param name="eventName">eventName</param>
        /// <param name="rawObject">rawObject</param>
        /// <param name="redactionStrategyType">redactionStrategyType</param>
        public static void Enrich(Activity activity, string eventName, object rawObject, RedactionStrategyType redactionStrategyType)
        {
            if (activity == null)
            {
                return;
            }
            if (eventName.Equals(Constants.OnStartActivity, StringComparison.OrdinalIgnoreCase) && rawObject is HttpRequest request)
            {
                var redactedValue = EUIIRedactor.RedactIngressPath(request.Path, redactionStrategyType);
                activity.SetTag(Constants.HttpTarget, string.Format(CultureInfo.InvariantCulture, "{0}:{1}", request.Url?.Host, request.Url?.Port));
                activity.SetTag(Constants.HttpUrl, Utility.DeriveHttpUrl(request, redactedValue));
                activity.DisplayName = $"/{redactedValue}";
            }
        }
    }
}
#endif