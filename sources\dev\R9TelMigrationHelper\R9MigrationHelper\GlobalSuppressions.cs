﻿// <copyright file="GlobalSuppressions.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

// This file is used by Code Analysis to maintain SuppressMessage
// attributes that are applied to this project.
// Project-level suppressions either have no target or are given
// a specific target and scoped to a namespace, type, member, etc.
using System.Diagnostics.CodeAnalysis;

[assembly: SuppressMessage("Reliability", "CA2007:Consider calling ConfigureAwait on the awaited task", Justification = "None", Scope = "member", Target = "~M:R9MigrationHelper.Implementations.LLMClient.GetToken~System.Threading.Tasks.Task{System.String}")]
[assembly: SuppressMessage("Reliability", "CA2007:Consider calling ConfigureAwait on the awaited task", Justification = "None", Scope = "member", Target = "~M:R9MigrationHelper.Implementations.LLMClient.SendRequest(System.String,System.String)~System.Threading.Tasks.Task{System.String}")]
[assembly: SuppressMessage("Reliability", "CA2007:Consider calling ConfigureAwait on the awaited task", Justification = "None", Scope = "member", Target = "~M:R9MigrationHelper.Implementations.LLMClient.SendStreamRequest(System.String,System.String)~System.Threading.Tasks.Task{System.String}")]
[assembly: SuppressMessage("Reliability", "CA2007:Consider calling ConfigureAwait on the awaited task", Justification = "None", Scope = "member", Target = "~M:R9MigrationHelper.Skills.BindingRedirectSkills.KustoQuerySkills.QueryAssemblyTable.GetAllAssemblyList~System.Threading.Tasks.Task{System.Collections.Generic.List{R9MigrationHelper.Model.AssemblyModel}}")]
[assembly: SuppressMessage("Reliability", "CA2007:Consider calling ConfigureAwait on the awaited task", Justification = "None", Scope = "member", Target = "~M:R9MigrationHelper.Skills.BindingRedirectSkills.KustoQuerySkills.QueryAssemblyTable.GetAssemblyList(System.Collections.Generic.HashSet{System.String})~System.Threading.Tasks.Task{System.Collections.Generic.List{R9MigrationHelper.Model.AssemblyModel}}")]
[assembly: SuppressMessage("Reliability", "CA2007:Consider calling ConfigureAwait on the awaited task", Justification = "None", Scope = "member", Target = "~M:R9MigrationHelper.Skills.GitSkills.CreatePullRequestSkill.CreatePullRequestAsync(System.String,System.String,System.String,System.String,System.String,System.String)~System.Threading.Tasks.Task{System.String}")]
[assembly: SuppressMessage("Reliability", "CA2007:Consider calling ConfigureAwait on the awaited task", Justification = "None", Scope = "member", Target = "~M:R9MigrationHelper.Skills.GitSkills.GetAliasSkill.GetAlias~System.Threading.Tasks.Task{System.String}")]
[assembly: SuppressMessage("Reliability", "CA2007:Consider calling ConfigureAwait on the awaited task", Justification = "None", Scope = "member", Target = "~M:R9MigrationHelper.Skills.GitSkills.ReadADOFileSkill.ReadADOFile(System.String,System.String,System.String,System.String)~System.Threading.Tasks.Task{System.String}")]
[assembly: SuppressMessage("Reliability", "CA2007:Consider calling ConfigureAwait on the awaited task", Justification = "None", Scope = "member", Target = "~M:R9MigrationHelper.Skills.KustoQuerySkills.QueryAssemblyList.GetAndFilterAssemblyList(System.Collections.Generic.List{R9MigrationHelper.Model.XmlFullDestination})~System.Threading.Tasks.Task{System.Collections.Generic.List{R9MigrationHelper.Model.AssemblyModel}}")]
[assembly: SuppressMessage("Reliability", "CA2007:Consider calling ConfigureAwait on the awaited task", Justification = "None", Scope = "member", Target = "~M:R9MigrationHelper.Skills.KustoQuerySkills.QueryAssemblyList.GetAssemblyList~System.Threading.Tasks.Task{System.Collections.Generic.List{R9MigrationHelper.Model.AssemblyModel}}")]
[assembly: SuppressMessage("Reliability", "CA2007:Consider calling ConfigureAwait on the awaited task", Justification = "None", Scope = "member", Target = "~M:R9MigrationHelper.Skills.KustoQuerySkills.QueryDependencyTable.GetAssemblyDataAsync(R9MigrationHelper.Model.AssemblyModel,System.String)~System.Threading.Tasks.Task{R9MigrationHelper.Model.AssemblyModel}")]
[assembly: SuppressMessage("Reliability", "CA2007:Consider calling ConfigureAwait on the awaited task", Justification = "None", Scope = "member", Target = "~M:R9MigrationHelper.Skills.KustoQuerySkills.QueryDependencyTable.GetAssemblyDataAsync(System.String,System.String,System.String)~System.Threading.Tasks.Task{R9MigrationHelper.Model.AssemblyModel}")]
[assembly: SuppressMessage("Reliability", "CA2007:Consider calling ConfigureAwait on the awaited task", Justification = "None", Scope = "member", Target = "~M:R9MigrationHelper.Skills.KustoQuerySkills.QueryXMLDropTable.DeterminXMLDropDuplicateAsync(System.String,System.String,System.String)~System.Threading.Tasks.Task{System.Boolean}")]
[assembly: SuppressMessage("Reliability", "CA2007:Consider calling ConfigureAwait on the awaited task", Justification = "None", Scope = "member", Target = "~M:R9MigrationHelper.Skills.StringParseSkills.ModifyXMLSkill.ModifyXML(System.String,System.String,System.Collections.Generic.List{R9MigrationHelper.Model.XmlFullDestination})~System.Threading.Tasks.Task{System.String}")]
[assembly: SuppressMessage("Reliability", "CA2007:Consider calling ConfigureAwait on the awaited task", Justification = "None", Scope = "member", Target = "~M:R9MigrationHelper.Program.Main~System.Threading.Tasks.Task")]
[assembly: SuppressMessage("Reliability", "CA2007:Consider calling ConfigureAwait on the awaited task", Justification = "None", Scope = "member", Target = "~M:R9MigrationHelper.Skills.GitSkills.CreatePullRequestSkill.CreatePullRequestAsync(System.String,System.String,System.String,System.String,System.String,System.String,System.String)~System.Threading.Tasks.Task{System.String}")]
[assembly: SuppressMessage("Reliability", "CA2007:Consider calling ConfigureAwait on the awaited task", Justification = "None", Scope = "member", Target = "~M:R9MigrationHelper.Program.AssembleSkillsBindingRedirect(R9MigrationHelper.Model.UserInput)~System.Threading.Tasks.Task")]
[assembly: SuppressMessage("Reliability", "CA2007:Consider calling ConfigureAwait on the awaited task", Justification = "None", Scope = "member", Target = "~M:R9MigrationHelper.Program.AssembleSkillsXMLDrop(R9MigrationHelper.Model.UserInput)~System.Threading.Tasks.Task{System.Collections.Generic.List{R9MigrationHelper.Model.AssemblyModel}}")]
[assembly: SuppressMessage("Reliability", "CA2007:Consider calling ConfigureAwait on the awaited task", Justification = "None", Scope = "member", Target = "~M:R9MigrationHelper.Skills.KustoQuerySkills.QueryAssemblyList.GetAndFilterAssemblyList(System.Collections.Generic.List{R9MigrationHelper.Model.XmlFullDestination},System.Collections.Generic.List{System.String})~System.Threading.Tasks.Task{System.Collections.Generic.List{R9MigrationHelper.Model.AssemblyModel}}")]
[assembly: SuppressMessage("Reliability", "CA2007:Consider calling ConfigureAwait on the awaited task", Justification = "None", Scope = "member", Target = "~M:R9MigrationHelper.Implementations.OpenAIService.AzureGPTConversation(System.String,System.String,System.Int32)~System.Threading.Tasks.Task{System.String}")]
[assembly: SuppressMessage("Reliability", "CA2007:Consider calling ConfigureAwait on the awaited task", Justification = "None", Scope = "member", Target = "~M:R9MigrationHelper.Skills.KustoQuerySkills.QueryDependencyTable.GetAssemblyDataAsync(R9MigrationHelper.Model.AssemblyModel)~System.Threading.Tasks.Task{R9MigrationHelper.Model.AssemblyModel}")]
[assembly: SuppressMessage("Reliability", "CA2007:Consider calling ConfigureAwait on the awaited task", Justification = "None", Scope = "member", Target = "~M:R9MigrationHelper.Skills.KustoQuerySkills.QueryAssemblyList.ChooseOtherInfoBySourcePath(R9MigrationHelper.Model.AssemblyModel)~System.Threading.Tasks.Task{R9MigrationHelper.Model.AssemblyModel}")]
