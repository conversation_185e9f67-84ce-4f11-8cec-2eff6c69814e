// <copyright file="ODLTcpTraceExporterOptionsInherited.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using Microsoft.M365.Core.Telemetry.Exporters.ODLTCP.Trace;

namespace Microsoft.M365.Core.Telemetry.R9.Tracing.Instrumentation.Accelerator
{
    /// <summary>
    /// ODLTraceExporterOptionsInherited
    /// </summary>
    public class ODLTcpTraceExporterOptionsInherited : ODLTcpTraceExporterOptions
    {
        /// <summary>
        /// IsEnabled
        /// </summary>
        public bool IsEnabled { get; set; }

        /// <summary>
        /// BatchExport
        /// </summary>
        public bool BatchExport { get; set; } = true;
    }
}
