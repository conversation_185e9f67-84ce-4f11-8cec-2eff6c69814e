﻿// <copyright file="DimValuesPartialTest.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>
using System.Collections.Generic;
using Microsoft.M365.Core.Telemetry.Enrichment;
using Xunit;

namespace Microsoft.M365.Core.Telemetry.EnrichmentTest
{
    /// <summary>
    /// Test cases for dimensionValues
    /// </summary>
    public class DimValuesPartialTest
    {
        /// <summary>
        /// Test sip rotate
        /// </summary>
        [Fact]
        public void TestDeployRingSip()
        {
            MokcRegistryKey registryKey = new MokcRegistryKey(new Dictionary<string, object>() { { "DeployRingWithSipRotation", "SIP" }, { "DeployRing", "WW" } });
            DimensionValues.InternalSetRegistryKey(registryKey);
            Assert.Equal("SIP", DimensionValues.DeployRing);
        }

        /// <summary>
        /// Test sip rotate
        /// </summary>
        [Fact]
        public void TestDeployRingSipWithEmptyDeployRing()
        {
            MokcRegistryKey registryKey = new MokcRegistryKey(new Dictionary<string, object>() { { "DeployRingWithSipRotation", "SIP" } });
            DimensionValues.InternalSetRegistryKey(registryKey);
            Assert.Equal("SIP", DimensionValues.DeployRing);
        }

        /// <summary>
        /// Test sip rotate
        /// </summary>
        [Fact]
        public void TestDeployRingSipWithEmptySIPDeployRing()
        {
            MokcRegistryKey registryKey = new MokcRegistryKey(new Dictionary<string, object>() { { "DeployRing", "SIP" } });
            DimensionValues.InternalSetRegistryKey(registryKey);
            Assert.Equal("SIP", DimensionValues.DeployRing);
        }
    }

    /// <summary>
    /// Used to above test cases
    /// </summary>
    internal class MokcRegistryKey : IRegistryKey
    {
        /// <summary>
        /// Key value pairs to store mock values
        /// </summary>
        public Dictionary<string, object> Registries;

        /// <summary>
        /// ctor
        /// </summary>
        /// <param name="registries">input value</param>
        public MokcRegistryKey(Dictionary<string, object> registries)
        {
            this.Registries = registries;
        }

        /// <inheritdoc/>
        public object GetValue(string name, object defaultValue = null)
        {
            Registries.TryGetValue(name, out var value);
            return value;
        }

        /// <inheritdoc/>
        public IRegistryKey OpenSubKey(string subkey)
        {
            if (subkey == @"SOFTWARE\Microsoft\ExchangeServer\v15\VariantConfiguration" || subkey == @"SOFTWARE\Microsoft\ExchangeServer\v15\Setup" || subkey == @"SOFTWARE\Microsoft\ExchangeServer\V15\Diagnostics\Topology")
            {
                return this;
            }

            return null;
        }
    }
}