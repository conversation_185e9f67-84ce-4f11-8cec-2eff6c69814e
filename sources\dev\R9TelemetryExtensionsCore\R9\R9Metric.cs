﻿// <copyright file="R9Metric.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using Microsoft.M365.Core.Telemetry.ECSClient;
using Microsoft.M365.Core.Telemetry.SDKLogger;

namespace Microsoft.M365.Core.Telemetry.R9
{
    /// <summary>
    /// R9Metric class.
    /// </summary>
    /// <typeparam name="T">The type of Metric.</typeparam>
    public class R9Metric<T>
    {
        private readonly string metricName;
        private readonly string metricCategory;
        private readonly string[] customerDimensions;

        /// <summary>
        /// R9Metric
        /// </summary>
        /// <param name="name"></param>
        /// <param name="listDimensions"></param>
        public R9Metric(string name, params string[] listDimensions)
        {
            SDKLog.Info($"Create R9Metric histogram. name: {name}, type: {typeof(T).FullName} dimensions: {string.Join(",", listDimensions)}.");
            this.metricName = name;
            this.metricCategory = typeof(T).FullName;
            this.customerDimensions = listDimensions;
        }

        /// <summary>
        /// Logs a metric.
        /// </summary>
        /// <param name="data">Data to log.</param>
        /// <param name="listDimensionValues">List of dimension values to log.</param>
        public void Log(long data, params string[] listDimensionValues)
        {
            if (!R9MetricEnabled())
            {
                return;
            }

            R9Services.GetMeter<T>().CreateHistogram(metricName, customerDimensions).Record(data, listDimensionValues);
        }

        private bool R9MetricEnabled()
        {
            return (R9Services.AlwaysEnableR9 || OperatingSystemHelper.IsLinux || R9Services.ShouldSendR9Metric(typeof(T).FullName) || R9Services.GetPassiveR9Config().MetricEnabledForR9(this.metricCategory)) && !R9Services.ShouldBlockMetric(metricName, metricCategory);
        }
    }
}
