#define HAVE_ABSEIL
#include "OdlTraceExporter.h"
#include "ODLNRTMessage.pb.h"

#include <gtest/gtest.h>
#include <opentelemetry/logs/noop.h>

namespace trace    = opentelemetry::trace;
namespace nostd    = opentelemetry::nostd;
namespace sdktrace = opentelemetry::sdk::trace;
namespace common   = opentelemetry::common;
using json         = nlohmann::json;
using namespace Microsoft::M365::ODLExporter;

namespace Microsoft::M365::Exporters
{
class OdlTraceExporterTest : public ::testing::Test {
  public:
    OdlTraceExporterTest() {
        // initialization code here
        options_.host = "localhost";
        options_.port = "12345";
        options_.source = "NanoProxy";
        options_.common_dimensions = {{"commonkey", "commonvalue"}};
        expected_span_ = absl::StrCat(
            "{\"schemaVersion\":\"2.0\",\"traceId\":\"00000000000000000000000000000001\",\"spanId\":\"0000000000000002\",",
            "\"kind\":\"\",\"status\":\"Unset\",\"operationName\":\"\",\"displayName\":\"\",",
            "\"source\":{\"Name\":\"\",\"Version\":\"\"},",
            "\"duration\":\"00:00:00.1234567\",\"startTimeUtc\":\"2024-01-01T00:00:00.1234560Z\"",
            ",\"tags\":{",
            "\"tagbool\":true,\"tagdouble\":1.1,\"tagint\":1,\"tagstr\":\"value1\"},",
            "\"events\":[",
            "{\"Name\":\"Event1\",\"Timestamp\":\"2024-01-01T00:00:00.1234560Z\",\"Tags\":{",
            "\"tagbool\":true,\"tagdouble\":1.1,\"tagint\":1,\"tagstr\":\"value1\"}",
            "}],",
            "\"parentId\":\"0000000000000003\",\"commonkey\":\"commonvalue\"}");
    }

    void SetUp() {
        // code here will execute just before the test ensues
    }

    void TearDown() {
        // code here will be called just after the test completes
        // ok to through exceptions from here if need be
    }

    std::string GetSerializedSpans(const nostd::span<std::unique_ptr<opentelemetry::sdk::trace::Recordable>> &spans) {
        auto noop_logger = std::make_shared<opentelemetry::logs::NoopLogger>();
        return OdlTraceExporter::serialize_spans(spans, options_, noop_logger);
    }

    std::unique_ptr<sdktrace::Recordable> MakeRecordable() {
        const trace::TraceId trace_id(std::array<const uint8_t, trace::TraceId::kSize>(
            {0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1}));
        const trace::SpanId span_id(
            std::array<const uint8_t, trace::SpanId::kSize>({0, 0, 0, 0, 0, 0, 0, 2}));
        const trace::SpanId parent_span_id(
            std::array<const uint8_t, trace::SpanId::kSize>({0, 0, 0, 0, 0, 0, 0, 3}));
        const trace::SpanContext span_context{trace_id, span_id,
                                              trace::TraceFlags{trace::TraceFlags::kIsSampled}, true};
        auto rec_ptr = std::unique_ptr<sdktrace::Recordable>(new OdlTraceExporterRecordable());
        rec_ptr->SetIdentity(span_context, parent_span_id);
        std::time_t epoch_time = 1704067200; // epoch time for 2024-01-01T00:00:00Z
        auto start_time = std::chrono::system_clock::from_time_t(epoch_time) + std::chrono::microseconds(123456);
        common::SystemTimestamp start_timestamp(start_time);
        rec_ptr->SetStartTime(start_timestamp);
        auto duration = std::chrono::nanoseconds(123456789);
        rec_ptr->SetDuration(duration);
        rec_ptr->SetAttribute("tagstr", "value1");
        rec_ptr->SetAttribute("tagint", 1);
        rec_ptr->SetAttribute("tagbool", true);
        rec_ptr->SetAttribute("tagdouble", 1.1);
        // Long not supported. Use int.
        //rec_ptr->SetAttribute("taglong", 1L);
        rec_ptr->AddEvent("Event1", start_time, common::KeyValueIterableView<std::map<std::string, common::AttributeValue>>({
            {"tagstr", "value1"},
            {"tagint", 1},
            {"tagbool", true},
            {"tagdouble", 1.1}}));
        return rec_ptr;
    }

    ~OdlTraceExporterTest() {
        // cleanup any pending stuff, but no exceptions allowed
    }

    OdlTraceExporterOptions options_;
    std::string expected_span_;
};

// Access network. Only run locally in a stress test environment.
TEST_F(OdlTraceExporterTest, DISABLED_InitTest) {
    auto odlExporter = std::make_unique<OdlTraceExporter>(OdlTraceExporterOptions{
        "localhost",
        "9527", 
        "NanoProxy" /*logtype*/, 
        {} /*commen fields*/, 
        60000 /*Reconnect period ms*/, 
        60 /*monitor period s*/});
    odlExporter->Shutdown();
}

TEST_F(OdlTraceExporterTest, SerializeSpansSingleSpan) {

    auto recordable = MakeRecordable();

    nostd::span<std::unique_ptr<opentelemetry::sdk::trace::Recordable>> spans{&recordable, 1};
    std::string serialized = GetSerializedSpans(spans);

    EXPECT_EQ('\x01', static_cast<unsigned char>(serialized[0]));

    unsigned int reported_len = 0;
    for (int i = 1; i <= 4; ++i) {
        reported_len = (reported_len << 8) + (static_cast<unsigned char>(serialized[5 - i]));
    }
    EXPECT_EQ(serialized.size() - 5, reported_len);

    std::string serialized_message = serialized.substr(5);
    ODLNRTRequest request;
    request.ParseFromString(serialized_message);
    // Mock time to test timestamps.
    EXPECT_EQ(1, request.head().sequence());
    EXPECT_EQ(ODLNRTCommandType::ODLNRTCmd_CommonMessageBatch, request.head().commandtype());
    EXPECT_EQ(1, request.commonmessagebatchreq().messages_size());
    EXPECT_EQ(36, request.commonmessagebatchreq().messages(0).id().size());
    EXPECT_EQ(1, request.commonmessagebatchreq().messages(0).eventid());
    EXPECT_EQ("NanoProxy", request.commonmessagebatchreq().messages(0).source());
    EXPECT_EQ(ODLNRTMessageType_String, request.commonmessagebatchreq().messages(0).type());

    EXPECT_EQ(expected_span_, request.commonmessagebatchreq().messages(0).valuestring());
}

TEST_F(OdlTraceExporterTest, SerializeSpansMultipleSpans) {
    std::unique_ptr<sdktrace::Recordable> test_spans[2] = {MakeRecordable(), MakeRecordable()};
    nostd::span<std::unique_ptr<opentelemetry::sdk::trace::Recordable>> spans(test_spans, 2);
    std::string serialized = GetSerializedSpans(spans);
    std::string serialized_message = serialized.substr(5);
    ODLNRTRequest request;
    request.ParseFromString(serialized_message);
    EXPECT_EQ(2, request.commonmessagebatchreq().messages_size());
    EXPECT_EQ("NanoProxy", request.commonmessagebatchreq().messages(0).source());
    EXPECT_EQ(ODLNRTMessageType_String, request.commonmessagebatchreq().messages(0).type());
    EXPECT_EQ(expected_span_, request.commonmessagebatchreq().messages(0).valuestring());
    EXPECT_EQ("NanoProxy", request.commonmessagebatchreq().messages(1).source());
    EXPECT_EQ(ODLNRTMessageType_String, request.commonmessagebatchreq().messages(1).type());
    EXPECT_EQ(expected_span_, request.commonmessagebatchreq().messages(1).valuestring());
}

} // namespace Microsoft::M365::Exporters