﻿// <copyright file="TagsHandler.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using OpenTelemetry;

namespace Microsoft.M365.Core.Telemetry.R9.Tracing.AdvancedSampling
{
    /// <summary>
    /// The handler for dye based on activity tags.
    /// </summary>
    public class TagsHandler
    {
        /// <inheritdoc/>
        public static void Invoke(Activity activity, Tags tags)
        {
            if (tags.Sample != null && tags.Sample.Count > 0 && FindTagsInActivity(activity.Tags, tags.Sample))
            {
                Baggage.SetBaggage(Constants.SampleBaggageName, Constants.SampleBaggage);

                return;
            }

            if (tags.Drop != null && tags.Drop.Count > 0 && FindTagsInActivity(activity.Tags, tags.Drop))
            {
                Baggage.SetBaggage(Constants.SampleBaggageName, Constants.DropBaggage);
            }
        }

        /// <summary>
        /// If could find the activityTags in the sampling current activity's activityTags.
        /// </summary>
        /// <param name="activityTags"></param>
        /// <param name="optionTagList"></param>
        /// <returns></returns>
        private static bool FindTagsInActivity(IEnumerable<KeyValuePair<string, string>> activityTags, ISet<string> optionTagList)
        {
            if (activityTags == null)
            {
                return false;
            }

            bool res = activityTags.Any(acticityTag => optionTagList.Any(tag =>
            {
                var parts = tag.Split(':');
                return (parts.Length > 1 && parts[0] == acticityTag.Key && parts[1] == acticityTag.Value as string)
                || (parts.Length == 1 && parts[0] == acticityTag.Key);
            }));

            return res;
        }
    }
}
