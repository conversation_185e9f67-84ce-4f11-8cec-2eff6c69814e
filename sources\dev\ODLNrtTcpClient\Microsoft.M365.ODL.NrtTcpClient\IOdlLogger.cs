﻿// <copyright file="IOdlLogger.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

namespace Microsoft.M365.ODL.NrtTcpClient
{
    /// <summary>
    /// Specify log level
    /// </summary>
    public enum LogLevel
    {
        /// <summary>
        ///     A critical log. This indicates the most significant problem the user should know
        ///     about (e.g. service interrupt).
        /// </summary>
        Critical = 0,

        /// <summary>
        ///     An error log. This indicates a significant problem the user should know about.
        /// </summary>
        Error = 1,

        /// <summary>
        ///     A warning log. This indicates a problem that is not immediately significant,
        ///     but that may signify conditions that could cause future problems.
        /// </summary>
        Warning = 2,

        /// <summary>
        ///     An information log. This indicates a significant, successful operation.
        /// </summary>
        Information = 4
    }

    /// <summary>
    /// The event ids
    /// </summary>
    public class LogEventId
    {
        /// <summary>
        /// A Generic error code for TcpClient
        /// </summary>
        public const int TcpClientCommonError = 10000;

        /// <summary>
        /// Error for common settings
        /// </summary>
        public const int TcpClientCommonSettingError = 10001;

        /// <summary>
        /// Error for file loggers
        /// </summary>
        public const int TcpClientFileLoggerError = 10002;

        /// <summary>
        /// Error for MessageSerilizer
        /// </summary>
        public const int MessageSerilizerError = 10003;

        /// <summary>
        /// Error for tcp client monitor
        /// </summary>
        public const int TcpClientMonitorError = 10004;

        /// <summary>
        /// Error for tcp client thread
        /// </summary>
        public const int TcpClientThreadError = 10005;

        /// <summary>
        /// Error related to tcp protocols
        /// </summary>
        public const int TcpProtocolError = 10006;

        /// <summary>
        /// Errors for TcpExporter
        /// </summary>
        public const int TcpExporterError = 10007;

        /// <summary>
        /// Errors for RegistryReader
        /// </summary>
        public const int RegistryReaderError = 10008;

        /// <summary>
        /// Errors for LoggingEvent
        /// </summary>
        public const int LoggingEventError = 10009;

        /// <summary>
        /// Errors for TcpClient Tls Authentication
        /// </summary>
        public const int TCPTlsAuthenticationError = 10010;

        /// <summary>
        /// Errors for TcpClient Tls Authentication
        /// </summary>
        public const int CertManagementError = 10011;

        /// <summary>
        /// A Generic info code for TcpClient
        /// </summary>
        public const int TcpClientCommonInfo = 11000;

        /// <summary>
        /// trace ID for statistical info
        /// </summary>
        public const int TcpClientStatisticalTraceInfo = 11001;

        /// <summary>
        /// TcpClient thread connection info
        /// </summary>
        public const int TcpClientThreadInfo = 11002;

        /// <summary>
        /// Warning for TcpClient Tls Authentication
        /// </summary>
        public const int TCPTlsAuthenticationWarning = 12000;
    }

    /// <summary>
    /// the logger interface for ODL components
    /// </summary>
    public interface IOdlLogger
    {
        /// <summary>
        /// log method
        /// </summary>
        /// <param name="level">log level</param>
        /// <param name="id">log id</param>
        /// <param name="message">string message</param>
        void Log(LogLevel level, int id, string message);

        /// <summary>
        /// Implementation of writing trace logs.
        /// </summary>
        /// <param name="severity">the log severity</param>
        /// <param name="traceId">the trace ID</param>
        /// <param name="message">log content</param>
        void TraceLog(LogLevel severity, int traceId, string message);
    }
}
