﻿// <copyright file="CommonInit.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System;
using System.Diagnostics.CodeAnalysis;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.R9.Extensions.ClusterMetadata.Cosmic;
using Microsoft.R9.Extensions.Enrichment;

namespace Microsoft.M365.Core.Telemetry.Enrichment
{
    /// <summary>
    /// Because metric enricher is based on static variables, we need to initialize the
    /// same set of enrichers everywhere so that whoever gets called first doesn't matter.
    /// </summary>
    [ExcludeFromCodeCoverage]
    internal static class CommonInit
    {
        private const string Missing = "missing";

        /// <summary>
        /// InitSubstrateEnrichers
        /// </summary>
        /// <param name="services"></param>
        /// <returns></returns>
        // This must be called only in SDKLogger because IfxExtension can only init once.
        public static IServiceCollection InitSubstrateEnrichers(this IServiceCollection services)
        {
            if (string.IsNullOrEmpty(Environment.GetEnvironmentVariable("COSMIC_PODNAME")))
            {
                services.AddMetricEnricher<B2PassiveMetricEnricher>();
                services.AddLogEnricher<B2PassiveLogEnricher>();
                return services;
            }

            services.AddCosmicClusterMetadata(metadata =>
            {
                // Any attribute missing is a bug in cosmic.
                metadata.Ring = Environment.GetEnvironmentVariable("COSMIC_RING") ?? Missing;
                metadata.Geo = Environment.GetEnvironmentVariable("COSMIC_REGION") ?? Missing;
                metadata.Region = Environment.GetEnvironmentVariable("COSMIC_LOCATION") ?? Missing;
                metadata.PodName = Environment.GetEnvironmentVariable("COSMIC_PODNAME") ?? Missing;
                metadata.NodeName = Environment.GetEnvironmentVariable("COSMIC_NODENAME") ?? Missing;
                metadata.Version = Environment.GetEnvironmentVariable("COSMIC_VERSION") ?? Missing;
                metadata.Partition = Environment.GetEnvironmentVariable("COSMIC_PARTITION") ?? Missing;
                metadata.Namespace = Environment.GetEnvironmentVariable("POD_NAMESPACE") ?? Missing;
                metadata.NamespaceInstanceId = Environment.GetEnvironmentVariable("COSMIC_NS_INSTANCE_ID") ?? Missing;
                string podName = Environment.GetEnvironmentVariable("COSMIC_PODNAME");
                metadata.Deployment = ((podName != null) ? podName!.Split('_')[0] : Missing) ?? Missing;
            });

            services.AddCosmicMetricEnricher(options =>
            {
                options.Cloud = true;
                options.Deployment = true;
                options.Node = true;
                options.Partition = true;
                options.Ring = true;
            });

            services.AddCosmicLogEnricher();
            return services;
        }
    }
}
