﻿// <copyright file="GetPackageDependencySkill.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System.IO.Compression;
using System.Net.Http.Headers;
using System.Reflection;
using System.Text;
using System.Text.RegularExpressions;
using Newtonsoft.Json;
using PackageUpgradeHelper.ADO;

namespace PackageUpgradeHelper.ProjectFile
{
    /// <summary>
    /// GetPackageDependencySkill
    /// </summary>
    public class GetPackageDependencySkill
    {
        private const string PAT = Globals.PAT; // Personal Access Token

        private const string FeedId = "146d30aa-40be-4842-95c8-d0b334fe339a"; // Feed Id

        private const string DownloadRootPath = "./DownloadedPackages";

        private const string CsvRootPath = "./DependencyCsv";

        private readonly HashSet<string> foundDlls = new HashSet<string>();

        private readonly HashSet<string> packageList = new HashSet<string>();

        /// <summary>
        /// Get the dependencies for the package.
        /// </summary>
        /// <param name="rootPackageName">The package name.</param>
        /// <param name="targetVersion">The package version.</param>
        /// <returns></returns>
        public async Task<List<DllRecord>> GetPackageDependencySingle(string rootPackageName, string targetVersion)
        {
            try
            {
                List<DllRecord> result = await GetHintpathSingle(rootPackageName, targetVersion).ConfigureAwait(false);
                return result;
            }
            catch (Exception ex)
            {
                Console.WriteLine(ex);
            }
            return new List<DllRecord>();
        }

        /// <summary>
        /// GetPackageDependencyFull
        /// </summary>
        /// <param name="rootPackageName"></param>
        /// <returns></returns>
        public async Task<string> GetPackageDependencyFull(string rootPackageName)
        {
            try
            {
                if (foundDlls.Count != 0)
                {
                    foundDlls.Clear();
                }

                var csvPath = Path.Combine(CsvRootPath, $"DllInfo.csv");
                CreateCsvFile(csvPath);

                _ = await GeneratePackageList(rootPackageName).ConfigureAwait(false);
            }
            catch (Exception ex)
            {
                Console.WriteLine(ex);
            }
            return string.Empty;
        }

        /// <summary>
        /// Get the hintpath for single package.
        /// </summary>
        /// <param name="rootPackageName"></param>
        /// <param name="targetVersion"></param>
        /// <returns></returns>
        public async Task<List<DllRecord>> GetHintpathSingle(string rootPackageName, string targetVersion)
        {
            try
            {
                // Download current package.
                List<DllRecord> result = await DownloadAndAnalyze(rootPackageName, targetVersion).ConfigureAwait(false);
                return result;
            }
            catch (Exception ex)
            {
                Console.WriteLine(ex);
            }

            return new List<DllRecord>();
        }

        /// <summary>
        /// Get the packages from the feed.
        /// </summary>
        /// <param name="feedId">The feed id.</param>
        /// <param name="packageNameQuery">The package name to query.</param>
        /// <returns></returns>
        public async Task<HttpResponseMessage> GetPackages(string feedId, string packageNameQuery)
        {
            using var client = GetHttpClient();
            string organization = "o365exchange";
            var response = await client.GetAsync($"https://feeds.dev.azure.com/{organization}/_apis/packaging/Feeds/{feedId}/packages?packageNameQuery={packageNameQuery}&api-version=7.1").ConfigureAwait(true);
            return response;
        }

        /// <summary>
        /// GetVersion
        /// </summary>
        /// <param name="feedId"></param>
        /// <param name="packageId"></param>
        /// <param name="packageVersionId"></param>
        /// <returns></returns>
        public async Task<HttpResponseMessage> GetVersion(string feedId, string packageId, string packageVersionId)
        {
            using var client = GetHttpClient();
            string organization = "o365exchange";
            var response = await client.GetAsync($"https://feeds.dev.azure.com/{organization}/_apis/packaging/Feeds/{feedId}/Packages/{packageId}/versions/{packageVersionId}?api-version=7.1").ConfigureAwait(true);
            return response;
        }

        /// <summary>
        /// Get the http client.
        /// </summary>
        /// <returns></returns>
        private HttpClient GetHttpClient()
        {
            HttpClient client = new HttpClient();
            client.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));
#pragma warning disable CA1305 // Specify IFormatProvider
            client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Basic", Convert.ToBase64String(Encoding.ASCII.GetBytes(string.Format("{0}:{1}", string.Empty, PAT))));
#pragma warning restore CA1305 // Specify IFormatProvider
            return client;
        }

        /// <summary>
        /// CreateCsvFile
        /// </summary>
        /// <param name="csvPath">The csv path.</param>
        private void CreateCsvFile(string csvPath)
        {
            if (File.Exists(csvPath))
            {
                File.Delete(csvPath);
            }

            // If the directory of csvPath does not exist, create it.
            if (!Directory.Exists(CsvRootPath))
            {
                Directory.CreateDirectory(CsvRootPath);
            }

            var header = "PackageName,PackageVersion,DllName,DllVersion,LibraryDirectoryPath,RootPackages,HintPath";
            using StreamWriter sw = new StreamWriter(csvPath, true);
            sw.WriteLine(header);
        }

        /// <summary>
        /// Generate the package list to analyze.
        /// </summary>
        /// <param name="rootPackageName">The root package name.</param>
        /// <returns></returns>
        private async Task<string> GeneratePackageList(string rootPackageName)
        {
            // Get the package from the feed.
            var packages = await GetPackages(FeedId, rootPackageName).ConfigureAwait(false);
            var responseContent = await packages.Content.ReadAsStringAsync().ConfigureAwait(false);
            PackageResponse? packageResponse = JsonConvert.DeserializeObject<PackageResponse>(responseContent);

            foreach (var package in packageResponse!.Value!)
            {
                // Check if the package name matches the query.
                if (!package.Name!.Equals(rootPackageName, StringComparison.OrdinalIgnoreCase))
                {
                    continue;
                }

                if (packageList.Contains(package.Name))
                {
                    continue;
                }

                VersionInfo? latestVersion = package.Versions!.FirstOrDefault(v => v.IsLatest == true);
                Console.WriteLine($"Analyzing {package.Name}({latestVersion!.Version!})");

                try
                {
                    // Download current package.
                    _ = await DownloadAndAnalyze(package.Name, latestVersion.Version!).ConfigureAwait(false);
                }
                catch (Exception ex)
                {
                    Console.WriteLine(ex);
                }
                finally
                {
                    packageList.Add(package.Name!);
                }

                var packageVersions = await GetVersion(FeedId, package!.Id!, latestVersion!.Id!).ConfigureAwait(false);
                responseContent = await packageVersions.Content.ReadAsStringAsync().ConfigureAwait(false);
                PackageVersionResponse packageVersionResponse = JsonConvert.DeserializeObject<PackageVersionResponse>(responseContent) ?? throw new Exception($"Failed to deserialize {responseContent} into PackageVersionResponse.");
                var packageDependencies = packageVersionResponse.Dependencies;
                if (packageDependencies!.Count != 0)
                {
                    foreach (var packageDependency in packageDependencies)
                    {
                        _ = await GeneratePackageList(packageDependency.PackageName!).ConfigureAwait(false);
                    }
                }
            }
            return string.Empty;
        }

        /// <summary>
        /// DownloadAndAnalyze
        /// </summary>
        /// <param name="packageName"></param>
        /// <param name="packageVersion"></param>
        private async Task<List<DllRecord>> DownloadAndAnalyze(string packageName, string packageVersion)
        {
            try
            {
                var download = await DownloadPackage(FeedId, packageName, packageVersion).ConfigureAwait(false);
                var fileName = download.Content.Headers.ContentDisposition!.FileName!.Trim('"').Replace(".nupkg", ".zip", StringComparison.OrdinalIgnoreCase);
                byte[] packageBytes = await download.Content.ReadAsByteArrayAsync().ConfigureAwait(false);
                string downloadPath = Path.Combine(DownloadRootPath, fileName);
                if (File.Exists(downloadPath))
                {
                    File.Delete(downloadPath);
                }

                if (!Directory.Exists(DownloadRootPath))
                {
                    Directory.CreateDirectory(DownloadRootPath);
                }
                File.WriteAllBytes(downloadPath, packageBytes);
                List<DllRecord> result = AnalyzePackageDllInfoSingle(packageName, downloadPath);
                return result;
            }
            catch (Exception ex)
            {
                Console.WriteLine(ex.Message);
            }
            return new List<DllRecord>();
        }

        /// <summary>
        /// AnalyzePackageDllInfoSingl
        /// </summary>
        /// <param name="packagePath"></param>
        /// <returns></returns>
        private List<DllRecord> AnalyzePackageDllInfoSingle(string packageName, string packagePath)
        {
            string extractPath = Path.Combine(DownloadRootPath, Path.GetFileNameWithoutExtension(packagePath));
            if (Directory.Exists(extractPath))
            {
                Directory.Delete(extractPath, true);
            }
            Directory.CreateDirectory(extractPath);
            ZipFile.ExtractToDirectory(packagePath, extractPath);

            // Traverse the extracted content and find all the folders containing dll files.
            var dllFiles = Directory.GetFiles(extractPath, "*.dll", SearchOption.AllDirectories);
            StringBuilder sb = new StringBuilder();
            List<DllRecord> dllRecords = new List<DllRecord>();
            foreach (var dllFile in dllFiles)
            {
                // Get fields.
                try
                {
                    var dllName = Path.GetFileName(dllFile);
                    var libraryDirectoryPath = dllFile.Replace(extractPath, string.Empty, StringComparison.Ordinal).Replace(dllName, string.Empty, StringComparison.Ordinal).Trim('\\');
                    if (!Directory.Exists(CsvRootPath))
                    {
                        Directory.CreateDirectory(CsvRootPath);
                    }
                    var csvPath = Path.Combine(CsvRootPath, $"DllInfo.csv");

                    // Distinct.
                    string hintpath = GenerateHintpath(dllName, libraryDirectoryPath);
                    string framework =
                    libraryDirectoryPath.Contains("net461", StringComparison.OrdinalIgnoreCase)
                    || libraryDirectoryPath.Contains("net462", StringComparison.OrdinalIgnoreCase)
                    || libraryDirectoryPath.Contains("net471", StringComparison.OrdinalIgnoreCase)
                    || libraryDirectoryPath.Contains("net472", StringComparison.OrdinalIgnoreCase) ? "NetFramework" : "NetCore";
                    DllRecord dllRecord = new DllRecord
                    {
                        DllName = dllName,
                        PackageName = packageName,
                        LibraryDirectoryPath = libraryDirectoryPath,
                        HintPath = hintpath,
                        TargetFramework = framework
                    };
                    dllRecords.Add(dllRecord);
                    //Console.WriteLine($"  -  Found dll {dllFile}");
                    string csvContent = $"{dllName},{packageName},{libraryDirectoryPath},{hintpath}";
                    sb.Append(csvContent + ";");
                    using StreamWriter sw = new StreamWriter(csvPath, true);
                    sw.WriteLine(csvContent);
                }
                catch (Exception ex)
                {
                    Console.WriteLine(ex);
                }
            }

            CleanupLocalPackage(packagePath, extractPath);
            return dllRecords;
        }

        /// <summary>
        /// AnalyzePackageDllInfo
        /// </summary>
        /// <param name="packagePath"></param>
        /// <returns></returns>
        private void AnalyzePackageDllInfo(string packageName, string packageVersion, string packagePath, string rootPackageName)
        {
            string extractPath = Path.Combine(DownloadRootPath, Path.GetFileNameWithoutExtension(packagePath));
            if (Directory.Exists(extractPath))
            {
                Directory.Delete(extractPath, true);
            }
            Directory.CreateDirectory(extractPath);
            ZipFile.ExtractToDirectory(packagePath, extractPath);

            // Traverse the extracted content and find all the folders containing dll files.
            var dllFiles = Directory.GetFiles(extractPath, "*.dll", SearchOption.AllDirectories);
            foreach (var dllFile in dllFiles)
            {
                // Get fields.
                try
                {
                    var dllVersion = AssemblyName.GetAssemblyName(dllFile).Version;
                    var dllName = Path.GetFileName(dllFile);
                    var libraryDirectoryPath = dllFile.Replace(extractPath, string.Empty, StringComparison.Ordinal).Replace(dllName, string.Empty, StringComparison.Ordinal).Trim('\\');
                    var csvPath = Path.Combine(CsvRootPath, $"DllInfo.csv");

                    // Distinct.
                    string dllInfo = $"{dllName},{dllVersion},{libraryDirectoryPath}";
                    if (!foundDlls.Contains(dllInfo))
                    {
                        Console.WriteLine($"  -  Found dll {dllInfo}");
                        foundDlls.Add(dllInfo);
                        string csvContent = $"{packageName},{packageVersion},{dllInfo},{rootPackageName}";
                        using StreamWriter sw = new StreamWriter(csvPath, true);
                        sw.WriteLine(csvContent);
                    }

                    string hintpath = GenerateHintpath(dllName, libraryDirectoryPath);
                    Console.WriteLine(hintpath);
                }
                catch (Exception ex)
                {
                    Console.WriteLine(ex);
                }
            }

            CleanupLocalPackage(packagePath, extractPath);
        }

        /// <summary>
        /// GenerateHintpath
        /// </summary>
        /// <param name="dllName"></param>
        /// <param name="libraryDirectoryPath"></param>
        /// <returns></returns>
        public static string GenerateHintpath(string dllName, string libraryDirectoryPath)
        {
            Regex macroEscape = new Regex("[^a-z0-9_]", RegexOptions.IgnoreCase);
            string hintpathHeader = "$(Pkg" + macroEscape.Replace(dllName, "_").Replace("_dll", string.Empty, StringComparison.OrdinalIgnoreCase) + ")";
            return Path.Combine(hintpathHeader, libraryDirectoryPath, dllName);
        }

        private void CleanupLocalPackage(string packagePath, string extractPath)
        {
            if (File.Exists(packagePath))
            {
                File.Delete(packagePath);
            }

            if (Directory.Exists(extractPath))
            {
                Directory.Delete(extractPath, true);
            }
        }

        /// <summary>
        /// DownloadPackage
        /// </summary>
        private async Task<HttpResponseMessage> DownloadPackage(string feedId, string packageName, string packageVersion)
        {
            using var client = GetHttpClient();
            string organization = "o365exchange";
            var response = await client.GetAsync($"https://pkgs.dev.azure.com/{organization}/_apis/packaging/feeds/{feedId}/nuget/packages/{packageName}/versions/{packageVersion}/content?api-version=7.1-preview").ConfigureAwait(false);
            return response;
        }

        /// <summary>
        /// The package response class.
        /// </summary>
        internal class PackageResponse
        {
            /// <summary>
            /// The response count.
            /// </summary>
            public int Count { get; set; }

            /// <summary>
            /// The list of packages.
            /// </summary>
            public List<Package>? Value { get; set; }
        }

        /// <summary>
        /// The package class.
        /// </summary>
        internal class Package
        {
            /// <summary>
            /// The ID.
            /// </summary>
            public string? Id { get; set; }

            /// <summary>
            /// Name
            /// </summary>
            public string? Name { get; set; }

            /// <summary>
            /// Versions
            /// </summary>
            public List<VersionInfo>? Versions { get; set; }
        }

        /// <summary>
        /// VersionInfo
        /// </summary>
        internal class VersionInfo
        {
            /// <summary>
            /// Id
            /// </summary>
            public string? Id { get; set; }

            /// <summary>
            /// Version
            /// </summary>
            public string? Version { get; set; }

            /// <summary>
            /// IsLatest
            /// </summary>
            public bool? IsLatest { get; set; }
        }

        /// <summary>
        /// PackageVersionResponse
        /// </summary>
        internal class PackageVersionResponse
        {
            /// <summary>
            /// Dependencies
            /// </summary>
            public List<PackageDependency>? Dependencies { get; set; }
        }

        /// <summary>
        /// PackageDependency
        /// </summary>
        internal class PackageDependency
        {
            /// <summary>
            /// Group
            /// </summary>
            public string? Group { get; set; }

            /// <summary>
            /// PackageName
            /// </summary>
            public string? PackageName { get; set; }

            /// <summary>
            /// VersionRange
            /// </summary>
            public string? VersionRange { get; set; }
        }
    }

    /// <summary>
    /// DllRecord
    /// </summary>
    public class DllRecord
    {
        /// <summary>
        /// DllName ex. Microsoft.R9.Extensions.Metering.Geneva.dll
        /// </summary>
        public string DllName { get; set; }

        /// <summary>
        /// PackageName ex. Microsoft.R9.Extensions.Metering.Geneva
        /// </summary>
        public string PackageName { get; set; }

        /// <summary>
        /// LibraryDirectoryPath. ex. lib\net461
        /// </summary>
        public string LibraryDirectoryPath { get; set; }

        /// <summary>
        /// HintPath ex. $(PkgMicrosoft_R9_Extensions_Logging)\lib\net462\Microsoft.R9.Extensions.Logging.dll
        /// </summary>
        public string HintPath { get; set; }

        /// <summary>
        /// TargetFramework ex. NetFramework
        /// </summary>
        public string TargetFramework { get; set; }
    }
}
