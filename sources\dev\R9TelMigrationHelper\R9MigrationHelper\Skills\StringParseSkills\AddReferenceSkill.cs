﻿// <copyright file="AddReferenceSkill.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Linq;
using R9MigrationHelper.Model;

namespace R9MigrationHelper.Skills.StringParseSkills
{
    /// <summary>
    /// AddReferenceSkill
    /// </summary>
    public class AddReferenceSkill
    {
        /// <summary>
        /// Add package reference to doc, used in sdk-style csproj files
        /// </summary>
        /// <param name="csproj"></param>
        /// <param name="dllName"></param>
        public void AddPackageReference(CsprojForDrop csproj, string dllName)
        {
            var doc = csproj.Doc;
            bool exist = doc.Descendants().Any(d => d.Attributes("Include").Equals(dllName));
            if (exist)
            {
                Console.WriteLine($"{dllName} has existed in {csproj.Path}");
                return;
            }
            var referenceNodeToParent = csproj.ReferenceNodeToParent;
            XNamespace rootNamespace = doc.Root!.Name.Namespace;
            var itemGroupNodes = doc.Descendants()
                    .Where(e => e.Name.LocalName == "ItemGroup" && e.Descendants().Any(d => d.Name.LocalName == "PackageReference"))
                    .ToList();
            var targetNode = new XElement(rootNamespace + "PackageReference");
            targetNode.SetAttributeValue("Include", dllName);
            if (itemGroupNodes.Count == 0) // There is no package reference in doc
            {
                var itemGroupNode = new XElement(rootNamespace + "ItemGroup");
                itemGroupNode.Attributes().Where(a => a.Name.LocalName == "xmlns").Remove();
                doc.Root!.Add(itemGroupNode);
                itemGroupNode.Add(targetNode);
            }
            else
            {
                var anyNotConditional = itemGroupNodes.Any(g => !g.Attributes().Any(a => a.Name.LocalName == "Condition"));
                if (anyNotConditional) // Add reference to first item group which is not conditional
                {
                    var itemGroupNode = itemGroupNodes.Where(g => !g.Attributes().Any(a => a.Name.LocalName == "Condition")).First();
                    itemGroupNode.Add(targetNode);
                }
                else // Add reference to all item groups
                {
                    foreach (var itemGroupNode in itemGroupNodes)
                    {
                        itemGroupNode.Add(targetNode);
                    }
                }
            }
        }

        /// <summary>
        /// Add reference to doc, used in non-sdk-style csproj files
        /// </summary>
        /// <param name="csproj"></param>
        /// <param name="dllPath"></param>
        public void AddScriptSharpReference(CsprojForDrop csproj, string dllPath)
        {
            var doc = csproj.Doc;
            bool exist = doc.Descendants().Any(d => d.Attributes("Include").Equals(dllPath));
            if (exist)
            {
                Console.WriteLine($"{dllPath} has existed in {csproj.Path}");
                return;
            }
            var referenceNodeToParent = csproj.ReferenceNodeToParent;
            XNamespace rootNamespace = doc.Root!.Name.Namespace;
            var itemGroupNodes = doc.Descendants()
                                .Where(e => e.Name.LocalName == "ItemGroup" && e.Descendants().Any(d => d.Name.LocalName == "WsdlGeneratorDependencies"))
                                .ToList();
            var targetNode = new XElement(rootNamespace + "WsdlGeneratorDependencies");
            targetNode.SetAttributeValue("Include", dllPath);

            if (itemGroupNodes.Count == 0) // There is no package reference in doc
            {
                var itemGroupNode = new XElement(rootNamespace + "ItemGroup");
                itemGroupNode.Attributes().Where(a => a.Name.LocalName == "xmlns").Remove();
                doc.Root!.Add(itemGroupNode);
                itemGroupNode.Add(targetNode);
            }
            else
            {
                var anyNotConditional = itemGroupNodes.Any(g => !g.Attributes().Any(a => a.Name.LocalName == "Condition"));
                if (anyNotConditional) // Add reference to first item group which is not conditional
                {
                    var itemGroupNode = itemGroupNodes.Where(g => !g.Attributes().Any(a => a.Name.LocalName == "Condition")).First();
                    itemGroupNode.Add(targetNode);
                }
                else // Add reference to all item groups
                {
                    foreach (var itemGroupNode in itemGroupNodes)
                    {
                        itemGroupNode.Add(targetNode);
                    }
                }
            }
        }

        /// <summary>
        /// Add reference to doc, used in non-sdk-style csproj files
        /// </summary>
        /// <param name="csproj"></param>
        /// <param name="dllPath"></param>
        public void AddReference(CsprojForDrop csproj, string dllPath)
        {
            var doc = csproj.Doc;
            bool exist = doc.Descendants().Any(d => d.Attributes("Include").Equals(dllPath));
            if (exist)
            {
                Console.WriteLine($"{dllPath} has existed in {csproj.Path}");
                return;
            }
            var referenceNodeToParent = csproj.ReferenceNodeToParent;
            XNamespace rootNamespace = doc.Root!.Name.Namespace;
            var itemGroupNodes = doc.Descendants()
                                .Where(e => e.Name.LocalName == "ItemGroup" && e.Descendants().Any(d => d.Name.LocalName == "Reference"))
                                .ToList();
            var targetNode = new XElement(rootNamespace + "Reference");
            targetNode.SetAttributeValue("Include", dllPath);

            if (itemGroupNodes.Count == 0) // There is no package reference in doc
            {
                var itemGroupNode = new XElement(rootNamespace + "ItemGroup");
                itemGroupNode.Attributes().Where(a => a.Name.LocalName == "xmlns").Remove();
                doc.Root!.Add(itemGroupNode);
                itemGroupNode.Add(targetNode);
            }
            else
            {
                var anyNotConditional = itemGroupNodes.Any(g => !g.Attributes().Any(a => a.Name.LocalName == "Condition"));
                if (anyNotConditional) // Add reference to first item group which is not conditional
                {
                    var itemGroupNode = itemGroupNodes.Where(g => !g.Attributes().Any(a => a.Name.LocalName == "Condition")).First();
                    itemGroupNode.Add(targetNode);
                }
                else // Add reference to all item groups
                {
                    foreach (var itemGroupNode in itemGroupNodes)
                    {
                        itemGroupNode.Add(targetNode);
                    }
                }
            }
        }

        /// <summary>
        /// Add reference to doc, used in non-sdk-style csproj files
        /// </summary>
        /// <param name="csproj"></param>
        /// <param name="dllName"></param>
        /// <param name="hintPath"></param>
        public void AddReferenceWithHintPath(CsprojForDrop csproj, string dllName, string hintPath)
        {
            var doc = csproj.Doc;
            bool exist = doc.Descendants().Any(d => d.Attributes("Include").Equals(hintPath));
            if (exist)
            {
                Console.WriteLine($"{hintPath} has existed in {csproj.Path}");
                return;
            }
            var referenceNodeToParent = csproj.ReferenceNodeToParent;
            XNamespace rootNamespace = doc.Root!.Name.Namespace;
            var itemGroupNodes = doc.Descendants()
                                .Where(e => e.Name == "ItemGroup" && e.Descendants().Any(d => d.Name.LocalName == "Reference"))
                                .ToList();
            var targetNode = new XElement(rootNamespace + "Reference");
            targetNode.SetAttributeValue("Include", dllName);
            var hintPathNode = new XElement(rootNamespace + "HintPath");
            hintPathNode.Value = hintPath;
            targetNode.Add(hintPathNode);

            if (itemGroupNodes.Count == 0) // There is no package reference in doc
            {
                var itemGroupNode = new XElement(rootNamespace + "ItemGroup");
                itemGroupNode.Attributes().Where(a => a.Name.LocalName == "xmlns").Remove();
                doc.Root!.Add(itemGroupNode);
                itemGroupNode.Add(targetNode);
            }
            else
            {
                var anyNotConditional = itemGroupNodes.Any(g => !g.Attributes().Any(a => a.Name.LocalName == "Condition"));
                if (anyNotConditional) // Add reference to first item group which is not conditional
                {
                    var itemGroupNode = itemGroupNodes.Where(g => !g.Attributes().Any(a => a.Name.LocalName == "Condition")).First();
                    itemGroupNode.Add(targetNode);
                }
                else // Add reference to all item groups
                {
                    foreach (var itemGroupNode in itemGroupNodes)
                    {
                        itemGroupNode.Add(targetNode);
                    }
                }
            }
        }
    }
}
