// <copyright file="ODLLogExporterExtensionTest.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Microsoft.R9.Extensions.Logging;
using Microsoft.R9.Extensions.Logging.Exporters;
using Microsoft.R9.Extensions.Tracing.Exporters;
using Newtonsoft.Json;
using Xunit;
using Assert = Xunit.Assert;

namespace Microsoft.R9.Extensions.Telemetry.Exporters.ODL.Test
{
    public class ODLLogExporterExtensionTest
    {
        [Fact]
        public void AddODLExporter_GivenInvalidArguments_ThrowsException()
        {
            Assert.Throws<ArgumentNullException>(() =>
           ((ILoggingBuilder)null!).AddODLExporter((Action<ODLLogExporterOptions>)null!, true));

            Assert.Throws<ArgumentNullException>(() =>
           new HostBuilder().ConfigureLogging(builder => builder
                            .AddOpenTelemetryLogging()
                            .AddODLExporter((Action<ODLLogExporterOptions>)null!, true))
                        .Build());
        }

        [Fact]
        public void AddODLExporter_GivenValidOptions_RegistersRequiredServices()
        {
            using var host = new HostBuilder()
                        .ConfigureLogging(builder => builder
                            .AddOpenTelemetryLogging()
                            .AddODLExporter(
                                options =>
                                {
                                    options.EnableFallBack = false;
                                    options.LogTypeMappings = new Dictionary<string, string>
                                    {
                                        ["*"] = "TestEVR",
                                    };
                                }, false))
                        .Build();

            var options = host.Services.GetService<IOptions<ODLLogExporterOptions>>();
            Assert.NotNull(options);
            Assert.IsAssignableFrom<IOptions<ODLLogExporterOptions>>(options);
        }

        [Fact]
        public void AddODLExporter_UseSection_RegistersRequiredServices()
        {
            var exception = Record.Exception(() =>
            {
                IConfigurationRoot jsonConfigRoot = new ConfigurationBuilder().AddJsonFile("appsettings.json").Build();
                var config = jsonConfigRoot.GetSection("ODLTraceExporterWithParam");

                var options = new ODLLogExporterOptions();
                jsonConfigRoot.Bind("ODLTraceExporterWithParam", options);
                config.Value = JsonConvert.SerializeObject(options);

                using var host = new HostBuilder()
                            .ConfigureServices(service => service
                                .AddOpenTelemetry().WithTracing(builder => builder
                                .AddODLExporter(config, true)))
                            .Build();
            });
            Assert.Null(exception);
        }

        [Fact]
        public void AddODLExporter_UseInvalidSection_RegistersRequiredServices()
        {
            var exception = Record.Exception(() =>
            {
                IConfigurationRoot jsonConfigRoot = new ConfigurationBuilder().AddJsonFile("appsettings.json").Build();
                var config = jsonConfigRoot.GetSection("ODLExporterEmpty");

                var options = new ODLLogExporterOptions();
                jsonConfigRoot.Bind("ODLExporterEmpty", options);
                config.Value = JsonConvert.SerializeObject(options);

                //settings not exist
                var configEmpty = jsonConfigRoot.GetSection("ODLExporterNotExist");
                var optionsEmpty = new ODLLogExporterOptions();
                jsonConfigRoot.Bind("ODLExporterNotExist", optionsEmpty);
                configEmpty.Value = JsonConvert.SerializeObject(optionsEmpty);

                //settings invalid
                var configInvalid = jsonConfigRoot.GetSection("ODLExporterInvalid");
                var optionInvalid = new ODLLogExporterOptions();
                jsonConfigRoot.Bind("ODLExporterInvalid", optionInvalid);
                configInvalid.Value = JsonConvert.SerializeObject(optionInvalid);

                //whenever empty or invalid or not exist, will use the default value of the options.
                Assert.Equal(configEmpty.Value, config.Value);
                Assert.Equal(configInvalid.Value, config.Value);

                using var host = new HostBuilder()
                            .ConfigureLogging(builder => builder
                                .AddOpenTelemetryLogging()
                                .AddODLExporter(
                                    config, true))
                            .Build();
            });
            Assert.NotNull(exception);
        }

        [Fact]
        public void AddODLExporter_UseInvalidFile_ThrowException()
        {
            var exception = Record.Exception(() =>
            {
                IConfigurationRoot jsonConfigRoot = new ConfigurationBuilder().AddJsonFile("appsettings2.json").Build();
            });
            Assert.NotNull(exception);
        }

        [Fact]
        public void AddODLExporter_GivenInvalidPrepopulatedFields_ThrowsException()
        {
            Assert.Throws<ArgumentNullException>(() =>
           new HostBuilder().ConfigureLogging(builder => builder
                            .AddOpenTelemetryLogging()
                            .AddODLExporter(
                                options =>
                            {
                                options.EnableFallBack = false;
                                options.PrepopulatedFields = new Dictionary<string, object>
                                {
                                    ["Test"] = null!,
                                };
                                options.LogTypeMappings = new Dictionary<string, string>
                                {
                                    ["*"] = "TestEVR",
                                };
                            }, false))
                        .Build());
            Assert.Throws<ArgumentException>(() =>
           new HostBuilder().ConfigureLogging(builder => builder
                            .AddOpenTelemetryLogging()
                            .AddODLExporter(
                                options =>
                                {
                                    options.EnableFallBack = false;
                                    options.PrepopulatedFields = new Dictionary<string, object>
                                    {
                                        ["Test"] = new TestObject(),
                                    };
                                    options.LogTypeMappings = new Dictionary<string, string>
                                    {
                                        ["*"] = "TestEVR",
                                    };
                                }, false))
                        .Build());
        }
    }
    
    internal class TestObject 
    { 
        public string Test { get; set; }
    }
}