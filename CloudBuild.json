{"EnlistmentRootPaths": {"VersionGenerationMode": "SkipVersionGeneration", "SourceRootDirs": ["sources"], "OutputRootDirs": ["target"], "ReleaseRootDirs": ["target\\distrib"]}, "PackageFeedConfig": {"Nuget": {}}, "BuildTool": {"Name": "VisualStudio", "Version": "17.13"}, "QuickBuildArgs": "-MSBuildRestore -DetectDuplicateBinplace DetectAll -UseHardlinksInCache -QTestDoNotUseLocalAdmin -QTestUseVm All -EnableMsBuildBinLogTracing"}