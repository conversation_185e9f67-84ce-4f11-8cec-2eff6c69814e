﻿// <copyright file="LogEvent.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System.Collections.Generic;
using System.Reflection;

using Microsoft.Extensions.Logging;
using Microsoft.M365.Core.Telemetry.ECSClient;

namespace Microsoft.M365.Core.Telemetry.R9
{
    /// <summary>
    /// LogEvent class.
    /// </summary>
    /// <typeparam name="T">The type of Event Schema.</typeparam>
    public class LogEvent<T>
    {
        // R9 also populate these. Including these values from Ifx will cause Geneva backend to randomly select one from R9 and Ifx.
        // Discard these values from Ifx. Only use R9 values.
        private readonly HashSet<string> r9EnvDims = new HashSet<string>() { "env_time", "env_name", "env_ver" };

        /// <summary>
        /// Logs an R9 event.
        /// </summary>
        /// <param name="customEvent"> Custom Event to be logged.</param>
        /// <param name="bondFields"> bondFields are either Property or Field.</param>
        public void Log(T customEvent, List<MemberInfo> bondFields)
        {
            if (!R9EventEnabled())
            {
                return;
            }

            List<KeyValuePair<string, object>> fieldValues = new List<KeyValuePair<string, object>>();
            foreach (var bondField in bondFields)
            {
                if (bondField.DeclaringType.FullName == "Ifx.PartASchema" && r9EnvDims.Contains(bondField.Name))
                {
                    continue;
                }
                fieldValues.Add(new KeyValuePair<string, object>(bondField.Name, GetBondFieldValue(customEvent, bondField)));
            }

            // Add event type value.
            fieldValues.Add(new KeyValuePair<string, object>("EventType", typeof(T).Name));

            R9Services.CreateLogger<T>().Log(LogLevel.Information, eventId: 0, fieldValues.ToArray(), exception: null, formatter: (_, _) => string.Empty);
        }

        /// <summary>
        /// A bond field is either Property or Field. Get the value. Return the help message to users on errors.
        /// </summary>
        /// <param name="customEvent"> The bond instance..</param>
        /// <param name="bondField"> MemberInfo for one bond field.</param>
        private string GetBondFieldValue(object customEvent, MemberInfo bondField)
        {
            // TODO(jiayiwang): As of year 2022, property and field are still mixed and undefined concepts.
            // Pin them to one when they are consolidated.
            PropertyInfo propInfo = bondField as PropertyInfo;
            FieldInfo fieldInfo = bondField as FieldInfo;

            string value = propInfo?.GetValue(customEvent)?.ToString();
            if (value == null)
            {
                value = fieldInfo?.GetValue(customEvent)?.ToString();
            }

            if (value == null)
            {
                // TODO(jiayiwang): This should never happen. Remove after rollout.
                value = "SDK error! Contact <EMAIL>";
            }

            return value;
        }

        private bool R9EventEnabled()
        {
            return R9Services.AlwaysEnableR9 || OperatingSystemHelper.IsLinux || R9Services.ShouldSendR9Event(typeof(T).FullName) || R9Services.GetPassiveR9Config().EventEnabledForR9(typeof(T).FullName);
        }
    }
}
