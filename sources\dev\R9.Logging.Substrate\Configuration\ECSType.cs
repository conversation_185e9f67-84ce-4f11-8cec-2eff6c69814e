﻿// <copyright file="ECSType.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

namespace Microsoft.M365.Core.Telemetry.R9.Logging.Substrate.Configuration
{
    /// <summary>
    /// Represents the ECS endpoint which customer wants to use.
    /// </summary>
    internal enum ECSType
    {
        /// <summary>
        /// Use our centralized ECS in Production environment.
        /// </summary>
        CentralizedProd = 0,

        /// <summary>
        /// Use our centralized ECS in Integration environment.
        /// The environment is for testing purposes.
        /// </summary>
        CentralizedInt = 1,

        /// <summary>
        /// Use customers' own ECS settings. 
        /// Please follow our <see href="https://eng.ms/docs/experiences-devices/m365-core/o365-substrate-fabric-extended-suzhou/o365-telemetry/m365-unified-telemetry-logsmetrics/m365-unified-telemetry/onboarding/loggingsolution/guidesteps/configuration">document</see> for reference.
        /// </summary>
        Custom = 2,
    }
}
