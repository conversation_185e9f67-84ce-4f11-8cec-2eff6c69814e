// ---------------------------------------------------------------------------
// <copyright file="Startup.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>
// ---------------------------------------------------------------------------

namespace Microsoft.M365.Core.Telemetry.NetCoreWebApi
{
    using System;
    using System.Threading;
    using Microsoft.AspNetCore.Builder;
    using Microsoft.Extensions.Configuration;
    using Microsoft.Extensions.DependencyInjection;
    using Microsoft.Extensions.Hosting;
    using Microsoft.M365.Core.Telemetry.R9;
    using Microsoft.M365.Core.Telemetry.R9.Tracing.Instrumentation.Accelerator;
    using Microsoft.R9.Extensions.Logging;
    using Microsoft.R9.Extensions.Logging.Exporters;
    using Microsoft.R9.Extensions.Metering;
    using OpenTelemetry.Trace;

    /// <summary>
    /// Startup
    /// </summary>
    public class Startup
    {
        /// <summary>
        /// Main
        /// </summary>
        /// <param name="args">args</param>
        public static void Main(string[] args)
        {
            var builder = WebApplication.CreateBuilder(args);
            builder.Configuration.AddJsonFile("Appsettings.json");
            var configuration = (IConfiguration)builder.Configuration;
            ConfigureServices(builder.Services, configuration);
            Configure(builder.Build());
        }

        /// <summary>
        /// Configure
        /// </summary>
        /// <param name="app">app</param>
        public static void Configure(WebApplication app)
        {
            app.UseRouting();
            app.UseHttpsRedirection();
            app.UseAuthorization();
            app.MapControllers();
            app.Run();
        }

        /// <summary>
        /// ConfigureServices
        /// </summary>
        /// <param name="services">services</param>
        /// <param name="config">configuration</param>
        public static void ConfigureServices(IServiceCollection services, IConfiguration config)
        {
            services.AddControllers();

            // R9 Tracing
            services.AddDistributedTracingService(config.GetSection("Microsoft_m365_core_telemetry:ServiceMetadata"), config.GetSection("Microsoft_m365_core_telemetry:Tracing"));
        }
    }
}
