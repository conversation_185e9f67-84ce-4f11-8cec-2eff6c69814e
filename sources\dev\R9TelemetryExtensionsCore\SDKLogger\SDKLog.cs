﻿// <copyright file="SDKLog.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Diagnostics.CodeAnalysis;
using System.IO;
using System.Reflection;
using System.Threading;

#if !NETFRAMEWORK
using System.Runtime.Loader;
#endif

namespace Microsoft.M365.Core.Telemetry.SDKLogger
{
    /// <summary>
    /// SDK internal log lib.
    /// </summary>
    [ExcludeFromCodeCoverage]
    public static class SDKLog
    {
        /// <summary>
        /// Process name, as shown in dependency database.
        /// </summary>
        private static readonly Lazy<string> processName = new Lazy<string>(
            () =>
            {
                string result = "UnknownDueToError";
                try
                {
                    result = String.Join(
                        "#", new string[]
                        {
                            Process.GetCurrentProcess().ProcessName,
                            Environment.GetEnvironmentVariable("APP_POOL_ID", EnvironmentVariableTarget.Process)
                        });
                }
                catch (Exception)
                {
                    // best effort
                }
                return result;
            });

        // Absolute path of an existing file. Used when all other exporting methods fails.
        // WARNING: slow down your code by 100X. DO NOT USE IN PROD.
        private static readonly Lazy<string> diskLogPath = new Lazy<string>(
            () =>
            {
                return Environment.GetEnvironmentVariable("Microsoft_M365_Core_Telemetry_SDKLogger_DiskLogPath");
            });

        private static void MaybeLogToDisk(string message)
        {
            if (!string.IsNullOrEmpty(diskLogPath.Value) && File.Exists(diskLogPath.Value))
            {
                using (StreamWriter outputFile = new StreamWriter(diskLogPath.Value, true))
                {
                    outputFile.WriteLine(message);
                }
            }
        }

        /// <summary>
        /// Writes to Geneva in .net core 3.1, writes to console in .net framework.
        /// </summary>
        /// <param name="message">message</param>
        // TODO(jiayiwang): Implement in netfx.
        [SuppressMessage("Microsoft.Performance", "CA1801", Justification = "No action")]
        internal static void Info(string message)
        {
            if (InternalSDKR9Services.BypassSDKLogger)
            {
                return;
            }
            InternalSDKLog.Info(InternalSDKR9Services.CreateLogger<SDKLogCategory>(), message, processName.Value, Thread.CurrentThread.ManagedThreadId);
            MaybeLogToDisk(message);
        }

        /// <summary>
        /// Writes to Geneva in .net core 3.1, writes to console in .net framework.
        /// </summary>
        /// <param name="message">message</param>
        [SuppressMessage("Microsoft.Performance", "CA1801", Justification = "No action")]
        internal static void Warning(string message)
        {
            if (InternalSDKR9Services.BypassSDKLogger)
            {
                return;
            }
            InternalSDKLog.Warning(InternalSDKR9Services.CreateLogger<SDKLogCategory>(), message, processName.Value, Thread.CurrentThread.ManagedThreadId);
            MaybeLogToDisk(message);
        }

        /// <summary>
        /// Writes to Geneva in .net core 3.1, writes to console in .net framework.
        /// </summary>
        /// <param name="message">message</param>
        [SuppressMessage("Microsoft.Performance", "CA1801", Justification = "No action")]
        internal static void Error(string message)
        {
            if (InternalSDKR9Services.BypassSDKLogger)
            {
                return;
            }
            InternalSDKLog.Error(InternalSDKR9Services.CreateLogger<SDKLogCategory>(), message, processName.Value, Thread.CurrentThread.ManagedThreadId);
            MaybeLogToDisk(message);
        }

        /// <summary>
        /// LogMetricInfo
        /// </summary>
        internal static void LogMetricInfo()
        {
            if (InternalSDKR9Services.BypassSDKLogger)
            {
                return;
            }
#if !NETFRAMEWORK
            try
            {
                string targetType = "Microsoft.Office.Datacenter.PassiveMonitoring.Metric.Metric`1";
                foreach (Type metricType in ProgramChecker.GetGenericTypes(targetType))
                {
                    Type baseType = ProgramChecker.GetMetricBaseType(metricType, targetType);
                    if (baseType != null)
                    {
                        object instance = baseType.GetField("Instance").GetValue(null);
                        object[] metric = null;
                        foreach (FieldInfo item in baseType.BaseType.GetFields(BindingFlags.Instance | BindingFlags.NonPublic))
                        {
                            if (item.Name == "metrics")
                            {
                                metric = (object[])item.GetValue(instance);
                                break;
                            }
                        }

                        if (metric != null)
                        {
                            string message = string.Empty;
                            foreach (FieldInfo item in metric[0].GetType().GetFields(BindingFlags.Instance | BindingFlags.Public | BindingFlags.NonPublic | BindingFlags.Static))
                            {
                                message += item.Name + ": " + item.GetValue(metric[0]) + ";";
                            }
                            SDKLog.Info(message);
                        }
                    }
                }
            }
            catch (Exception e)
            {
                SDKLog.Error($"LogMetricInfo exception: {e}");
            }
#endif
        }

        /// <summary>
        /// Break a message into chunks.
        /// </summary>
        /// <param name="message"></param>
        /// <param name="chunkLines"></param>
        internal static List<string> Chunk(string message, int chunkLines)
        {
            List<string> res = new List<string>();
            if (InternalSDKR9Services.BypassSDKLogger)
            {
                return res;
            }
            string[] lines = message.Split('\n');
            int full_chunks = lines.Length / chunkLines;

            // Only add full chunks.
            for (int i = 0; i < full_chunks; i++)
            {
                res.Add(string.Join("\n", new ArraySegment<string>(lines, i * chunkLines, chunkLines)));
            }

            // Add last chunk if there is one.
            if (lines.Length > full_chunks * chunkLines)
            {
                res.Add(string.Join(
                    "\n",
                    new ArraySegment<string>(
                        lines,
                        full_chunks * chunkLines,
                        lines.Length - (full_chunks * chunkLines))));
            }
            return res;
        }

        /// <summary>
        /// GetAssemblyLoadContextMessage
        /// </summary>
        /// <returns></returns>
        internal static string GetAssemblyLoadContextMessage()
        {
            string res = string.Empty;
            if (InternalSDKR9Services.BypassSDKLogger)
            {
                return res;
            }
#if !NETFRAMEWORK
            foreach (AssemblyLoadContext alc in AssemblyLoadContext.All)
            {
                res = res + alc.Name + "\n";
                foreach (Assembly a in alc.Assemblies)
                {
                    res = res + "  " + a.FullName + "\n";
                }
            }
#endif
            return res;
        }

        /// <summary>
        /// InternalResetInUnitTest
        /// </summary>
        internal static void InternalResetInUnitTest()
        {
            if (InternalSDKR9Services.BypassSDKLogger)
            {
                return;
            }

            InternalSDKR9Services.ResetInUnitTest();
        }
    }

    /// <summary>
    /// SDKLogCategory
    /// </summary>
    internal class SDKLogCategory
    {
    }
}
