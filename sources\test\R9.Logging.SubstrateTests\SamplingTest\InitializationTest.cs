﻿// <copyright file="InitializationTest.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System.Collections.Generic;
using System.IO;
using System.Text;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.M365.Core.Telemetry.R9.Logging.Substrate.Sampler;
using OpenTelemetry.Logs;
using Xunit;

namespace Microsoft.M365.Core.Telemetry.R9.Logging.Substrate.Test.SamplingTest
{
    public class InitializationTest
    {
        private const string LoggerName = "InitializationTest";

        private const string Config = @"
{
    ""SubstrateLogging"": {
        ""RuleBasedSampler"": {
            ""InitializationTest"": [
                {
                    ""Constraints"": [],
                    ""Strategy"": {
                        ""Type"": ""Random"",
                        ""SampleRate"": ""0.0""
                    }
                }
            ]
        }
    }
}";

        [Fact]
        public void AddSingleSampler_ShouldNotSample()
        {
            IConfiguration configuration = new ConfigurationBuilder()
                .AddJsonStream(new MemoryStream(Encoding.UTF8.GetBytes(Config)))
                .Build();
            var exportedItems = new List<LogRecord>();
            var factory = LoggerFactory.Create(builder =>
            {
                builder
                    .SetMinimumLevel(LogLevel.Trace)
                    .AddRuleBasedSampler(configuration.GetSection("SubstrateLogging"))
                    .AddOpenTelemetry(options =>
                    {
                        options.AddInMemoryExporter(exportedItems);
                    });
            });
            var logger = factory.CreateLogger(LoggerName);
            logger.LogInformation("Should not be sampled.");
            Assert.Empty(exportedItems);
        }

        [Fact]
        public void AddMultipleSamplers_ShouldBeOverriden()
        {
            IConfiguration configuration = new ConfigurationBuilder()
                .AddJsonStream(new MemoryStream(Encoding.UTF8.GetBytes(Config)))
                .Build();
            var exportedItems = new List<LogRecord>();
            var factory = LoggerFactory.Create(builder =>
            {
                builder
                    .SetMinimumLevel(LogLevel.Trace)
                    .AddRuleBasedSampler(configuration.GetSection("SubstrateLogging"))
                    .AddRandomProbabilisticSampler(1.0) // This sampler should override the rule-based sampler. The prob is 1.0, the log should be sampled.
                    .AddOpenTelemetry(options =>
                    {
                        options.AddInMemoryExporter(exportedItems);
                    });
            });
            var logger = factory.CreateLogger(LoggerName);
            logger.LogInformation("Should be sampled.");
            Assert.Single(exportedItems);
        }

        [Fact]
        public void AddMultipleSamplers_ShouldOverrideOthers()
        {
            IConfiguration configuration = new ConfigurationBuilder()
                .AddJsonStream(new MemoryStream(Encoding.UTF8.GetBytes(Config)))
                .Build();
            var exportedItems = new List<LogRecord>();
            var factory = LoggerFactory.Create(builder =>
            {
                builder
                    .SetMinimumLevel(LogLevel.Trace)
                    .AddRandomProbabilisticSampler(1.0) 
                    .AddRuleBasedSampler(configuration.GetSection("SubstrateLogging"))
                    .AddOpenTelemetry(options =>
                    {
                        options.AddInMemoryExporter(exportedItems);
                    });
            });
            var logger = factory.CreateLogger(LoggerName);
            logger.LogInformation("Should not be sampled.");
            Assert.Empty(exportedItems);
        }
    }
}
