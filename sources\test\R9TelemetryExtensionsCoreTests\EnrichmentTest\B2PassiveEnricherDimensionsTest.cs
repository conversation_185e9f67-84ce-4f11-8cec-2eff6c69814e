﻿// <copyright file="B2PassiveEnricherDimensionsTest.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using FluentAssertions;
using Microsoft.M365.Core.Telemetry.Enrichment;
using Microsoft.M365.Core.Telemetry.TestCommon;
using Xunit;
using Xunit.Abstractions;

namespace Microsoft.M365.Core.Telemetry.EnrichmentTest
{
    /// <summary>
    /// B2PassiveEnricherDimensionsTest
    /// </summary>
    [Collection("Do not parallel")]
    public class B2PassiveEnricherDimensionsTest : BaseTest
    {
        /// <summary>
        /// B2PassiveEnricherDimensionsTest
        /// </summary>
        /// <param name="output"></param>
        public B2PassiveEnricherDimensionsTest(ITestOutputHelper output) : base(output)
        {
        }

        /// <summary>
        /// GetDimensionNamesReturnsAnArrayOfDimensionNames
        /// </summary>
        [Fact]
        public void GetDimensionNamesReturnsAnArrayOfDimensionNames()
        {
            IReadOnlyList<string> dimensions = B2PassiveEnricherDimensions.DimensionNames;

            string[] expectedDimensions = GetStringConstants(typeof(B2PassiveEnricherDimensions));

            dimensions.Should().BeEquivalentTo(expectedDimensions);
        }

        private static string[] GetStringConstants(IReflect type)
        {
            FieldInfo[] fields = type.GetFields(BindingFlags.Public | BindingFlags.Static);

            return fields
                .Where(f => f.IsLiteral && f.FieldType == typeof(string))
                .Select(f => (string)f.GetValue(null)!)
                .ToArray();
        }
    }
}
