#define HAVE_ABSEIL
#include "OdlTraceExporterRecordable.h"

#include <absl/strings/str_cat.h>
#include <gtest/gtest.h>
#include <nlohmann/json.hpp>
#include <opentelemetry/common/attribute_value.h>
#include <opentelemetry/common/key_value_iterable_view.h>
#include <opentelemetry/common/timestamp.h>
#include <opentelemetry/logs/noop.h>
#include <opentelemetry/nostd/span.h>
#include <opentelemetry/nostd/string_view.h>
#include <opentelemetry/nostd/utility.h>
#include <opentelemetry/sdk/instrumentationscope/instrumentation_scope.h>
#include <opentelemetry/sdk/resource/resource.h>
#include <opentelemetry/sdk/trace/recordable.h>
#include <opentelemetry/trace/span_context.h>
#include <opentelemetry/trace/span_id.h>
#include <opentelemetry/trace/span_metadata.h>
#include <opentelemetry/trace/trace_flags.h>
#include <opentelemetry/trace/trace_id.h>

#include <array>
#include <chrono>
#include <cstdint>
#include <map>
#include <memory>
#include <string>
#include <vector>

namespace trace    = opentelemetry::trace;
namespace nostd    = opentelemetry::nostd;
namespace sdktrace = opentelemetry::sdk::trace;
namespace common   = opentelemetry::common;
using json         = nlohmann::json;
using namespace Microsoft::M365::Exporters;

class OdlTraceExporterRecordableTest : public ::testing::Test {
  public:
    std::unordered_map<std::string, std::string> common_dimensions_ = {{"commonkey", "commonvalue"}};
};

// Testing Shutdown functionality of OStreamSpanExporter, should expect no data to be sent to Stream
TEST_F(OdlTraceExporterRecordableTest, SetIdentity)
{
    OdlTraceExporterRecordable rec;
    const trace::TraceId trace_id(std::array<const uint8_t, trace::TraceId::kSize>(
        {0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1}));

    const trace::SpanId span_id(
        std::array<const uint8_t, trace::SpanId::kSize>({0, 0, 0, 0, 0, 0, 0, 2}));

    const trace::SpanId parent_span_id(
        std::array<const uint8_t, trace::SpanId::kSize>({0, 0, 0, 0, 0, 0, 0, 3}));

    const trace::SpanContext span_context{trace_id, span_id,
                                          trace::TraceFlags{trace::TraceFlags::kIsSampled}, true};

    rec.SetIdentity(span_context, parent_span_id);
    std::string expected = absl::StrCat(
        "{\"schemaVersion\":\"2.0\",\"traceId\":\"00000000000000000000000000000001\",\"spanId\":\"0000000000000002\",",
        "\"kind\":\"\",\"status\":\"Unset\",\"operationName\":\"\",\"displayName\":\"\",\"source\":",
        "{\"Name\":\"\",\"Version\":\"\"},\"duration\":\"\",\"startTimeUtc\":\"\",\"tags\":null,"
        "\"events\":null,\"parentId\":\"0000000000000003\",\"commonkey\":\"commonvalue\"}"
    );
    auto noop_logger = std::make_shared<opentelemetry::logs::NoopLogger>();
    EXPECT_EQ(expected, rec.Serialize(common_dimensions_, noop_logger));
}

TEST_F(OdlTraceExporterRecordableTest, SetIdentityEmptyParent)
{
    OdlTraceExporterRecordable rec;
    const trace::TraceId trace_id(std::array<const uint8_t, trace::TraceId::kSize>(
        {0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1}));

    const trace::SpanId span_id(
        std::array<const uint8_t, trace::SpanId::kSize>({0, 0, 0, 0, 0, 0, 0, 2}));

    const trace::SpanId parent_span_id(
        std::array<const uint8_t, trace::SpanId::kSize>({0, 0, 0, 0, 0, 0, 0, 0}));

    const trace::SpanContext span_context{trace_id, span_id,
                                          trace::TraceFlags{trace::TraceFlags::kIsSampled}, true};

    rec.SetIdentity(span_context, parent_span_id);
    std::string expected = absl::StrCat(
        "{\"schemaVersion\":\"2.0\",\"traceId\":\"00000000000000000000000000000001\",\"spanId\":\"0000000000000002\"," ,
        "\"kind\":\"\",\"status\":\"Unset\",\"operationName\":\"\",\"displayName\":\"\",\"source\":",
        "{\"Name\":\"\",\"Version\":\"\"},\"duration\":\"\",\"startTimeUtc\":\"\",\"tags\":null,",
        "\"events\":null,\"parentId\":\"\",\"commonkey\":\"commonvalue\"}");
    auto noop_logger = std::make_shared<opentelemetry::logs::NoopLogger>();
    EXPECT_EQ(expected, rec.Serialize(common_dimensions_, noop_logger));
}

TEST_F(OdlTraceExporterRecordableTest, SetName)
{
    nostd::string_view name = "Test Span";
    OdlTraceExporterRecordable rec;
    rec.SetName(name);
    std::string expected = absl::StrCat(
        "{\"schemaVersion\":\"2.0\",\"traceId\":\"\",\"spanId\":\"\",\"kind\":\"\",\"status\":\"Unset\",\"operationName\":\"\",",
        "\"displayName\":\"Test Span\",\"source\":{\"Name\":\"\",\"Version\":\"\"},\"duration\":\"\",\"startTimeUtc\":\"\",\"tags\":null,",
        "\"events\":null,\"parentId\":\"\",\"commonkey\":\"commonvalue\"}"
    );
    auto noop_logger = std::make_shared<opentelemetry::logs::NoopLogger>();
    EXPECT_EQ(expected, rec.Serialize(common_dimensions_, noop_logger));
}

TEST_F(OdlTraceExporterRecordableTest, SetStartTimeAndDuration)
{
    OdlTraceExporterRecordable rec;
    std::time_t epoch_time = 1704067200; // epoch time for 2024-01-01T00:00:00Z
    auto start_time = std::chrono::system_clock::from_time_t(epoch_time) + std::chrono::microseconds(123456);
    common::SystemTimestamp start_timestamp(start_time);
    rec.SetStartTime(start_timestamp);
    auto duration = std::chrono::nanoseconds(123456789);
    rec.SetDuration(duration);
    std::string expected = absl::StrCat(
        "{\"schemaVersion\":\"2.0\",\"traceId\":\"\",\"spanId\":\"\",\"kind\":\"\",\"status\":\"Unset\",\"operationName\":\"\",",
        "\"displayName\":\"\",\"source\":{\"Name\":\"\",\"Version\":\"\"},\"duration\":\"00:00:00.1234567\",\"startTimeUtc\":\"2024-01-01T00:00:00.1234560Z\",",
        "\"tags\":null,\"events\":null,\"parentId\":\"\",\"commonkey\":\"commonvalue\"}"
    );
    auto noop_logger = std::make_shared<opentelemetry::logs::NoopLogger>();
    EXPECT_EQ(expected, rec.Serialize(common_dimensions_, noop_logger));
}

TEST_F(OdlTraceExporterRecordableTest, SetStatus)
{
    std::string description                     = "Error description";
    std::vector<trace::StatusCode> status_codes = {trace::StatusCode::kError, trace::StatusCode::kOk};
    OdlTraceExporterRecordable rec;
    rec.SetStatus(trace::StatusCode::kError, description);
    std::string expected = absl::StrCat(
        "{\"schemaVersion\":\"2.0\",\"traceId\":\"\",\"spanId\":\"\",\"kind\":\"\",\"status\":\"Error\",\"operationName\":\"\",",
        "\"displayName\":\"\",\"source\":{\"Name\":\"\",\"Version\":\"\"},\"duration\":\"\",\"startTimeUtc\":\"\",\"tags\":null,\"events\":null,\"parentId\":\"\",\"commonkey\":\"commonvalue\"}"
    );
    auto noop_logger = std::make_shared<opentelemetry::logs::NoopLogger>();
    EXPECT_EQ(expected, rec.Serialize(common_dimensions_, noop_logger));
    rec.SetStatus(trace::StatusCode::kOk, description);
    expected = absl::StrCat(
        "{\"schemaVersion\":\"2.0\",\"traceId\":\"\",\"spanId\":\"\",\"kind\":\"\",\"status\":\"Ok\",\"operationName\":\"\",",
        "\"displayName\":\"\",\"source\":{\"Name\":\"\",\"Version\":\"\"},\"duration\":\"\",\"startTimeUtc\":\"\",\"tags\":null,\"events\":null,\"parentId\":\"\",\"commonkey\":\"commonvalue\"}"
    );
    EXPECT_EQ(expected, rec.Serialize(common_dimensions_, noop_logger));
}

TEST_F(OdlTraceExporterRecordableTest, SetSpanKind)
{
    OdlTraceExporterRecordable rec;
    rec.SetSpanKind(trace::SpanKind::kClient);
    std::string expected = absl::StrCat(
        "{\"schemaVersion\":\"2.0\",\"traceId\":\"\",\"spanId\":\"\",\"kind\":\"Client\",\"status\":\"Unset\",\"operationName\":\"\",",
        "\"displayName\":\"\",\"source\":{\"Name\":\"\",\"Version\":\"\"},\"duration\":\"\",\"startTimeUtc\":\"\",\"tags\":null,\"events\":null,\"parentId\":\"\",\"commonkey\":\"commonvalue\"}"
    );
    auto noop_logger = std::make_shared<opentelemetry::logs::NoopLogger>();
    EXPECT_EQ(expected, rec.Serialize(common_dimensions_, noop_logger));
}

TEST_F(OdlTraceExporterRecordableTest, SetInstrumentationScope)
{
    OdlTraceExporterRecordable rec;
    rec.SetInstrumentationScope(*opentelemetry::sdk::instrumentationscope::InstrumentationScope::Create("TestName", "1.0"));
    std::string expected = absl::StrCat(
        "{\"schemaVersion\":\"2.0\",\"traceId\":\"\",\"spanId\":\"\",\"kind\":\"\",\"status\":\"Unset\",\"operationName\":\"\",",
        "\"displayName\":\"\",\"source\":{\"Name\":\"TestName\",\"Version\":\"1.0\"},\"duration\":\"\",\"startTimeUtc\":\"\",\"tags\":null,\"events\":null,\"parentId\":\"\",\"commonkey\":\"commonvalue\"}"
    );
    auto noop_logger = std::make_shared<opentelemetry::logs::NoopLogger>();
    EXPECT_EQ(expected, rec.Serialize(common_dimensions_, noop_logger));
}

TEST_F(OdlTraceExporterRecordableTest, AddEventDefault)
{
    OdlTraceExporterRecordable rec;
    nostd::string_view name = "Test Event";
    common::SystemTimestamp timestamp(std::chrono::system_clock::from_time_t(1704067200));
    rec.sdktrace::Recordable::AddEvent(name, timestamp);
    std::string expected = absl::StrCat(
        "{\"schemaVersion\":\"2.0\",\"traceId\":\"\",\"spanId\":\"\",\"kind\":\"\",\"status\":\"Unset\",\"operationName\":\"\",",
        "\"displayName\":\"\",\"source\":{\"Name\":\"\",\"Version\":\"\"},\"duration\":\"\",\"startTimeUtc\":\"\",\"tags\":null,\"events\":[",
        "{\"Name\":\"Test Event\",\"Timestamp\":\"2024-01-01T00:00:00.0Z\",\"Tags\":null}],\"parentId\":\"\",\"commonkey\":\"commonvalue\"}"  // if no nano seconds, it shows as "0Z"
    );
    auto noop_logger = std::make_shared<opentelemetry::logs::NoopLogger>();
    EXPECT_EQ(expected, rec.Serialize(common_dimensions_, noop_logger));
}

TEST_F(OdlTraceExporterRecordableTest, AddEventWithAttributes)
{
    OdlTraceExporterRecordable rec;
    std::map<std::string, int> attributes = {{"attr1", 1}, {"attr2", 2}};
    common::SystemTimestamp timestamp(std::chrono::system_clock::from_time_t(1704067200));
    rec.AddEvent("Test Event", timestamp,
                 common::KeyValueIterableView<std::map<std::string, int>>(attributes));
    std::string expected = absl::StrCat(
        "{\"schemaVersion\":\"2.0\",\"traceId\":\"\",\"spanId\":\"\",\"kind\":\"\",\"status\":\"Unset\",\"operationName\":\"\",",
        "\"displayName\":\"\",\"source\":{\"Name\":\"\",\"Version\":\"\"},\"duration\":\"\",\"startTimeUtc\":\"\",\"tags\":null,\"events\":[",
        "{\"Name\":\"Test Event\",\"Timestamp\":\"2024-01-01T00:00:00.0Z\",\"Tags\":{\"attr1\":1,\"attr2\":2}}],\"parentId\":\"\",\"commonkey\":\"commonvalue\"}"
    );
    auto noop_logger = std::make_shared<opentelemetry::logs::NoopLogger>();
    EXPECT_EQ(expected, rec.Serialize(common_dimensions_, noop_logger));
}

// Test non-int single types. Int single types are tested using templates (see IntAttributeTest)
TEST_F(OdlTraceExporterRecordableTest, SetSingleAtrribute)
{
    OdlTraceExporterRecordable rec;
    nostd::string_view bool_key = "bool_attr";
    common::AttributeValue bool_val(true);
    rec.SetAttribute(bool_key, bool_val);
  
    nostd::string_view double_key = "double_attr";
    common::AttributeValue double_val(3.3);
    rec.SetAttribute(double_key, double_val);
  
    nostd::string_view str_key = "str_attr";
    common::AttributeValue str_val(nostd::string_view("Test"));
    rec.SetAttribute(str_key, str_val);

    std::string expected = absl::StrCat(
        "{\"schemaVersion\":\"2.0\",\"traceId\":\"\",\"spanId\":\"\",\"kind\":\"\",\"status\":\"Unset\",\"operationName\":\"\",",
        "\"displayName\":\"\",\"source\":{\"Name\":\"\",\"Version\":\"\"},\"duration\":\"\",\"startTimeUtc\":\"\",\"tags\":{",
        "\"bool_attr\":true,\"double_attr\":3.3,\"str_attr\":\"Test\"},\"events\":null,\"parentId\":\"\",\"commonkey\":\"commonvalue\"}"
    );
    auto noop_logger = std::make_shared<opentelemetry::logs::NoopLogger>();
    EXPECT_EQ(expected, rec.Serialize(common_dimensions_, noop_logger));
}

namespace
{
// The std::string value expires when the function returns. std::string_view backed by it will be invalid.
void SetExpiringStringAttribute(OdlTraceExporterRecordable &rec, std::string key, std::string value)
{
    common::AttributeValue str_val(value);
    rec.SetAttribute(key, str_val);
}
}  // namespace

// Test string attr that goes out of scope when serialize.
TEST_F(OdlTraceExporterRecordableTest, SetExpiringStringAtrribute)
{
    OdlTraceExporterRecordable rec;
    SetExpiringStringAttribute(rec, "m365.dt.bogus", "foo");

    // When iterating over map, it's somewhat ordered by key. Not a guarantee though.
    // However we don't want to sort the attrs for better performance.
    // So this test might be flaky.
    std::string expected = absl::StrCat(
        "{\"schemaVersion\":\"2.0\",\"traceId\":\"\",\"spanId\":\"\",\"kind\":\"\",\"status\":\"Unset\",\"operationName\":\"\",",
        "\"displayName\":\"\",\"source\":{\"Name\":\"\",\"Version\":\"\"},\"duration\":\"\",\"startTimeUtc\":\"\",\"tags\":{",
        "\"m365.dt.bogus\":\"foo\"},"
        "\"events\":null,\"parentId\":\"\",\"commonkey\":\"commonvalue\"}"
    );
    auto noop_logger = std::make_shared<opentelemetry::logs::NoopLogger>();
    EXPECT_EQ(expected, rec.Serialize(common_dimensions_, noop_logger));
}

TEST_F(OdlTraceExporterRecordableTest, OverrideAttribute)
{
    OdlTraceExporterRecordable rec;
    rec.SetAttribute("attr", common::AttributeValue(1));
    rec.SetAttribute("attr", common::AttributeValue(2));

    std::string expected = absl::StrCat(
        "{\"schemaVersion\":\"2.0\",\"traceId\":\"\",\"spanId\":\"\",\"kind\":\"\",\"status\":\"Unset\",\"operationName\":\"\",",
        "\"displayName\":\"\",\"source\":{\"Name\":\"\",\"Version\":\"\"},\"duration\":\"\",\"startTimeUtc\":\"\",\"tags\":{",
        "\"attr\":2},\"events\":null,\"parentId\":\"\",\"commonkey\":\"commonvalue\"}"
    );
    auto noop_logger = std::make_shared<opentelemetry::logs::NoopLogger>();
    EXPECT_EQ(expected, rec.Serialize(common_dimensions_, noop_logger));
}

// Test non-int array types. Int array types are tested using templates (see IntAttributeTest)
TEST_F(OdlTraceExporterRecordableTest, SetArrayAtrribute)
{
    OdlTraceExporterRecordable rec;
    const int kArraySize  = 3;
    bool bool_arr[kArraySize] = {true, false, true};
    nostd::span<const bool> bool_span(bool_arr);
    rec.SetAttribute("bool_arr_attr", bool_span);
    double double_arr[kArraySize] = {22.3, 33.4, 44.5};
    nostd::span<const double> double_span(double_arr);
    rec.SetAttribute("double_arr_attr", double_span);
    nostd::string_view str_arr[kArraySize] = {"Hello", "World", "Test"};
    nostd::span<const nostd::string_view> str_span(str_arr);
    rec.SetAttribute("str_arr_attr", str_span);
  
    std::string expected = absl::StrCat(
        "{\"schemaVersion\":\"2.0\",\"traceId\":\"\",\"spanId\":\"\",\"kind\":\"\",\"status\":\"Unset\",\"operationName\":\"\",",
        "\"displayName\":\"\",\"source\":{\"Name\":\"\",\"Version\":\"\"},\"duration\":\"\",\"startTimeUtc\":\"\",\"tags\":{",
        "\"bool_arr_attr\":[true,false,true],\"double_arr_attr\":[22.3,33.4,44.5],\"str_arr_attr\":[\"Hello\",\"World\",\"Test\"]},",
        "\"events\":null,\"parentId\":\"\",\"commonkey\":\"commonvalue\"}"
    );
    auto noop_logger = std::make_shared<opentelemetry::logs::NoopLogger>();
    EXPECT_EQ(expected, rec.Serialize(common_dimensions_, noop_logger));
}

/**
 * AttributeValue can contain different int types, such as int, int64_t,
 * unsigned int, and uint64_t. To avoid writing test cases for each, we can
 * use a template approach to test all int types.
 */
template <typename T>
struct OdlIntAttributeTest : public testing::Test
{
  using IntParamType = T;
};

using IntTypes = testing::Types<int, int64_t, unsigned int, uint64_t>;
TYPED_TEST_SUITE(OdlIntAttributeTest, IntTypes);

TYPED_TEST(OdlIntAttributeTest, SetIntSingleAttribute)
{
  using IntType = typename TestFixture::IntParamType;
  IntType i     = 2;
  common::AttributeValue int_val(i);

  OdlTraceExporterRecordable rec;
  rec.SetAttribute("int_attr", int_val);
  
  std::string expected = absl::StrCat(
      "{\"schemaVersion\":\"2.0\",\"traceId\":\"\",\"spanId\":\"\",\"kind\":\"\",\"status\":\"Unset\",\"operationName\":\"\",",
      "\"displayName\":\"\",\"source\":{\"Name\":\"\",\"Version\":\"\"},\"duration\":\"\",\"startTimeUtc\":\"\",\"tags\":{",
      "\"int_attr\":2},\"events\":null,\"parentId\":\"\",\"commonkey\":\"commonvalue\"}"
  );
  auto noop_logger = std::make_shared<opentelemetry::logs::NoopLogger>();
  EXPECT_EQ(expected, rec.Serialize({{"commonkey", "commonvalue"}}, noop_logger));
}

TYPED_TEST(OdlIntAttributeTest, SetIntArrayAttribute)
{
  using IntType = typename TestFixture::IntParamType;

  const int kArraySize        = 3;
  IntType int_arr[kArraySize] = {4, 5, 6};
  nostd::span<const IntType> int_span(int_arr);

  OdlTraceExporterRecordable rec;
  rec.SetAttribute("int_arr_attr", int_span);
  std::string expected = absl::StrCat(
      "{\"schemaVersion\":\"2.0\",\"traceId\":\"\",\"spanId\":\"\",\"kind\":\"\",\"status\":\"Unset\",\"operationName\":\"\",",
      "\"displayName\":\"\",\"source\":{\"Name\":\"\",\"Version\":\"\"},\"duration\":\"\",\"startTimeUtc\":\"\",\"tags\":{",
      "\"int_arr_attr\":[4,5,6]},\"events\":null,\"parentId\":\"\",\"commonkey\":\"commonvalue\"}"
  );
  auto noop_logger = std::make_shared<opentelemetry::logs::NoopLogger>();
  EXPECT_EQ(expected, rec.Serialize({{"commonkey", "commonvalue"}}, noop_logger));
}