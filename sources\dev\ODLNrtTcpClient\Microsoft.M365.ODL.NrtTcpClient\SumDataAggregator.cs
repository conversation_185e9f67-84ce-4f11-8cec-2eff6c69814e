﻿// <copyright file="SumDataAggregator.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System.Collections.Concurrent;
using System.Diagnostics.CodeAnalysis;

namespace Microsoft.M365.ODL.NrtTcpClient
{
    /// <summary>
    /// An aggregator to summarize the values, copied from substrate ODL
    /// </summary>
    [ExcludeFromCodeCoverage]
    public class SumDataAggregator : IDataAggregator<long>
    {
        /// <summary>
        /// The data being aggregated
        /// </summary>
        private long result;

        /// <summary>
        /// The key value pair of data being aggregated
        /// </summary>
        private ConcurrentDictionary<string, long> kvp;

        /// <summary>
        /// Initializes a new instance of the <see cref="SumDataAggregator" /> class.
        /// </summary>
        public SumDataAggregator()
        {
            this.result = 0;
            this.kvp = new ConcurrentDictionary<string, long>();
        }

        /// <summary>
        /// Get the current value being aggregated.
        /// </summary>
        public long CurrentValue
        {
            get
            {
                return Interlocked.Read(ref this.result);
            }
        }

        /// <summary>
        /// Get the current key value pair being aggregated.
        /// </summary>
        public ConcurrentDictionary<string, long> CurrentKvp
        {
            get
            {
                return this.kvp;
            }
        }

        /// <summary>
        /// Aggregate a new value
        /// </summary>
        /// <param name="value">The value to be aggregated</param>
        public void Aggregate(long value)
        {
            Interlocked.Add(ref this.result, value);
        }

        /// <summary>
        /// Aggregate a new key value pair
        /// </summary>
        /// <param name="countsKvp"></param>
        public void AggregateKvp(Dictionary<string, long> countsKvp)
        {
            if (countsKvp == null)
            {
                return;
            }

            foreach (var kvp in countsKvp)
            {
                this.kvp.AddOrUpdate(kvp.Key, kvp.Value, (k, v) => v + kvp.Value);
            }
        }

        /// <summary>
        /// Gets the current value and reset the aggregator.
        /// </summary>
        /// <param name="value">Value returned</param>
        /// <returns>true if success, false if data is invalid or not exist</returns>
        public bool GetAndReset(out long value)
        {
            long val = Interlocked.Exchange(ref this.result, 0);
            value = val;
            return val > 0;
        }

        /// <summary>
        /// Gets the current key value pair and reset the aggregator.
        /// </summary>
        /// <param name="value"></param>
        /// <returns></returns>
        public bool GetAndResetKvp(out ConcurrentDictionary<string, long> value)
        {
            var newKvp = new ConcurrentDictionary<string, long>();
            var oldKvp = Interlocked.Exchange(ref this.kvp, newKvp);
            value = oldKvp;
            return oldKvp.Count > 0;
        }
    }
}
