﻿// <copyright file="ECSClientUtilities.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Globalization;
using System.Threading;
using Microsoft.M365.Core.Telemetry.Enrichment;
using Microsoft.M365.Core.Telemetry.SDKLogger;
using Microsoft.Skype.ECS.Client;

using Newtonsoft.Json.Linq;

namespace Microsoft.M365.Core.Telemetry.ECSClient
{
    /// <summary>
    /// ECSClientUtilities
    /// </summary>
    internal static class ECSClientUtilities
    {
        /// <summary>
        /// Parse config from ecs json object.
        /// </summary>
        /// <param name="configRoot"> The ecs config json object. </param>
        /// <param name="configName"> The config name. </param>
        /// <returns>The SettingsETag result.</returns>
        public static JToken ParseConfig(JObject configRoot, string configName)
        {
            _ = configRoot ?? throw new ArgumentNullException(nameof(configRoot));
            JToken tokenRes = configRoot.SelectToken(configName);
            if (tokenRes == null)
            {
                SDKLog.Warning($"Fail to find the {configName} from {configRoot}");
            }

            return tokenRes;
        }

        /// <summary>
        /// Update UnifiedTelemetryEnabled value.
        /// </summary>
        /// <param name="team">The project team.</param>
        /// <param name="ecsContext">ECS filters.</param>
        /// <param name="requester">ECS client requester.</param>
        /// <returns>The configs with Json format</returns>
        public static JObject GetUnifiedTelemetryConfig(string team, Dictionary<string, string> ecsContext, IECSConfigurationRequester requester)
        {
            try
            {
                SettingsETag settingsETag = GetSettingsWithRetry(Constants.DefaultRetryTimes, ecsContext, requester);

                JObject configRoot;
                if (!settingsETag.Settings.TryGetRootValue(team, out configRoot))
                {
                    SDKLog.Error($"fail to find {team}'s config");
                    return null;
                }
                return configRoot;
            }
            catch (Exception e)
            {
                SDKLog.Error($"Uncaught ECS refresh exception: {e}");
            }
            return null;
        }

        /// <summary>
        /// Get ECS settings with retry.
        /// </summary>
        /// <param name="maxRetries">Max retry times.</param>
        /// <param name="ecsContext">Ecs context.</param>
        /// <param name="requester">ECS client requester.</param>
        /// <returns>The SettingsETag result.</returns>
        public static SettingsETag GetSettingsWithRetry(int maxRetries, Dictionary<string, string> ecsContext, IECSConfigurationRequester requester)
        {
            _ = requester ?? throw new ArgumentNullException(nameof(requester));
            int count = 0;
            while (count < maxRetries)
            {
                try
                {
                    SettingsETag result = requester.GetSettings(ecsContext).GetAwaiter().GetResult();
                    return result;
                }
                catch (Exception e)
                {
                    SDKLog.Error($"GetSettingsWithRetry failed (retry={count}). {e}");
                    count++;
                    Thread.Sleep(TimeSpan.FromSeconds(count));
                }
            }
            return null;
        }

        /// <summary>
        /// Compare given dictionaries.
        /// </summary>
        /// <param name="dict1"></param>
        /// <param name="dict2"></param>
        /// <returns></returns>
        public static bool DictionaryEquals(Dictionary<string, object> dict1, Dictionary<string, object> dict2)
        {
            if (dict1 == dict2)
            {
                return true;
            }

            if ((dict1 == null) || (dict2 == null) || (dict1.Count != dict2.Count))
            {
                return false;
            }

            foreach (var kvp in dict1)
            {
                if (!dict2.TryGetValue(kvp.Key, out object dict2Value))
                {
                    return false;
                }

                if (!Equals(kvp.Value, dict2Value))
                {
                    return false;
                }
            }
            return true;
        }

        /// <summary>
        /// Judge whether current workload is EOP.
        /// </summary>
        /// <returns></returns>
        public static string IsEOP()
        {
#pragma warning disable CA1307 // Specify StringComparison
            string isEOP = !string.IsNullOrWhiteSpace(DimensionValues.Forest) && DimensionValues.Forest.Contains("eop") ? "true" : "false";
#pragma warning restore CA1307 // Specify StringComparison
            return isEOP;
        }

        /// <summary>
        /// IsCosmicService
        /// </summary>
        /// <param name="runtimeModel"></param>
        /// <returns></returns>
        internal static bool IsCosmic(string runtimeModel)
        {
            return !string.IsNullOrEmpty(Environment.GetEnvironmentVariable("COSMIC_PODNAME"))
                || (runtimeModel ?? string.Empty).Equals("cosmic", StringComparison.OrdinalIgnoreCase);
        }

        /// <summary>
        /// IsCosmicService
        /// </summary>
        /// <param name="runtimeModel"></param>
        /// <returns></returns>
        internal static bool IsModelD(string runtimeModel)
        {
            runtimeModel = runtimeModel ?? string.Empty;
            return runtimeModel.IndexOf("ModelD", StringComparison.OrdinalIgnoreCase) >= 0
                && runtimeModel.IndexOf("ModelD2", StringComparison.OrdinalIgnoreCase) < 0;
        }

        /// <summary>
        /// Convert the JObject fetched from ECS into a Dictionary.
        /// </summary>
        /// <param name="jsonObject"></param>
        /// <returns></returns>
        public static ConcurrentDictionary<string, Dictionary<string, object>> ConvertJObjectToNestedDictionary(JObject jsonObject)
        {
            ConcurrentDictionary<string, Dictionary<string, object>> result = new ();
            foreach (var property in jsonObject.Properties())
            {
                if (property.Value is JObject nestedObject)
                {
                    result[property.Name] = ConvertJObjectToDictionary(nestedObject);
                }
                else
                {
                    result[property.Name] = new Dictionary<string, object>
                    {
                        { property.Name, property.Value.ToObject<object>() }
                    };
                }
            }
            return result;
        }

        /// <summary>
        /// Convert the Jobject into dictionary.
        /// </summary>
        /// <param name="jsonObject"></param>
        /// <returns></returns>
        private static Dictionary<string, object> ConvertJObjectToDictionary(JObject jsonObject)
        {
            Dictionary<string, object> result = new ();
            foreach (var property in jsonObject.Properties())
            {
                if (property.Value is JObject nestedObject)
                {
                    result[property.Name] = ConvertJObjectToDictionary(nestedObject);
                }
                else if (property.Value is JArray array)
                {
                    result[property.Name] = array.ToObject<List<object>>();
                }
                else
                {
                    result[property.Name] = property.Value.ToObject<object>();
                }
            }
            return result;
        }
    }
}
