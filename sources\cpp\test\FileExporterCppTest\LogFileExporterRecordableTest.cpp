#include "LogFileExporterRecordable.h"

#include <gtest/gtest.h>
#include <opentelemetry/common/timestamp.h>
#include <opentelemetry/logs/severity.h>
#include <opentelemetry/nostd/span.h>
#include <opentelemetry/nostd/string_view.h>
#include <opentelemetry/sdk/instrumentationscope/instrumentation_scope.h>
#include <opentelemetry/sdk/logs/exporter.h>
#include <opentelemetry/sdk/resource/resource.h>
#include <opentelemetry/trace/span_id.h>
#include <opentelemetry/trace/trace_flags.h>
#include <opentelemetry/trace/trace_id.h>

#include <chrono>
#include <string>

namespace sdklogs       = opentelemetry::sdk::logs;
namespace logs_api      = opentelemetry::logs;
namespace nostd         = opentelemetry::nostd;
using json              = nlohmann::json;
using namespace Microsoft::M365::Exporters;

TEST(LogFileExporterRecordableTest, BasicTests)
{
  const auto severity = logs_api::Severity::kFatal;
  const std::array<nostd::string_view, 2> stringlist{
      {nostd::string_view("string1"), nostd::string_view("string2")}};

  const std::int64_t expected_observed_ts = 1732063944999647774LL;
  const std::string expected_timestamp("1732063944999647");
  const std::string expected_severity(
      opentelemetry::logs::SeverityNumToText[static_cast<std::size_t>(severity)]);
  const std::string expected_body("Body of the log message");
  const std::string expected_scope_name("scope_name");
  const bool expected_boolean  = false;
  const int expected_int       = 1;
  const double expected_double = 2.0;

  const opentelemetry::trace::TraceId trace_id(std::array<const uint8_t, opentelemetry::trace::TraceId::kSize>(
      {0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1}));
  const opentelemetry::trace::SpanId span_id(
      std::array<const uint8_t, opentelemetry::trace::SpanId::kSize>({0, 0, 0, 0, 0, 0, 0, 2}));
  opentelemetry::trace::TraceFlags trace_flag(opentelemetry::trace::TraceFlags::kIsSampled);
  // TODO(jiayiwang): Some bug in AttributeMap with variant double deletes the AttributeMap or its content,
  // causing crash in ~Resource. Avoid the use of non empty resource. Find some other way to add commin dim.
  // Raise to Tom.
  auto resource = opentelemetry::sdk::resource::Resource::GetEmpty();

  const nlohmann::json expected{
      {"@timestamp", expected_timestamp},
      {"boolean", expected_boolean},
      {"double", expected_double},
      {"event_id", 123},
      {"event_name", "TestEvent"},
      {"int", expected_int},
      {"log", {{"level", expected_severity}, {"logger", expected_scope_name}}},
      {"message", expected_body},
      {"observedtimestamp", expected_observed_ts},
      {"stringlist", {stringlist[0], stringlist[1]}},
      {"traceid", "00000000000000000000000000000001"},
      {"spanid", "0000000000000002"},
      {"traceflags", "01"},
  };

  const opentelemetry::common::SystemTimestamp now{std::chrono::nanoseconds(expected_observed_ts)};

  const auto scope =
      opentelemetry::sdk::instrumentationscope::InstrumentationScope::Create(expected_scope_name);

  LogFileExporterRecordable recordable;
  recordable.SetTimestamp(now);
  recordable.SetObservedTimestamp(now);
  recordable.SetSeverity(severity);
  recordable.SetBody(expected_body);
  recordable.SetInstrumentationScope(*scope);
  recordable.SetEventId(123, "TestEvent");
  recordable.SetTraceId(trace_id);
  recordable.SetSpanId(span_id);
  recordable.SetTraceFlags(trace_flag);
  recordable.SetResource(resource);

  recordable.SetAttribute("boolean", expected_boolean);
  recordable.SetAttribute("int", expected_int);
  recordable.SetAttribute("double", expected_double);
  recordable.SetAttribute("stringlist", stringlist);

  const auto actual = recordable.GetJSON();

  EXPECT_EQ(actual, expected);
}