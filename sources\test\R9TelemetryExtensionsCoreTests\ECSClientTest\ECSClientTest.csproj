<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFrameworks>net472;net6.0;net8.0</TargetFrameworks>
        <DocumentationFile>bin\$(Configuration)\$(TargetFramework)\$(AssemblyName).xml</DocumentationFile>
        <Authors>SOTELS</Authors>
        <RootNamespace>Microsoft.M365.Core.Telemetry.ECSClientTest</RootNamespace>
        <AssemblyName>Microsoft.M365.Core.Telemetry.ECSClientTest</AssemblyName>
        <IsCodedUITest>False</IsCodedUITest>
        <TestProjectType>UnitTest</TestProjectType>
        <PlatformTarget>anycpu</PlatformTarget>
        <LangVersion>9</LangVersion>
        <SuppressTfmSupportBuildWarnings>true</SuppressTfmSupportBuildWarnings>
    </PropertyGroup>
    <ItemGroup>
        <ProjectReference
            Include="..\..\..\dev\R9TelemetryExtensionsCore\ECSClient\ECSClient.csproj" />
    </ItemGroup>
    <ItemGroup>
        <PackageReference Include="Microsoft.NET.Test.Sdk" />
        <PackageReference Include="NSubstitute" />
        <PackageReference Include="xunit" />
        <PackageReference Include="xunit.runner.visualstudio" />
        <PackageReference Include="coverlet.collector" />
    </ItemGroup>

</Project>
  