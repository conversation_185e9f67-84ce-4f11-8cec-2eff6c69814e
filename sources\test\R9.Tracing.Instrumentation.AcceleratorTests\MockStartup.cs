﻿// <copyright file="MockStartup.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

#if !NETFRAMEWORK
using System;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.R9.Extensions.Tracing.Http;
using OpenTelemetry.Trace;

namespace Microsoft.M365.Core.Telemetry.R9.Tracing.Instrumentation.Accelerator.Tests
{
    /// <summary>
    /// MockStartup
    /// </summary>
    internal class MockStartup
    {
        /// <summary>
        /// The ConfigureServices
        /// </summary>
        /// <param name="services">The services</param>
        public void ConfigureServices(IServiceCollection services)
        {
            IConfigurationRoot configuration = new ConfigurationBuilder().AddJsonFile("appsettings.json").Build();
            HttpTracingOptionsInherited options = new HttpTracingOptionsInherited
            {
                IsEnabled = true,
            };

            Action<TracerProviderBuilder> action = (builder) =>
            {
                builder.AddHttpTracing(options);
            };

            services.AddDistributedTracingService(configuration.GetSection("Microsoft_m365_core_telemetry:ServiceMetadata"), configuration.GetSection("Microsoft_m365_core_telemetry:Tracing"), action);
        }

        /// <summary>
        /// Configure
        /// </summary>
        /// <param name="app"></param>
        /// <param name="env"></param>
#pragma warning disable CA1801
        public void Configure(IApplicationBuilder app, IWebHostEnvironment env)
#pragma warning restore CA1801
        {
        }
    }
}
#endif