﻿// <copyright file="RandomStrategyTest.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using OpenTelemetry.Logs;
using Xunit;

namespace Microsoft.M365.Core.Telemetry.R9.Logging.Substrate.Test.SamplingTest
{
    public class RandomStrategyTest
    {
        private const string LoggerName = "RandomStrategyTest";

        private const string ConfigTemplate = @"
{
    ""SubstrateLogging"": {
        ""RuleBasedSampler"": {
            ""RandomStrategyTest"": [
                {
                    ""Constraints"": [],
                    ""Strategy"": {
                        ""Type"": ""Random"",
                        ""SampleRate"": ""SampleRatePlaceholder""
                    }
                }
            ]
        }
    }
}";

        private static string ReplaceConfigPlaceholder(double sampleRate)
        {
            return ConfigTemplate.Replace("SampleRatePlaceholder", sampleRate.ToString());
        }

        [Theory]
        [CombinatorialData]
        public async Task MultiThread_WithFullSampling_ShouldSampleAllLogs(
            [CombinatorialValues(10, 100)] int threadCount,
            [CombinatorialValues(1000)] int logsPerThread)
        {
            double expectedRate = 1.0; // 100% sampling rate
            int totalLogs = threadCount * logsPerThread;
            int expectedSampledCount = totalLogs; // No deviation allowed for 100% sampling rate

            var threadResults = new ConcurrentDictionary<int, List<LogRecord>>();
            var config = ReplaceConfigPlaceholder(expectedRate);

            var tasks = Enumerable.Range(0, threadCount)
                .Select(threadId => Task.Run(() =>
                {
                    var threadExportedItems = new List<LogRecord>();
                    var threadLogger = LoggerTestUtils.CreateLoggerWithConfig(config, threadExportedItems, LoggerName);

                    for (int i = 0; i < logsPerThread; i++)
                    {
                        threadLogger.LogInformation("Test message from thread {ThreadId}", threadId);
                    }

                    threadResults[threadId] = threadExportedItems;
                }));

            await Task.WhenAll(tasks);

            int totalSampledCount = threadResults.Values.Sum(items => items.Count);
            Assert.Equal(expectedSampledCount, totalSampledCount);
        }

        [Theory]
        [CombinatorialData]
        public async Task MultiThread_WithNonSampling_ShouldDiscardAllLogs(
        [CombinatorialValues(10, 100)] int threadCount,
        [CombinatorialValues(1000)] int logsPerThread)
        {
            double expectedRate = 0.0; // 0% sampling rate
            int totalLogs = threadCount * logsPerThread;
            int expectedSampledCount = 0;

            var threadResults = new ConcurrentDictionary<int, List<LogRecord>>();
            var config = ReplaceConfigPlaceholder(expectedRate);

            var tasks = Enumerable.Range(0, threadCount)
                .Select(threadId => Task.Run(() =>
                {
                    var threadExportedItems = new List<LogRecord>();
                    var threadLogger = LoggerTestUtils.CreateLoggerWithConfig(config, threadExportedItems, LoggerName);

                    for (int i = 0; i < logsPerThread; i++)
                    {
                        threadLogger.LogInformation("Test message from thread {ThreadId}", threadId);
                    }

                    threadResults[threadId] = threadExportedItems;
                }));

            await Task.WhenAll(tasks);

            int totalSampledCount = threadResults.Values.Sum(items => items.Count);
            Assert.Equal(expectedSampledCount, totalSampledCount);
        }
    }
}
