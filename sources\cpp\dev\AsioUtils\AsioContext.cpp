#include "AsioContext.h"

#include <iostream>

namespace Microsoft {
namespace M365 {
namespace Exporters {

AsioContext::AsioContext() : io_context_(), timer_(io_context_) {
    start_timer();
    for (int i = 0; i < 2; ++i)
    {
        threads_.emplace_back([this]() {
            io_context_.run();
        });
    }
}

void AsioContext::start_timer() {
    timer_.expires_after(std::chrono::hours(1));
    timer_.async_wait([this](const boost::system::error_code&) {
        start_timer();
    });
}

AsioContext& AsioContext::Get() {
    static AsioContext* instance_ptr = new AsioContext();
    return *instance_ptr;
}

boost::asio::io_context& AsioContext::GetIoContext() noexcept {
    return Get().io_context_;
}

void AsioContext::SetThreadCount(int count) noexcept {
    auto& context = Get();
    std::lock_guard<std::mutex> lock(context.mutex_);
    context.io_context_.stop();
    for (auto& thread : context.threads_)
    {
        thread.join();
    }
    context.threads_.clear();
    context.start_timer();
    for (int i = 0; i < count; ++i)
    {
        context.threads_.emplace_back([&context]() {
            context.io_context_.run();
        });
    }
}

} // namespace Exporters
} // namespace M365
} // namespace Microsoft