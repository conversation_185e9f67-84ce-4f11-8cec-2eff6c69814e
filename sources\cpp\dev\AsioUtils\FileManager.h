#pragma once

#include <boost/asio.hpp>
#include <boost/asio/stream_file.hpp>

#include <string>
#include <mutex>
#include <memory>

namespace Microsoft {
namespace M365 {
namespace Exporters {

// Theoretically this class can throw. <PERSON> noexcept to explicity crash the process if an exception is thrown.
// TODO(jiayiwang): Catch and log the exception before going to SDF.
class FileManager {
  public:
    FileManager(boost::asio::io_context& io_context, const std::string& file_path, std::chrono::seconds reset_duration) noexcept;
    void Write(std::shared_ptr<std::string> data) noexcept;
    // Stop timer. Close file.
    void ShutDown() noexcept;

  private:
    void do_write(std::shared_ptr<std::string> data);
    void start_reset_timer();
    void reset_file_position(const boost::system::error_code& error);

    boost::asio::stream_file file_;
    // TODO(jiayiang): Now we lock all function bodies in whole. Refine the lock scope.
    std::mutex mutex_;
    boost::asio::io_context& io_context_;
    boost::asio::strand<boost::asio::io_context::executor_type> strand_;
    boost::asio::steady_timer timer_; // Timer for resetting file position
    std::chrono::seconds reset_duration_; // Duration for resetting file position
};

} // namespace Exporters
} // namespace M365
} // namespace Microsoft
