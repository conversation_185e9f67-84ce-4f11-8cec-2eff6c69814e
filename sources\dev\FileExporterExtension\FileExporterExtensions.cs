﻿// <copyright file="FileExporterExtensions.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System;
using Microsoft.R9.Extensions.SecurityTelemetry;

namespace Microsoft.M365.Core.Telemetry.FileExporter
{
    /// <summary>
    /// Extensions for the <see cref="ODLFileExporter"/> class.
    /// </summary>
    public static class FileExporterExtensions
    {
        /// <summary>
        /// Create a <see cref="ODLFileExporter"/> instance for SecurityRecord to be exported to file.
        /// </summary>
        /// <param name="builder"></param>
        /// <returns></returns>
        public static ISecurityTelemetryPipelineBuilder AddODLFileExporter(this ISecurityTelemetryPipelineBuilder builder)
        {
            _ = builder ?? throw new ArgumentNullException(nameof(builder));
            return builder.WithExporter<ODLFileExporter>();
        }

        /// <summary>
        /// Create a <see cref="ODLFileExporter"/> instance for NRT fallback to disk.
        /// </summary>
        /// <returns>The configured <see cref="ODLFileExporter"/>.</returns>
        public static NRTFileExporter CreatNRTFileExporter()
        {
            var defaultOptions = new TextFileLoggerOptions();

            var fileExporter =
                new NRTFileExporter(new TextFileLogger(defaultOptions, SystemFileManager.Instance));

            return fileExporter;
        }

        /// <summary>
        /// Create a <see cref="ODLFileExporter"/> instance for NRT fallback to disk.
        /// </summary>
        /// <param name="options"><see cref="TextFileLoggerOptions"/>.</param>
        /// <returns>The configured <see cref="ODLFileExporter"/>.</returns>
        public static NRTFileExporter CreatNRTFileExporter(TextFileLoggerOptions options)
        {
            _ = options ?? throw new ArgumentNullException(nameof(options));
            options.Validate();

            var fileExporter =
                new NRTFileExporter(new TextFileLogger(options, SystemFileManager.Instance));

            return fileExporter;
        }
    }
}
