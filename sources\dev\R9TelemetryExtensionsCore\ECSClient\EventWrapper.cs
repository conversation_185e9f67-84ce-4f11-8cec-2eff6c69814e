﻿// <copyright file="EventWrapper.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System;
using System.Diagnostics.CodeAnalysis;
using System.Threading.Tasks;

using Microsoft.M365.Core.Telemetry.SDKLogger;

namespace Microsoft.M365.Core.Telemetry.ECSClient
{
    /// <summary>
    /// EventWrapper
    /// </summary>
    internal class EventWrapper
    {
        /// <summary>
        /// Update config event
        /// </summary>
        public event EventHandler<UpdateConfigEventArgs> UpdateConfig;

        /// <summary>
        /// Notify event has bee triggered
        /// </summary>
        /// <param name="eventArgs">event arguments</param>
        public void Notify(UpdateConfigEventArgs eventArgs)
        {
            OnUpdateConfig(eventArgs);
        }

        /// <summary>
        /// Update configuration
        /// </summary>
        private void OnUpdateConfig(UpdateConfigEventArgs eventArgs)
        {
            if (UpdateConfig != null)
            {
                var eventListeners = UpdateConfig.GetInvocationList();

                foreach (var delegated in eventListeners)
                {
                    try
                    {
                        var myEventHandler = delegated as EventHandler<UpdateConfigEventArgs>;
                        if (myEventHandler != null)
                        {
                            Task.Run(() => myEventHandler(this, eventArgs));
                        }
                    }
                    catch (Exception e)
                    {
                        SDKLog.Error($"Failed when invoke an event listener. {e}");
                    }
                }

                if (!eventArgs.WaitHandleCounter.WaitOne(TimeSpan.FromSeconds(Constants.DefaultBlockedTime)))
                {
                    SDKLog.Warning($"Update config event timeout.");
                }
            }
        }
    }
}
