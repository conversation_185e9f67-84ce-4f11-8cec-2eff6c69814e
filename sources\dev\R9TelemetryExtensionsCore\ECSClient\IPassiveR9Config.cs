﻿// <copyright file="IPassiveR9Config.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

namespace Microsoft.M365.Core.Telemetry.ECSClient
{
    public interface IPassiveR9Config
    {
        /// <summary>
        /// If R9 Event is enabled.
        /// </summary>
        bool R9EventEnabled { get; set; }

        /// <summary>
        /// If R9 Metric is enabled.
        /// </summary>
        bool R9MetricEnabled { get; set; }

        /// <summary>
        /// If Ifx Event is enabled.
        /// </summary>
        bool IfxEventEnabled { get; set; }

        /// <summary>
        /// If Ifx Metric is enabled.
        /// </summary>
        bool IfxMetricEnabled { get; set; }

        /// <summary>
        /// [For debug] If debug info collection is enabled.
        /// </summary>
        bool IsDebugInfoCollectionEnabled { get; set; }

        /// <summary>
        /// [For debug] If mds trace is enabled.
        /// </summary>
        bool MdsTraceEnabled { get; set; }

        /// <summary>
        /// [For debug] EventId list that included in TraceLog.
        /// </summary>
        string IncludedTraceLog { get; set; }

        /// <summary>
        /// Returns if an event is DISABLED for Ifx.
        /// </summary>
        /// <param name="category"></param>
        /// <returns></returns>
        public bool EventDisabledForIfx(string category);

        /// <summary>
        /// Returns if a metric is DISABLED for Ifx.
        /// </summary>
        /// <param name="category"></param>
        /// <returns></returns>
        public bool MetricDisabledForIfx(string category);

        /// <summary>
        /// Returns if an event is enabled for R9.
        /// </summary>
        /// <param name="category"></param>
        /// <returns></returns>
        public bool EventEnabledForR9(string category);

        /// <summary>
        /// Returns if a metric is enabled for R9.
        /// </summary>
        /// <param name="category"></param>
        /// <returns></returns>
        public bool MetricEnabledForR9(string category);
    }
}
