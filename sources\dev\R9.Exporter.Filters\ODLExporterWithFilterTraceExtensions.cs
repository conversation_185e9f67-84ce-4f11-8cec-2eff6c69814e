﻿// <copyright file="ODLExporterWithFilterTraceExtensions.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System;
using System.Diagnostics;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Options;
using Microsoft.R9.Extensions.Diagnostics;
using Microsoft.R9.Extensions.Tracing.Exporters;
using OpenTelemetry.Exporter.Filters;
using OpenTelemetry.Trace;

namespace Microsoft.R9.Extensions.Telemetry.Exporter.Filters;

/// <summary>
/// ODL NRT exporter extension.
/// </summary>
public static class ODLExporterWithFilterTraceExtensions
{
    /// <summary>
    /// Adds ODL NRT exporter as a configuration to the OpenTelemetry ILoggingBuilder.
    /// </summary>
    /// <param name="builder">Logging builder where the exporter will be added.</param>
    /// <param name="configure">ODL exporter extended options to be configured.</param>
    /// <param name="batchExport">determine whether to use batchLogRecordExportProcessor</param>
    /// <param name="filter">filter for exporter</param>
    /// <returns>The instance of <see cref="TracerProviderBuilder"/> to chain the calls.</returns>
    public static TracerProviderBuilder AddODLExporter(this TracerProviderBuilder builder, Action<ODLTraceExporterOptions> configure, bool batchExport, BaseFilter<Activity> filter)
    {
        _ = Throws.IfNull(builder);
        _ = Throws.IfNull(configure);
        _ = builder.ConfigureServices(services => services.Configure(configure));
        return builder.AddODLProcessor(batchExport, filter);
    }

    /// <summary>
    /// Adds ODL NRT exporter as a configuration to the OpenTelemetry ILoggingBuilder.
    /// </summary>
    /// <param name="builder">Logging builder where the exporter will be added.</param>
    /// <param name="configure">ODL exporter extended options to be configured.</param>
    /// <param name="batchExport">determine whether to use batchLogRecordExportProcessor</param>
    /// <param name="sampler">sampler for sampler filter</param>
    /// <returns>The instance of <see cref="TracerProviderBuilder"/> to chain the calls.</returns>
    public static TracerProviderBuilder AddODLExporter(this TracerProviderBuilder builder, Action<ODLTraceExporterOptions> configure, bool batchExport, Sampler sampler)
    {
        _ = Throws.IfNull(builder);
        _ = Throws.IfNull(configure);
        _ = builder.ConfigureServices(services => services.Configure(configure));
        return builder.AddODLProcessor(batchExport, new SamplerFilter(sampler));
    }

    /// <summary>
    /// Extension method to add Geneva exporter.
    /// </summary>
    /// <param name="builder">Logging builder where the exporter will be added.</param>
    /// <param name="section">Configuration section that contains <see cref="ODLTraceExporterOptions"/>.</param>
    /// <param name="batchExport">determine whether to use batchLogRecordExportProcessor</param>
    /// <param name="filter">filter for exporter</param>
    /// <returns>The instance of <see cref="TracerProviderBuilder"/>.</returns>
    /// <exception cref="ArgumentNullException">When <paramref name="builder"/> or <paramref name="section"/> is <see langword="null" />.</exception>
    public static TracerProviderBuilder AddODLExporter(this TracerProviderBuilder builder, IConfigurationSection section, bool batchExport, BaseFilter<Activity> filter)
    {
        _ = Throws.IfNull(builder);
        _ = Throws.IfNull(section);
        _ = builder.ConfigureServices(services => services.Configure<ODLTraceExporterOptions>(section));
        return builder.AddODLProcessor(batchExport, filter);
    }

    /// <summary>
    /// Extension method to add Geneva exporter.
    /// </summary>
    /// <param name="builder">Logging builder where the exporter will be added.</param>
    /// <param name="section">Configuration section that contains <see cref="ODLTraceExporterOptions"/>.</param>
    /// <param name="batchExport">determine whether to use batchLogRecordExportProcessor</param>
    /// <param name="sampler">sampler for sampler filter</param>
    /// <returns>The instance of <see cref="TracerProviderBuilder"/>.</returns>
    /// <exception cref="ArgumentNullException">When <paramref name="builder"/> or <paramref name="section"/> is <see langword="null" />.</exception>
    public static TracerProviderBuilder AddODLExporter(this TracerProviderBuilder builder, IConfigurationSection section, bool batchExport, Sampler sampler)
    {
        _ = Throws.IfNull(builder);
        _ = Throws.IfNull(section);
        _ = builder.ConfigureServices(services => services.Configure<ODLTraceExporterOptions>(section));
        return builder.AddODLProcessor(batchExport, new SamplerFilter(sampler));
    }

    private static TracerProviderBuilder AddODLProcessor(this TracerProviderBuilder builder, bool batchExport, BaseFilter<Activity> filter)
    {
        _ = builder.AddProcessor(sp =>
        {
            var options = sp.GetRequiredService<IOptions<ODLTraceExporterOptions>>();
            if (batchExport)
            {
                return new BatchActivityExportProcessorWithFilter(new ODLTraceExporter(options), filter);
            }
            else
            {
                return new SimpleActivityExportProcessorWithFilter(new ODLTraceExporter(options), filter);
            }
        });
        return builder;
    }
}