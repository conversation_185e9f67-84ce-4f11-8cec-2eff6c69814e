@if "%_echo%"=="" echo off

@REM Installing Quickbuild
SET QUICKBUILD_PATH=%LocalAppData%\CloudBuild
IF NOT EXIST %QUICKBUILD_PATH%\quickbuild.cmd (
  ECHO Downloading Quickbuild
  powershell -NoProfile -Command "Set-ExecutionPolicy Bypass -Scope Process -Force; iex ((New-Object System.Net.WebClient).DownloadString('https://aka.ms/qbootstrap'))"
)

SET "PATH=%QUICKBUILD_PATH%;%PATH%"
set failInit=False
ECHO Installing the credential scanner hook
set /a "x = 0"
set hooksInstalled=False
:install_githook
  if %x% leq 2 (
    build\GitHooks\install.bat | find "Git hooks successfully installed!" && set /a "x = 2" && set hooksInstalled=True
    set /a "x = x + 1"
    goto :install_githook
  )

IF /I NOT "%hooksInstalled%"=="True" (
    build\GitHooks\install.bat | find "Build FAILED" && set failInit=True
    build\GitHooks\install.bat
    ECHO Git hooks failed to install, please investigate.
    goto :exitNow
    
)

ECHO Git hooks successfully installed!

ECHO Default initialization complete.

ECHO If you would like to add any customization like aliases, use public\Build\CustomSetup.cmd

goto :run_customsetup

:run_customsetup
    public\Build\CustomSetup.cmd

:exitNow
IF /I "%failInit%"=="True" (
Exit /b 1