﻿// <copyright file="BaseFileLoggerOptions.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System;

namespace Microsoft.M365.Core.Telemetry.FileExporter
{
    /// <summary>
    /// Base File logging options.
    /// </summary>
    public class BaseFileLoggerOptions
    {
        /// <summary>
        /// Gets or sets the directory where to store log files.
        /// </summary>
        /// <remarks>Default to the current working directory.</remarks>
        public string Directory { get; set; } = string.Empty;

        /// <summary>
        /// Gets or sets log file name.
        /// </summary>
        /// <remarks>Default to "application.log".</remarks>
        public string FileName { get; set; } = "application";

        /// <summary>
        /// Gets or sets log file extension.
        /// </summary>
        /// <remarks>Default to "application.log".</remarks>
        public string FileExtension { get; set; } = ".log";

        /// <summary>
        /// Gets or sets a value indicating whether to enable archived files clean up or not.
        /// </summary>
        /// <remarks>Default to false.</remarks>
        public bool EnableCleanUpArchivedFiles { get; set; } = false;

        /// <summary>
        /// Gets or sets the datetime pattern for rotated files.
        /// </summary>
        /// <remarks>Default to "yyyy_MM_dd_HH".</remarks>
        public string FileDateTimePattern { get; set; } = "yyyy_MM_dd_HH";

        /// <summary>
        /// Gets or sets the max retention <see cref="TimeSpan"/>.
        /// </summary>
        /// <remarks>Default to 30 days.</remarks>
        public TimeSpan RetentionInterval { get; set; } = TimeSpan.FromDays(30);

        /// <summary>
        /// Gets or sets the max retention count.
        /// </summary>
        /// <remarks>Default to 99 files.</remarks>
        public long RetentionCount { get; set; } = 99;

        /// <summary>
        /// Gets or sets a value indicating whether to enable batch processing.
        /// </summary>
        /// <remarks>Default to true.</remarks>
        /// <remarks>
        /// Enable batch processing.
        /// By enabling batch processing, logs will be written to files at a lower frequency to reduce IO operations. This is recommended in the production environment.
        /// </remarks>
        public bool EnableBatchProcessing { get; set; } = true;

        private static readonly char[] invalidCharsForFileName = { '/', '\\', '|', '?', '*', '<', '>', ':' };

        /// <summary>
        /// Check invalid values specified in options.
        /// </summary>
        internal void Validate()
        {
            if (FileDateTimePattern.IndexOfAny(invalidCharsForFileName) != -1)
            {
                throw new ArgumentException(
                    "Invalid FileDateTimePattern specified, should not contain any of /\\|*?<>");
            }
        }
    }
}
