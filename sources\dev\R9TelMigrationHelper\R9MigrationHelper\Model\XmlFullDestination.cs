﻿// <copyright file="XmlFullDestination.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Kusto.Cloud.Platform.Security;

namespace R9MigrationHelper.Model
{
    /// <summary>
    /// Full destination for XML drop.
    /// </summary>
    public class XmlFullDestination
    {
        string root = string.Empty;
        string destination = string.Empty;

        /// <summary>
        /// Root, i.e. DATACENTER, COMPONENTS, etc.
        /// </summary>
        public string Root
        {
            get { return root; }
            set { root = value; }
        }

        /// <summary>
        /// Destination path.
        /// </summary>
        public string Destination
        {
            get { return destination; }
            set { destination = value; }
        }

        /// <summary>
        /// Equals.
        /// </summary>
        /// <param name="obj">object</param>
#pragma warning disable CS8600, CS8602, CS8765 // Converting null literal or possible null value to non-nullable type.
        public override bool Equals(object obj)
        {
            if (obj == null || this == null) { return false; }
            if (obj is XmlFullDestination)
            {
                XmlFullDestination otherXmlFullDestination = obj as XmlFullDestination;
                return otherXmlFullDestination.Root == this.Root && otherXmlFullDestination.Destination == this.Destination;
#pragma warning restore CS8600, CS8602, CS8765 // Converting null literal or possible null value to non-nullable type.
            }
            return false;
        }

        /// <summary>
        /// GetHashCode.
        /// </summary>
        public override int GetHashCode()
        {
            int hash = 13;
            hash = (hash * 7) + Root.GetHashCode(StringComparison.InvariantCulture);
            hash = (hash * 7) + Destination.GetHashCode(StringComparison.InvariantCulture);
            return hash;
        }
    }
}
