﻿// <copyright file="AssemblyModel.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace R9MigrationHelper.Model
{
    /// <summary>
    /// AssemblyModel.
    /// </summary>
    public class AssemblyModel
    {
        string name = string.Empty;
        string version = string.Empty;
        string sourcePath = string.Empty;
        string packageName = string.Empty;
        string packageVersion = string.Empty;
        string publicKeyToken = string.Empty;
        HashSet<XmlFullDestination> dropDestinations = new HashSet<XmlFullDestination>();
        string oldVersion = string.Empty;
        HashSet<string> sourcePathCandidates = new HashSet<string>();
        string possibleSourcePaths = string.Empty;
        Dictionary<string, HashSet<SourcePathRelatedInfo>> sourcePathCandidatesInfo = new Dictionary<string, HashSet<SourcePathRelatedInfo>>(); // sourcePath, <packageName, packageVersion, AssemblyVersion, TopPackageName>
        string binPlaceSourcePath = string.Empty;
        List<string> existedDropSourcePaths = new List<string>();

        /// <summary>
        /// Assembly Name.
        /// </summary>
        public string Name
        {
            get { return name; }
            set { name = value; }
        }

        /// <summary>
        /// Assembly Version.
        /// </summary>
        public string Version
        {
            get { return version; }
            set { version = value; }
        }

        /// <summary>
        /// Assembly SourcePath.
        /// </summary>
        public string SourcePath
        {
            get { return sourcePath; }
            set { sourcePath = value; }
        }

        /// <summary>
        /// Package Name.
        /// </summary>
        public string PackageName
        {
            get { return packageName; }
            set { packageName = value; }
        }

        /// <summary>
        /// Package Version.
        /// </summary>
        public string PackageVersion
        {
            get { return packageVersion; }
            set { packageVersion = value; }
        }

        /// <summary>
        /// Assembly PublicKeyToken.
        /// </summary>
        public string PublicKeyToken
        {
            get { return publicKeyToken; }
            set { publicKeyToken = value; }
        }

        /// <summary>
        /// Assembly drop destinations.
        /// </summary>
        public HashSet<XmlFullDestination> DropDestinations
        {
            get { return dropDestinations; }
            set { dropDestinations = value; }
        }

        /// <summary>
        /// Assembly Old Version.
        /// </summary>
        public string OldVersion
        {
            get { return oldVersion; }
            set { oldVersion = value; }
        }

        /// <summary>
        /// Source Path Candidates.
        /// </summary>
        public HashSet<string> SourcePathCandidates
        {
            get { return sourcePathCandidates; }
            set { sourcePathCandidates = value; }
        }

        /// <summary>
        /// Possible Source Paths.
        /// </summary>
        public string PossibleSourcePaths
        {
            get { return possibleSourcePaths; }
            set { possibleSourcePaths = value; }
        }

        /// <summary>
        /// Source Path Candidates.
        /// </summary>
        public Dictionary<string, HashSet<SourcePathRelatedInfo>> SourcePathCandidatesInfo
        {
            get { return sourcePathCandidatesInfo; }
            set { sourcePathCandidatesInfo = value; }
        }

        /// <summary>
        /// BinPlaceSourcePath.
        /// </summary>
        public string BinPlaceSourcePath
        {
            get { return binPlaceSourcePath; }
            set { binPlaceSourcePath = value; }
        }

        /// <summary>
        /// ExsitedDropSourcePaths.
        /// </summary>
        public List<string> ExsitedDropSourcePaths
        {
            get { return existedDropSourcePaths; }
            set { existedDropSourcePaths = value; }
        }

        /// <summary>
        /// Assembly ToString.
        /// </summary>
        public override string ToString()
        {
            return $"Assembly Name: {name}\tAssembly Version: {version}\tPackage Name: {packageName}\tSource Path: {sourcePath}\n";
        }
    }
}
