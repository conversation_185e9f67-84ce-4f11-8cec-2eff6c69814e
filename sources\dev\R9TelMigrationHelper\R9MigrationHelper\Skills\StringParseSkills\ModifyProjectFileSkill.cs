﻿// <copyright file="ModifyProjectFileSkill.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml;
using System.Xml.Linq;
using R9MigrationHelper.Model;

namespace R9MigrationHelper.Skills.StringParseSkills
{
    /// <summary>
    /// ModifyProjectFileSkill.
    /// </summary>
    public class ModifyProjectFileSkill
    {
        /// <summary>
        /// Add package reference to project file.
        /// </summary>
        /// <param name="projectFileContent"></param>
        /// <param name="packageList"></param>
        /// <returns></returns>
        public string AddPackageReference(string projectFileContent, List<string> packageList)
        {
            string res = projectFileContent;
            if (string.IsNullOrEmpty(projectFileContent) || packageList.Count == 0)
            {
                return projectFileContent;
            }
            HashSet<string> packagesImported = new HashSet<string>();
            try
            {
                var projDoc = XDocument.Parse(projectFileContent);
                var ns = projDoc.Root!.Name.Namespace;
                packagesImported = (
                        from pkgRef in projDoc.Element(ns + "Project")!.Descendants(ns + "PackageReference")
                        let includeValue = pkgRef.Attribute("Include")?.Value
                        where !string.IsNullOrEmpty(includeValue)
                        select includeValue).ToHashSet(StringComparer.OrdinalIgnoreCase);
            }
            catch (Exception e)
            {
                Console.WriteLine($"Not able to extract imported package from project file, error: {e.Message}");
            }

            HashSet<string> packageDroped = packageList.ToHashSet();
            HashSet<string> packageMissed = packageDroped.Except(packagesImported).ToHashSet();

            try
            {
                XmlDocument projDoc = new XmlDocument();
                projDoc.LoadXml(projectFileContent);
                XmlNode itemGrop = projDoc.CreateNode(XmlNodeType.Element, "ItemGroup", string.Empty);
                foreach (string pkg in packageMissed)
                {
                    XmlNode pkgRef = projDoc.CreateNode(XmlNodeType.Element, "PackageReference", string.Empty);

                    XmlAttribute include = projDoc.CreateAttribute("Include");
                    include.Value = pkg;
                    pkgRef.Attributes!.Append(include);

                    XmlAttribute noWarn = projDoc.CreateAttribute("NoWarn");
                    noWarn.Value = "NU1603,NU1605";
                    pkgRef.Attributes!.Append(noWarn);

                    XmlAttribute privateAssets = projDoc.CreateAttribute("PrivateAssets");
                    privateAssets.Value = "All";
                    pkgRef.Attributes!.Append(privateAssets);

                    XmlAttribute excludeAssets = projDoc.CreateAttribute("ExcludeAssets");
                    excludeAssets.Value = "All";
                    pkgRef.Attributes!.Append(excludeAssets);

                    itemGrop.AppendChild(pkgRef);
                }
                projDoc.SelectSingleNode("Project")!.AppendChild(itemGrop);
                res = XElement.Parse(projDoc.OuterXml).ToString();
            }
            catch (Exception e)
            {
                Console.WriteLine($"Not able to add package reference to project file, error: {e.Message}");
            }

            return res;
        }
    }
}
