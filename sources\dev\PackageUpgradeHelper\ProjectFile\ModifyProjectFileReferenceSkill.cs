﻿// <copyright file="ModifyProjectFileReferenceSkill.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml;
using System.Xml.Linq;
using PackageUpgradeHelper.ADO;

namespace PackageUpgradeHelper.ProjectFile
{
    /// <summary>
    /// Modify project file
    /// Including .csproj .targets .nuget.proj
    /// </summary>
    class ModifyProjectFileReferenceSkill
    {
        /// <summary>
        /// Update hint path
        /// This api will be covered by SubstrateCLI tool:
        /// e.g. substrate-cli hintpaths update by-package -n Microsoft.R9.Extensions.ClusterMetadata.Cosmic -v 8.11.0
        /// </summary>
        /// <param name="fileInfo"></param>
        /// <param name="targetDll"></param>
        /// <param name="newHintPath"></param>
        /// <param name="branch"></param>
        /// <returns></returns>
        public string UpdateHintPath(ADOFileInfo fileInfo, string targetDll, string newHintPath, string branch)
        {
            var getFileContentSkill = new GetFileContentSkill();
            var odlContent = getFileContentSkill.GetFileContent(fileInfo.Path, branch).Result;
            var xml = new XmlDocument();
            xml.LoadXml(odlContent);
            var root = xml.SelectSingleNode("Project");
            if (root != null)
            {
                root.SelectNodes("ItemGroup")?.Cast<XmlNode>().ToList().ForEach(node =>
                {
                    node.SelectNodes("Reference")?.Cast<XmlNode>().ToList().ForEach(reference =>
                    {
                        var include = reference.Attributes?["Include"]?.Value;
                        var hintPath = reference.SelectSingleNode("HintPath");
                        if (hintPath != null)
                        {
                            if (include != null && targetDll.StartsWith(include))
                            {
                                hintPath.InnerText = newHintPath;
                            }
                        }
                        else
                        {
                            var dllName = include?.Substring(include.LastIndexOf("/") + 1);
                            if (dllName != null && dllName.Equals(targetDll))
                            {
                                node.Attributes!["include"]!.Value = newHintPath;
                            }
                        }
                    });
                });
            }
            return XDocument.Parse(xml.OuterXml).ToString();
        }

        /// <summary>
        /// Add new package reference
        /// </summary>
        /// <param name="fileInfo"></param>
        /// <param name="rootPackages"></param>
        /// <param name="hintPath"></param>
        /// <param name="branch"></param>
        /// <returns></returns>
        public string AddNewPackageReference(ADOFileInfo fileInfo, List<string> rootPackages, string hintPath, string branch)
        {
            var getFileContentSkill = new GetFileContentSkill();
            var odlContent = getFileContentSkill.GetFileContent(fileInfo.Path, branch).Result;
            var csproj = new Csproj(odlContent);
            if (!csproj.HasRelatedReferences(rootPackages))
            {
                return string.Empty;
            }
            csproj.AddReferenceForNewPackage(hintPath);
            return csproj.ToString();
        }
    }

    /// <summary>
    /// Csproj for adding new introduced package reference
    /// </summary>
    internal class Csproj
    {
        /// <summary>
        /// Doc
        /// </summary>
        public XDocument Doc { get; set; }

        /// <summary>
        /// IsSDKStyle
        /// </summary>
        public bool IsSDKStyle { get; set; }

        /// <summary>
        /// IsScriptSharp
        /// </summary>
        public bool IsScriptSharp { get; set; }

        /// <summary>
        /// Whether xml document needs encoding declaration when save as string
        /// </summary>
        public bool NeedEncoding { get; set; }

        /// <summary>
        /// Whether this csproj use script sharp
        /// </summary>
        public bool UseScriptSharp { get; set; }

        /// <summary>
        /// Constructor
        /// </summary>
        /// <param name="content"></param>
        /// <param name="path"></param>
        public Csproj(string content)
        {
            Doc = XDocument.Parse(content);
            NeedEncoding = content.Contains("encoding=\"utf-8\"");
            CheckProjectStyle();
            CheckUseScriptSharp();
        }

        private void CheckProjectStyle()
        {
            var root = Doc.Descendants().Where(e => e.Name == "Project");
            IsSDKStyle = root.Attributes().Any(a => a.Name == "Sdk");
        }

        private void CheckUseScriptSharp()
        {
            var nodes = Doc.Descendants().Where(e => e.Name.LocalName == "UseScriptSharp").ToList();
            if (nodes.Count == 0)
            {
                UseScriptSharp = false;
            }
            else
            {
                UseScriptSharp = Boolean.Parse(nodes.First().Value);
            }
        }

        /// <summary>
        /// Check whether this csproj has exactly matched related package
        /// Scan AssemblyRef, ProjectReference, Reference, PackageReference
        /// </summary>
        /// <param name="rootPackages"></param>
        /// <returns></returns>
        public bool HasRelatedReferences(List<string> rootPackages)
        {
            var nodesWithInclude = Doc.Descendants().Where(e => e.Attribute("Include") != null).ToList(); // get all nodes contain Include attribute
            foreach (var node in nodesWithInclude)
            {
                var attr = node.Attribute("Include")!.Value;
                switch (node.Name.LocalName)
                {
                    case "AssemblyRef" or "WsdlGeneratorDependencies":
                        if (rootPackages.Any(d => attr.EndsWith($"{d}.dll", StringComparison.OrdinalIgnoreCase))) // end with dll
                        {
                            return true;
                        }
                        break;
                    case "ProjectReference":
                        if (rootPackages.Any(d => attr.EndsWith($"{d}.csproj", StringComparison.OrdinalIgnoreCase))) // end with csproj
                        {
                            return true;
                        }
                        break;
                    case "Reference":
                        if (rootPackages.Any(d => attr.EndsWith(d, StringComparison.OrdinalIgnoreCase))) // end with nothing
                        {
                            return true;
                        }
                        break;
                    default:
                        break;
                }
            }
            return false;
        }

        /// <summary>
        /// Add reference for new package
        /// </summary>
        /// <param name="hintPath"></param>
        public void AddReferenceForNewPackage(string hintPath)
        {
            if (!IsSDKStyle)
            {
                if (UseScriptSharp)
                {
                    AddScriptSharpReference(hintPath);
                }
                else
                {
                    AddReference(hintPath);
                }
            }
        }

        private void AddPackageReference(string dllName)
        {
            var doc = Doc;
            bool exist = doc.Descendants().Any(d => d.Attributes("Include").Equals(dllName));
            if (exist)
            {
                return;
            }
            XNamespace rootNamespace = doc.Root!.Name.Namespace;
            var itemGroupNodes = doc.Descendants()
                    .Where(e => e.Name.LocalName == "ItemGroup" && e.Descendants().Any(d => d.Name.LocalName == "PackageReference"))
                    .ToList();
            var targetNode = new XElement(rootNamespace + "PackageReference");
            targetNode.SetAttributeValue("Include", dllName);
            if (itemGroupNodes.Count == 0) // There is no package reference in doc
            {
                var itemGroupNode = new XElement(rootNamespace + "ItemGroup");
                itemGroupNode.Attributes().Where(a => a.Name.LocalName == "xmlns").Remove();
                doc.Root!.Add(itemGroupNode);
                itemGroupNode.Add(targetNode);
            }
            else
            {
                var anyNotConditional = itemGroupNodes.Any(g => !g.Attributes().Any(a => a.Name.LocalName == "Condition"));
                if (anyNotConditional) // Add reference to first item group which is not conditional
                {
                    var itemGroupNode = itemGroupNodes.Where(g => !g.Attributes().Any(a => a.Name.LocalName == "Condition")).First();
                    itemGroupNode.Add(targetNode);
                }
                else // Add reference to all item groups
                {
                    foreach (var itemGroupNode in itemGroupNodes)
                    {
                        itemGroupNode.Add(targetNode);
                    }
                }
            }
        }

        private void AddReference(string dllPath)
        {
            var doc = Doc;
            bool exist = doc.Descendants().Any(d => d.Attributes("Include").Equals(dllPath));
            if (exist)
            {
                return;
            }
            XNamespace rootNamespace = doc.Root!.Name.Namespace;
            var itemGroupNodes = doc.Descendants()
                                .Where(e => e.Name.LocalName == "ItemGroup" && e.Descendants().Any(d => d.Name.LocalName == "Reference"))
                                .ToList();
            var targetNode = new XElement(rootNamespace + "Reference");
            targetNode.SetAttributeValue("Include", dllPath);

            if (itemGroupNodes.Count == 0) // There is no package reference in doc
            {
                var itemGroupNode = new XElement(rootNamespace + "ItemGroup");
                itemGroupNode.Attributes().Where(a => a.Name.LocalName == "xmlns").Remove();
                doc.Root!.Add(itemGroupNode);
                itemGroupNode.Add(targetNode);
            }
            else
            {
                var anyNotConditional = itemGroupNodes.Any(g => !g.Attributes().Any(a => a.Name.LocalName == "Condition"));
                if (anyNotConditional) // Add reference to first item group which is not conditional
                {
                    var itemGroupNode = itemGroupNodes.Where(g => !g.Attributes().Any(a => a.Name.LocalName == "Condition")).First();
                    itemGroupNode.Add(targetNode);
                }
                else // Add reference to all item groups
                {
                    foreach (var itemGroupNode in itemGroupNodes)
                    {
                        itemGroupNode.Add(targetNode);
                    }
                }
            }
        }

        private void AddScriptSharpReference(string hintPath)
        {
            var doc = Doc;
            bool exist = doc.Descendants().Any(d => d.Attributes("Include").Equals(hintPath));
            if (exist)
            {
                return;
            }
            XNamespace rootNamespace = doc.Root!.Name.Namespace;
            var itemGroupNodes = doc.Descendants()
                                .Where(e => e.Name.LocalName == "ItemGroup" && e.Descendants().Any(d => d.Name.LocalName == "WsdlGeneratorDependencies"))
                                .ToList();
            var targetNode = new XElement(rootNamespace + "WsdlGeneratorDependencies");
            targetNode.SetAttributeValue("Include", hintPath);

            if (itemGroupNodes.Count == 0) // There is no package reference in doc
            {
                var itemGroupNode = new XElement(rootNamespace + "ItemGroup");
                itemGroupNode.Attributes().Where(a => a.Name.LocalName == "xmlns").Remove();
                doc.Root!.Add(itemGroupNode);
                itemGroupNode.Add(targetNode);
            }
            else
            {
                var anyNotConditional = itemGroupNodes.Any(g => !g.Attributes().Any(a => a.Name.LocalName == "Condition"));
                if (anyNotConditional) // Add reference to first item group which is not conditional
                {
                    var itemGroupNode = itemGroupNodes.Where(g => !g.Attributes().Any(a => a.Name.LocalName == "Condition")).First();
                    itemGroupNode.Add(targetNode);
                }
                else // Add reference to all item groups
                {
                    foreach (var itemGroupNode in itemGroupNodes)
                    {
                        itemGroupNode.Add(targetNode);
                    }
                }
            }
        }

        private string ToStringWithDeclaration(XDocument doc)
        {
            if (doc == null)
            {
                throw new ArgumentNullException("doc");
            }

            StringBuilder builder = new StringBuilder();
            using (TextWriter writer = new Utf8Writer(builder))
            {
                doc.Save(writer);
            }
            return builder.ToString();
        }

        /// <summary>
        /// ToString
        /// </summary>
        /// <returns></returns>
        public override string ToString()
        {
            return NeedEncoding ? ToStringWithDeclaration(Doc) : Doc.ToString();
        }

        private sealed class Utf8Writer : StringWriter
        {
#pragma warning disable CA1305 // Specify IFormatProvider
            public Utf8Writer(StringBuilder sb) : base(sb)
            {
            }
#pragma warning restore CA1305 // Specify IFormatProvider

            public override Encoding Encoding => Encoding.UTF8;
        }
    }
}
