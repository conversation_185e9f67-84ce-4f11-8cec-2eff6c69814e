﻿// <copyright file="TestUtils.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.Diagnostics.CodeAnalysis;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Threading.Tasks;
using Xunit;

namespace Microsoft.M365.Core.Telemetry.ECSClientTest
{
    /// <summary>
    /// Test utilities.
    /// </summary>
    [ExcludeFromCodeCoverage]
    public static class TestUtils
    {
        #region PrivateStatic Field

        /// <summary>
        /// Get private static field.
        /// </summary>
        /// <typeparam name="T">The type of instance.</typeparam>
        /// <param name="o"> The object. </param>
        /// <param name="name"> The name. </param>
        public static T GetPrivateStaticField<T>(object o, string name)
        {
            BindingFlags flags = BindingFlags.Static | BindingFlags.NonPublic;
            if (o is Type)
            {
                FieldInfo fiInfo = ((Type)o).GetField(name, flags);
                return (T)fiInfo.GetValue(null);
            }
            else
            {
                FieldInfo fiInfo = o.GetType().GetField(name, flags);
                return (T)fiInfo.GetValue(o);
            }
        }

        /// <summary>
        /// Set private static field.
        /// </summary>
        /// <param name="t"> The type. </param>
        /// <param name="name"> The name. </param>
        /// <param name="value"> The value. </param>
        public static void SetPrivateStaticField(Type t, string name, object value)
        {
            FieldInfo fiInfo = t.GetField(name, BindingFlags.Static | BindingFlags.NonPublic);
            fiInfo.SetValue(null, value);
        }

        #endregion

        #region Private Field

        /// <summary>
        /// Get private field.
        /// </summary>
        /// <typeparam name="T">The type of instance.</typeparam>
        /// <param name="o"> The object. </param>
        /// <param name="name"> The name. </param>
        public static T GetPrivateField<T>(object o, string name)
        {
            FieldInfo fiInfo = o.GetType().GetField(name, BindingFlags.NonPublic | BindingFlags.Instance);
            return (T)fiInfo.GetValue(o);
        }

        /// <summary>
        /// Set private field.
        /// </summary>
        /// <param name="o"> The object. </param>
        /// <param name="name"> The name. </param>
        /// <param name="value"> The value. </param>
        public static void SetPrivateField(object o, string name, object value)
        {
            FieldInfo fiInfo = o.GetType().GetField(name, BindingFlags.Instance | BindingFlags.NonPublic);
            fiInfo.SetValue(o, value);
        }
        #endregion

        #region PrivateStatic Method

        /// <summary>
        /// Invoke private static method.
        /// </summary>
        /// <typeparam name="T">The type of instance.</typeparam>
        /// <param name="type"> The class name. </param>
        /// <param name="methodName"> The method name. </param>
        /// <param name="param"> The parameters. </param>
        public static T InvokePrivateStaticMethod<T>(Type type, string methodName, params object[] param)
        {
            MethodInfo mi = GetPrivateStaticMethod(type, methodName);
            return (T)mi.Invoke(null, param);
        }

        private static MethodInfo GetPrivateStaticMethod(object o, string name)
        {
            Type t = o is Type ? o as Type : o.GetType();

            return t.GetMethod(name, BindingFlags.Static | BindingFlags.NonPublic);
        }
        #endregion

        #region Private Method

        /// <summary>
        /// Invoke private method.
        /// </summary>
        /// <typeparam name="T">The type of instance.</typeparam>
        /// <param name="o"> The object. </param>
        /// <param name="methodName"> The method name. </param>
        /// <param name="methodParams"> The parameters. </param>
        public static T InvokePrivateMethod<T>(object o, string methodName, params object[] methodParams)
        {
            MethodInfo dynMethod = GetPrivateMethod(o.GetType(), methodName);
            return (T)dynMethod.Invoke(o, methodParams);
        }

        private static MethodInfo GetPrivateMethod(object o, string name)
        {
            Type t = o is Type ? o as Type : o.GetType();

            return t.GetMethod(name, BindingFlags.Instance | BindingFlags.NonPublic);
        }
        #endregion
    }
}
