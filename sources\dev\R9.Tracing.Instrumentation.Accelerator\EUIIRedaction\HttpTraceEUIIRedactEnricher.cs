﻿// <copyright file="HttpTraceEUIIRedactEnricher.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

#if !NETFRAMEWORK
namespace Microsoft.M365.Core.Telemetry.R9.Tracing.Instrumentation.Accelerator
{
    using System.Diagnostics;
    using Microsoft.AspNetCore.Http;
    using Microsoft.R9.Extensions.Tracing.Http;

    /// <summary>
    /// HttpTraceEUIIRedactEnricher
    /// </summary>
    internal sealed class HttpTraceEUIIRedactEnricher : IHttpTraceEnricher
    {
        private HttpTracingOptionsInherited Options { get; }

        /// <summary>
        /// HttpTraceEUIIRedactEnricher
        /// </summary>
        /// <param name="options"></param>
        public HttpTraceEUIIRedactEnricher(HttpTracingOptionsInherited options)
        {
            this.Options = options;
        }

        /// <summary>
        /// Enrich
        /// </summary>
        /// <param name="activity"></param>
        /// <param name="request"></param>
        public void Enrich(Activity activity, HttpRequest request)
        {
            if (request == null || activity == null)
            {
                return;
            }

            var redactedHttpPath = EUIIRedactor.RedactIngressPath(request.Path, Options.RedactionStrategyType);
            var tagValue = $"/{redactedHttpPath}";

            // This is required before R9 upgrade to V1.27+,  otherwise it will print error log per request comes in.
            activity.SetTag(Constants.HttpRoute, tagValue);
            activity.SetTag(Constants.HttpPath, tagValue);
            activity.SetTag(Constants.HttpUrlBackup, Utility.DeriveHttpUrl(request, redactedHttpPath));
            activity.DisplayName = tagValue;
        }
    }
}
#endif
