﻿// <copyright file="HttpTracingOptionsInherited.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

namespace Microsoft.M365.Core.Telemetry.R9.Tracing.Instrumentation.Accelerator
{
    using Microsoft.R9.Extensions.Tracing.Http;

    /// <summary>
    /// HttpTracingOptionsInherited
    /// </summary>
    public class HttpTracingOptionsInherited : HttpTracingOptions
    {
        /// <summary>
        /// IsEnabled
        /// </summary>
        public bool IsEnabled { get; set; }

        /// <summary>
        /// RedactionStrategyType
        /// </summary>
        public RedactionStrategyType RedactionStrategyType { get; set; } = RedactionStrategyType.Default;
    }
}