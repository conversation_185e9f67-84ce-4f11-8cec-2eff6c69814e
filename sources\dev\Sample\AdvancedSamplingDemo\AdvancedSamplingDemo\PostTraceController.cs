﻿// <copyright file="PostTraceController.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using Microsoft.AspNetCore.Mvc;

namespace Microsoft.M365.Core.Tracing.AdvancedSamplingServer
{
    /// <summary>
    /// post trace controller
    /// </summary>
    public class PostTraceController : ControllerBase
    {
        /// <summary>
        /// client
        /// </summary>
        /// <returns></returns>
        [HttpGet("postserver")]
        public IActionResult Server()
        {
            Console.WriteLine("DyeBasedController::Server");
            return NotFound("PostTraceController::Server");
        }
    }
}
