﻿// <copyright file="LogEventID.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System.Diagnostics.CodeAnalysis;

namespace Microsoft.M365.Core.Telemetry.SDKLogger
{
    /// <summary>
    /// Log event id for logging
    /// </summary>
    [ExcludeFromCodeCoverage]
    internal class LogEventID
    {
        /// <summary>
        /// Event id to indicate the passive sdk infomation
        /// </summary>
        public const int PassiveSDKInfomation = 1001;

        /// <summary>
        /// Event id to indicate the passive sdk metric debug info
        /// </summary>
        public const int PassiveSDKMetricDebugInfomation = 1002;

        /// <summary>
        /// Event id to indicate the passive sdk errors
        /// </summary>
        public const int PassiveSDKError = 6001;
    }
}
