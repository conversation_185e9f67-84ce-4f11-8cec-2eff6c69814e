﻿// <copyright file="DynamicSamplerTest.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System.Collections.Generic;
using System.Diagnostics;
using Microsoft.Extensions.Configuration;
using Microsoft.M365.Core.Telemetry.ECS.DynamicComponent.Samplers;
using Microsoft.M365.Core.Telemetry.ECSClient;
using OpenTelemetry.Trace;
using Xunit;

namespace Microsoft.M365.Core.Telemetry.ECS.DynamicComponentTest
{
    /// <summary>
    /// DynamicSamplerTest
    /// </summary>
    public class DynamicSamplerTest
    {
        private IConfiguration configuration;

        /// <summary>
        /// DynamicSamplerTest
        /// </summary>
        public DynamicSamplerTest()
        {
            var mockConfiguration = new Dictionary<string, string>
            {
                ["Microsoft_m365_core_telemetry:ServiceMetadata:ServiceName"] = "Pop3Test",
                ["Microsoft_m365_core_telemetry:ServiceMetadata:RuntimeModel"] = "ModelB2"
            };

            configuration = new ConfigurationBuilder()
                .AddInMemoryCollection(mockConfiguration)
                .Build();
        }

        /// <summary>
        /// TestShouldSample
        /// </summary>
        [Fact]
        public void TestShouldSample()
        {
            R9TracingConfig config = new R9TracingConfig(configuration);
            DynamicSampler dynamicSampler = new DynamicSampler(config);

            //config R9DTdisable
            SamplingParameters samplingParameters = new SamplingParameters();
            SamplingResult result = dynamicSampler.ShouldSample(samplingParameters);
            Assert.Equal(SamplingDecision.Drop, result.Decision);

            //change to AlwaysOn
            config.R9DTEnabled = true;
            config.ConfigChanged = true;
            config.SamplerType = Constants.DynamicSamplerType.AlwaysOn;
            result = dynamicSampler.ShouldSample(samplingParameters);
            Assert.Equal(SamplingDecision.RecordAndSample, result.Decision);
            Assert.False(config.ConfigChanged);

            //change to AlwaysOff
            config.ConfigChanged = true;
            config.SamplerType = Constants.DynamicSamplerType.AlwaysOff;
            result = dynamicSampler.ShouldSample(samplingParameters);
            Assert.Equal(SamplingDecision.Drop, result.Decision);
        }

        /// <summary>
        /// TestShouldSampleRatioSampler
        /// </summary>
        [Fact]
        public void TestShouldSampleRatioSampler()
        {
            R9TracingConfig config = new R9TracingConfig(configuration);
            DynamicSampler dynamicSampler = new DynamicSampler(config);
            SamplingParameters samplingParameters = new SamplingParameters();
            config.R9DTEnabled = true;

            //change to ratio sampler with TraceSampleRate=0
            config.ConfigChanged = true;
            config.SamplerType = Constants.DynamicSamplerType.RatioBased;
            SamplingResult result = dynamicSampler.ShouldSample(samplingParameters);
            Assert.Equal(SamplingDecision.Drop, result.Decision);

            //ratio sampler with TraceSampleRate=1
            config.TraceSampleRate = 1;
            result = dynamicSampler.ShouldSample(samplingParameters);
            Assert.Equal(SamplingDecision.RecordAndSample, result.Decision);

            //ratio sampler with TraceSampleRate=0.5
            string activityDisplayName = "MyActivityName";
            ActivityKind activityKindServer = ActivityKind.Server;
            config.TraceSampleRate = 0.5F;
            var notSampledtraceId =
                ActivityTraceId.CreateFromBytes(
                    new byte[]
                    {
                      0x8F,
                      0xFF,
                      0xFF,
                      0xFF,
                      0xFF,
                      0xFF,
                      0xFF,
                      0xFF,
                      0,
                      0,
                      0,
                      0,
                      0,
                      0,
                      0,
                      0,
                    });
            samplingParameters = new SamplingParameters(default, notSampledtraceId, activityDisplayName, activityKindServer, null, null);
            result = dynamicSampler.ShouldSample(samplingParameters);
            Assert.Equal(SamplingDecision.Drop, result.Decision);
        }

        /// <summary>
        /// TestShouldSampleParentSampler
        /// </summary>
        [Fact]
        public void TestShouldSampleParentSampler()
        {
            R9TracingConfig config = new R9TracingConfig(configuration);
            DynamicSampler dynamicSampler = new DynamicSampler(config);
            config.R9DTEnabled = true;

            var notSampledtraceId =
                ActivityTraceId.CreateFromBytes(
                    new byte[]
                    {
                      0x8F,
                      0xFF,
                      0xFF,
                      0xFF,
                      0xFF,
                      0xFF,
                      0xFF,
                      0xFF,
                      0,
                      0,
                      0,
                      0,
                      0,
                      0,
                      0,
                      0,
                    });
            string activityDisplayName = "MyActivityName";
            ActivityKind activityKindServer = ActivityKind.Server;
            SamplingParameters samplingParameters = new SamplingParameters(default, notSampledtraceId, activityDisplayName, activityKindServer, null, null);

            //change to parent sampler with AlwaysOff
            config.ConfigChanged = true;
            config.SamplerType = Constants.DynamicSamplerType.ParentBased;
            SamplingResult result = dynamicSampler.ShouldSample(samplingParameters);
            Assert.Equal(SamplingDecision.Drop, result.Decision);

            //change to parent sampler with AlwaysOn
            config.ConfigChanged = true;
            config.ParentRootSamplerType = Constants.DynamicSamplerType.AlwaysOn;
            result = dynamicSampler.ShouldSample(samplingParameters);
            Assert.Equal(SamplingDecision.RecordAndSample, result.Decision);

            //change to parent sampler with ratio
            config.ConfigChanged = true;
            config.ParentRootSamplerType = Constants.DynamicSamplerType.RatioBased;
            result = dynamicSampler.ShouldSample(samplingParameters);
            Assert.Equal(SamplingDecision.Drop, result.Decision);

            //Sampled parent
            samplingParameters = new SamplingParameters(
                parentContext: new ActivityContext(
                    ActivityTraceId.CreateRandom(),
                    ActivitySpanId.CreateRandom(),
                    ActivityTraceFlags.Recorded),
                traceId: notSampledtraceId, name: "Span", kind: ActivityKind.Client);
            result = dynamicSampler.ShouldSample(samplingParameters);
            Assert.Equal(SamplingDecision.RecordAndSample, result.Decision);
        }
    }
}
