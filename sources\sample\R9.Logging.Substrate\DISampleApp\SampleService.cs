// <copyright file="SampleService.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;

namespace DISampleConsoleApp
{
    /// <summary>
    /// SampleService
    /// </summary>
    public class SampleService : IHostedService
    {
        ILogger<SampleService> defaultLogger;

        IConfiguration configuration;

        ILogger CustomizedLogger { get; }

        /// <summary>
        /// SampleService ctor.
        /// </summary>
        /// <param name="defaultLogger"></param>
        /// <param name="configuration"></param>
        /// <param name="loggerFactory"></param>
        public SampleService(ILogger<SampleService> defaultLogger, IConfiguration configuration, ILoggerFactory loggerFactory)
        {
            this.defaultLogger = defaultLogger;
            this.configuration = configuration;
            CustomizedLogger = loggerFactory.CreateLogger("TableA");
        }

        /// <summary>
        /// StartAsync
        /// </summary>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        public Task StartAsync(CancellationToken cancellationToken)
        {
            // Start processing here
            Console.WriteLine("Service is starting...");
            var cnt = 0;
            while (!cancellationToken.IsCancellationRequested)
            {
                Thread.Sleep(5000);

                CustomizedLogger.LogInformation("[DI] Test {cat}: {cnt}. Exported to {1}", "TableA", ++cnt, configuration["SubstrateLogging:CompositeExporter:VirtualTableExports:TableA:0:ExportTable"]);
                CustomizedLogger.TestLogSimple(cnt, "[DI]");
                defaultLogger.LogInformation("[DI] Test {cat}: {cnt}.", "DISampleConsoleApp.SampleService", cnt);
            }
            return Task.CompletedTask;
        }

        /// <summary>
        /// StopAsync
        /// </summary>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        public Task StopAsync(CancellationToken cancellationToken)
        {
            // Clean up any resources here
            Console.WriteLine("Service is stopping...");
            return Task.CompletedTask;
        }
    }
}
