﻿// <copyright file="B2PassiveMetricEnricher.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using Microsoft.R9.Extensions.Enrichment;

namespace Microsoft.M365.Core.Telemetry.Enrichment
{
    /// <summary>
    /// B2PassiveMetricEnricher
    /// </summary>
    public class B2PassiveMetricEnricher : IMetricEnricher
    {
        /// <summary>
        /// Enrich
        /// </summary>
        /// <param name="enrichmentBag"></param>
        public void Enrich(IEnrichmentPropertyBag enrichmentBag)
        {
            enrichmentBag.Add(B2PassiveEnricherDimensions.DeployRing, DimensionValues.DeployRing);
            enrichmentBag.Add(B2PassiveEnricherDimensions.Role, DimensionValues.Role);
            enrichmentBag.Add(B2PassiveEnricherDimensions.Forest, DimensionValues.Forest);
            enrichmentBag.Add(B2PassiveEnricherDimensions.Region, DimensionValues.Region);
            enrichmentBag.Add(B2PassiveEnricherDimensions.AvailabilityGroup, DimensionValues.AvailabilityGroup);
            enrichmentBag.Add(B2PassiveEnricherDimensions.Machine, DimensionValues.Machine);
            enrichmentBag.Add(B2PassiveEnricherDimensions.BuildVersion, DimensionValues.BuildVersion);
            enrichmentBag.Add(B2PassiveEnricherDimensions.MachineProvisioningState, DimensionValues.MachineProvisioningState);
            enrichmentBag.Add(B2PassiveEnricherDimensions.IsR9, true);
        }
    }
}
