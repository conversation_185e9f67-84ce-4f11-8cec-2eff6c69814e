﻿// <copyright file="IManagedServiceCollection.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.R9.Extensions.Metering;

namespace Microsoft.M365.Core.Telemetry.ECSClient
{
    /// <summary>
    /// IManagedServiceCollection
    /// </summary>
    internal interface IManagedServiceCollection
    {
        /// <summary>
        /// GetMeter
        /// </summary>
        /// <typeparam name="T">T</typeparam>
        /// <returns></returns>
        IMeter<T> GetMeter<T>();

        /// <summary>
        /// GetLogger
        /// </summary>
        /// <typeparam name="T">T</typeparam>
        /// <returns></returns>
        ILogger<T> GetLogger<T>();

        /// <summary>
        /// UpdateInstance
        /// </summary>
        /// <param name="option"></param>
        void UpdateInstance(IConfigurationRoot option);
    }
}
