// Disable opentelemetry nostd. Workaround for symple conflict between
// opentelemetry nostd and stl in protobuf.
#include "TcpClient.h"

#include <iostream>

namespace Microsoft {
namespace M365 {
namespace Exporters {

TcpClient::TcpClient(TcpClientOptions options, boost::asio::io_context& io_context, opentelemetry::nostd::shared_ptr<opentelemetry::logs::Logger> logger) noexcept
    : options_(options),
      io_context_(io_context),
      resolver_(io_context),
      socket_(std::make_unique<boost::asio::ip::tcp::socket>(io_context)),
      logger_(logger),
      monitor_timer_(io_context), // Initialize timer with 10 seconds interval
      reconnect_timer_(io_context) // Initialize reconnect timer
{
    start_connect();
    start_monitor_timer(); // Start the timer
}

void TcpClient::ShutDown() noexcept {
    std::lock_guard<std::mutex> lock(mutex_);
    io_context_.stop();
    monitor_timer_.cancel();
    reconnect_timer_.cancel();
    if (is_connected_) {
        socket_->close();
    }
    // Wait for io_context to flush. 100ms should be enough since we cancelled all. Check with OdlTraceExporterTest.InitTest.
    // Without this, when multiple process running this code exit at the same time, some of them crash.
    // TODO(jiayiwang): Find the system resource that is shared between processes and see if we can release it explicitly.
    std::this_thread::sleep_for(std::chrono::milliseconds(100));
}

void TcpClient::start_connect() {
    std::lock_guard<std::mutex> lock(mutex_);
    if (is_connected_) {
        return;
    }
    auto endpoints = resolver_.resolve(options_.host, options_.port);
    logger_->Log(opentelemetry::logs::Severity::kInfo, "Client: Starting connection to " + options_.host + ":" + options_.port);
    boost::asio::async_connect(*socket_, endpoints,
        [this](const boost::system::error_code& error, const boost::asio::ip::tcp::endpoint& /*endpoint*/) {
            handle_connect(error);
        });
}

void TcpClient::handle_connect(const boost::system::error_code& error) {
    std::lock_guard<std::mutex> lock(mutex_);
    if (!error) {
        logger_->Log(opentelemetry::logs::Severity::kInfo, "Client: Connected to server.");
        is_connected_ = true;
    } else {
        logger_->Log(opentelemetry::logs::Severity::kError, "Client: Connect error: " + error.message() + ", code: " + std::to_string(error.value()));
        retry_connect();
    }
}

// Called within the lock.
void TcpClient::retry_connect() {
    reconnect_timer_.expires_after(std::chrono::milliseconds(options_.reconnect_interval_ms));
    reconnect_timer_.async_wait([this](const boost::system::error_code& /*error*/) {
        start_connect();
    });
}

void TcpClient::Send(std::shared_ptr<std::string> data) noexcept {
    do_write(data);
}

void TcpClient::do_write(std::shared_ptr<std::string> data) {
    std::lock_guard<std::mutex> lock(mutex_);
    if (!is_connected_) {
        logger_->Log(opentelemetry::logs::Severity::kError, "Client: Not connected. Dropping message.");
        failure_count_++;
        return;
    }
    boost::asio::async_write(*socket_, boost::asio::buffer(*data),
        [this, data](const boost::system::error_code& error, std::size_t bytes_transferred) {
            handle_write(error, bytes_transferred, data);
        });
}

void TcpClient::handle_write(const boost::system::error_code& error, std::size_t bytes_transferred, std::shared_ptr<std::string> data) {
    std::lock_guard<std::mutex> lock(mutex_);
    if (!error) {
        if (bytes_transferred != data->size()) {
            logger_->Log(opentelemetry::logs::Severity::kError, "Client: Sent partial message to server, bytes transferred: " + std::to_string(bytes_transferred) + ", expected: " + std::to_string(data->size()));
            failure_count_++;
        } else {
            success_count_++;
            success_size_ += bytes_transferred;
        }
    } else {
        if (is_connected_) {
            logger_->Info("Client: Write error. Closing connection and retry connect.");
            socket_->close();
            is_connected_ = false;
            retry_connect();
        }
        failure_count_++;
        logger_->Log(opentelemetry::logs::Severity::kError, "Client: Write error: " + error.message() + ", code: " + std::to_string(error.value()));
        // TODO(jiayiwang): Log connection down time.
    }
}

// Called within the lock.
void TcpClient::start_monitor_timer() {
    monitor_timer_.expires_after(std::chrono::seconds(options_.monitor_interval_s));
    monitor_timer_.async_wait([this](const boost::system::error_code& error) {
        handle_monitor_timer(error);
    });
}

void TcpClient::handle_monitor_timer(const boost::system::error_code& error) {
    std::lock_guard<std::mutex> lock(mutex_);
    if (!error) {
        logger_->Log(
            opentelemetry::logs::Severity::kInfo,
            (
                "Client: Success count: " + std::to_string(success_count_) + ", Success size: " + std::to_string(success_size_) +
                ", Failure count: " + std::to_string(failure_count_)
            ));
    } else if (error.value() == 995) {
        // Message: The I/O operation has been aborted because of either a thread exit or an application request.
        // Hard stop. The TcpClient object may not be valid anymore.
        return;
    } else {
        logger_->Log(opentelemetry::logs::Severity::kError, "Client: Monitor timer error: " + error.message() + ", code: " + std::to_string(error.value()));
    }
    start_monitor_timer(); // Restart the timer
}

} // namespace Exporters
} // namespace M365
} // namespace Microsoft