﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFrameworks>net8.0;net6.0;net472</TargetFrameworks>
    <AssemblyName>Microsoft.M365.Core.Telemetry.SampleConsoleApp</AssemblyName>
    <RootNamespace>Microsoft.M365.Core.Telemetry.SampleConsoleApp</RootNamespace>
    <PlatformTarget>anycpu</PlatformTarget>
    <LangVersion>10</LangVersion>
    <NoWarn>SA1516</NoWarn>
    <AssemblyVersion>18.0.0.0</AssemblyVersion>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\R9TelemetryExtensionsCore\ECSClient\ECSClient.csproj" />
    <ProjectReference Include="..\..\R9TelemetryExtensionsCore\R9\R9.csproj" />
    <ProjectReference Include="..\..\R9TelemetryExtensionsCore\SDKLogger\SDKLogger.csproj" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.R9.Extensions.Logging" />
    <PackageReference Include="Microsoft.R9.Extensions.Metering.Abstractions" />
  </ItemGroup>

</Project>