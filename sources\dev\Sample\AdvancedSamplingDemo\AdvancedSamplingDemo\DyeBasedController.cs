﻿// <copyright file="DyeBasedController.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using Microsoft.AspNetCore.Mvc;

namespace Microsoft.M365.Core.Tracing.AdvancedSamplingServer
{
    /// <summary>
    /// dye based controller
    /// </summary>
    [ApiController]
    [Route("api/")]
    public class DyeBasedController : ControllerBase
    {
        /// <summary>
        /// server
        /// </summary>
        /// <returns></returns>
        [HttpGet("dyeserverFirst")]
        public async Task<IActionResult> ServerFirstAsync()
        {
            var url = "https://localhost:5002/api/dyeserverSecond";

            var client = new HttpClient();
            await client.GetAsync(url).ConfigureAwait(true);

            return Ok("DyeBasedController::Server");
        }

        /// <summary>
        /// server
        /// </summary>
        /// <returns></returns>
        [HttpGet("dyeserverSecond")]
        public IActionResult ServerSecond()
        {
            return Ok("DyeBasedController::Server");
        }
    }
}