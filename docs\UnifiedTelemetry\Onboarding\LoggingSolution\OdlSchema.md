# Odl export schema

The attributes of odl export schema can be classified into 2 categories:

-  Fixed Columns
Basic information about the machine and the log. All logs contain these attributes, populated by SDK.

-  Log Related Columns
Custom columns defined by users. These columns follow a specific order according to the log/event definition. (with **[fast logging](https://eng.ms/docs/experiences-devices/r9-sdk/insights/how-tos/ht-migrate-to-net-fast-logging)**)

## Fixed Columns

Overall, the old data structure consists of 3 parts in the following order: fixed columns, log-related columns, and fixed columns.

For more detail about **Fixed Columns**, please refer [this page on M365 Passive Monitoring](https://eng.ms/docs/experiences-devices/m365-core/o365-substrate-fabric-extended-suzhou/o365-telemetry/m365-passive-monitoring/m365-passive-monitoring/geneva/mds-to-odl-migration-schema)

Here is an example schema with all fixed columns listed:

```csharp
// Part1: Fixed Columns: dimensions from odl exporter, won’t change
env_name: string;
env_ver: string;
env_time: datetime;
env_dt_traceId: string;
env_dt_spanId: string;
severityText: string;
severityNumber: int64;
name: string;

// Part2: Log Related Columns: depends on log definition
cnt: int64;
body: string;

// Part3: Fixed Columns: enricher dimensions, depends on enrichers and their definitions. Applied for all events.
IsR9: bool;
env_cloud_environment: string;
env_cloud_role: string;
env_cloud_deploymentUnit: string;
env_cloud_location: string;
env_cloud_name: string;
env_cloud_roleInstance: string;
env_cloud_roleVer: string;
buildVersion: string;
```

## Log Related Columns

For part 2, as we use fast logging, it can be decided from the log definition.

For **derived type**, the order is from child attributes to parents’ attributes.

For **nested type**, must set Transitive=true to expand all nested properties, or annotate [LogProperties] to the nested type that want to expand.

### Example Definition

Assume a set of log definition as follows: A complex type `ComplexData` derives from a base type `SampleBaseData` and contains a nested type `SimpleData->RootData` (3 layers in total).

```
ComplexData
├── Inherits: SampleBaseData
│     ├── Property: Count
│     └── Property: Msg
├── Embeds: SimpleData
│     ├── Embeds: RootData
│     │      └── Property: RootMsg
│     └── Property: NestedMsg
└── Property: Extra
```

### Example Output

> [!Note]
> Json content is exported as string, here shows raw json for simplicity.

With `Transitive=false`, the order of part 2 will be:

| Column  | Type   | Sample Data                                                                                            |
|---------|--------|--------------------------------------------------------------------------------------------------------|
| body    | string | “Test Object: {Complex}”                                                                               |
| Complex | string | <pre><code>{<br>  "Simple": {<br>    "Root": {<br>      "RootMsg": "Msg from RootData"<br>    },<br>    "NestedMsg": "Msg from SimpleData"<br>  },<br>  "Extra": "[DI]",<br>  "Count": 1,<br>  "Msg": "default"<br>}</code></pre> |
| Simple  | string | <pre><code>{<br>  "Root": {<br>    "RootMsg": "Msg from RootData"<br>  },<br>  "NestedMsg": "Msg from SimpleData"<br>}</code></pre> |
| Extra   | string | "[DI]"                                                                                                  |
| Count   | int    | 1                                                                                                       |
| Msg     | string | "default"                                                                                               |


With `Transitive=true`, it becomes:

| Column               | Type   | Sample Data                                                                                           |
|----------------------|--------|-------------------------------------------------------------------------------------------------------|
| body                 | string | “Test Object: {Complex}”                                                                              |
| Complex              | string | <pre><code>{<br>  "Simple": {<br>    "Root": {<br>      "RootMsg": "Msg from RootData"<br>    },<br>    "NestedMsg": "Msg from SimpleData"<br>  },<br>  "Extra": "[DI]",<br>  "Count": 1,<br>  "Msg": "default"<br>}</code></pre> |
| Simple.Root.RootMsg  | string | "Msg from RootData"                                                                                   |
| Simple.NestedMsg     | string | "Msg from SimpleData"                                                                                 |
| Extra                | string | "[DI]"                                                                                                |
| Count                | int    | 1                                                                                                     |
| Msg                  | string | "default"                                                                                             |


### Type Definition

```c#
public static partial class SampleLogMessages
{
    [LoggerMessage(
        EventId = 3, Level = LogLevel.Information, Message = "Test Object: {Complex}")]
    public static partial void TestLogObject(
        this ILogger logger,
        [LogProperties(OmitReferenceName = true, SkipNullProperties = true, Transitive = true)] ComplexData complex);
}

public class SampleBaseData(int count, string msg)
{
    public int Count { get; } = count;
    public string Msg { get; } = msg;
}

public class RootData(string msg)
{
    public string RootMsg { get; } = msg;
}

public class SimpleData(string msg)
{
    public RootData Root { get; } = new RootData("Msg from RootData")
    public string NestedMsg { get; } = msg;
}

public class ComplexData : SampleBaseData
{
    public SimpleData Simple { get; }
    public string Extra { get; }
    public ComplexData(int count, string msg, string extra) : base(count, msg)
    {
        Simple = new SimpleData("Msg from SimpleData ");
        Extra = extra;
    }
}
```
