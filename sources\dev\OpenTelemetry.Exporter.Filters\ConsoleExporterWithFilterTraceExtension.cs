// <copyright file="ConsoleExporterWithFilterTraceExtension.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System;
using System.Diagnostics;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Options;
using OpenTelemetry.Exporter.Filters.Internal;
using OpenTelemetry.Trace;

namespace OpenTelemetry.Exporter.Filters;

public static class ConsoleExporterHelperExtensions
{
    /// <summary>
    /// Adds Console exporter to the TracerProvider.
    /// </summary>
    /// <param name="builder"><see cref="TracerProviderBuilder"/> builder to use.</param>
    /// <param name="filter"><see cref="BaseFilter{T}"/>filter added to exporter processor.</param>
    /// <returns>The instance of <see cref="TracerProviderBuilder"/> to chain the calls.</returns>
    public static TracerProviderBuilder AddConsoleExporter(this TracerProviderBuilder builder, BaseFilter<Activity> filter)
        => AddConsoleExporter(builder, name: null, configure: null, filter);

    /// <summary>
    /// Adds Console exporter to the TracerProvider.
    /// </summary>
    /// <param name="builder"><see cref="TracerProviderBuilder"/> builder to use.</param>
    /// <param name="sampler"><see cref="Sampler"/> sampler added to exporter processor.</param>
    /// <returns>The instance of <see cref="TracerProviderBuilder"/> to chain the calls.</returns>
    public static TracerProviderBuilder AddConsoleExporter(this TracerProviderBuilder builder, Sampler sampler)
        => AddConsoleExporter(builder, name: null, configure: null, new SamplerFilter(sampler));

    /// <summary>
    /// Adds Console exporter to the TracerProvider.
    /// </summary>
    /// <param name="builder"><see cref="TracerProviderBuilder"/> builder to use.</param>
    /// <param name="configure">Callback action for configuring <see cref="ConsoleExporterOptions"/>.</param>
    /// <param name="filter"><see cref="BaseFilter{T}"/>filter added to exporter processor.</param>
    /// <returns>The instance of <see cref="TracerProviderBuilder"/> to chain the calls.</returns>
    public static TracerProviderBuilder AddConsoleExporter(this TracerProviderBuilder builder, Action<ConsoleExporterOptions> configure, BaseFilter<Activity> filter)
        => AddConsoleExporter(builder, name: null, configure, filter);

    /// <summary>
    /// Adds Console exporter to the TracerProvider.
    /// </summary>
    /// <param name="builder"><see cref="TracerProviderBuilder"/> builder to use.</param>
    /// <param name="configure">Callback action for configuring <see cref="ConsoleExporterOptions"/>.</param>
    /// <param name="sampler"><see cref="Sampler"/> sampler added to exporter processor.</param>
    /// <returns>The instance of <see cref="TracerProviderBuilder"/> to chain the calls.</returns>
    public static TracerProviderBuilder AddConsoleExporter(this TracerProviderBuilder builder, Action<ConsoleExporterOptions> configure, Sampler sampler)
        => AddConsoleExporter(builder, name: null, configure, new SamplerFilter(sampler));

    /// <summary>
    /// Adds Console exporter to the TracerProvider.
    /// </summary>
    /// <param name="builder"><see cref="TracerProviderBuilder"/> builder to use.</param>
    /// <param name="name">Name which is used when retrieving options.</param>
    /// <param name="configure">Callback action for configuring <see cref="ConsoleExporterOptions"/>.</param>
    /// <param name="sampler"><see cref="Sampler"/> sampler added to exporter processor.</param></param>
    /// <returns>The instance of <see cref="TracerProviderBuilder"/> to chain the calls.</returns>
    public static TracerProviderBuilder AddConsoleExporter(this TracerProviderBuilder builder, string name, Action<ConsoleExporterOptions> configure, Sampler sampler)
        => AddConsoleExporter(builder, name, configure, new SamplerFilter(sampler));

    /// <summary>
    /// Adds Console exporter to the TracerProvider.
    /// </summary>
    /// <param name="builder"><see cref="TracerProviderBuilder"/> builder to use.</param>
    /// <param name="name">Name which is used when retrieving options.</param>
    /// <param name="configure">Callback action for configuring <see cref="ConsoleExporterOptions"/>.</param>
    /// <param name="filter"><see cref="BaseFilter{T}"/>filter added to exporter processor.</param>
    /// <returns>The instance of <see cref="TracerProviderBuilder"/> to chain the calls.</returns>
    public static TracerProviderBuilder AddConsoleExporter(
        this TracerProviderBuilder builder,
        string name,
        Action<ConsoleExporterOptions> configure, BaseFilter<Activity> filter)
    {
        Guard.ThrowIfNull(builder);

        name ??= Options.DefaultName;

        if (configure != null)
        {
            builder.ConfigureServices(services => services.Configure(name, configure));
        }

        return builder.AddProcessor(sp =>
        {
            var options = sp.GetRequiredService<IOptionsMonitor<ConsoleExporterOptions>>().Get(name);

            return new SimpleActivityExportProcessorWithFilter(new ConsoleActivityExporter(options), filter);
        });
    }
}