﻿// <copyright file="LogExporterTests.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.Diagnostics.CodeAnalysis;
using System.Diagnostics.Tracing;
using System.Linq;
using System.Threading;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.R9.Extensions.Logging;
using Microsoft.R9.Extensions.Logging.Exporters;
using Microsoft.R9.Extensions.Telemetry.Exporters.Base;
using OpenTelemetry;
using OpenTelemetry.Logs;
using Xunit;
using Assert = Xunit.Assert;

namespace Microsoft.R9.Extensions.Telemetry.Exporters.ODL.Test
{
    public class ExporterTests
    {
        [Fact]
        public void LogUnformattedWarning_LogCrital()
        {
            using var exporter = new TestExporter();
            using var listener = new TestEventListener();
            listener.EnableEvents(ExporterLogger.Log, EventLevel.Verbose, EventKeywords.All);

            listener.ClearMessages();
            ILogger logger = GetLogger(enableLogTypeMapping: true, enableCustomerFields: true, enablePrepolulatedFields: true, testExporter: exporter, mappingType: new Dictionary<string, string>
            {
                ["*"] = "TestEVR",
            }, useFormattedMessage: false);

            string logMessage = "This is testing {user}";
            logger.LogError(logMessage, "testUser");
            EventWrittenEventArgs actualEvent = listener.Messages.Last();
            Assert.Equal("CriticalLogs", actualEvent.EventName);
            listener.ClearMessages();
        }

        [Fact]
        public void AddODLExporter_GivenOptions_ExportToLogType()
        {
            using var exporter = new TestExporter();
            ILogger logger = GetLogger(enableLogTypeMapping: true, enableCustomerFields: true, enablePrepolulatedFields: true, testExporter: exporter, mappingType: new Dictionary<string, string>
            {
                ["*"] = "TestEVR",
            });
            string logMessage = "This is testing {user}";

            var dictExpected = new Dictionary<string, object>
            {
                { "{OriginalFormat}", logMessage },
                { "user", "testUser" }
            };
            
            logger.LogError(logMessage, "testUser");
            logger.LogInformation(logMessage, "testUser");
            logger.LogCritical(logMessage, "testUser");
            logger.LogWarning(logMessage, "testUser");
            logger.LogDebug(logMessage, "testUser");
            Assert.True(CompareStateValues(exporter.FirstState!, dictExpected));
        }

        [Fact]
        public void AddODLExporter_SizeLimited_FallBackToDisk()
        {
            var exception = Record.Exception(() =>
            {
                using var exporter = new TestExporter();
                ILogger logger = GetLogger(enableLogTypeMapping: true, enableCustomerFields: true, enablePrepolulatedFields: true, exporter, new Dictionary<string, string>
                {
                    ["*"] = "TestEVR",
                }, useFormattedMessage: true, enableBatch: false, enableFallback: true);
                string logMessage = new string('a', 33000);
                logger.LogInformation(logMessage);
            });
            Assert.Null(exception);
        }

        [Fact]
        public void AddODLExporter_InvalidMapping_ExportToCategory()
        {
            var exception = Record.Exception(() =>
            {
                using var exporter = new TestExporter();
                ILogger logger = GetLogger(enableLogTypeMapping: true, enableCustomerFields: true, enablePrepolulatedFields: true, exporter, new Dictionary<string, string>
                {
                    ["Microsoft.R9.Extensions.Logging.Exporters.Tests.LogTests"] = String.Empty,
                });
            });
            Assert.NotNull(exception);

            exception = Record.Exception(() =>
            {
                using var exporter = new TestExporter();
                ILogger logger = GetLogger(enableLogTypeMapping: true, enableCustomerFields: true, enablePrepolulatedFields: true, exporter, new Dictionary<string, string>
                {
                    ["Microsoft.R9.Extensions.Logging.Exporters.Tests.LogTests"] = null,
                });
            });
            Assert.NotNull(exception);

            exception = Record.Exception(() =>
            {
                using var exporter = new TestExporter();
                ILogger logger = GetLogger(enableLogTypeMapping: true, enableCustomerFields: true, enablePrepolulatedFields: true, exporter, new Dictionary<string, string>
                {
                    [string.Empty] = "test",
                });
            });
            Assert.NotNull(exception);
        }

        [Fact]
        public void AddODLExporter_StarMapping_ExportToLogType()
        {
            var exception = Record.Exception(() =>
            {
                using var exporter = new TestExporter();
                ILogger logger = GetLogger(true, true, true, exporter, new Dictionary<string, string>
                {
                    ["*"] = "TestEVR",
                });
                string logMessage = "test log";
                logger.LogInformation(logMessage);
            });
            Assert.Null(exception);
        }

        [Fact]
        public void AddODLExporter_InvalidMapping_SkipRecord()
        {
            var exception = Record.Exception(() =>
            {
                using var exporter = new TestExporter();
                ILogger logger = GetLogger(true, true, true, exporter, new Dictionary<string, string>
                {
                    ["other"] = "TestEVR",
                });
                string logMessage = "test log";
                logger.LogInformation(logMessage);
            });
            Assert.Null(exception);
        }

        private static ILogger GetLogger(bool enableLogTypeMapping, bool enableCustomerFields, bool enablePrepolulatedFields, TestExporter testExporter, Dictionary<string, string> mappingType, bool useFormattedMessage = true, bool enableBatch = false, bool enableFallback = false)
        {
            var host = new HostBuilder()
                        .ConfigureLogging(builder => builder
                            .AddOpenTelemetryLogging(options =>
                            {
                                options.UseFormattedMessage = useFormattedMessage;
                            })
                            .SetMinimumLevel(LogLevel.Debug)
                            .AddProcessor(new SimpleLogRecordExportProcessor(testExporter))
                            .AddODLExporter(
                                options =>
                            {
                                if (enableLogTypeMapping)
                                {
                                    options.LogTypeMappings = mappingType;
                                }
                                if (enableCustomerFields)
                                {
                                    options.CustomFields = new string[] { "user" };
                                }
                                options.EnableFallBack = enableFallback;
                                if (enablePrepolulatedFields)
                                {
                                    options.PrepopulatedFields = new Dictionary<string, object>
                                    {
                                        { "role", "testUser" }
                                    };
                                }
                            }, enableBatch))
                        .Build();
            return host.Services.GetRequiredService<ILoggerFactory>().CreateLogger<ExporterTests>();
        }

        private static bool CompareStateValues(IReadOnlyCollection<KeyValuePair<string, object>> stateValues, Dictionary<string, object> dictExpected)
        {
            if (stateValues.Count != dictExpected.Count)
            {
                return false;
            }

            foreach (var entry in stateValues)
            {
                if (dictExpected.TryGetValue(entry.Key, out var value))
                {
                    if (entry.Value.ToString() != value.ToString())
                    {
                        return false;
                    }
                }
                else
                {
                    return false;
                }
            }

            return true;
        }
    }

#nullable enable
    [ExcludeFromCodeCoverage]
    internal class TestExporter : BaseExporter<LogRecord>
    {
        internal LogRecord? FirstLogRecord { get; set; }

        internal List<KeyValuePair<string, object?>>? FirstState { get; set; }

        public override ExportResult Export(in Batch<LogRecord> batch)
        {
            foreach (var logRecord in batch)
            {
                FirstLogRecord = logRecord;
                FirstState = logRecord.Attributes is null ? null : new (logRecord.Attributes);
            }

            return ExportResult.Success;
        }
    }

#nullable enable
    [ExcludeFromCodeCoverage]
    internal class TestEventListener : EventListener
    {
        /// <summary>A queue of events that have been logged.</summary>
        private readonly Queue<EventWrittenEventArgs> events;

        /// <summary>
        /// Lock for event writing tracking.
        /// </summary>
        private readonly AutoResetEvent eventWritten;

        /// <summary>
        /// Initializes a new instance of the <see cref="TestEventListener"/> class.
        /// </summary>
        public TestEventListener()
        {
            this.events = new Queue<EventWrittenEventArgs>();
            this.eventWritten = new AutoResetEvent(false);
            this.OnOnEventWritten = e =>
            {
                this.events.Enqueue(e);
                this.eventWritten.Set();
            };
        }

        /// <summary>Gets or sets the handler for event source creation.</summary>
        public Action<EventSource>? OnOnEventSourceCreated { get; set; }

        /// <summary>Gets or sets the handler for event source writes.</summary>
        public Action<EventWrittenEventArgs> OnOnEventWritten { get; set; }

        /// <summary>Gets the events that have been written.</summary>
        public IEnumerable<EventWrittenEventArgs> Messages
        {
            get
            {
                if (this.events.Count == 0)
                {
                    this.eventWritten.WaitOne(TimeSpan.FromSeconds(5));
                }

                while (this.events.Count != 0)
                {
                    yield return this.events.Dequeue();
                }
            }
        }

        /// <summary>
        /// Clears all event messages so that testing can assert expected counts.
        /// </summary>
        public void ClearMessages()
        {
            this.events.Clear();
        }

        /// <summary>Handler for event source writes.</summary>
        /// <param name="eventData">The event data that was written.</param>
        protected override void OnEventWritten(EventWrittenEventArgs eventData)
        {
            Console.WriteLine(eventData.Message);
            this.OnOnEventWritten(eventData);
        }

        /// <summary>Handler for event source creation.</summary>
        /// <param name="eventSource">The event source that was created.</param>
        protected override void OnEventSourceCreated(EventSource eventSource)
        {
            // Check for null because this method is called by the base class constror before we can initialize it
#pragma warning disable CS8600 // Converting null literal or possible null value to non-nullable type.
            Action<EventSource> callback = this.OnOnEventSourceCreated;
#pragma warning restore CS8600 // Converting null literal or possible null value to non-nullable type.
            callback?.Invoke(eventSource);
        }
    }
}
