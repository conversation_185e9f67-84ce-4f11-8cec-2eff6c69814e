﻿Questions:
1. Whether R9Host will be deprecated? Is it acceptable to just keep it internal?
2. Shall we expose the enrichers to customers? E.g.:InitSubstrateEnrichers, B2PassiveLogEnricher, B2PassiveMetricEnricher, etc.
3. Will external usage rely on SDKLogger?

 If we cannot give a answer to above questions right now, we can keep it internal first and gradually expose them to customers in the future.

```
appsettings.json
{
    "Microsoft_m365_core_telemetry": {
        "ServiceMetadata": {
            "ServiceName": "PassiveValidation",
            "RuntimeModel": "ModelA"
        }
    },
    "R9": {
        "Logging": {
            "GenevaLogging": {
                "ConnectionString": "EtwSession=o365PassiveMonitoringSessionR9",
                "TableNameMappings": {
                    "Microsoft.M365.Core.PassiveValidation.SampleEvent": "SampleEventR9",
                    "*": "PassiveR9"
                }
            }
        },
        "Metering": {
            "GenevaMetering": {
                "Protocol": "Etw",
                "MonitoringAccount": "R9Telemetry.Guardian",
                "MonitoringNamespace": "R9Telemetry.GuardianR9",
                "MonitoringAccountOverrides":{
                    "Microsoft.M365.Core.PassiveValidation.SampleMetric" = "M365_Monitoring_Account",
                },
                "MonitoringNamespaceOverrides":{
                    "Microsoft.M365.Core.PassiveValidation.SampleMetric" = "M365_Monitoring_NamespaceR9",
                }
            }
        }
    }
}
```
```csharp

namespace Microsoft.M365.Core.Telemetry.ECSClient
{
    public static class Constants
    {
        public const string R9DTEnabled = "R9DTEnabled";
        public const string TraceSampleRate = "TraceSampleRate";
        public const string SamplerType = "SamplerType";
        public const string ParentRootSamplerType = "ParentRootSamplerType";
        public const int DefaultRefreshInterval = 5;
        public enum DynamicSamplerType
        {
            AlwaysOn,
            AlwaysOff,
            RatioBased,
            ParentBased
        }
    }

    public interface IPassiveR9Config
    {
        bool R9EventEnabled { get; set; }
        bool R9MetricEnabled { get; set; }
        bool IfxEventEnabled { get; set; }
        bool IfxMetricEnabled { get; set; }
        bool IsDebugInfoCollectionEnabled { get; set; }
        bool MdsTraceEnabled { get; set; }
        string IncludedTraceLog { get; set; }
        public bool EventDisabledForIfx(string category);
        public bool MetricDisabledForIfx(string category);
        public bool EventEnabledForR9(string category);
        public bool MetricEnabledForR9(string category);
    }

    public class R9TracingConfig // used in ECS.DynamicComponent, R9.Tracing.Instrumentation.Accelerator
    {
        public bool R9DTEnabled { get; set; }
        public float TraceSampleRate { get; set; }
        public Microsoft.M365.Core.Telemetry.ECSClient.Constants.DynamicSamplerType SamplerType { get; set; } = Microsoft.M365.Core.Telemetry.ECSClient.Constants.DynamicSamplerType.AlwaysOff;
        public Microsoft.M365.Core.Telemetry.ECSClient.Constants.DynamicSamplerType ParentRootSamplerType { get; set; } = Microsoft.M365.Core.Telemetry.ECSClient.Constants.DynamicSamplerType.AlwaysOff;
        public bool ConfigChanged { get; set; }
        public R9TracingConfig(IConfiguration configuration);
    }
}

namespace Microsoft.M365.Core.Telemetry.Enrichment
{
    public class B2PassiveLogEnricher : Microsoft.R9.Extensions.Enrichment.ILogEnricher
    {
        public B2PassiveLogEnricher();
        public void Enrich(Microsoft.R9.Extensions.Enrichment.IEnrichmentPropertyBag enrichmentBag)
    }

    public class B2PassiveMetricEnricher : Microsoft.R9.Extensions.Enrichment.IMetricEnricher
    {
        public B2PassiveMetricEnricher();
        public void Enrich(Microsoft.R9.Extensions.Enrichment.IEnrichmentPropertyBag enrichmentBag)
    }
}

namespace Microsoft.M365.Core.Telemetry.R9
{
    public interface IBlockList
    {
        public bool ShouldBlockMetric(string name, string category);
    }

    public class LogEvent<T>
    {
        public void Log(T customEvent, System.Collections.Generic.List<System.Reflection.MemberInfo> bondFields);
    }

    public static class R9Extensions
    {
        public static Microsoft.Extensions.DependencyInjection.IServiceCollection InitTelemetry(this Microsoft.Extensions.DependencyInjection.IServiceCollection services, Microsoft.Extensions.Configuration.IConfiguration configuration);
        public static Microsoft.Extensions.DependencyInjection.IServiceCollection InitTelemetry(this Microsoft.Extensions.DependencyInjection.IServiceCollection services, Microsoft.Extensions.Configuration.IConfiguration configuration, bool alwaysEnableR9)
    }

    public class R9Metric<T>
    {
        public R9Metric(string name, params string[] listDimensions);
        public void Log(long data, params string[] listDimensionValues);
    }

    public static class R9Services
    {
        public static Microsoft.M365.Core.Telemetry.ECSClient.IPassiveR9Config GetPassiveR9Config();
        public static bool Initialized { get; set; }
    }
}

namespace Microsoft.M365.Core.Telemetry.SDKLogger
{
    public static class SDKLog
    {
        public static void InternalResetInUnitTest();
    }
}

namespace Microsoft.M365.Core.Telemetry.ECS.DynamicComponent
{
    public class ServiceInfo
    {
        public string ServiceName { get; set; } = "*";
        public ServiceRunningModel RunningModel { get; set; } = ServiceRunningModel.Default;
        public ServiceInfo();
        public ServiceInfo(string serviceName);
        public ServiceInfo (string serviceName, ServiceRunningModel runningModel);
    }

    public enum ServiceRunningModel
    {
        Default,
        ModelA,
        ModelB,
        ModelB2,
        ModelD,
        ModelD2,
        Cosmic
    }

    public class DynamicSampler : OpenTelemetry.Trace.Sampler
    {
        public DynamicSampler(R9TracingConfig config);
        public override OpenTelemetry.Trace.SamplingResult ShouldSample(in OpenTelemetry.Trace.SamplingParameters samplingParameters)
    }
}

```
