﻿// <copyright file="DetermineSharedBingRedirectSkill.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Linq;
using Kusto.Cloud.Platform.Utils;
using R9MigrationHelper.Model;
using R9MigrationHelper.Skills.StringParseSkills;

namespace R9MigrationHelper.Skills.BindingRedirectSkills.StringParseSkills
{
    /// <summary>
    /// DetermineSharedBingRedirectSkill.
    /// </summary>
    public class DetermineSharedBingRedirectSkill
    {
        /// <summary>
        /// DropToBinPlace.
        /// </summary>
        /// <param name="serviceConfigContent">service Config Content.</param>
        public bool DropToBinPlace(string serviceConfigContent)
        {
            DetermineBinPlaceSkill determineBinPlaceSkill = new DetermineBinPlaceSkill();
            return determineBinPlaceSkill.DetermineBinPlace(serviceConfigContent);
        }

        /// <summary>
        /// DetermineSharedBingRedirect.
        /// </summary>
        /// <param name="serviceConfigContent">service Config Content.</param>
        public bool DetermineSharedBingRedirect(string serviceConfigContent)
        {
            if (serviceConfigContent.IsNullOrEmpty())
            {
                return false;
            }
            XElement xelementServiceConfig = XElement.Parse(serviceConfigContent);
            if (xelementServiceConfig == null)
            {
                return false;
            }
            XElement? sharedBindingRedirects = FindXElementWithAttribute(xelementServiceConfig, "linkedConfiguration", "href", @"file://C:\Program Files\Microsoft\Exchange Server\V15\bin\SharedBindingRedirects.config");
            return sharedBindingRedirects != null;
        }

        /// <summary>
        /// FilterUnsharedAssembly.
        /// </summary>
        /// <param name="assemblyList">assemblyList.</param>
        /// <param name="sharedBingRedirectContent">sharedBingRedirectContent.</param>
        public List<AssemblyModel> FilterUnsharedAssembly(List<AssemblyModel> assemblyList, string sharedBingRedirectContent)
        {
            XElement xelementSharedBingRedirect = XElement.Parse(sharedBingRedirectContent);
            List<AssemblyModel> assemblyListFiltered = new List<AssemblyModel>();
            foreach (AssemblyModel assembly in assemblyList)
            {
                if (FindXElementWithAttribute(xelementSharedBingRedirect, "assemblyIdentity", "name", assembly.Name) != null)
                {
                    assemblyListFiltered.Add(assembly);
                }
            }
            return assemblyListFiltered;
        }

        private XElement? FindXElementWithAttribute(XElement root, string elementName, string attributeName, string attributeValue)
        {
            if (root == null)
            {
                return null;
            }
            foreach (XElement el in root.Descendants(elementName))
            {
                if (el.Attribute(attributeName)?.Value == attributeValue)
                {
                    return el;
                }
            }
            return null;
        }
    }
}
