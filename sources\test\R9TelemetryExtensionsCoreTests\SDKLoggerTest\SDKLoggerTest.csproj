<Project Sdk="Microsoft.NET.Sdk">
    <PropertyGroup>
      <DocumentationFile>bin\$(Configuration)\$(TargetFramework)\$(AssemblyName).xml</DocumentationFile>    
      <PlatformTarget>anycpu</PlatformTarget>
      <Authors>SOTELS</Authors>
      <RootNamespace>Microsoft.M365.Core.Telemetry.SDKLoggerTest</RootNamespace>
      <AssemblyName>Microsoft.M365.Core.Telemetry.SDKLoggerTest</AssemblyName>
      <TargetFrameworks>net472;net6.0;net8.0</TargetFrameworks>
      <IsCodedUITest>False</IsCodedUITest>
      <TestProjectType>UnitTest</TestProjectType>
      <PlatformTarget>anycpu</PlatformTarget>
      <LangVersion>10</LangVersion>  
      <SuppressTfmSupportBuildWarnings>true</SuppressTfmSupportBuildWarnings>
    </PropertyGroup>
    <ItemGroup>
      <ProjectReference Include="..\..\..\dev\R9TelemetryExtensionsCore\SDKLogger\SDKLogger.csproj" />
      <ProjectReference Include="..\..\..\dev\TestCommon\TestCommon.csproj" />
    </ItemGroup>  
    <ItemGroup>
      <PackageReference Include="Microsoft.NET.Test.Sdk" />
      <PackageReference Include="NSubstitute" />
      <PackageReference Include="xunit" />
      <PackageReference Include="xunit.runner.visualstudio" />
      <PackageReference Include="coverlet.collector" />
    </ItemGroup>
  </Project>