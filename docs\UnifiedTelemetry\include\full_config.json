{
    "SubstrateLogging": {
        "R9Logging": {
            "MaxStackTraceLength": 3000
        },
        "UseCompositeExporter": true,
        "CompositeExporter": {
            "VirtualTableMappings": {
                "Test.MyService": "MyServiceTable"
            },
            "Geneva": {
                "ConnectionString": "EtwSession=test"
            },
            "OdlTcp": {
                "ConnectionString": "tcp://localhost:1234"
            },
            "VirtualTableExports": {
                "MyServiceTable": [
                    {
                        "ExporterType": "Geneva",
                        "ExportTable": "ServiceEvent"
                    },
                    {
                        "ExporterType": "OdlTcp",
                        "ExportTable": "TestLogType"
                    }
                ]
            }
        },
        // Won't be used as "UseCompositeExporter" is true here.
        "GenevaExporter": {
            "ConnectionString": "EtwSession=TestSession",
            "TableNameMappings": {
                "MyLogger": "CustomizedR9Event1",
                "Test.Foo": "CustomizedR9Event2"
            }
        }
    }
}