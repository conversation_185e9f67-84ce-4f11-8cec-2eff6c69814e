﻿// <copyright file="EcsAuthHttpHandler.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System;
using System.Diagnostics.CodeAnalysis;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Identity.Client;
using Microsoft.M365.Core.Telemetry.SDKLogger;

namespace Microsoft.M365.Core.Telemetry.ECSClient
{
    /// <summary>
    /// The handler to add ECS token to the request header.
    /// </summary>
    // Exclude the class from code coverage because the required cert doesn't exist in the test environment.
    // TODO: Refactor the cert as service dependency and test it.
    [ExcludeFromCodeCoverage]
    public sealed class EcsAuthHttpHandler : DelegatingHandler
    {
        private readonly IConfidentialClientApplication app;
        private readonly string[] scopes = new[] { "https://ecs.skype.ame.gbl/.default" };

        /// <summary>
        /// Constructor of handler.
        /// </summary>
        /// <param name="app">The app to acquire token.</param>
        public EcsAuthHttpHandler(IConfidentialClientApplication app)
        {
            this.app = app;
        }

        /// <summary>
        /// Send request to acquire token.
        /// </summary>
        /// <param name="request">The request to send.</param>
        /// <param name="cancellationToken">The cancellationToken.</param>
        /// <returns></returns>
        protected override async Task<HttpResponseMessage> SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
        {
            if (app == null)
            {
                SDKLog.Error("Acquire token for client failed. Application is null.");
                return null;
            }

            if (request == null)
            {
                SDKLog.Error("Acquire token for client failed. Request is null.");
                return null;
            }

            // TODO: Switch to configurable retry policy provided by ECS SDK after upgrading to version >= 18.1
            AuthenticationResult token = null;
            try
            {
                token = await app.AcquireTokenForClient(scopes).ExecuteAsync(cancellationToken).ConfigureAwait(false);
                SDKLog.Info("Acquired token for client successfully.");
            }
            catch (Exception e)
            {
                SDKLog.Error($"Acquire token for client failed. {e}");
            }

            if (token != null)
            {
                request.Headers.Add("X-ECS-ClientAppToken", token.CreateAuthorizationHeader());
            }
            return await base.SendAsync(request, cancellationToken).ConfigureAwait(false);
        }
    }
}
