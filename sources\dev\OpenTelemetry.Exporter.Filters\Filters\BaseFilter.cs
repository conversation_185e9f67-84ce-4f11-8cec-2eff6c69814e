﻿// <copyright file="BaseFilter.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System;

namespace OpenTelemetry.Exporter.Filters
{
    /// <summary>
    /// Base class for Filters, defines the interfaces
    /// </summary>
    /// <typeparam name="T">A generic type parameter for Filter.</typeparam>
    public abstract class BaseFilter<T> : IDisposable
    {
        /// <summary>
        /// dispose the instance
        /// </summary>
        public void Dispose()
        {
            this.Dispose(true);
            GC.SuppressFinalize(this);
        }

        /// <summary>
        /// interface to decide whether to filter the data 
        /// </summary>
        /// <param name="t">generic type parameter</param>
        /// <returns>if true returned, data will be kept. Else will be dropped.</returns>
        public abstract bool ShouldFilter(T t);

        /// <summary>
        /// desciption of the filter
        /// </summary>
        /// <returns></returns>
        public abstract string GetDescription();

        /// <summary>
        /// name of the filter
        /// </summary>
        /// <returns></returns>
        public virtual string GetName()
        {
            return GetType().Name;
        }

        /// <summary>
        /// dispose the instance
        /// </summary>
        /// <param name="disposing"></param>
        protected virtual void Dispose(bool disposing)
        {
        }

        ~BaseFilter()
        {
            this.Dispose(false);
        }
    }
}
