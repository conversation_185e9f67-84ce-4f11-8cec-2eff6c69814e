// <copyright file="InitializeTest.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System;
using System.Diagnostics.Metrics;
using System.IO;
using System.Text;
using System.Text.Json.Nodes;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.M365.Core.Telemetry.R9.Metering.Substrate;
using Microsoft.R9.Extensions.Metering;
using Microsoft.R9.Extensions.Metering.Exporters;
using OpenTelemetry;
using OpenTelemetry.Metrics;
using Xunit;

namespace R9.Metering.SubstrateTest
{
    /// <summary>
    /// Test Extension for Substrate Metering Initialization
    /// </summary>
    public class InitializeTest
    {
        private const string BasicConfig = @"
{
    ""SubstrateMetering"":{
        ""R9Metering"": {
            ""MaxMetricPointsPerStream"": 2100,
            ""MeterStateOverrides"": {
                ""Microsoft.MyDomain"": ""Enabled""
            }
        },
        ""GenevaExporter"": {
            ""MonitoringAccount"": ""PassiveMonitoringTest"",
            ""MonitoringNamespace"": ""TestNamespace""
        }
    }
}";

        /// <summary>
        /// call to initialize normally
        /// </summary>
        [Fact]
        public void InitializeSubstrateSuccessProcess()
        {
            IConfiguration configuration = new ConfigurationBuilder()
                .AddJsonStream(new MemoryStream(Encoding.UTF8.GetBytes(BasicConfig)))
                .Build();

            var host = new HostBuilder().ConfigureServices(services =>
            {
                var exception = Record.Exception(() =>
                {
                    services.AddSubstrateMetering(configuration, meterBuilder =>
                    {
                        meterBuilder.AddConsoleExporter();
                    });
                    services.BuildServiceProvider();
                });
                Assert.Null(exception);
            }).Build();

            var meterFactory = host.Services.GetService<IMeterFactory>();
            Assert.NotNull(meterFactory);
            host.RunAsync();
            var counter = meterFactory?.Create("Microsoft.MyDomain").CreateHistogram<int>("MyCounter");
            counter.Record(1);
        }

        /// <summary>
        /// Examine the case where some optional sections are missing
        /// </summary>
        /// <param name="section"></param>
        /// <param name="newValue"></param>
        [Theory]
        [InlineData("SubstrateMetering:R9Metering")]
        public void InitializeSubstrateMinimalConfig(string section, string newValue = null)
        {
            var trimedConfig = UpdateJsonSection(BasicConfig, section, newValue);
            IConfiguration configuration = new ConfigurationBuilder()
                .AddJsonStream(new MemoryStream(Encoding.UTF8.GetBytes(trimedConfig)))
                .Build();

            var host = new HostBuilder().ConfigureServices(services =>
            {
                var exception = Record.Exception(() =>
                {
                    services.AddSubstrateMetering(configuration);
                    services.BuildServiceProvider();
                });
                Assert.Null(exception);
            }).Build();

            var meterFactory = host.Services.GetService<IMeterFactory>();
            Assert.NotNull(meterFactory);
            var counter = meterFactory?.Create("Microsoft.MyDomain").CreateHistogram<int>("MyCounter");
            counter.Record(1);
        }

        /// A theory test for the following scenario:
        /// - Section "GenevaMeteringExporter" missing or has wrong value of the "MonitoringAccount" property.
        /// - Section "GenevaMeteringExporter" missing or has wrong value of the "MonitoringNamespace" property.
        /// <summary>
        /// A theory test for exceptions on configuration values
        /// </summary>
        /// <param name="section">The section with wrong value</param>
        /// <param name="newValue">Wrong value to be validated</param>
        [Theory]
        [InlineData("SubstrateMetering:R9Metering:MeterStateOverrides")]
        [InlineData("SubstrateMetering:R9Metering:MeterStateOverrides", "{}")]
        [InlineData("SubstrateMetering:GenevaExporter:MonitoringAccount")]
        [InlineData("SubstrateMetering:GenevaExporter:MonitoringAccount", "")]
        [InlineData("SubstrateMetering:GenevaExporter:MonitoringNamespace")]
        [InlineData("SubstrateMetering:GenevaExporter:MonitoringNamespace", "")]
        public void OptionsInvalidConfigSections(string section, string newValue = null)
        {
            var editedConfig = UpdateJsonSection(BasicConfig, section, newValue);

            IConfiguration configuration = new ConfigurationBuilder()
                .AddJsonStream(new MemoryStream(Encoding.UTF8.GetBytes(editedConfig)))
                .Build();
            var services = new ServiceCollection();
            var exception = Record.Exception(() =>
            {
                new MeteringOptions().UpdateAndValidateOptions(configuration);
                new GenevaMeteringExporterOptions().UpdateAndValidateOptions(configuration);
            });
            
            Assert.IsType<ArgumentException>(exception);
            Assert.Contains(section.Split(':')[^1], exception.Message, StringComparison.CurrentCultureIgnoreCase);
        }

        /// <summary>
        /// Examine the case where an option is not registered with "configSectionNames"
        /// </summary>
        [Fact]
        public void OptionNotRegistered()
        {
            IConfiguration configuration = new ConfigurationBuilder()
                .AddJsonStream(new MemoryStream(Encoding.UTF8.GetBytes(BasicConfig)))
                .Build();
            var unRegistered = new { Id = 1, Name = "name" };

            var exception = Record.Exception(() =>
            {
                unRegistered.UpdateOption(configuration);
            });

            Assert.IsType<ArgumentException>(exception);
        }

        [Fact]
        public void AddMetering_nonDI()
        {
            IConfiguration configuration = new ConfigurationBuilder()
                .AddJsonStream(new MemoryStream(Encoding.UTF8.GetBytes(BasicConfig)))
                .Build();

            SubstrateMeteringExtension.ConfigureSubstrateMetering(configuration, meterBuilder =>
            {
                meterBuilder.AddConsoleExporter(options => options.Targets = OpenTelemetry.Exporter.ConsoleExporterOutputTargets.Debug);
            });

            //add a new meter provider
            SubstrateMeteringExtension.ConfigureSubstrateMetering(configuration);
        }

        /// <summary>
        /// multiple geneva exporters is not allowed
        /// </summary>
        [Fact]
        public void MultipleGenevaExporter_ThrowException()
        {
            IConfiguration configuration = new ConfigurationBuilder()
                .AddJsonStream(new MemoryStream(Encoding.UTF8.GetBytes(BasicConfig)))
                .Build();

            // Non-DI, multiple exporter
            Assert.Throws<Exception>(() =>
            {
                Sdk.CreateMeterProviderBuilder()
                .AddGenevaExporter(opt => opt.MonitoringAccount = "TestAccount2")
                .ConfigureSubstrateMetering(configuration)
                .Build();
            });

            // DI, multiple exporter
            var exception = Record.Exception(() =>
            {
                var host = new HostBuilder()
                .ConfigureServices(services =>
                {
                    services.AddSubstrateMetering(configuration, meterBuilder =>
                    {
                        meterBuilder.AddGenevaExporter(opt => opt.MonitoringAccount = "TestAccount2");
                    });
                })
                .Build();
            });
            Assert.NotNull(exception);
        }

        private static string UpdateJsonSection(string json, string section, string newValue = null)
        {
            var jsonObject = JsonNode.Parse(json).AsObject();
            var sections = section.Split(':');
            var subObject = jsonObject; 
            for (int i = 0; i < sections.Length - 1; i++)
            {
                subObject = subObject[sections[i]].AsObject();
            }
            if (newValue is null)
            {
                subObject.Remove(sections[^1]);
            }
            else
            {
                subObject[sections[^1]] = newValue;
            }
            return jsonObject.ToString();
        }
    }
}