﻿// <copyright file="TextFileLogger.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System.Collections.Generic;
using Microsoft.R9.Extensions.SecurityTelemetry;

namespace Microsoft.M365.Core.Telemetry.FileExporter
{
    /// <summary>
    /// Delegate class to export log to file for ODL NRT Pipeline.
    /// </summary>
    internal class TextFileLogger : AbstractFileLogger<string>
    {
        private new readonly TextFileLoggerOptions options;

        /// <summary>
        /// Initializes a new instance of the <see cref="TextFileLogger"/> class.
        /// </summary>
        /// <param name="options"><see cref="FileExporterOptions"/>.</param>
        /// <param name="fileManager"><see cref="IFileManager"/>.</param>
        internal TextFileLogger(TextFileLoggerOptions options, IFileManager fileManager)
            : base(options, fileManager)
        {
            this.options = options;
        }

        /// <summary>
        /// Log Batch of string to disk.
        /// </summary>
        /// <param name="batch"></param>
        public override void Log(in Batch<string> batch)
        {
            var fileLogEntries = new List<string>();

            foreach (var str in batch)
            {
                fileLogEntries.Add(options.StringFormatter(str));
            }

            LogToFile(fileLogEntries);
        }

        /// <summary>
        /// Log List of string to disk.
        /// </summary>
        /// <param name="records"></param>
        public void Log(in List<string> records)
        {
            var fileLogEntries = new List<string>();

            foreach (var str in records)
            {
                fileLogEntries.Add(options.StringFormatter(str));
            }

            LogToFile(fileLogEntries);
        }

        /// <summary>
        /// Log single string to disk.
        /// </summary>
        /// <param name="logStr"></param>
        public override void Log(string logStr)
        {
            var fileLogEntries = new List<string>();

            fileLogEntries.Add(options.StringFormatter(logStr));

            LogToFile(fileLogEntries);
        }
    }
}
