﻿// <copyright file="CertUtils.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System.Diagnostics.CodeAnalysis;
using System.Reflection;
using System.Security.Cryptography;
using System.Security.Cryptography.X509Certificates;

namespace Microsoft.M365.ODL.NrtTcpClient
{
    /// <summary>
    /// For certificate related utilities
    /// </summary>
    [ExcludeFromCodeCoverage]
    public sealed class CertUtils
    {
        /// <summary>
        /// The tls certificate file name
        /// </summary>
        public const string TlsCertificateFilePath = "C:/ODL/TLS/tls.pfx";

        /// <summary>
        /// The tls certificate password
        /// </summary>
        public const string TlsCertificatePassword = "password";

        /// <summary>
        /// The tls server ca certificate file path for tls authentication
        /// </summary>
        public const string TlsSeverCACertificateFilePath = "C:/ODL/TLS/ca.crt";

        /// <summary>
        /// The folder path where tls related cert/key/ca cert files are stored
        /// </summary>
        public const string TlsCertsFolderPath = "C:/ODL/TLS/";

        /// <summary>
        /// Get tls certificate collections
        /// </summary>
        /// <param name="certFolderPath"></param>
        /// <param name="pfxPassword"></param>
        /// <param name="certFilePath"></param>
        /// <param name="certName"></param>
        /// <param name="storeLocation"></param>
        /// <param name="storeName"></param>
        /// <returns></returns>
        public static X509Certificate2Collection? GetTlsCertificates(string certFolderPath, string pfxPassword, string certFilePath, string certName, StoreLocation storeLocation = StoreLocation.LocalMachine, StoreName storeName = StoreName.Root)
        {
            if (!string.IsNullOrEmpty(certName))
            {
                return GetCertificates(certName, storeName, storeLocation);
            }
            if (!string.IsNullOrEmpty(certFilePath))
            {
                return GetCertificates(certFilePath, pfxPassword);
            }
            if (!string.IsNullOrEmpty(certFolderPath))
            {
                string certPath = GetFile(certFolderPath, "*tls*.pfx", ".pfx").FullName;
                return GetCertificates(certPath, pfxPassword);
            }
            return GetCertificates(TlsCertificateFilePath, TlsCertificatePassword);
        }

        /// <summary>
        /// Get certificate from store by cert subject name
        /// </summary>
        /// <param name="certName"></param>
        /// <param name="storeName"></param>
        /// <param name="storeLocation"></param>
        /// <returns></returns>
        public static X509Certificate2Collection? GetCertificates(string certName, StoreName storeName, StoreLocation storeLocation)
        {
            X509Certificate2Collection certificates = null;

            var store = new X509Store(storeName, storeLocation);
            try
            {
                store.Open(OpenFlags.ReadOnly | OpenFlags.OpenExistingOnly);
                certificates = store.Certificates.Find(X509FindType.FindBySubjectName, certName, false);
                return certificates;
            }
            catch (CryptographicException)
            {
                throw new KeyNotFoundException($"The specified cert {certName} was not found from store.");
            }
            finally
            {
                store.Close();
            }
        }

        /// <summary>
        /// Get certificate from file path
        /// </summary>
        /// <param name="certFilePath"></param>
        /// <param name="pfxPassword"></param>
        /// <returns></returns>
        public static X509Certificate2 GetCertificate(string certFilePath, string pfxPassword)
        {
            try
            {
                return string.IsNullOrEmpty(pfxPassword) ? new X509Certificate2(certFilePath) : new X509Certificate2(certFilePath, pfxPassword);
            }
            catch (CryptographicException)
            {
                throw new KeyNotFoundException($"The specified cert was not found from path {certFilePath}");
            }
        }

        /// <summary>
        /// Get certificate from file path
        /// </summary>
        /// <param name="certFilePath"></param>
        /// <param name="pfxPassword"></param>
        /// <returns></returns>
        public static X509Certificate2Collection GetCertificates(string certFilePath, string pfxPassword)
        {
            return new X509Certificate2Collection(GetCertificate(certFilePath, pfxPassword));
        }

        /// <summary>
        /// Get proper file path for the tls related files
        /// </summary>
        /// <param name="folderPath"></param>
        /// <param name="searchPattern"></param>
        /// <param name="fileExtention"></param>
        /// <returns></returns>
        public static FileInfo GetFile(string folderPath, string searchPattern, string fileExtention)
        {
            DirectoryInfo dir = new DirectoryInfo(folderPath);
            var fileList = dir.GetFiles(searchPattern, SearchOption.AllDirectories);

            var fileQuery = from file in fileList
                            where file.Extension == fileExtention
                            orderby file.Name
                            select file;

            var newestFile = (from file in fileQuery
                              orderby file.CreationTime
                              select new { file.FullName, file.CreationTime })
                              .Last();

            return new FileInfo(newestFile.FullName);
        }

        /// <summary>
        /// Get server side ca cert for cert validation
        /// </summary>
        /// <param name="caCertPath"></param>
        /// <param name="certFolderPath"></param>
        /// <returns></returns>
        public static X509Certificate2 GetCACertificate(string caCertPath, string certFolderPath)
        {
            if (!string.IsNullOrEmpty(caCertPath))
            {
                return new X509Certificate2(caCertPath);
            }
            string certPath = GetFile(string.IsNullOrEmpty(certFolderPath) ? TlsCertsFolderPath : certFolderPath, "*ca*.crt", ".crt").FullName;
            return GetCertificate(certPath, string.Empty);
        }

        /// <summary>
        /// Try to resolve the certificate file path
        /// </summary>
        /// <param name="unresolvedFilePath"></param>
        /// <param name="resolvedFilePath"></param>
        /// <returns></returns>
        public static bool TryResolveCertificateFilePath(string unresolvedFilePath, out string resolvedFilePath)
        {
            bool result = false;
            resolvedFilePath = null;
            if (File.Exists(unresolvedFilePath))
            {
                resolvedFilePath = unresolvedFilePath;
                result = true;
            }
            else
            {
                string directoryName = Path.GetDirectoryName(Assembly.GetExecutingAssembly().Location);
                string text = Path.Combine(directoryName, unresolvedFilePath);
                if (File.Exists(text))
                {
                    resolvedFilePath = text;
                    result = true;
                }
            }

            return result;
        }
    }
}
