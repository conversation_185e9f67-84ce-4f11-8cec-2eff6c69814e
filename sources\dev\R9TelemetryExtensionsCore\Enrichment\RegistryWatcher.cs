﻿// <copyright file="RegistryWatcher.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System;
using System.Diagnostics.CodeAnalysis;
using System.Runtime.InteropServices;
using System.Threading;
#if !NETFRAMEWORK
using Microsoft.M365.Core.Portable.Registry;
#else
using Microsoft.Win32;
#endif

namespace Microsoft.M365.Core.Telemetry.Enrichment
{
    /// <summary>
    /// RegistryWatcher class to receive notification of changes under a registry key
    /// </summary>
    [ExcludeFromCodeCoverage]
    internal sealed class RegistryWatcher : IRegistryWatcher, IDisposable
    {
        /// <summary>
        /// Monitor thread
        /// </summary>
        private Thread monitorThread;

        /// <summary>
        /// Handle to registry
        /// </summary>
        private SafeHandle registryHandle;

        /// <summary>
        /// shut down event
        /// </summary>
        private AutoResetEvent shutdownEvent;

        /// <summary>
        /// Registry CHanged Event
        /// </summary>
        private AutoResetEvent registryChangedEvent;

        /// <summary>
        /// Custom Constructor
        /// </summary>
        /// <param name="hive">Registry Hive</param>
        /// <param name="keyPath">The Key Path</param>
        public RegistryWatcher(RegistryHive hive, string keyPath)
            : this(hive, keyPath, false)
        {
        }

        /// <summary>
        /// Custom Constructor
        /// </summary>
        /// <param name="hive">Registry Hive</param>
        /// <param name="keyPath">The Key Path</param>
        /// <param name="watchSubtree">Should watch subtree</param>
        public RegistryWatcher(RegistryHive hive, string keyPath, bool watchSubtree)
            : this(hive, keyPath, watchSubtree, RegistryChangeNotificationFilter.NameChange | RegistryChangeNotificationFilter.ValueChange)
        {
        }

        /// <summary>
        /// Custom constructor
        /// </summary>
        /// <param name="hive">Registry Hive</param>
        /// <param name="keyPath">The Key Path</param>
        /// <param name="watchSubtree">Should watch subtree</param>
        /// <param name="filter">Notification filter</param>
        public RegistryWatcher(RegistryHive hive, string keyPath, bool watchSubtree, RegistryChangeNotificationFilter filter)
        {
            this.Hive = hive;
            this.KeyPath = keyPath;
            this.WatchSubtree = watchSubtree;
            this.Filter = filter;
        }

        /// <summary>
        /// Gets the hive this watcher is currently monitoring
        /// </summary>
        public RegistryHive Hive
        {
            get;
            private set;
        }

        /// <summary>
        /// Gets the key path this watcher is currently monitoring
        /// </summary>
        public string KeyPath
        {
            get;
            private set;
        }

        /// <summary>
        /// Gets a value indicating whether or not the sub-tree is being monitored
        /// </summary>
        public bool WatchSubtree
        {
            get;
            private set;
        }

        /// <summary>
        /// Gets the filter for the current watcher
        /// </summary>
        public RegistryChangeNotificationFilter Filter
        {
            get;
            private set;
        }

        /// <summary>
        /// Raised when an error occurs while monitoring the key.
        /// </summary>
        public Action<Exception> Error
        {
            get;
            set;
        }

        /// <summary>
        /// to be deleted
        /// Raised each time a registry change is detected according to the parameters of the watcher
        /// </summary>
        public Action KeyChanged
        {
            get;
            set;
        }

        /// <summary>
        /// Dispose Method
        /// </summary>
        public void Dispose()
        {
            this.Stop();
        }

        /// <summary>
        /// Stops monitoring the registry key.
        /// </summary>
        public void Stop()
        {
            if (this.monitorThread != null && this.shutdownEvent != null)
            {
                // Since we are going to stop the thread that is monitoring the registry key
                // We need to mark the event handle as invalid which will stop monitoring
                // Otherwise when the thread stops the event is going to signal and that will cause an exception
                // (becase the safehandle is already disposed)
                this.registryChangedEvent.SafeWaitHandle.SetHandleAsInvalid();

                // Tell the thread to stop
                this.shutdownEvent.Set();

                // Join with moniter thread only if stop is not called from moniter thread. Else there will be a deadlock.
                if (this.monitorThread.ManagedThreadId != Thread.CurrentThread.ManagedThreadId)
                {
                    this.monitorThread.Join();
                }

                this.monitorThread = null;
            }
            else
            {
                this.StopInternal();
            }
        }

        /// <summary>
        /// Begins monitoring of the registry key according to the supplied parameters.
        /// </summary>
        public void Start()
        {
            if (this.registryHandle == null)
            {
                this.registryHandle = RegistryHelper.OpenSubKey(this.Hive, this.KeyPath, RegistryAccessMask.Read | RegistryAccessMask.Notify);

                if (this.registryHandle != null)
                {
                    this.shutdownEvent = new AutoResetEvent(false);
                    this.registryChangedEvent = new AutoResetEvent(false);

                    this.monitorThread = new Thread(new ThreadStart(this.MonitorRegistryKey));
                    this.monitorThread.IsBackground = true;
                    this.monitorThread.Start();
                }
            }
        }

        /// <summary>
        /// Handles exception
        /// </summary>
        /// <param name="ex">The exception object</param>
        private void OnError(Exception ex)
        {
            if (this.Error != null)
            {
                try
                {
                    this.Error(ex);
                }
                catch (Exception)
                {
                    // Since invoked from the background thread do not let this propagate
                }
            }
        }

        /// <summary>
        /// to be deleted
        /// </summary>
        private void OnKeyChanged()
        {
            if (this.KeyChanged != null)
            {
                try
                {
                    this.KeyChanged();
                }
                catch (Exception)
                {
                    // Since invoked from the background thread do not let this propagate
                }
            }
        }

        /// <summary>
        /// Stops the Registry Watcher
        /// </summary>
        private void StopInternal()
        {
            if (this.registryHandle != null)
            {
                this.registryHandle.Dispose();
                this.registryHandle = null;
            }

            if (this.shutdownEvent != null)
            {
                ((IDisposable)this.shutdownEvent).Dispose();
                this.shutdownEvent = null;
            }

            if (this.registryChangedEvent != null)
            {
                ((IDisposable)this.registryChangedEvent).Dispose();
                this.registryChangedEvent = null;
            }
        }

        /// <summary>
        /// Starts Monitoring the key
        /// </summary>
        /// <returns>Returns a boolean value</returns>
        private bool StartKeyMonitor()
        {
            try
            {
                RegistryHelper.NotifyChangeKeyValue(this.registryHandle, this.WatchSubtree, this.Filter, this.registryChangedEvent.SafeWaitHandle, true);
                return true;
            }
            catch (Exception ex)
            {
                // For instance, m_registryChangedEvent.SafeWaitHandle can be invalid which will cause an exception
                this.StopInternal();
                this.OnError(ex);
                return false;
            }
        }

        /// <summary>
        /// Monitors the registry key
        /// </summary>
        private void MonitorRegistryKey()
        {
            try
            {
                if (!this.StartKeyMonitor())
                {
                    return;
                }

                while (true)
                {
                    int waitResult = WaitHandle.WaitAny(new WaitHandle[] { this.shutdownEvent, this.registryChangedEvent, });

                    // Shutdown has been initiated
                    if (waitResult == 0)
                    {
                        this.StopInternal();
                        break;
                    }
                    else
                    {
                        if (!this.StartKeyMonitor())
                        {
                            return;
                        }

                        this.OnKeyChanged();
                    }
                }
            }
            catch (Exception ex)
            {
                this.StopInternal();
                this.OnError(ex);
            }
        }
    }
}
