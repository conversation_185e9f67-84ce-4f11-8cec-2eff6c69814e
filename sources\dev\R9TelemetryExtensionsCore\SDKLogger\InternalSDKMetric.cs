﻿// <copyright file="InternalSDKMetric.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System.Diagnostics.CodeAnalysis;
using Microsoft.R9.Extensions.Metering;

namespace Microsoft.M365.Core.Telemetry.SDKLogger
{
    [ExcludeFromCodeCoverage]
    internal static partial class InternalSDKMetric
    {
        /// <summary>
        /// CreateSDKCounter
        /// </summary>
        /// <param name="meter">meter</param>
        /// <returns></returns>
        [Histogram("Event", "Type", "Program")]
        public static partial SDKCounter CreateSDKCounter(IMeter meter);

        /// <summary>
        /// CreateSDKDebugPerfCounter
        /// </summary>
        /// <param name="meter">meter</param>
        /// <returns></returns>
        [Gauge]
        public static partial SDKDebugPerCounter CreateSDKDebugPerfCounter(IMeter meter);
    }
}
