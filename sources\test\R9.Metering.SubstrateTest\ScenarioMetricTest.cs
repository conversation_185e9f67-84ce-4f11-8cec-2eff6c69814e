﻿// <copyright file="ScenarioMetricTest.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System.Diagnostics.Metrics;
using Microsoft.M365.Core.Telemetry.R9.Metering.Substrate;
using Xunit;

namespace R9.Metering.SubstrateTest
{
    public class ScenarioMetricTest
    {
        [Fact]
        public void TestCreateLatencyMetric()
        {
            var meter = new Meter("test");
            var latencyMetric = R9ScenarioMetric.CreateLatencyMetric(meter);
            Assert.NotNull(latencyMetric);

            latencyMetric.Record(123, new ScenarioDimension("component", "scenario", "subScenario"));
        }

        [Fact]
        public void TestCreateAvailabilityMetric()
        {
            var meter = new Meter("test");
            var availabilityMetric = R9ScenarioMetric.CreateAvailabilityMetric(meter);
            Assert.NotNull(availabilityMetric);

            availabilityMetric.Record(1, new ScenarioDimension("component", "scenario", "subScenario"));
        }

        [Fact]
        public void TestCreateErrorMetric()
        {
            var meter = new Meter("test");
            var errorMetric = R9ScenarioMetric.CreateErrorMetric(meter);
            Assert.NotNull(errorMetric);

            errorMetric.Add(1, new ErrorScenario("component", "scenario", "subScenario", "Test"));
        }
    }
}
