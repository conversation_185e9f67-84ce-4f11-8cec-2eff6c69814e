# About Logging Solution

*<div style="text-align: right; font-size:12px">Last Modified: @@LastModified</div>*

Welcome to the **Substrate Logging Solution!** 

The solution is recommended by the M365 Unified Telemetry (Logs/Metrics) Team. Overall, we recommend all Substrate service leverage **.NET Logging** ([Logging in C# and .NET](https://learn.microsoft.com/en-us/dotnet/core/extensions/logging?tabs=command-line)). 


## Benefits

The adopt will brings in those benefits for logging:
- **Enabled Linux support** 
- **Enable cross model supporting ability**, single code serves Model A/B2/COSMIC. 
- **Performance improvement**, ILogger API, is designed for high performance.
- **New features**, log sampling to reduce log volume, routing between exporters and etc.


## Main work
The implementation consists of two main parts: instrumentation and configure LoggerFactory

![](../../.images/DotNetLogging.png)

### 1. Instrumentation
Use [ILogger](https://learn.microsoft.com/en-us/dotnet/api/microsoft.extensions.logging.ilogger?view=net-9.0-pp) and [Compile-time logging source generation API (aka Fast Logging)](https://learn.microsoft.com/en-us/dotnet/core/extensions/logger-message-generator) provided by [Microsoft.Extensions.Telemetry.Abstractions](https://www.nuget.org/packages/Microsoft.Extensions.Telemetry.Abstractions/). The compile-time logging solution is typically considerably faster at run time than existing logging approaches. It achieves this by eliminating boxing, temporary allocations, and copies to the maximum extent possible. Examples could be found [.NET extensions complexObjectLogging example](https://github.com/dotnet/extensions-samples/tree/0d088b48e0d4114748ad4c13103202307527f946/src/Telemetry/Logging/ComplexObjectLogging)


### 2. Configure LoggerFactory
Use [Substrate Logging Extension](../../SDKs/LoggingExtensions/About.md).  It is designed for Substrate services to configure loggerProvider with common enrichers, exporters, ECS and shared metrics built-in, allowing service teams to focus on their specific needs without worrying about fundamental details.

## Adopt the solution
No matter you are creating new services or migrating existing service telemetry from legacy SDKs like IFx/PassiveMon, you can follow these steps to complete the adoption

### Step-by-Step Guide
- [Prerequisites](GuideSteps/Prerequisites.md)
- [Set Up Configuration](GuideSteps/Configuration.md)
- [Initialize R9 Telemetry](GuideSteps/InitR9.md)
- [Instrument Data](GuideSteps/Instrumentation.md)
- [Validate Locally](GuideSteps/LocalValid.md)
- [Update Monitoring Agent](GuideSteps/UpdateMA.md)
- [(For Migration) Check Potential Risks](GuideSteps/PotentialRisks.md)
- [(For Migration) Switch Data Seamlessly](GuideSteps/MigrationSteps.md)

### Sample PRs

- Sample PR on SDK: 
	- [Pull Request 3808523 : [Minor]Migrate Log from PassiveMon(IFx) to R9 Telemetry(Open Telemetry)](https://o365exchange.visualstudio.com/O365%20Core/_git/dsapisdk/pullrequest/3808523)

- Sample PRs on Monitoring Agent (can be merged to one):
	- [Pull request 3875976: include r9 events for dsapisdk](https://o365exchange.visualstudio.com/O365%20Core/_git/PassiveMonCore/pullrequest/3875976?_a=files)
	- [Pull request 3978685: dump DsApiSdkR9Event to kusto](https://o365exchange.visualstudio.com/O365%20Core/_git/PassiveMonCore/pullrequest/3978685?_a=files&path=/sources/dev/PassiveMonitoring/src/Deployment/Agent/Configuration/O365PassiveSdf/O365PassiveSdf-BE.xml)
	- [Pull request 4009087: dump to kusto](https://o365exchange.visualstudio.com/O365%20Core/_git/PassiveMonCore/pullrequest/4009087)