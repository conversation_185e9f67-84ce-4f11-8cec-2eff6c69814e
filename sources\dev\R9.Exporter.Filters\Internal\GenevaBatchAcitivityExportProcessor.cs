﻿// <copyright file="GenevaBatchAcitivityExportProcessor.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System;
using System.Diagnostics.CodeAnalysis;
using Microsoft.Extensions.Options;
using Microsoft.R9.Extensions.Tracing.Exporters;
using OpenTelemetry;
using OpenTelemetry.Exporter.Geneva;

namespace Microsoft.R9.Extensions.Telemetry.Exporter.Filters
{
    /// <summary>
    /// Geneva Exporter with batch paramaters
    /// </summary>
    /// <remarks>will remove in one of the conditions
    /// - R9 make the class visible to this project
    /// - the geneva extension with filter will be added to Tracing.Exporters.Geneva 
    /// </remarks>
    [ExcludeFromCodeCoverage]
    internal class GenevaBatchActivityExportProcessor : BatchActivityExportProcessor
    {
        public GenevaBatchActivityExportProcessor(IOptions<GenevaTraceExporterOptions> options)
            : base(
#pragma warning disable CA2000 // Dispose objects before losing scope
                new GenevaTraceExporter(options.Value),
#pragma warning restore CA2000 // Dispose objects before losing scope
                maxQueueSize: options.Value.MaxQueueSize,
                maxExportBatchSize: options.Value.MaxExportBatchSize,
                scheduledDelayMilliseconds: Convert.ToInt32(options.Value.ExportInterval.TotalMilliseconds))
        {
        }
    }
}
