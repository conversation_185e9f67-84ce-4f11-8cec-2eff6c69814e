﻿// <copyright file="RegistryChangeNotificationFilter.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

namespace Microsoft.M365.Core.Telemetry.Enrichment
{
    /// <summary>
    /// RegistryChangeNotificationFilter filter class
    /// </summary>
    internal enum RegistryChangeNotificationFilter : uint
    {
        /// <summary>
        /// Notify the caller if a subkey is added or deleted. 
        /// Corrsponds to Win32 Value REG_NOTIFY_CHANGE_NAME 
        /// </summary>
        NameChange = 1,

        /// <summary>
        /// Notify the caller of changes to the attributes of the key.  
        /// Corrsponds to Win32 Value REG_NOTIFY_CHANGE_ATTRIBUTES 
        /// </summary>
        AttributeChange = 2,

        /// <summary>
        /// Notify the caller of changes to a value of the key. 
        /// This can include adding or deleting a value, or changing an existing value. 
        /// 
        /// Corrsponds to Win32 Value REG_NOTIFY_CHANGE_LAST_SET
        /// </summary>
        ValueChange = 4,

        /// <summary>
        /// Notify the caller of changes to the security descriptor of the key.
        /// 
        /// Corrsponds to Win32 Value REG_NOTIFY_CHANGE_SECURITY
        /// </summary>
        SecurityChange = 8,
    }
}
