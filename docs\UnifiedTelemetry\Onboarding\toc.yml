- name: Logging
  href: LoggingSolution/About.md
  items:
  - name: R9 Onboarding
    items:
    - name: Step 0 - Prerequisites
      href: LoggingSolution/GuideSteps/Prerequisites.md
    - name: Step 1 - Set Up Configration
      href: LoggingSolution/GuideSteps/Configuration.md
    - name: Step 2 - Initialize R9 Telemetry
      href: LoggingSolution/GuideSteps/InitR9.md
    - name: Step 3 - Instrument Data
      href: LoggingSolution/GuideSteps/Instrumentation.md
    - name: Step 4 - Validate Locally
      href: LoggingSolution/GuideSteps/LocalValid.md
    - name: Step 5 - Update Monitoring Agent
      href: LoggingSolution/GuideSteps/UpdateMA.md
  - name: Migration from IFx/PassiveMon SDK
    href: IfxPassiveMonDeprecate.md
    items:
    - name: Step 0 - Prerequisites
      href: LoggingSolution/GuideSteps/MiPrerequisites.md
    - name: Step 1 - Migrate Instrumentaion
      href: LoggingSolution/GuideSteps/MigrationInstrumentaion.md
    - name: Step 2 - Check Potential Risks
      href: LoggingSolution/GuideSteps/PotentialRisks.md
    - name: Step 3 - Switch Data Seamlessly
      href: LoggingSolution/GuideSteps/MigrationSteps.md
  - name: MDS to ODL Migration
    href: LoggingSolution/OdlKustoDumper.md
    items:
    - name: Step-by-step Guide
      href: LoggingSolution/StepByStep.md
    - name: Odl Export Schema
      href: LoggingSolution/OdlSchema.md
  - name: Sampling
    href: LoggingSolution/Sampling.md


- name: Metering
  href: MeteringSolution/About.md
  items:
  - name: R9 Onboarding
    items:
    - name: Step 0 - Prerequisites
      href: MeteringSolution/GuideSteps/Prerequisites.md
    - name: Step 1 - Set Up Configration
      href: MeteringSolution/GuideSteps/Configuration.md
    - name: Step 2 - Initialize R9 Telemetry
      href: MeteringSolution/GuideSteps/InitR9.md
    - name: Step 3 - Instrument Data
      href: MeteringSolution/GuideSteps/Instrumentation.md
    - name: Step 4 - Validate Locally
      href: MeteringSolution/GuideSteps/LocalValid.md
  - name: Migration from IFx/PassiveMon SDK
    href: IfxPassiveMonDeprecate.md
    items:
    - name: Step 0 - Prerequisites
      href: MeteringSolution/GuideSteps/MiPrerequisites.md
    - name: Step 1 - Migrate Instrumentaion
      href: MeteringSolution/GuideSteps/IfxMigration.md
    - name: Step 2 - Check Potential Risks
      href: MeteringSolution/GuideSteps/PotentialRisks.md
    - name: Step 3 - Switch Data Seamlessly
      href: MeteringSolution/GuideSteps/MigrationSteps.md