﻿// <copyright file="TcpClientCommonTest.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System.Diagnostics;
using System.Drawing;
using System.Text;
using Google.Protobuf.WellKnownTypes;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.M365.Core.Telemetry.ODL.SecurityTelemetryTcpExporter;
using Microsoft.M365.ODL.NrtTcpClient;
using Microsoft.Office.BigData.DataLoader;
using Microsoft.R9.Extensions.SecurityTelemetry;
using Newtonsoft.Json.Linq;
using Xunit;

#pragma warning disable R9EXP0008
#pragma warning disable R9EXPDEV
namespace TcpExporterTest
{
    /// <summary>
    /// TcpClientCommonTest
    /// </summary>
    public class TcpClientCommonTest
    {
        /// <summary>
        /// get request
        /// </summary>
        /// <param name="seq"></param>
        /// <returns></returns>
        public static ODLNRTRequest GetRequest(long seq)
        {
            ODLNRTRequest oDLNRTRequest = new ODLNRTRequest();
            oDLNRTRequest.Head = new ODLNRTCommonHead();
            oDLNRTRequest.Head.CommandType = ODLNRTCommandType.OdlnrtcmdSecurityMessageBatch;
            oDLNRTRequest.Head.Timestamp = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds();
            oDLNRTRequest.Head.Sequence = seq;

            oDLNRTRequest.SecurityMessageBatchReq = new ODLNRTSecurityMessageBatchReq();
            oDLNRTRequest.SecurityMessageBatchReq.Head = new ODLNRTMessageHead();
            oDLNRTRequest.SecurityMessageBatchReq.Head.Timestamp = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds();

            for (int i = 0; i < 30; i++)
            {
                var message = new ODLNRTMessage();
                oDLNRTRequest.SecurityMessageBatchReq.Messages.Add(message);

                StringBuilder sb = new StringBuilder();
                sb.Append(seq);
                for (int j = 0; j < 1800; j++)
                {
                    sb.Append("+");
                }
                message.Message = sb.ToString();
                message.ID = $"{seq}";
                message.EventID = seq;
                message.Source = $"schema{seq}";
            }

            return oDLNRTRequest;
        }

        /// <summary>
        /// CreateSecurityRecords
        /// </summary>
        /// <param name="schemaName"></param>
        /// <param name="keyLen"></param>
        /// <param name="valueLen"></param>
        /// <param name="staticKeyCnt"></param>
        /// <param name="dynamicKeyCnt"></param>
        /// <param name="recordCnt"></param>
        /// <param name="prepopulatedFields"></param>
        /// <returns></returns>
        public static SecurityRecord[] CreateSecurityRecords(string schemaName, int keyLen, int valueLen, int staticKeyCnt, int dynamicKeyCnt, int recordCnt,
            out Dictionary<string, object> prepopulatedFields)
        {
            prepopulatedFields = new Dictionary<string, object>();
            SecurityRecord[] recordList = new SecurityRecord[recordCnt];
            StringBuilder sb = new StringBuilder();
            List<string> generatedKeys = new List<string>(), generatedVals = new List<string>();

            for (int i = 0; i < staticKeyCnt; i++)
            {
                for (int j = 1; j < keyLen; j++)
                {
                    sb.Append('k');
                }
                sb.Append(i);

                generatedKeys.Add(sb.ToString());
                sb.Clear();

                for (int j = 1; j < valueLen; j++)
                {
                    sb.Append('v');
                }
                sb.Append(i);
                generatedVals.Add(sb.ToString());
                sb.Clear();
            }
            var staticKeys = new List<string>(generatedKeys);
            int w = 0;
            foreach (string key in staticKeys)
            {
                prepopulatedFields[key] = generatedVals[w++];
            }

            for (int i = 0; i < recordCnt; i++)
            {
                SecurityRecord securityRecord = new SecurityRecord();
                recordList[i] = securityRecord;
                securityRecord.Initialize(schemaName, staticKeys);

                for (int j = staticKeyCnt; j < dynamicKeyCnt + staticKeyCnt; j++)
                {
                    sb.Clear();
                    for (int k = 1; k < keyLen; k++)
                    {
                        sb.Append('k');
                    }
                    sb.Append(j);
                    string key = sb.ToString();

                    sb.Clear();
                    for (int k = 1; k < valueLen; k++)
                    {
                        sb.Append('v');
                    }
                    sb.Append(j);
                    string value = sb.ToString();

                    securityRecord.AddProperty(key, value);
                }
            }

            return recordList;
        }

        /// <summary>
        /// TestDefaultSecurityRecordFormatter
        /// </summary>
        [Fact]
        public void TestDefaultSecurityRecordFormatter()
        {
            Dictionary<string, object> prepopulated = new Dictionary<string, object>();
            var record = CreateSecurityRecords("testSchema", 3, 3, 5, 10, 1, out prepopulated);
            var formattedStr = DefaultSecurityRecordFormatter.FormatSecurityRecord(record[0], prepopulated);
            Assert.Equal(@"{""kk5"":""vv5"",""kk6"":""vv6"",""kk7"":""vv7"",""kk8"":""vv8"",""kk9"":""vv9"",""kk10"":""vv10"",""kk11"":""vv11"",""kk12"":""vv12"",""kk13"":""vv13"",""kk14"":""vv14"",""SchemaName"":""testSchema"",""ValidationStatus"":0,""kk0"":""vv0"",""kk1"":""vv1"",""kk2"":""vv2"",""kk3"":""vv3"",""kk4"":""vv4""}", formattedStr);
        }

        /// <summary>
        /// TestSecurityTelemetryTcpExporterExtensions
        /// </summary>
        [Fact]
        public void TestSecurityTelemetryTcpExporterExtensions()
        {
            Dictionary<string, object> prepopulatedFields = new Dictionary<string, object>();
            var record = CreateSecurityRecords("testSchema", 3, 3, 5, 10, 1, out prepopulatedFields);

            IHostBuilder hostbuilder = new HostBuilder()
    .ConfigureServices((context, services) =>
    {
        _ = services.AddSecurityTelemetry(builder =>
        {
            _ = services.Configure<SecurityTelemetryOptions>(option =>
            {
                option.PrepopulatedFields = prepopulatedFields;
            });
            _ = services.Configure<SecurityTelemetryTcpExporterOptions>(option =>
            {
                option.ConnectionHealthCheckInterval = 30000;
                option.RecordCountPerRequest = 30;
            });
            _ = builder.AddSecurityTelemetryTcpExporter();
        });
    });
            var iHost = hostbuilder.Build();

            var exporter = (SecurityTelemetryExporter)iHost.Services.GetRequiredService<ISecurityTelemetryExporter>();
            Assert.IsType<SecurityTelemetryExporter>(exporter);
        }

        /// <summary>
        /// Test GetFileLoggerOptions
        /// </summary>
        [Fact]
        public void TestGetFileLoggerOptions()
        {
            var logger = FileLoggerManager.GetNRTFileExporter("testSchemaName", true, TimeSpan.FromDays(1), 100);
            Assert.NotNull(logger);

            Assert.Throws<ArgumentNullException>(() => FileLoggerManager.GetNRTFileExporter(string.Empty, true, TimeSpan.FromDays(1), 100));
        }

        /// <summary>
        /// Test NRTMessageSerilizer
        /// </summary>
        [Fact]
        public void TestNRTMessageSerilizer()
        {
            ODLNRTRequest? request = GetRequest(1);
            byte[] buffer = new byte[1024 * 30 * 2];
            NRTMessageSerilizer.Serilize(request, buffer, out byte[] result);
            Assert.Equal(buffer, result);

            buffer = new byte[1];
            NRTMessageSerilizer.Serilize(request, buffer, out result);
            Assert.NotEqual(buffer, result);

            var len = NRTMessageSerilizer.Serilize(request, Array.Empty<byte>(), out result);
            Assert.Equal(result.Length, len);

            byte[] byteArray = new byte[]
            {
            0x0A, 0x0B, 0x08, 0x02, 0x10, 0x81, 0x84, 0xDE,
            0xE9, 0x8E, 0x32, 0x18, 0x01, 0xB2, 0x06, 0x00
            };

            try
            {
                using (MemoryStream memoryStream = new MemoryStream(byteArray))
                {
                    memoryStream.Position = 0;
                    var reponse = ODLNRTResponse.Parser.ParseFrom(memoryStream);
                }
            }
            finally
            { }

            int val = BitConverter.ToInt32(new byte[] { 0x10, 0x00, 0x00, 0x00 }, 0);
            Assert.True(val == 16, "should be 16");
        }

        /// <summary>
        /// E2E test for security telemtry pipeline
        /// </summary>
        [Fact]
        public void TestTcpUtils()
        {
            byte[] byteArray = { 0x48, 0x65, 0x6C, 0x6C, 0x6F };
            string hexString = TcpUtils.ConvertBytesToHexStr(byteArray, 0, 5);
            Assert.Equal(@"48-65-6C-6C-6F", hexString);
        }

        /// <summary>
        /// E2E Test for fallback to disk
        /// </summary>
        private void TestFallbackToDisk()
        {
            ODLNRTRequest oDLNRTRequest = new ODLNRTRequest();
            oDLNRTRequest.Head = new ODLNRTCommonHead();

            oDLNRTRequest.Head.CommandType = ODLNRTCommandType.OdlnrtcmdCommonMessageBatch;
            oDLNRTRequest.Head.Timestamp = DateTime.UtcNow.Millisecond;
            oDLNRTRequest.Head.Sequence = 1;

            oDLNRTRequest.CommonMessageBatchReq = new ODLNRTCommonMessageBatchReq();
            oDLNRTRequest.CommonMessageBatchReq.Head = new ODLNRTCommonMessageHead();
            oDLNRTRequest.CommonMessageBatchReq.Head.Timestamp = DateTime.UtcNow.Millisecond;

            for (int i = 0; i < 100; i++)
            {
                ODLNRTCommonMessage message = new ODLNRTCommonMessage();
                oDLNRTRequest.CommonMessageBatchReq.Messages.Add(message);
                message.ID = Guid.NewGuid().ToString();
                message.EventID = 1;
                message.Source = "testSDKSource";
                message.ValueString = $"string log line {i}";
            }

            NrtTcpClientThread.FallbackToDisk(oDLNRTRequest, new SumDataAggregator(), true, TimeSpan.FromDays(1), 99, new Dictionary<string, List<string>>());
        }

        /// <summary>
        /// E2E test for security telemtry pipeline
        /// </summary>
        private void TestTcpExporter()
        {
            Dictionary<string, object> prepopulatedFields;
            var records = CreateSecurityRecords("testSchema", 10, 20, 5, 55, 512,
                out prepopulatedFields);
            IHostBuilder hostbuilder = new HostBuilder()
                .ConfigureServices((context, services) =>
                {
                    _ = services.AddSecurityTelemetry(builder =>
                    {
                        _ = services.Configure<SecurityTelemetryOptions>(option =>
                        {
                            option.PrepopulatedFields = prepopulatedFields;
                        });
                        _ = services.Configure<SecurityTelemetryTcpExporterOptions>(option =>
                        {
                            option.ConnectionHealthCheckInterval = **********;
                            option.RecordCountPerRequest = 20;
                            option.EnableCleanUpArchivedFiles = true;
                            option.RetentionCount = 88;
                            option.RetentionInterval = TimeSpan.FromDays(1);
                        });

                        builder.WithExporter<SecurityTelemetryExporter>();
                    });
                });
            var iHost = hostbuilder.Build();

            var exporter = (SecurityTelemetryExporter)iHost.Services.GetRequiredService<ISecurityTelemetryExporter>();

            var batch = new Batch<SecurityRecord>(records, records.Length);

            Stopwatch sp = new Stopwatch();
            double total = 0;

            for (int i = 0; i < 100; i++)
            {
                sp.Restart();
                exporter.ExportBatch(batch);
                sp.Stop();
                total += sp.Elapsed.TotalMilliseconds;
            }

            double avg = total / 100;
        }
    }
}
