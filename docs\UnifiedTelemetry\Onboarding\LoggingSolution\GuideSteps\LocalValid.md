# Step 4 - Validate Locally

Although we have configured Geneva Log Exporter at SDK side, logs will not be sent to Dgrep (Geneva) directly.
Instead, there will be a **Geneva Monitoring Agent** that picks up events from services and upload them to Dgrep.
It can also upload logs to Kusto by configuring extra streaming rule.

## Test on TDS

You can test the changes in local dev box or in a TDS.
For creating TDS: [create tds based on focus job - Overview](https://o365exchange.visualstudio.com/O365%20Core/_wiki/wikis/O365%20Core.wiki/496123/create-tds-based-on-focus-job)

## Set up Monitoring Agent on TDS or dev machine

You also need to set up Monitoring Agent to receive your logs: [Set up Geneva on a TDS or dev machine | M365 Passive Monitoring](https://eng.ms/docs/experiences-devices/m365-core/o365-substrate-fabric-extended-suzhou/o365-telemetry/m365-passive-monitoring/m365-passive-monitoring/geneva/tds-setup).

## Update Geneva Monitoring Agent Configuration

<!-- This part should be merge to passive doc -->
When using Geneva Exporter, you will need to modify an xml configuraton file to tell the Agent to monitor and upload your event.

It's like:
Before add the R9 version for them under the EtwSession you configured for these R9 logs in [Configurations](./Configuration.md).
```xml
<OneDSProviders>
    <OneDSProvider name="SampleR9Session" storeType="CentralBond">
        <!-- Sample event -->
        <Event eventName="CustomizedR9Event1" />
        <Event eventName="CustomizedR9Event2" />
    </OneDSProvider>
</OneDSProviders>
```

The `name` of `<OneDSProvider>` specifies the ETW session name, which should align with the session you set in [Configuration](./Configuration.md) for `GenevaExporter`.

The `<Event>` tag defines the event names, which should align with the names specified by `TableNameMappings`(in other words, the values of `TableNameMappings`).

## Summary

We have go through to local validation to make sure the code, app configuraiton and monitoring config all set correctly.

**Next Step**: [Update Monitoring Agent](./UpdateMA.md)
