﻿// <copyright file="DimensionValuesTest.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System;
using System.Reflection;
using Microsoft.M365.Core.Telemetry.Enrichment;
using Microsoft.M365.Core.Telemetry.TestCommon;
using Xunit;
using Xunit.Abstractions;

namespace Microsoft.M365.Core.Telemetry.EnrichmentTest
{
    /// <summary>
    /// DimensionValuesTest
    /// </summary>
    [Collection("Do not parallel")]
    public class DimensionValuesTest : BaseTest
    {
        /// <summary>
        /// DimensionValuesTest
        /// </summary>
        /// <param name="output"></param>
        public DimensionValuesTest(ITestOutputHelper output) : base(output)
        {
        }

        /// <summary>
        /// DefaultTest
        /// </summary>
        [Fact]
        public void DefaultTest()
        {
            Assert.Equal(FakeRegistryKey.Registries[B2PassiveEnricherDimensions.DeployRing], DimensionValues.DeployRing);
            Assert.Equal(FakeRegistryKey.Registries[B2PassiveEnricherDimensions.Role], DimensionValues.Role);
            Assert.Equal(FakeRegistryKey.Registries[B2PassiveEnricherDimensions.Forest], DimensionValues.Forest);
            Assert.Equal(FakeRegistryKey.Registries[B2PassiveEnricherDimensions.Region], DimensionValues.Region);
            Assert.Equal(FakeRegistryKey.Registries[DimensionValues.AvailabilityGroupRegistryName], DimensionValues.AvailabilityGroup);
            Assert.Equal(FakeRegistryKey.Registries[DimensionValues.EnablePassiveECSAADAuthName], DimensionValues.EnablePassiveECSAADAuth);
            Assert.Equal(Environment.MachineName, DimensionValues.Machine);
            Assert.Equal(FakeRegistryKey.DefaultBuildVersion, DimensionValues.BuildVersion);
            Assert.Equal(DimensionValues.MachineProvisioningStateLive, DimensionValues.MachineProvisioningState);
        }

        /// <summary>
        /// RegistryNull
        /// </summary>
        [Fact]
        public void RegistryNull()
        {
            TestRegistryKey.RemoveVariantKey = true;
            DimensionValues.InternalSetRegistryKey(TestRegistryKey);
            Assert.Equal("Unknown", DimensionValues.DeployRing);
            Assert.Equal("Unknown", DimensionValues.Role);
            Assert.Equal("Unknown", DimensionValues.Forest);
            Assert.Equal("Unknown", DimensionValues.Region);
            Assert.Equal("Unknown", DimensionValues.AvailabilityGroup);
            Assert.Equal("Unknown", DimensionValues.Machine);
            Assert.Equal("Unknown", DimensionValues.BuildVersion);
            Assert.Equal("false", DimensionValues.EnablePassiveECSAADAuth);
            Assert.Equal(DimensionValues.MachineProvisioningStateLive, DimensionValues.MachineProvisioningState);

            //Assert.Equal("0.00.0000.000", DimensionValues.BuildVersion);
        }

        /// <summary>
        /// RegistryWatcherWindows
        /// </summary>
        [Fact]
        public void RegistryWatcherWindows()
        {
            var regField = typeof(DimensionValues).GetField("RegWatcher", BindingFlags.Static | BindingFlags.NonPublic);
            var regWatcher = regField.GetValue(null) as Lazy<IRegistryWatcher>;

            FakeRegistryKey.Registries["IsLive"] = "false";
            TestRegistryKey = new FakeRegistryKey();
            DimensionValues.InternalSetRegistryKey(TestRegistryKey);
            regWatcher.Value.KeyChanged();
            Assert.Equal(DimensionValues.MachineProvisioningStateMaintenance, DimensionValues.MachineProvisioningState);

            FakeRegistryKey.Registries["IsLive"] = "false";
            DimensionValues.InternalSetRegistryKey(TestRegistryKey);
            regWatcher.Value.KeyChanged();    
            Assert.NotNull(regWatcher);
            Assert.IsType<RegistryWatcher>(regWatcher.Value);

            FakeRegistryKey.Registries[DimensionValues.EnablePassiveECSAADAuthName] = "true";
            DimensionValues.InternalSetRegistryKey(TestRegistryKey);
            regWatcher.Value.KeyChanged();
            Assert.NotNull(regWatcher);
            Assert.IsType<RegistryWatcher>(regWatcher.Value);
            Assert.Equal("true", DimensionValues.EnablePassiveECSAADAuth);

            FakeRegistryKey fakeRegistryKey = new FakeRegistryKey();
            FieldInfo fiInfo = typeof(DimensionValues).GetField("registryKey", BindingFlags.Static | BindingFlags.NonPublic);
            fiInfo.SetValue(null, fakeRegistryKey);
            FakeRegistryKey.Registries[DimensionValues.EnablePassiveECSAADAuthName] = "false";
            regWatcher.Value.KeyChanged();
            Assert.Equal("false", DimensionValues.EnablePassiveECSAADAuth);
        }

        /// <summary>
        /// RegistryWatcherLinux
        /// </summary>
        [Fact]
        public void RegistryWatcherLinux()
        {
            var regField = typeof(DimensionValues).GetField("RegWatcher", BindingFlags.Static | BindingFlags.NonPublic);
            var regWatcher = regField.GetValue(null) as Lazy<IRegistryWatcher>;
            FakeRegistryKey.Registries["IsLive"] = "false";
            TestRegistryKey = new FakeRegistryKey();
            DimensionValues.InternalSetRegistryKey(TestRegistryKey);
            regWatcher.Value.KeyChanged();
            Assert.Equal(DimensionValues.MachineProvisioningStateMaintenance, DimensionValues.MachineProvisioningState);
            Assert.NotNull(regWatcher);

            FakeRegistryKey.Registries[DimensionValues.EnablePassiveECSAADAuthName] = "true";
            DimensionValues.InternalSetRegistryKey(TestRegistryKey);
            regWatcher.Value.KeyChanged();
            Assert.NotNull(regWatcher);
            Assert.IsType<RegistryWatcher>(regWatcher.Value);
            Assert.Equal("true", DimensionValues.EnablePassiveECSAADAuth);

            FakeRegistryKey fakeRegistryKey = new FakeRegistryKey();
            FieldInfo fiInfo = typeof(DimensionValues).GetField("registryKey", BindingFlags.Static | BindingFlags.NonPublic);
            fiInfo.SetValue(null, fakeRegistryKey);
            FakeRegistryKey.Registries[DimensionValues.EnablePassiveECSAADAuthName] = "false";
            regWatcher.Value.KeyChanged();
            Assert.Equal("false", DimensionValues.EnablePassiveECSAADAuth);
        }

        /// <summary>
        /// DeriveRegionRegistryRegionEmpty
        /// </summary>
        [Fact]
        public void DeriveRegionRegistryRegionEmpty()
        {
            FakeRegistryKey.Registries[B2PassiveEnricherDimensions.Region] = string.Empty;
            FakeRegistryKey.Registries[B2PassiveEnricherDimensions.Forest] = "prdmgt02";
            TestRegistryKey = new FakeRegistryKey();
            DimensionValues.InternalSetRegistryKey(TestRegistryKey);
            Assert.Equal("prd", DimensionValues.Region);
        }

        /// <summary>
        /// DeriveRegionRegistryRegionNull
        /// </summary>
        [Fact]
        public void DeriveRegionRegistryRegionNull()
        {
            FakeRegistryKey.Registries[B2PassiveEnricherDimensions.Region] = null;
            FakeRegistryKey.Registries[B2PassiveEnricherDimensions.Forest] = "prdmgt02";
            TestRegistryKey = new FakeRegistryKey();
            DimensionValues.InternalSetRegistryKey(TestRegistryKey);
            Assert.Equal("prd", DimensionValues.Region);
        }

        /// <summary>
        /// DeriveRegionRegistryRegionWhitespace
        /// </summary>
        [Fact]
        public void DeriveRegionRegistryRegionWhitespace()
        {
            FakeRegistryKey.Registries[B2PassiveEnricherDimensions.Region] = "  ";
            FakeRegistryKey.Registries[B2PassiveEnricherDimensions.Forest] = "prdmgt02";
            TestRegistryKey = new FakeRegistryKey();
            DimensionValues.InternalSetRegistryKey(TestRegistryKey);
            Assert.Equal("prd", DimensionValues.Region);
        }

        /// <summary>
        /// DeriveRegionRegistryRegionUnknown
        /// </summary>
        [Fact]
        public void DeriveRegionRegistryRegionUnknown()
        {
            FakeRegistryKey.Registries[B2PassiveEnricherDimensions.Region] = "Unknown";
            FakeRegistryKey.Registries[B2PassiveEnricherDimensions.Forest] = "prdmgt02";
            TestRegistryKey = new FakeRegistryKey();
            DimensionValues.InternalSetRegistryKey(TestRegistryKey);
            Assert.Equal("prd", DimensionValues.Region);
        }

        /// <summary>
        /// DeriveRegionNoForest
        /// </summary>
        [Fact]
        public void DeriveRegionNoForest()
        {
            FakeRegistryKey.Registries[B2PassiveEnricherDimensions.Region] = string.Empty;
            FakeRegistryKey.Registries[B2PassiveEnricherDimensions.Forest] = string.Empty;
            TestRegistryKey = new FakeRegistryKey();
            DimensionValues.InternalSetRegistryKey(TestRegistryKey);
            Assert.Equal("Unknown", DimensionValues.Region);
        }

        /// <summary>
        /// DeriveRegionShortForest
        /// </summary>
        [Fact]
        public void DeriveRegionShortForest()
        {
            FakeRegistryKey.Registries[B2PassiveEnricherDimensions.Region] = string.Empty;
            FakeRegistryKey.Registries[B2PassiveEnricherDimensions.Forest] = "ab";
            TestRegistryKey = new FakeRegistryKey();
            DimensionValues.InternalSetRegistryKey(TestRegistryKey);
            Assert.Equal("Unknown", DimensionValues.Region);
        }

        /// <summary>
        /// DeriveRegionSpecialForest
        /// </summary>
        [Fact]
        public void DeriveRegionSpecialForest()
        {
            FakeRegistryKey.Registries[B2PassiveEnricherDimensions.Region] = string.Empty;
            FakeRegistryKey.Registries[B2PassiveEnricherDimensions.Forest] = "prdmgt01";
            TestRegistryKey = new FakeRegistryKey();
            DimensionValues.InternalSetRegistryKey(TestRegistryKey);
            Assert.Equal("NAM", DimensionValues.Region);
        }

        /// <summary>
        /// GetMachineVersionBuildValueNull
        /// </summary>
        [Fact]
        public void GetMachineVersionBuildValueNull()
        {
            FakeRegistryKey.Registries[DimensionValues.BuildMinor] = null;
            TestRegistryKey = new FakeRegistryKey();
            DimensionValues.InternalSetRegistryKey(TestRegistryKey);
            Assert.Equal("0.00.0000.000", DimensionValues.BuildVersion);
        }

        /// <summary>
        /// GetMachineVersionFasttrain
        /// </summary>
        [Fact]
        public void GetMachineVersionFasttrain()
        {
            FakeRegistryKey.Registries[DimensionValues.FastTrainVersionValueName] = "15.20.6789.001";
            TestRegistryKey = new FakeRegistryKey();
            DimensionValues.InternalSetRegistryKey(TestRegistryKey);
            Assert.Equal("15.20.6789.001", DimensionValues.BuildVersion);
        }
    }
}
