﻿// <copyright file="WaitHandleCounter.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System;
using System.Threading;

namespace Microsoft.M365.Core.Telemetry.ECSClient
{
    /// <summary>
    /// WaitHandleCounter
    /// </summary>
    internal class WaitHandleCounter : WaitHandle
    {
        /// <summary>
        /// Wait handler count.
        /// </summary>
        private int count;

        /// <summary>
        /// Manual reset event.
        /// </summary>
        private ManualResetEvent mstEvent = new ManualResetEvent(false);

        /// <summary>
        /// Ctor.
        /// </summary>
        /// <param name="initialCount">The Wait handler count.</param>
        public WaitHandleCounter(int initialCount)
        {
            count = initialCount;
        }

        /// <summary>
        /// Add count.
        /// </summary>
        public void AddCount()
        {
            Interlocked.Increment(ref count);
        }

        /// <summary>
        /// Set signal.
        /// </summary>
        public void Signal()
        {
            if (Interlocked.Decrement(ref count) == 0)
            {
                mstEvent.Set();
            }
        }

        /// <summary>
        /// Wait one block caller.
        /// </summary>
        /// <param name="timeInterval"> Time to wait. </param>
        public override bool WaitOne(TimeSpan timeInterval)
        {
            return mstEvent.WaitOne(timeInterval);
        }
    }
}
