﻿// <copyright file="Globals.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace PackageUpgradeHelper.ADO
{
    /// <summary>
    /// Used to store global variables
    /// </summary>
    public class Globals
    {
        /// <summary>
        /// Organization
        /// </summary>
        public const string ORGANIZATION = "O365Exchange";

        /// <summary>
        /// Project
        /// </summary>
        public const string PROJECT = "O365 Core";

        /// <summary>
        /// Repository
        /// </summary>
        public const string REPOSITORY = "Substrate";

        /// <summary>
        /// Source provider 
        /// </summary>
        public const string SOURCE_PROVIDER = "tfsGit";

        /// <summary>
        /// ADO service API version
        /// </summary>
        public const string ADO_API_VERSION = "7.1";

        /// <summary>
        /// PAT
        /// </summary>
        public const string PAT = "PAT";
    }
}
