﻿// <copyright file="ExporterLogger.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System;
using System.Diagnostics.Tracing;

namespace Microsoft.R9.Extensions.Telemetry.Exporters.Base
{
    /// <summary>
    /// export internal telemetry
    /// </summary>
    [EventSource(Name = ODLExporterCommonSettings.InternalDebugEventSourceName)]
    internal class ExporterLogger : EventSource
    {
        /// <summary>
        /// Event id indicating an unexpected error in ETWTraceEventSource processing
        /// </summary>
        public const int ErrorTraceEventSourceProcessing = 4021;

        public static ExporterLogger Log = new ExporterLogger();

        public void LogWriteEventError(string logType, string errorMessage)
        {
            if (Log.IsEnabled(EventLevel.Error, EventKeywords.All))
            {
                // TODO : implement the fields
                Write(
                            "CriticalLogs",
                            new
                            {
                                Level = EventLevel.Error,
                                EventId = ErrorTraceEventSourceProcessing,
                                Message = $"Failed to write etw event for SDKSource {logType} with {errorMessage}",
                                ProductVersion = string.Empty,
                                ODLInstance = string.Empty
                            });
            }
        }

        public void LogUnformattedWarning(string logType)
        {
            if (Log.IsEnabled(EventLevel.Error, EventKeywords.All))
            {
                Write(
                            "CriticalLogs",
                            new
                            {
                                Level = EventLevel.Error,
                                EventId = ErrorTraceEventSourceProcessing,
                                Message = $"UseFormattedMessage is false or FormattedMessage is empty, will not export the log for SDKSource {logType}",
                                ProductVersion = string.Empty,
                                ODLInstance = string.Empty
                            });
            }
        }
    }
}