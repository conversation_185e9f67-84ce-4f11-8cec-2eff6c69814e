# Substrate R9 Telemetry Logging Extension

This extension package is designed for Substrate users to facilitate the utilization of R9 Logging.
It aims to streamline the setup and configuration process for logging scenarios, providing users with the flexibility to easily configure data sampling and the destination to store.

We mostly use configuration to flexibly control the logging behavior.
Services must have essential configurations when using this extension package.

- [Substrate R9 Telemetry Logging Extension](#substrate-r9-telemetry-logging-extension)
  - [Add configurations](#add-configurations)
    - [Populate appsettings.json](#populate-appsettingsjson)
      - [Exporter Option 1: Use Composite Exporter](#exporter-option-1-use-composite-exporter)
      - [Exporter Option 2 (legacy): Use Geneva Exporter](#exporter-option-2-legacy-use-geneva-exporter)
      - [ECS related configurations](#ecs-related-configurations)
    - [Populate default configuration for ECS](#populate-default-configuration-for-ecs)
    - [Load configuration](#load-configuration)
      - [DI](#di)
      - [Non-DI](#non-di)
  - [Initialize R9 Logging in code](#initialize-r9-logging-in-code)
    - [Use IServiceCollection](#use-iservicecollection)
    - [Use ILoggingBuilder](#use-iloggingbuilder)
    - [Advanced - Customize ILoggerBuilder when using ServiceCollection](#advanced---customize-iloggerbuilder-when-using-servicecollection)
  - [Configuration Example and Reference](#configuration-example-and-reference)

## Add configurations
The package control the telemetry behavior with configurations.
It loads specific sections of an `IConfiguration` (which should be input when calling the method) and takes essential settings.
A top-level seciton `SubstrateLogging` is introduced to store our settings.

### Populate appsettings.json
The exporter related configurations are recommended to put in appsettings.json and managed by customers. Also, some ECS related configurations can be included for customization.
#### Exporter Option 1: Use Composite Exporter
When configuring `Composite Exporter`, there **MUST BE** a setting `SubstrateLogging:UseCompositeExporter` with value `true` to enable and a section `SubstrateLogging:CompositeExporter` containing the related configurations.

- `UseCompositeExporter`: Set true to use CompositeExporter.
- `VirtualTableMappings`: Mapping log categories to a virtual table. Each virtual table will be assigned with a export behavior in `VirtualTableExports`.
- `VirtualTableExports`: Define export behavoir of each virtual table.
The key is virtual table name defined in `VirtualTableMappings` and the value is an array.
Each element correspondence to an exporter.
`ExporterType` decides wich exporter to use (finally which platform will the log be emit to).
`ExporterTable` decides the table name on this platform for this event table.
It's the event name on Geneva, and the log type on Odl.
- `Geneva`: Settings for Geneva exporter except `TableNameMappings`, which will be constructed with `VirtualTableMappings` and `VirtualTableExports`.
- `OdlTcp`: Settings for OdlTcp exporter except `LogTypeMappings`, which will be constructed with `VirtualTableMappings` and `VirtualTableExports`.

```jsx
{
    "SubstrateLogging": {
        "UseCompositeExporter": true,
        "CompositeExporter": {
            "VirtualTableMappings": {
                "Test.MyService": "MyServiceTable"
            },
            "Geneva": {
                "ConnectionString": "EtwSession=test"
            },
            "OdlTcp": {
                "ConnectionString": "tcp://localhost:1234"
            },
            "VirtualTableExports": {
                "MyServiceTable": [
                    {
                        "ExporterType": "Geneva",
                        "ExportTable": "ServiceEvent"
                    },
                    {
                        "ExporterType": "OdlTcp",
                        "ExportTable": "TestLogType"
                    }
                ]
            }
        }
    }
}
```

#### Exporter Option 2 (legacy): Use Geneva Exporter
When configuring single Geneva Exporter, there **MUST BE** a `ConnectionString` and `TableNameMappings` section inside subsection `SubstrateLogging:GenevaExporter`.

- `ConnectionString` is used to configure Geneva logger.
- `TableNameMappings` maps logs to event tables.
```jsx
{
    "SubstrateLogging": {
        "GenevaExporter": {
            "ConnectionString": "EtwSession=TestSeesion",
            "TableNameMappings": {
                "Microsoft.App.SomeService": "SomeEvent",
            }
        }
    }
}
```

#### ECS related configurations
> [!Important]
> This section is outdated. Please refer to our [official document](https://eng.ms/docs/experiences-devices/m365-core/o365-substrate-fabric-extended-suzhou/o365-telemetry/m365-unified-telemetry-logsmetrics/m365-unified-telemetry/onboarding/loggingsolution/about).

Totally there are 4 parameters related to ECS, and each of them is **REQUIRED**.
- `ECSIdentifiers` are required when fetching configuration from ECS platform. In order to optimize performance and get the configuration precisely, it's required that customers pass the `ServiceName` identifier. Other identifers are optional and should align with ECS portal's settings.
- `EnvironmentType` indicates the corresponding ECS endpoint to use. `Production` for prod endpoint, and `Integration` for integration endpoint. If other values are provided, it will be set to default value `Integration`.
- `Client` is the name of ECS client. If you want to use our ECS client, please set it to `UnifiedTelemetry`.
- `Agents` includes the project teams under the client. This field should be an **array** even if only 1 project team is included, otherwise exception will be thrown during initialization. If you want to use our ECS project team, please set it to [ "Log" ].
```jsx
{
    "ECSParameters": {
        "ECSIdentifiers": {
            "ServiceName": "<Your service name>"
        },
        "EnvironmentType": "Integration",
        "Client": "UnifiedTelemetry",
        "Agents": [ "Log" ]
    }
}
```
**Note**: 
- Please double check the values of each parameter, because there will be no exception thrown if the values are incorrect. The logging behavior will use default values and can't be controlled by ECS then.
- If you want to use our ECS client and project team, and want to view or edit the ECS configurations by yourself, please apply for SG `SUBSTRATE_R9_LOGGING_USERS` at https://aks.ms/idweb

### Populate default configuration for ECS
To support global control of telemetry, we introduce ECS to implement configuration refresh during runtime. And to avoid failing to fetch configuration when ECS service is not available, we need to provide a copy of default configuration as fallback.

Customer could use following API to get an example of default configuration:
https://s2s.config.skype.com/config/v1/{ClientName}/1.0?agents={AgentName}&EcsCanary=1. 
The returned result is a **full set** of your service's configuration. Customer should pick the configuration items they want, and put them into **ECSDefaultConfig.json**.

Take ClientName=UnifiedTelemetry, AgentName=UnifiedTelemetry as an example. The result is as follows:

```jsx
{
    "ECS": {
        "c72ea287-ed77-4fa6-a480-3712406c367e": "aka.ms/EcsCanary"
    },
    "UnifiedTelemetry": {
        "SubstrateLogging": {
            "R9Logging": {},
            "Exporters": {           
                "Geneva": {
                    "ConnectionString": "EtwSession=test"
                },
                "OdlTcp": {
                    "ConnectionString": "tcp://localhost:1234"
                },
                "VirtualTableMappings": {
                    "Test.TestEvent": "TestEventTable",
                    "Test.TestEvent3": "TestEventTable",
                    "TestMetric.MyService": "MyServiceTable"
                },                
                "VirtualTableExports": {
                    "TestEventTable": [
                        {
                            "ExportPlatform": "Odl",
                            "ExportTableName": "TestLogType"
                        }
                    ],
                    "MyServiceTable": [
                        {
                            "ExportPlatform": "Geneva",
                            "ExportTableName": "TestGeneva"
                        },
                        {
                            "ExportPlatform": "Odl",
                            "ExportTableName": "TestOdl"
                        }
                    ]
                }
            }
        }
    }        
}        
```

### Load configuration
As prerequisite to initialize R9 Logging, we need to load configuration from both **appsettings.json** and ECS into an `IConfiguration` object. There are 2 APIs for DI and non-DI scenarios to load the configuration.
#### DI
In this scenario, we provide an extension method on **IHostBuilder**. It receives 2 parameters:
- `defaultConfigPath`(**required**): Set the path to ECS embedded defaults file.
- `useDefaultOnly`: If set to true, no runtime dependencies on ECS service will be introduced and only the embedded default configuration will be used.

Example usage:
```csharp
var host = Host.CreateDefaultBuilder(args)
            .RegisterConfiguration(Path.Combine(Directory.GetCurrentDirectory(), "ECSDefaultConfig", "ECSDefaultConfig.json"))
```
**Note**: Please make sure the `appsettings.json` has been added into the configuration source of `IHostBuilder` before calling this API.

#### Non-DI
In this scenario, we provide an static method to merge the service side configuration from appsettings.json and the ECS side configuration. It receives 3 parameters:
- `defaultConfigPath`(**required**): Set the path to ECS embedded defaults file.
- `appsettingsPath`(**required**): Set the path to apsettings.json.
- `useDefaultOnly`: If set to true, no runtime dependencies on ECS service will be introduced and only the embedded default configuration will be used.

Examle usage:
```csharp
            IConfiguration configuration = ConfigurationHelper.LoadConfiguration(
                Path.Combine(Directory.GetCurrentDirectory(), "ECSDefaultConfig", "ECSDefaultConfig.json"), 
                Path.Combine(Directory.GetCurrentDirectory(), "appsettings.json"));
```

## Initialize R9 Logging in code
Before calling the initialization method, there **must be** a `IConfiguration configuration` that has loaded the configurations above.

### Use IServiceCollection

```C#
serviceCollection.AddSubstrateLogging(configuration)
```

### Use ILoggingBuilder

```C#
loggingBuilder.ConfigureSubstrateLogging(configuration);
```

### Advanced - Customize ILoggerBuilder when using ServiceCollection
It supports customize the logger by passing a `Action<ILoggingBuilder>` to config `ILoggingBuilder` when adding logger for serviceCollection:
```C#
serviceCollection.AddSubstrateLogging(configuration, loggingBuilder =>
{
    loggingBuilder.AddProcesser<MyProcessor>();
})
```

## Configuration Example and Reference
Here is an example of user config with document reference link in the comments:
```jsx
{
    // We introduce a new section
    "SubstrateLogging": {
        // config of R9 LoggingOptions, see:
        // https://eng.ms/docs/experiences-devices/r9-sdk/refdocs/microsoftr9extensionsloggingloggingextensions
        "R9Logging": {
            "MaxStackTraceLength": 3000
        },
        "UseCompositeExporter": true,
        "CompositeExporter": {
            "VirtualTableMappings": {
                "Test.MyService": "MyServiceTable"
            },
            // https://eng.ms/docs/experiences-devices/r9-sdk/docs/telemetry/logging/geneva-log-export#logger-configuration
            "Geneva": {
                "ConnectionString": "EtwSession=test"
            },
            // https://o365exchange.visualstudio.com/O365%20Core/_git/TelemetryCore?path=/sources/dev/Telemetry.Exporters.ODLTcp/Log/README.md&_a=preview
            "OdlTcp": {
                "ConnectionString": "tcp://localhost:1234"
            },
            "VirtualTableExports": {
                "MyServiceTable": [
                    {
                        "ExporterType": "Geneva",
                        "ExportTable": "ServiceEvent"
                    },
                    {
                        "ExporterType": "OdlTcp",
                        "ExportTable": "TestLogType"
                    }
                ]
            }
        }
        // config of GenevaLogExporterOptions, see:
        // https://eng.ms/docs/experiences-devices/r9-sdk/docs/telemetry/logging/geneva-log-export#logger-configuration
        "GenevaExporter": {
            "ConnectionString": "EtwSession=TestSession"
        }
    },
    // .Net general Logging config, not our scope.
    // Can be set separately
    "Logging": {
        "LogLevel": {
            //...
        }
    },
    //...
}
```