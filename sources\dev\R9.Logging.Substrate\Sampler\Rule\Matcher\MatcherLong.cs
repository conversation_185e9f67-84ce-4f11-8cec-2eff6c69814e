// <copyright file="MatcherLong.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Microsoft.M365.Core.Telemetry.R9.Logging.Substrate.Sampler
{
    /// <summary>
    /// Long related methods for matcher.
    /// <see cref="TryMatch{TState}(in Extensions.Logging.Abstractions.LogEntry{TState})"/> for matching logic.
    /// </summary>
    internal partial class Matcher
    {
        private static bool CompareLongValues(IReadOnlyList<KeyValuePair<string, object?>> state, string fieldName, string fieldValue, Func<long, long, bool> compareFunc)
        {
            var fieldKVP = state.FirstOrDefault(kv => kv.Key == fieldName);
            if (fieldKVP.Value == null)
            {
                return false;
            }

            var valueStr = fieldKVP.Value.ToString();
            if (valueStr == null)
            {
                return false;
            }

            if (!long.TryParse(valueStr, System.Globalization.NumberStyles.Any, System.Globalization.CultureInfo.InvariantCulture, out long logValue) ||
                !long.TryParse(fieldValue, System.Globalization.NumberStyles.Any, System.Globalization.CultureInfo.InvariantCulture, out long constraintValue))
            {
                return false;
            }

            return compareFunc(logValue, constraintValue);
        }

        private static MatchFunc GenerateLongMatchFunc(Constraint constraint)
        {
            var fieldName = constraint.Field;
            var fieldValue = constraint.Value;

            return constraint.RuleOperator switch
            {
                OperatorType.GreaterThan => (_, _, state) =>
                    CompareLongValues(state, fieldName, fieldValue, (s, c) => s > c),

                OperatorType.GreaterThanOrEqual => (_, _, state) =>
                    CompareLongValues(state, fieldName, fieldValue, (s, c) => s >= c),

                OperatorType.LessThanOrEqual => (_, _, state) =>
                    CompareLongValues(state, fieldName, fieldValue, (s, c) => s <= c),

                OperatorType.LessThan => (_, _, state) =>
                    CompareLongValues(state, fieldName, fieldValue, (s, c) => s < c),

                OperatorType.NumericEquals => (_, _, state) =>
                    CompareLongValues(state, fieldName, fieldValue, (s, c) => s == c),

                OperatorType.NumericNotEquals => (_, _, state) =>
                    CompareLongValues(state, fieldName, fieldValue, (s, c) => s != c),

                _ => throw new NotImplementedException($"Unsupported operator type: {constraint.RuleOperator}") // This should never happen, we should have validated the constraints before.
            };
        }
    }
}
