
param (
    [Parameter(Mandatory=$false)]
    [string]$InputFilePath = ".\OutputFiles\result.txt",
    
    [Parameter(Mandatory=$false)]
    [string]$OutputPath = ".\Scripts\complete-bindingredirect-commands.ps1",
    
    [Parameter(Mandatory=$false)]
    [string]$ProjectPath = "Q:\src\Substrate\"
)

# Read the content of result.txt
$lines = Get-Content -Path $InputFilePath

# Rebuild the content to handle multi-line commands
$rebuiltContent = ""
$currentCommand = ""
$inCommand = $false

foreach ($line in $lines) {
    # Check if this line starts a binding redirect command
    if ($line -match "substrate-cli bindingredirects update by-package -n") {
        # If we were already processing a command, add it to the rebuilt content
        if ($inCommand) {
            $rebuiltContent += $currentCommand.Trim() + "`n"
        }
        
        # Start a new command
        $currentCommand = $line
        $inCommand = $true
    }
    # Check if this line is a continuation of a binding redirect command
    elseif ($inCommand -and $line -match "-[vftsnp]\s") {
        # Append to current command with a space
        $currentCommand += " " + $line.Trim()
    }
    # Check if this line contains a version number that should be part of a command
    elseif ($inCommand -and $line -match "^\s*\d+\.\d+\.\d+\.\d+\s*$") {
        # Append to current command with a space
        $currentCommand += " " + $line.Trim()
    }
    else {
        # If we were processing a command, add it to the rebuilt content
        if ($inCommand) {
            $rebuiltContent += $currentCommand.Trim() + "`n"
            $inCommand = $false
            $currentCommand = ""
        }
        
        # Add the current line to the rebuilt content
        $rebuiltContent += $line + "`n"
    }
}

# Add the last command if there is one
if ($inCommand) {
    $rebuiltContent += $currentCommand.Trim() + "`n"
}

# Now extract the complete binding redirect commands
$pattern = "substrate-cli bindingredirects update by-package -n\s+([^\s]+)\s+-v\s+([^\s]+).*?-f\s+([^\s]+)"
$matches = [regex]::Matches($rebuiltContent, $pattern)

# Extract the complete commands and append -P parameter
$commands = @()
$matches | ForEach-Object { 
    # Replace any multiple spaces with a single space and append -P parameter
    $cleanCommand = $_.Value.Trim() -replace "\s+", " "
    if (-not $cleanCommand.Contains(" -P ")) {
        $cleanCommand = "$cleanCommand -P `"$ProjectPath`""
    }
    $commands += $cleanCommand
}

# Also look for "Run:" command patterns
$runPattern = "Run:\s+substrate-cli bindingredirects update by-package.*?-f\s+([^\s]+)"
$runMatches = [regex]::Matches($rebuiltContent, $runPattern)
$runCommands = @()
$runMatches | ForEach-Object {
    $cmd = $_.Value.Replace("Run:", "").Trim() -replace "\s+", " "
    if (-not $cmd.Contains(" -P ")) {
        $cmd = "$cmd -P `"$ProjectPath`""
    }
    if (-not $commands.Contains($cmd)) {
        $runCommands += $cmd
    }
}

# Create output directory if it doesn't exist
$outputDir = Split-Path -Path $OutputPath -Parent
if (-not (Test-Path -Path $outputDir)) {
    New-Item -Path $outputDir -ItemType Directory -Force | Out-Null
}

# Create the header for the file
$executionHeader = @"
# Binding redirect commands for project path: $ProjectPath
# Generated: $(Get-Date)
# Execute these commands one by one

"@

# Write the header to the file
Set-Content -Path $OutputPath -Value $executionHeader

# Append each command on its own line
foreach ($cmd in $commands) {
    Add-Content -Path $OutputPath -Value $cmd
}

# Display summary
Write-Host "Found and extracted $($commands.Count) complete binding redirect commands to $OutputPath"

# Add any additional commands found
if ($runCommands.Count -gt 0) {
    # Add a separator
    Add-Content -Path $OutputPath -Value ""
    Add-Content -Path $OutputPath -Value "# Additional commands from 'Run:' instructions"
    
    # Append each additional command on its own line
    foreach ($cmd in $runCommands) {
        Add-Content -Path $OutputPath -Value $cmd
    }
    
    Write-Host "Added $($runCommands.Count) additional commands from 'Run:' instructions"
}

Write-Host "Script complete. Run the generated file to execute binding redirect commands."