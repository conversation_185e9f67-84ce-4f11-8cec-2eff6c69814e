param (
    [Parameter(Mandatory=$false)]
    [string]$InputFile = ".\OutputFiles\targetPackages.txt",
    
    [Parameter(Mandatory=$false)]
    [string]$OutputFile = ".\OutputFiles\result.txt",

    [Parameter(Mandatory=$false)]
    [string]$SubstratePath = "Q:\src\Substrate"
)

# Validate that the input file exists
if (-not (Test-Path $InputFile)) {
    Write-Error "Input file not found: $InputFile"
    exit 1
}

# Ensure the output file exists
if (-not (Test-Path $OutputFile)) {
    New-Item -Path $OutputFile -ItemType File -Force | Out-Null
}

Write-Host "Analyzing package dependencies..."
Write-Host "Input file: $InputFile"
Write-Host "Output file: $OutputFile"

Get-Content $InputFile | Where-Object { $_ -match "\S" -and -not $_.StartsWith("#") } | ForEach-Object {
    # Split the line by space
    $elements = $_ -split " "
    # Extract the package name and version
    $packageName = $elements[0]
    $version = $elements[1]
    
    # Output the package name and version to console
    Write-Host "Analyzing package: $packageName version: $version"
    
    # Run substrate-cli and append output to file
    substrate-cli package plan-upgrade $packageName -v $version -P $SubstratePath | Out-File -FilePath $OutputFile -Append
    
    Write-Host "Processed package: $packageName"
}

Write-Host "Analysis complete! Results written to: $OutputFile"