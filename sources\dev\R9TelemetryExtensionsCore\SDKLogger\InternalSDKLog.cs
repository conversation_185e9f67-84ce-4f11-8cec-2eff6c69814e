﻿// <copyright file="InternalSDKLog.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System.Diagnostics.CodeAnalysis;
using Microsoft.Extensions.Logging;
using Microsoft.R9.Extensions.Logging;

namespace Microsoft.M365.Core.Telemetry.SDKLogger
{
    [ExcludeFromCodeCoverage]
    internal static partial class InternalSDKLog
    {
#pragma warning disable R9G014 // Argument is not referenced from the logging message
        [LoggerMessage(0, LogLevel.Information, "")]
        public static partial void Info(ILogger logger, string message, string process, int threadId);

        [LoggerMessage(1, LogLevel.Warning, "")]
        public static partial void Warning(ILogger logger, string message, string process, int threadId);

        [LoggerMessage(2, LogLevel.Error, "")]
        public static partial void Error(ILogger logger, string message, string process, int threadId);
#pragma warning restore R9G014 // Argument is not referenced from the logging message
    }
}
