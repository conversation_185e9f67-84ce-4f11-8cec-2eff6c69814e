<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <AssemblyName>Microsoft.M365.Core.Telemetry.R9.Exporter.Filters</AssemblyName>
    <RootNamespace>$(AssemblyName)</RootNamespace>
    <Description>OpenTelemetry based .NET Processors with Filter for Exporters</Description>
    <TargetFrameworks>net472;net6.0;net8.0</TargetFrameworks>
    <LangVersion>latest</LangVersion>
    <Nullable>enable</Nullable>
    <Workstream>Telemetry</Workstream>
    <Category>Telemetry</Category>
    <UseR9Generators>true</UseR9Generators>
    <UseOptionsValidationGenerator>true</UseOptionsValidationGenerator>
    <InjectSharedDataValidation>true</InjectSharedDataValidation>
    <GeneratePackageOnBuild>true</GeneratePackageOnBuild>
    <PlatformTarget>anycpu</PlatformTarget>
    <PackageId>Microsoft.M365.Core.Telemetry.R9.Exporter.Filters</PackageId>
    <PackageVersion>$(R9TelemetryExtensionsCorePackageVersion)</PackageVersion>
    <PackageReleaseNotes>$(R9TelemetryExtensionsCoreReleaseNotes)</PackageReleaseNotes>
    <NoWarn>SA1600,SA1309</NoWarn>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.R9.Extensions.Tracing" />
    <PackageReference Include="Microsoft.R9.Extensions.Tracing.Exporters.Geneva" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\OpenTelemetry.Exporter.Filters\OpenTelemetry.Exporter.Filters.csproj"></ProjectReference>
    <ProjectReference Include="..\Telemetry.Exporters.ODL\Telemetry.Exporters.ODL.csproj"></ProjectReference>
		<ProjectReference Include="..\Telemetry.Exporters.ODLTcp\Telemetry.Exporters.ODLTcp.csproj"></ProjectReference>
  </ItemGroup>

  <ItemGroup>
    <InternalsVisibleToTest Include="$(AssemblyName).Test" />
  </ItemGroup>

</Project>