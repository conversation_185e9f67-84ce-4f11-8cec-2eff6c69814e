// <copyright file="IFileManager.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System.Collections.Generic;
using System.IO;

namespace Microsoft.M365.Core.Telemetry.FileExporter
{
    /// <summary>
    /// Standard file manage interface for the <see cref="SystemFileManager"/>.
    /// </summary>
    internal interface IFileManager
    {
        /// <summary>
        /// Create directory.
        /// </summary>
        /// <param name="directoryFullPath">Full directory path.</param>
        public void CreateDirectory(string directoryFullPath);

        /// <summary>
        /// Get all files under the given directory.
        /// </summary>
        /// <param name="directoryFullPath">Full directory path.</param>
        /// <returns>Array of file names.</returns>
        public string[] GetFiles(string directoryFullPath);

        /// <summary>
        /// Delete the given file.
        /// </summary>
        /// <param name="fullFileName">Full file name.</param>
        public void DeleteFile(string fullFileName);

        /// <summary>
        /// Create file with given name.
        /// </summary>
        /// <param name="fullFileName">Full file name.</param>
        /// <returns>Resulted <see cref="FileStream"/>.</returns>
        public FileStream CreateFile(string fullFileName);

        /// <summary>
        /// Check whether directory exists.
        /// </summary>
        /// <param name="directoryPath">Full directory path.</param>
        /// <returns>Boolean indicates whether directory exists.</returns>
        public bool DirectoryExists(string directoryPath);

        /// <summary>
        /// Check whether file exists.
        /// </summary>
        /// <param name="fullFileName">Full file name.</param>
        /// <returns>Boolean indicates whether file exists.</returns>
        public bool FileExists(string fullFileName);

        /// <summary>
        /// Rename the given file to the given new name.
        /// </summary>
        /// <param name="originalFileName">Original full file name.</param>
        /// <param name="newFileName">New full file name.</param>
        public void RenameFile(string originalFileName, string newFileName);

        /// <summary>
        /// Write all lines to the given file.
        /// </summary>
        /// <param name="fullFileName">Full file name.</param>
        /// <param name="lines">String lines to write.</param>
        public void WriteAllLines(string fullFileName, IEnumerable<string> lines);

        /// <summary>
        /// Create <see cref="LogFile"/> for given path.
        /// </summary>
        /// <param name="fullFileName">Full file name.</param>
        /// <returns><see cref="LogFile"/>.</returns>
        public LogFile CreateLogFile(string fullFileName);
    }
}
