﻿// <copyright file="ReadSharedBindingRedirectsSkill.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Threading.Tasks;
using System.Xml;
using PackageUpgradeHelper.ADO;

namespace PackageUpgradeHelper.ProjectFile
{
    /// <summary>
    /// Read shared binding redirects
    /// </summary>
    class ReadSharedBindingRedirectsSkill
    {
        /// <summary>
        /// Read assembly XML from local SharedBindingRedirects.config file
        /// </summary>
        /// <param name="path"></param>
        /// <param name="targetAssembly"></param>
        /// <returns></returns>
        public string ReadAssemblyXMLLocally(string path, string targetAssembly)
        {
            throw new NotImplementedException();
        }

        /// <summary>
        /// Read assembly XML from remote SharedBindingRedirects.config file
        /// After local file is updated, this method will be called to read the updated file
        /// </summary>
        /// <param name="targetAssembly"></param>
        /// <param name="branch"></param>
        /// <param name="path"></param>
        /// <returns></returns>
        public string ReadAssemblyXMLRemotely(string targetAssembly, string branch, string path = "/sources/dev/common/src/common/SharedBindingRedirects.config")
        {
            var getFileContentSkill = new GetFileContentSkill();
            var content = getFileContentSkill.GetFileContent(path, branch).Result;
            var xml = new XmlDocument();
            xml.LoadXml(content);
            var root = xml.SelectSingleNode("configuration");
            if (root != null)
            {
                var runtime = root.SelectSingleNode("runtime");
                if (runtime != null)
                {
                    var nsManager = new XmlNamespaceManager(xml.NameTable);
                    nsManager.AddNamespace("ns", "urn:schemas-microsoft-com:asm.v1");
                    var assemblyBinding = runtime.SelectSingleNode("ns:assemblyBinding", nsManager);
                    if (assemblyBinding != null)
                    {
                        foreach (XmlNode node in assemblyBinding.ChildNodes)
                        {
                            if (node.Name.Equals("dependentAssembly"))
                            {
                                var assemblyIdentity = node.SelectSingleNode("ns:assemblyIdentity", nsManager);
                                if (assemblyIdentity != null && assemblyIdentity.Attributes!["name"]!.Value == targetAssembly)
                                {
                                    return node.OuterXml;
                                }
                            }
                        }
                    }
                }
            }
            return string.Empty;
        }
    }
}
