﻿// <copyright file="EcsConfigurationExtensions.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

extern alias InternalizedDependencies;

using System;
using System.Collections.Generic;
using System.Diagnostics.CodeAnalysis;
using System.IO;
using System.Linq;
using System.Net.Http;
using System.Security.Cryptography.X509Certificates;
using InternalizedDependencies::Microsoft.Extensions.Configuration;
using InternalizedDependencies::Microsoft.Extensions.Configuration.Ecs;
using InternalizedDependencies::Microsoft.Extensions.Configuration.Ecs.Authentication;
using InternalizedDependencies::Microsoft.Skype.ECS.Client;
using InternalizedDependencies::Microsoft.Skype.ECS.Client.Authentication;
using Microsoft.Extensions.Configuration;
using Microsoft.Identity.Client;
using Microsoft.M365.Core.Telemetry.Enrichment;
using Microsoft.M365.Security.CredSMART;

namespace Microsoft.M365.Core.Telemetry.R9.Logging.Substrate.Configuration
{
    /// <summary>
    /// Provides extension methods for configuring ECS with validation.
    /// </summary>
    internal static class EcsConfigurationExtensions
    {
        private static string ecsFileCachePath = "SubstrateR9ExtensionECSCache"; // Relative path of current folder.

        /// <summary>
        /// Validates ECS parameters from the <paramref name="validationSource"/> configuration and adds the appropriate ECS configuration sources 
        /// to the <paramref name="builder"/>.
        /// </summary>
        /// <param name="builder">The configuration builder to add ECS sources to.</param>
        /// <param name="validationSource">The configuration source used to validate parameters (e.g., from appsettings or initial host configuration).</param>
        /// <param name="defaultConfigPath">The path to ECS embedded default configuration files.</param>
        /// <param name="useDefaultOnly">Whether to use embedded default configuration only.</param>
        /// <returns>The original configuration builder for chaining.</returns>
        /// <exception cref="ArgumentNullException">Thrown if builder or validationSource is null.</exception>
        internal static IConfigurationBuilder AddValidatedEcsConfiguration(
            this IConfigurationBuilder builder,
            IConfiguration validationSource,
            string defaultConfigPath,
            bool useDefaultOnly = false)
        {
            if (builder == null)
            {
                throw new ArgumentNullException(nameof(builder));
            }

            if (validationSource == null)
            {
                throw new ArgumentNullException(nameof(validationSource));
            }

            ECSType ecsType = validationSource.ValidateAndDetermineECSType();

            if (ecsType.Equals(ECSType.Custom))
            {
                builder.AddEcsAuthentication("ECSParameters:AuthenticationOption");
            }

            builder.AddEcs(providerSection: "ECSParameters", contextSection: "ECSParameters:ECSIdentifiers",
                source => ConfigureECSSource(source, defaultConfigPath, useDefaultOnly, ecsType));

            return builder;
        }

        /// <summary>
        /// GenerateECSCacheBuilder
        /// </summary>
        /// <param name="defaultConfigPath"></param>
        /// <param name="useDefaultOnly"></param>
        /// <returns></returns>
        /// <exception cref="FileNotFoundException">Throw if the default config doesn't exist.</exception>
        private static ECSOfflineCacheBuilder GenerateECSCacheBuilder(string defaultConfigPath, bool useDefaultOnly = false)
        {
            if (useDefaultOnly && !File.Exists(defaultConfigPath))
            {
                throw new FileNotFoundException($"The default configuration file is not found at the path: {defaultConfigPath}. Please make sure the file exists.");
            }

            var cacheBuilder = new ECSOfflineCacheBuilder()
                .Configure(cacheOptions => cacheOptions.DefaultConfigJsonPath = defaultConfigPath)
                .AddFileCache(options => options.CacheRootPath = ecsFileCachePath);

            if (useDefaultOnly)
            {
                cacheBuilder.OnlyUseDefaults();
            }

            return cacheBuilder;
        }

        /// <summary>
        /// ConfigureECSSource
        /// </summary>
        /// <param name="source"></param>
        /// <param name="defaultConfigPath"></param>
        /// <param name="useDefaultOnly"></param>
        /// <param name="ecsType"></param>
        /// <exception cref="InvalidOperationException">Throw if ECS configuration source is null.</exception>
        private static void ConfigureECSSource(EcsConfigurationSource source, string defaultConfigPath, bool useDefaultOnly, ECSType ecsType)
        {
            if (source.Configuration == null)
            {
                throw new InvalidOperationException("The ECS configuration source is not properly initialized.");
            }

            // If running on TDS machines, redirect the requests to Integartion endpoint.
            bool isTDS = DimensionValues.IsTDS.Equals("True", StringComparison.OrdinalIgnoreCase);
            if (isTDS || ecsType.Equals(ECSType.CentralizedInt))
            {
                source.Configuration.Client = Constants.ECSClientName;
                source.Configuration.Agents = Constants.ECSTeamNameInt;
                source.Configuration.Environment = EnvironmentType.Integration;
            }
            else if (ecsType.Equals(ECSType.CentralizedProd))
            {
                // If use our ECS, populate the default values.
                source.Configuration.Client = Constants.ECSClientName;
                source.Configuration.Agents = Constants.ECSTeamNameProd;
                source.Configuration.Environment = EnvironmentType.Production;

                string certificateName = GetCertificateName();
                IConfidentialClientApplication? confidentialClientApplication = null;
                X509Certificate2? certificate = null;
                try
                {
                    certificate = GenerateCertificate(certificateName);
                    confidentialClientApplication = GenerateConfidential(certificate);

                    source.Configuration.HttpMessageHandlers = new List<Func<DelegatingHandler>>
                            {
                                () => new EcsAuthHttpHandler(confidentialClientApplication)
                            };
                }
                catch (Exception) // TODO: For exceptions thrown, add telemetry to record them.
                { }
            }

            source.Configuration.Cache = GenerateECSCacheBuilder(defaultConfigPath, useDefaultOnly).Build();
            Dictionary<string, string> mergedIdentifiers = source.Configuration.DefaultRequestIdentifiers?.ToDictionary(kvp => kvp.Key, kvp => kvp.Value) ?? new Dictionary<string, string>();
            Dictionary<string, string> machineLevelIdentifiers = new Dictionary<string, string>()
            {
                { "DeployRing", DimensionValues.DeployRing },
                { "Forest", DimensionValues.Forest },
                { "Machine", DimensionValues.Machine }
            };

            foreach (var identifier in machineLevelIdentifiers)
            {
                mergedIdentifiers[identifier.Key] = identifier.Value;
            }
            source.Configuration.DefaultRequestIdentifiers = mergedIdentifiers;
        }

        /// <summary>
        /// Generate the certificate name according to machine metadata.
        /// Currently support EXO, EOP, Gallatin, Itar in Model A/B/B2, and Cosmic.
        /// TODO(chenzejun): Whether let customer pass RuntimeModel to support Model D easily.
        /// </summary>
        /// <returns></returns>
        [ExcludeFromCodeCoverage]
        private static string GetCertificateName()
        {
            string certificateName = Constants.CertificateNames["EXO"];

            if (!string.IsNullOrEmpty(DimensionValues.DeployRing) &&
                     DimensionValues.DeployRing.IndexOf("gallatin", StringComparison.OrdinalIgnoreCase) >= 0)
            {
                certificateName = Constants.CertificateNames["Gallatin"];
            }
            else if (!string.IsNullOrEmpty(DimensionValues.DeployRing) &&
                     DimensionValues.DeployRing.IndexOf("itar", StringComparison.OrdinalIgnoreCase) >= 0)
            {
                certificateName = Constants.CertificateNames["Itar"];
            }
            else if (!string.IsNullOrEmpty(DimensionValues.Forest) &&
                DimensionValues.Forest.IndexOf("eop", StringComparison.OrdinalIgnoreCase) >= 0)
            {
                certificateName = Constants.CertificateNames["EOP"];
            }
            else if (!string.IsNullOrEmpty(Environment.GetEnvironmentVariable("COSMIC_PODNAME")))
            {
                certificateName = Constants.CertificateNames["Cosmic"];
            }

            return certificateName;
        }

        /// <summary>
        /// Generate confidential client application with certificate.
        /// </summary>
        /// <param name="certificate">The certificate.</param>
        /// <returns></returns>
        [ExcludeFromCodeCoverage]
        private static IConfidentialClientApplication GenerateConfidential(X509Certificate2? certificate)
        {
            return ConfidentialClientApplicationBuilder.Create(Constants.ApplicationId)
                .WithCertificate(certificate, sendX5C: true)
                .WithAuthority(new Uri($"{Constants.MicrosoftHost}/{Constants.TenantId}"))
                .Build();
        }

        /// <summary>
        /// Generate certificate from its name.
        /// </summary>
        /// <param name="certificateName">The certificate name.</param>
        /// <returns></returns>
        private static X509Certificate2? GenerateCertificate(string certificateName)
        {
            if (string.IsNullOrEmpty(certificateName))
            {
                return null;
            }

            try
            {
                var secretProvider = new M365Secret();
                return secretProvider.GetLatestM365CertificateV2(certificateName);
            }
            catch (Exception)
            {
                return null;
            }
        }
    }
}