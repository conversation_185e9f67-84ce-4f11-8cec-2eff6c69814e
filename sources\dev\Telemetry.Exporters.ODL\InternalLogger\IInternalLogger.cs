﻿// ---------------------------------------------------------------------------
// <copyright file="IInternalLogger.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>
// ---------------------------------------------------------------------------
using System.Collections.Generic;
using Microsoft.Extensions.Logging;

namespace Microsoft.R9.Extensions.Telemetry.Exporters.Base
{
    /// <summary>
    /// Internal loggers
    /// </summary>
    internal interface IInternalLogger
    {
        /// <summary>
        /// Interface of Logging.
        /// </summary>
        /// <param name="logLevel">Log level</param>
        /// <param name="logtype">Log type</param>
        /// <param name="atguid">Audit trail guid</param>
        /// <param name="message">payload</param>
        /// <param name="dimensionPairs">customer dimension names and values</param>
        void Log(LogLevel logLevel, string logtype, string atguid, string message, Dictionary<string, string> dimensionPairs);
        
        /// <summary>
        /// Interface of Logging trace 
        /// </summary>
        /// <param name="logtype">Log type</param>
        /// <param name="atguid">Audit trail guid</param>
        /// <param name="message">formatted message</param>
        void Trace(string logtype, string atguid, string message);
    }
}