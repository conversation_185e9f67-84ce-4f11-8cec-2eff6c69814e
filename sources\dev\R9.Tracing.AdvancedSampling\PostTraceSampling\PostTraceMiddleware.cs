﻿// <copyright file="PostTraceMiddleware.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System.Diagnostics;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;

namespace Microsoft.M365.Core.Telemetry.R9.Tracing.AdvancedSampling
{
    /// <summary>
    /// This middleware will add a header to the response if http status code is in error range  or if the activity has an inherited error baggage.
    /// </summary>
    public class PostTraceMiddleware
    {
        private readonly RequestDelegate nextDelegate;

        /// <summary>
        /// Constructor
        /// </summary>
        /// <param name="next"></param>
        public PostTraceMiddleware(RequestDelegate next)
        {
            nextDelegate = next;
        }

        /// <summary>
        /// Actions to be invoked
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        public async Task InvokeAsync(HttpContext context)
        {
            context.Response.OnStarting(() =>
            {
                var activity = Activity.Current;
                bool hasError = context.Response.StatusCode >= 400;
                bool foundErrorBaggage = activity != null && activity.GetBaggageItem(Constants.PostTraceBaggageName) != null;
                
                if (foundErrorBaggage || hasError)
                {
                    context.Response.Headers.Append(Constants.PostTraceBaggageName, Constants.SampleBaggage);

                    if (!foundErrorBaggage)
                    {
                        var curr = activity;
                        while (curr != null)
                        {
                            curr.SetBaggage(Constants.PostTraceBaggageName, Constants.SampleBaggage);
                            curr = curr.Parent;
                        }
                    }
                }
                return Task.CompletedTask;
            });

            // Call the next delegate/middleware in the pipeline.
            await nextDelegate(context).ConfigureAwait(true);
        }
    }
}
