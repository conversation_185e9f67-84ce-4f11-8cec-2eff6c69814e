# PackageUpgradeHelper

PackageUpgradeHelper is an automation tool designed to simplify various operations during package upgrades. This tool helps users automatically handle package dependencies, update configuration files, and manage binding redirects.

## Main Features

The tool provides the following main features:

1. **Branch Management**
   - Automatically create new feature branches
   - Create branches based on the latest master commit
   - Support custom branch names or auto-generated names

2. **Substrate CLI Integration**
   - Run basic upgrade scripts
   - Analyze target package dependencies
   - Update package.props and corext configurations
   - Update binding redirects for existing packages
   - Update sharedBingredirects

3. **Package Version Upgrade**
   - Analyze output files and identify new packages
   - Add Binplace references for newly introduced packages
   - Add XMLDrop entries
   - Update package references in csproj files
   - Add binding redirects for new packages

## Usage Flow

The tool executes the following 7 main steps:

1. **Create Target Branch**
   - Ensure target branch exists
   - Prompt user to switch to the newly created branch

2. **Run Basic Upgrade Scripts**
   - Execute Substrate CLI
   - Complete basic configuration updates

3. **Analyze Output Files**
   - Identify upgraded and new packages
   - Display new package information

4. **Update Binplace References**
   - Add necessary Binplace references for newly introduced packages

5. **Update XMLDrop Files**
   - Add XMLDrop entries for newly introduced packages

6. **Update Project References**
   - Add new package references in relevant csproj files

7. **Configure Binding Redirects**
   - Add necessary binding redirect configurations for new packages

## Important Notes

- Ensure important files are backed up before running the tool
- Some operations may take considerable time (e.g., running Substrate CLI may take about 1 hour)
- The tool automatically commits changes and pushes to remote branch
- Please review changes in the remote branch after completion

## Output Files

The tool generates temporary working files under the `./OutputFiles` directory during execution:

- `UpgradedPackages.txt`: List of upgraded packages
- `AddedPackages.txt`: List of newly added packages
- `filterPackages.txt`: List of filtered packages
- `RootNewPackageMap.txt`: Mapping between root packages and new packages
- `targetPackages.txt`: List of target packages

## Final Results

The tool's final output consists of the following changes pushed to your target branch:

1. **Package Configuration Updates**
   - Updated package.props with new package versions
   - Modified corext configuration files
   - Updated binding redirects in relevant config files

2. **Project File Changes**
   - Added new package references in csproj files
   - Updated Binplace references for new packages
   - Added XMLDrop entries for package dependencies

3. **Source Control Integration**
   - All changes are automatically committed to your target branch
   - Changes are pushed to the remote repository
   - Ready for creating a Pull Request (PR)

After the tool completes execution, you can:
1. Review all changes in your target branch
2. Create a Pull Request from your target branch to master
3. Follow standard code review process for package upgrade changes 

## Feature Plans
1. Add commands
2. Make it more generic for more scenarios and specific upgrade cases