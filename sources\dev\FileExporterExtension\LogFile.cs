// <copyright file="LogFile.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System;
using System.IO;

namespace Microsoft.M365.Core.Telemetry.FileExporter
{
    /// <summary>
    /// Delegate class for the <see ref="FileInfo" />.
    /// </summary>
    internal sealed class LogFile
    {
        /// <summary>
        /// Gets the file name.
        /// </summary>
        public string Name { get; }

        /// <summary>
        /// Gets the full name.
        /// </summary>
        public string FullName { get; }

        /// <summary>
        /// Gets the size in bytes.
        /// </summary>
        public long Size { get; }

        /// <summary>
        /// Gets the last write date time offset.
        /// </summary>
        public DateTimeOffset LastWriteTimeOffset { get; }

        /// <summary>
        /// Initializes a new instance of the <see cref="LogFile"/> class.
        /// </summary>
        /// <param name="name">Name of the file.</param>
        /// <param name="fullName">Full name of the file.</param>
        /// <param name="size">Byte size of the file.</param>
        /// <param name="lastWriteTimeOffset">Last write date time offset.</param>
        public LogFile(string name, string fullName, long size, DateTimeOffset lastWriteTimeOffset)
        {
            Name = name;
            FullName = fullName;
            Size = size;
            LastWriteTimeOffset = lastWriteTimeOffset;
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="LogFile"/> class.
        /// </summary>
        /// <param name="fileInfo"><see cref="FileInfo"/>.</param>
        public LogFile(FileInfo fileInfo)
        {
            Name = fileInfo.Name;
            FullName = fileInfo.FullName;
            Size = fileInfo.Length;
            LastWriteTimeOffset = fileInfo.LastWriteTimeUtc;
        }
    }
}
