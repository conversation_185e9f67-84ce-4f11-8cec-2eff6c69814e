trigger:
- main

parameters:
- name: RunCloudTest
  type: boolean
  default: false

resources:
  repositories:
    - repository: PipelineRepo
      type: git
      name: Pipelines
      ref: refs/heads/master

extends:
  template: sources/dev/templates/stages/precheckin-cloudtest.yml@PipelineRepo
  parameters:
    Flavor: 'debug'
    RunCloudTest: ${{ parameters.RunCloudTest }}
    ParserProperties: 'HoldTrigger=Completion'
    NotificationSubscribers: '<EMAIL>'