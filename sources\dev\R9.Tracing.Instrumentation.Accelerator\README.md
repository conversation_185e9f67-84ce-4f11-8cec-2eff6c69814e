﻿# About This Project
This  project aims helping Substrate services to instrument Distributed Tracing quickly. The bottom layer is mainly implemented based on R9, but due to the complexity of Substrate itself, some of its unique requirements cannot be met by R9 at present, and the SOTELS team proposed a solution for those feature gaps. These solutions are summarized in this Library. Any questions please contact `<EMAIL>`. 

## Target Audience
Substrate services

## Supported Scenarios

| Target Date| Version| Type| Operation System  |Running Model |Framework | TargetFrameworkVersion| Protocols | EUII Redaction | Other Requirements |
| --- | --- | --- | --- | --- | --- | --- | --- | --- |  --- | 
| 7/29/2023 |1.0.0-rc0.1| Service | Windows | A | Asp .Net Core|netcoreapp3.1;net6.0;net8.0| Http | N/A | N/A
| 8/8/2023 | 9.0.0 | Service | Windows | A |Asp .Net |.NetFramework 4.72| Http| N/A | N/A
| 8/13/2023 | 9.0.0 | Service | Windows | D2S |Asp .Net Core|netcoreapp3.1;net6.0;net8.0| Http | N/A | N/A
| 8/30/2023 | 9.0.0 | Service | Windows | B2|Asp .Net Core|netcoreapp3.1;net6.0;net8.0| Http | N/A | N/A
| 8/30/2023 | 9.0.0 | Service | Windows | B2|Asp .Net |.NetFramework 4.72| Http| N/A | N/A
| --- | Coming soon | Service | Windows| A/B2|WCF|.NetFramework 4.72| WCF| Default Type | Support: API + Wcf instrumentation lib
| --- | Not Planned yet | Service | Linux| D2S|Asp .Net Core|netcoreapp3.1;net6.0| Http | Default Type | N/A

_**Will not support in this stage:**_
 Owin, MEF, CoreWcf, GRPC streaming and Class Library.

## Distributed Tracing(aka: DT) Instrumentation Accelerator

### *Main purpose*:

Provide efficient APIs for Substrate services to instrument DT without caring about too many details, such as complex DT concepts, DT data format, DT data transmission, data compliance etc.

### Design philosophy

#### Technology Stack

The bottom layer is mainly implemented based on R9, but due to the complexity of Substrate itself, some of its unique requirements cannot be met by R9 at present, and the SOTELS team proposed a solution for those feature gaps.

#### Technology Selection

Use a highly encapsulated library and a configuration file to achieve low-code development. Meanwhile, we are trying to simplify the configuration. Only 2 configuration items are mandatory. If you do not have special requirements, default configuration values are enough for Common Substrate services. Customization requires additional configurations. 

### Usage

#### .Net Core

Here is the API:
`AddDistributedTracingService` is extension to `IServiceCollection`, provide generic DT instrumentation for Substrate services.

```CSharp
        /// <summary>
        /// Mainly for Substrate services, configure Distributed Tracing with settings defined in configuration, default including those components:
        ///     1. ECS controlled Dynamic Sampler
        ///     2. Auto-collect in/out http requests with all tags that might contains EUII redacted
        ///     3. Geneva trace exporter
        ///     4. Necessary enrichers for mandatory information, like env_cloud_role, env_cloud_roleInstance etc.
        ///     5. Customized activity sources
        /// </summary>
        /// <param name="services">Container <see cref="IServiceCollection"/></param>
        /// <param name="serviceMetaDataConfig">Service related informations <see cref="IConfiguration"/></param>
        /// <param name="tracingAcceleratorConfig">Tracing related configurations holder <see cref="IConfiguration"/></param>
        /// <param name="extraTraceProviderBuilderConfigure">Action<TracerProviderBuilder> This is enrichment for special needs. If there are no special needs, please leave this parameter empty. If you really need this parameter, please be sure to understand the default configuration in the interface to avoid configuration conflicts: same component is configured twice, resulting in unexpected behavior</param>
        /// <returns>Service Collection</returns>
        public static IServiceCollection AddDistributedTracingService(this IServiceCollection services, IConfiguration serviceMetaDataConfig, IConfiguration tracingAcceleratorConfig = default, Action<TracerProviderBuilder> extraTraceProviderBuilderConfigure = default)
```

***How To Use***

* If you do not have special requirements for DT and your scenarios are supported by us, you can call `AddDistributedTracingService` directly with ServiceMetaData related configurations only. Example shows below.

  ***Example***

    ```csharp
    internal class MockStartup
    {
        /// <summary>
        /// The ConfigureServices
        /// </summary>
        /// <param name="services">The services</param>
        public void ConfigureServices(IServiceCollection services)
        {
            // Build a config object, using env vars and JSON providers.
            IConfigurationRoot configuration = new ConfigurationBuilder().AddJsonFile($"appsettings.{env.EnvironmentName}.json", true, true).Build();

            /******* The code you need to add *******/
            /******* Begin *******/

            services.AddDistributedTracingService(configuration.GetSection("Microsoft_m365_core_telemetry:ServiceMetadata")); 

            /******* End *******/
        }

        public void Configure(IApplicationBuilder app, IWebHostEnvironment env)
        {
        }
    }
    ```

* If you want to customize the DT pipeline, you can call `AddDistributedTracingService` with ServiceMetaData related configurations, tracing relalted configurations and customized TracerProviderBuilder configure action. Example shows below.

  ***Example***

  ```csharp
  internal class MockStartup
	{
		/// <summary>
		/// The ConfigureServices
		/// </summary>
		/// <param name="services">The services</param>
		public void ConfigureServices(IServiceCollection services)
		{
            // Build a config object, using env vars and JSON providers.
            IConfigurationRoot configuration = new ConfigurationBuilder().AddJsonFile($"appsettings.{env.EnvironmentName}.json", true, true).Build();
  
			/******* The code you need to add *******/
			/******* Begin *******/
			Action<TracerProviderBuilder> action = (builder) =>
			{
			    builder.AddWcfInstrumentation();
			};
           
			services.AddDistributedTracingService(configuration.GetSection("Microsoft_m365_core_telemetry:ServiceMetadata"), configuration.GetSection("Microsoft_m365_core_telemetry:Tracing"), action);
  
			/******* End *******/
		}
	}
	```
  >__*Must Read*__:
    >
    >>__Pay attention to `AddWcfInstrumentation` in the above example. This is a customized configure action. If you do not have extra need, please do not add it! If you really need to add it, please make sure to understand the default configuration of the API to avoid conflicts: same component is configured twice, resulting in unexpected behavior.__
    >
    >*By default, below configurations are covered DT initialization, please do not add duplicate configuration.*
    >
    >```csharp
    >   private static TracerProviderBuilder AddDistributedTracingBasicInternal(this TracerProviderBuilder builder, TracingAcceleratorOptions options, ServiceMetaDataOptions serviceMetaOptions, R9TracingConfig r9Config)
    >   {
    >        Throws.IfNull(builder, nameof(builder));
    >        Throws.IfNull(options, nameof(options));
    >        Throws.IfNull(serviceMetaOptions, nameof(serviceMetaOptions));
    >        Throws.IfNull(r9Config, nameof(r9Config));
    >
    >        if (options.ActivitySources.Length != 0)
    >        {
    >            builder.AddSource(options.ActivitySources);
    >        }
    >
    >        return builder
    >        .SetSampler(new DynamicSampler(r9Config))
    >        .AddTraceEnricher<RoleInfoEnricher>()
    >        .AddTraceEnricher<BuildVersionEnricher>()
    >        .SetResourceBuilder(ResourceBuilder.CreateDefault().AddService(serviceMetaOptions.ServiceName, serviceMetaOptions.ServiceVersion))
    >        .AddHttpClientTracing(options.HttpClientTracing)
    >        .AddGenevaExporter(options.GenevaTraceExporter)
    >        .AddConsoleExporter(options.ConsoleTracingExporter);
    >    }
    >```

#### .NET Framework

Overall, we have two options available,

* Option 1 - Internal Container [***Recommended***]

  You do not have to do any initialization for service containers, just call the easiest API in the start up class.

  ```csharp
  /// <summary>
  /// Init Distributed Tracing Service
  /// the overall flow is as below:
  /// 
  /// new ServiceCollection() 
  /// AddDistributedTracingService() to ServiceCollection which contains:
  ///     1. ECS controlled Dynamic Sampler
  ///     2. Auto-collect in/out http requests with all tags that might contains EUII redacted
  ///     3. Geneva trace exporter
  ///     4. Necessary enrichers for mandatory information, like env_cloud_role, env_cloud_roleInstance etc.
  ///     5. Customized activity sources
  /// Start all hosted services in ServiceCollection
  /// 
  /// </summary>
  /// <param name="serviceMetaDataConfig"></param>
  /// <param name="tracingAcceleratorConfig"></param>
  /// <param name="extraTraceProviderBuilderConfigure"></param>
  /// <returns>Service Provider built from serviceCollection inside</returns>
  public static ServiceProvider InitDistributedTracingService(IConfiguration serviceMetaDataConfig, IConfiguration tracingAcceleratorConfig = default, Action<TracerProviderBuilder> extraTraceProviderBuilderConfigure = default)
  ```

* Option 2 - Shared Container
  
  If you want to share the Service Collection with other components in your service, or there has already been a Service Collection, you can call `AddDistributedTracingService` directly with related configurations.

  `AddDistributedTracingService` is extension to `IServiceCollection`, provide generic DT instrumentation for Substrate services.

```CSharp
        /// <summary>
        /// Mainly for Substrate services, configure Distributed Tracing with settings defined in configuration, default including those components:
        ///     1. ECS controlled Dynamic Sampler
        ///     2. Auto-collect in/out http requests with all tags that might contains EUII redacted
        ///     3. Geneva trace exporter
        ///     4. Necessary enrichers for mandatory information, like env_cloud_role, env_cloud_roleInstance etc.
        ///     5. Customized activity sources
        /// </summary>
        /// <param name="services">Container <see cref="IServiceCollection"/></param>
        /// <param name="serviceMetaDataConfig">Service related informations <see cref="IConfiguration"/></param>
        /// <param name="tracingAcceleratorConfig">Tracing related configurations holder <see cref="IConfiguration"/></param>
        /// <param name="extraTraceProviderBuilderConfigure">Action<TracerProviderBuilder> This is enrichment for special needs. If there are no special needs, please leave this parameter empty. If you really need this parameter, please be sure to understand the default configuration in the interface to avoid configuration conflicts: same component is configured twice, resulting in unexpected behavior</param>
        /// <returns>Service Collection</returns>
        public static IServiceCollection AddDistributedTracingService(this IServiceCollection services, IConfiguration serviceMetaDataConfig, IConfiguration tracingAcceleratorConfig = default, Action<TracerProviderBuilder> extraTraceProviderBuilderConfigure = default)
```

***How To Use***

* Internal Container [**Recommended**]

  * If you do not have special requirements for DT and your scenarios are supported by us. `main` function e.g.
  
    ***Example***

     ```csharp
    static void Main(string[] args)
    {
      
      /******* The code you need to add *******/
      /******* Begin *******/
    
      IConfigurationRoot configuration = new ConfigurationBuilder().AddJsonFile("appsettings.json").Build();  
  	  TracerSdk.InitDistributedTracingService(configuration.GetSection("Microsoft_m365_core_telemetry:ServiceMetadata"),
                                              configuration.GetSection("Microsoft_m365_core_telemetry:Tracing"));
      
      /******* End *******/
    }
    ```

  * If you want to customize the DT pipeline, you need extrally define a TracerProviderBuilder configure action as a paramater of the API.

    ***Example***

    ```csharp
    static void Main(string[] args)
    {
      
      /******* The code you need to add *******/
      /******* Begin *******/

      Action<TracerProviderBuilder> action = (builder) =>
  		{
  		    builder.AddWcfInstrumentation();
  		};
      IConfigurationRoot configuration = new ConfigurationBuilder().AddJsonFile("appsettings.json").Build();  
  	  TracerSdk.InitDistributedTracingService(configuration.GetSection("Microsoft_m365_core_telemetry:ServiceMetadata"),
                                              configuration.GetSection("Microsoft_m365_core_telemetry:Tracing"),action);
      
      /******* End *******/
    }
    ```

* Shared Container
  
  * If you do not have special requirements for DT and your scenarios are supported by us. `main` function e.g.
  
    ***Example***

    ```csharp
    static void Main(string[] args)
    {
      var services = new ServiceCollection(); 
  
        /******* The code you need to add *******/
        /******* Begin *******/
      
      IConfigurationRoot configuration = new ConfigurationBuilder().AddJsonFile("appsettings.json").Build();
  	
      services.AddDistributedTracingService(this.configuration.GetSection("Microsoft_m365_core_telemetry:ServiceMetadata"),
                                              this.configuration.GetSection("Microsoft_m365_core_telemetry:Tracing"));
      
      // This is an optional helper function to quick start the hosted services.
      // !Attention: It will start all the services inside the ServiceCollection
      var serviceProvider = services.StartServices();
      /******* End *******/
      
      
      // This is an optional helper function to quick stop the hosted services.
      // !Attention: It will stop all the services inside the ServiceCollection
      serviceProvider.StopServices();
    }
    ```

  * If you want to customize the DT pipeline, you need extrally define a TracerProviderBuilder configure action as a paramater of the API.
  
    ***Example***

    ```csharp
    static void Main(string[] args)
    {
     var services = new ServiceCollection(); 
  
        /******* The code you need to add *******/
        /******* Begin *******/
      
      IConfigurationRoot configuration = new ConfigurationBuilder().AddJsonFile("appsettings.json").Build();
  	  Action<TracerProviderBuilder> action = (builder) =>
  	  	{
  	  	    builder.AddWcfInstrumentation();
  	  	};
      services.AddDistributedTracingService(this.configuration.GetSection("Microsoft_m365_core_telemetry:ServiceMetadata"),
                                                this.configuration.GetSection("Microsoft_m365_core_telemetry:Tracing"),action);

      // This is an optional helper function to quick start the hosted services.
      // !Attention: It will start all the services inside the ServiceCollection
      var serviceProvider = services.StartServices();
      /******* End *******/
       
      // This is an optional helper function to quick stop the hosted services.
      // !Attention: It will stop all the services inside the ServiceCollection
      serviceProvider.StopServices();
    }
    ```
>__*Must Read*__:
  >
  > *ASP .NET in .NET Framework App Specific*
  > 
  > Once you use common API in .NET Framework App, `OpenTelemetry.Instrumentation.AspNet` requires adding an additional `HttpModule` to your web server. This additional HttpModule is shipped as part of [`OpenTelemetry.Instrumentation.AspNet.TelemetryHttpModule`](https://www.nuget.org/packages/OpenTelemetry.Instrumentation.AspNet.TelemetryHttpModule/) which is implicitly brought by `OpenTelemetry.Instrumentation.AspNet`. The following shows changes required to your `Web.config` when using IIS web server.
  >
  >```xml
  ><system.webServer>
  >  <modules>
  >     <add name="TelemetryHttpModule" type="OpenTelemetry.Instrumentation.AspNet.TelemetryHttpModule, OpenTelemetry.Instrumentation.AspNet.TelemetryHttpModule, Version=*******, Culture=neutral, PublicKeyToken=7bd6737fe5b67e3c" preCondition="integratedMode,managedHandler" />
  >  </modules>
  ></system.webServer>
  >```
  > *Note*: Version of TelemetryHttpModule would be changed following the update of AspNet instrumentation. 

### Configuration

The example shows below. Clarification could be found in comments. If you have any question, please contact us.

#### Appsettings.json Example

```json
{
  "Microsoft_m365_core_telemetry": {
    "ServiceMetadata": {

      // required, your service identifier
      "ServiceName": "Auth",

      // required, your service model: ModelA, ModelB, ModelB2, ModelD, Cosmic
      "RuntimeModel": "Cosmic"
    },
    "Tracing": {

      // optional Default: V1
      "ScehmaVersion": "V1",

      // optional Default: []
      "ActivitySources": [ "Source1", "Source2" ],

      // Configurations for trace auto collector for incoming http requests
      // Extra configuration items please reference https://eng.ms/docs/experiences-devices/r9-sdk/docs/telemetry/distributed-tracing/http-tracing for more info.
      "HttpTracing": {

        // optional Default: false
        "IsEnabled": true,

        // optional Default: "Default", currently we just support Default
        "RedactionStrategyType": "Default"
      },
      // Configurations for trace auto collector for outgoing http requests
      // Extra configuration items please reference https://eng.ms/docs/experiences-devices/r9-sdk/docs/telemetry/distributed-tracing/http-client-tracing for more info.
      "HttpClientTracing": {

        // optional Default: true
        "IsEnabled": true, 

        // optional Default: "Default", currently we just support Default
        "RedactionStrategyType": "Default"
      },
      "ConsoleTracingExporter": {

        // optional Default: false
        "IsEnabled": true
      },
      // Check out https://eng.ms/docs/experiences-devices/r9-sdk/docs/telemetry/distributed-tracing/geneva-trace-export for more info.
      "GenevaTraceExporter": {

        // optional Default: EtwSession=PassiveR9TestSession
        "ConnectionString": "EtwSession=PassiveR9TestSession"
        // geneva sample rate, default value: 0.1
        "TraceIdBasedSampleRatio": 0.1
      },
      //Configurations to export trace to ODL, odl logtype name : R9TraceExporter
      "ODLTraceExporter": {
        //optional default: true
        "IsEnabled": true,
        "BatchExport": false
      },
      //Configurations to enable DT or set Samplers in local, depend on DeployRing
      "TracingSamplerAndEnableOptions": [
        {
          "DeployRing": "TDF",
          "R9DTEnabled": true,
          "SamplerType": "AlwaysOn"
        },
        {
          "DeployRing": "SDFV2,MSIT",
          "R9DTEnabled": false,
          "SamplerType": "ParentBased",
          "ParentRootSamplerType": "RatioBased",
          "TraceSampleRate": 0.1
        }
      ]
    }
  }
}
```

## Other resources

* More Usage Guide: [Distributed Tracing Adoption Guidance](https://o365exchange.visualstudio.com/O365%20Core/_wiki/wikis/O365%20Core.wiki/290024/DT-Utility-Extension-Recommended-)

* Contact: `<EMAIL>`