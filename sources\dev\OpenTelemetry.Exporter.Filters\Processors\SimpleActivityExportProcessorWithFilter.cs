﻿// <copyright file="SimpleActivityExportProcessorWithFilter.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>
using System.Diagnostics;
using OpenTelemetry.Exporter.Filters.Internal;

namespace OpenTelemetry.Exporter.Filters
{
    /// <summary>
    /// SimpleActicityExporterProcessor with a filter internal and do filtering before export
    /// </summary>
    public class SimpleActivityExportProcessorWithFilter : SimpleActivityExportProcessor
    {
        /// <summary>
        /// internal filter
        /// </summary>
        internal readonly BaseFilter<Activity> Filter;

        /// <summary>
        /// initialize the instance with filter and exporter
        /// </summary>
        /// <param name="exporter"></param>
        /// <param name="filter"></param>
        public SimpleActivityExportProcessorWithFilter(BaseExporter<Activity> exporter, BaseFilter<Activity> filter)
            : base(exporter)
        {
            Guard.ThrowIfNull(filter, nameof(filter));
            this.Filter = filter;
        }
        
        /// <summary>
        /// filter the data before they are actually exported.
        /// </summary>
        /// <param name="activity">completed activity</param>
        public override void OnEnd(Activity activity)
        {
            if (this.Filter.ShouldFilter(activity))
            {
                base.OnEnd(activity);
            }
        }
    }
}
