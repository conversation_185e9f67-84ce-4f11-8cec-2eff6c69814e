﻿{
  "Microsoft_m365_core_telemetry": {
    "ServiceMetadata": {
      "ServiceName": "Auth",
      "RuntimeModel": "Cosmic"
    },
    "Tracing": {
      "ScehmaVersion": "V1",
      "ActivitySources": [ "Source1", "Source2" ],
      "HttpTracing": {
        "IsEnabled": true,
        "RedactionStrategyType": "Default"
      },
      "HttpClientTracing": {
        "IsEnabled": true,
        "RedactionStrategyType": "Default"
      },
      "ConsoleTracingExporter": {
        "IsEnabled": true
      },
      "GenevaTraceExporter": {
        "ConnectionString": "EtwSession=PassiveR9TestSession",
        "TraceIdBasedSampleRatio": 0.9
      },
      "ODLTraceExporter": {
        "IsEnabled": true,
        "BatchExport": false
      },
      "TracingSamplerAndEnabled": [
        {
          "DeployRing": "Unknown",
          "R9DTEnabled": true,
          "SamplerType": "AlwaysOn"
        },
        {
          "DeployRing": "SDFV2,MSIT",
          "R9DTEnabled": false,
          "SamplerType": "ParentBased",
          "ParentRootSamplerType": "RatioBased",
          "TraceSampleRate": 0.1
        }
      ]
    }
  }
}