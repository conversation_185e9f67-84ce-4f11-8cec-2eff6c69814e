{"options": [{"enabled": false, "definition": {"id": "a9db38f9-9fdc-478c-b0f9-464221e58316"}, "inputs": {"workItemType": "399868", "assignToRequestor": "false", "additionalFields": "{}"}}, {"enabled": false, "definition": {"id": "5d58cc01-7c75-450c-be18-a388ddb129ec"}, "inputs": {"branchFilters": "[\"+refs/heads/*\"]", "additionalFields": "{}"}}], "variables": {"NewRepoName": {"value": "{{RepositoryName}}"}, "SecurityGroup": {"value": "{{RepositoryName}}Admin"}}, "retentionRules": [{"branches": ["+refs/heads/*"], "artifacts": [], "artifactTypesToDelete": [], "daysToKeep": 30, "minimumToKeep": 10, "deleteBuildRecord": false, "deleteTestResults": false}], "properties": {}, "tags": [], "jobAuthorizationScope": 1, "jobTimeoutInMinutes": 240, "jobCancelTimeoutInMinutes": 5, "process": {"phases": [{"steps": [{"environment": {}, "enabled": true, "continueOnError": true, "alwaysRun": true, "displayName": "Task group: M365NewRepoGenerationBaseCoral {{RepositoryName}}", "timeoutInMinutes": 0, "condition": "succeededOrFailed()", "task": {"id": "f9fcf8ee-f4b9-411a-ab1b-617c1a1e0ea9", "versionSpec": "1.*", "definitionType": "metaTask"}, "inputs": {"Build.PipeLine": "$(NewRepoName)", "Build.SecurityGroup": "$(SecurityGroup)", "Build.Repository.Id": "$(Build.Repository.Id)"}}], "name": "M365Coral Post Repo Generation Setup", "refName": "Job_1", "condition": "succeeded()", "target": {"executionOptions": {"type": 0}, "allowScriptsAuthAccessOption": true, "type": 1}, "jobAuthorizationScope": 1, "jobCancelTimeoutInMinutes": 1}], "type": 1}, "repository": {"properties": {"labelSources": "0", "reportBuildStatus": "false", "fetchDepth": "0", "gitLfsSupport": "false", "skipSyncSource": "false", "cleanOptions": "0", "checkoutNestedSubmodules": "false", "labelSourcesFormat": "$(build.buildNumber)"}, "id": "{{RepositoryId}}", "type": "TfsGit", "name": "{{RepositoryName}}", "url": "{{RepositoryUrl}}", "defaultBranch": "refs/heads/master", "clean": "true", "checkoutSubmodules": false}, "processParameters": {}, "queue": {"id": 26, "name": "Official", "url": "https://o365exchange.visualstudio.com/_apis/build/Queues/26", "pool": {"id": 11, "name": "Official"}}, "name": "M365Coral Post Repo Generation Setup {{RepositoryName}}", "path": "\\M365CoralAdmin", "type": 2, "queueStatus": 0, "revision": 1}