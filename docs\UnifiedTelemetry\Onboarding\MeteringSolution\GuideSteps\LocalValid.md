# Step 4 - Validate Locally

We prefer using `MetricDog` to validate metrics on a **Windows** machine. For step-by-step instructions see [Windows Local Validation for Metrics](https://eng.ms/docs/products/geneva/collect/instrument/opentelemetrydotnet/otel-sdklocalvalidation/otel-metricslocalvalidation).

Please use `dotnet-trace` on a **Linux** machine and follow up with this wiki. [Metrics troubleshooting using donet-trace R9 SDK](https://eng.ms/docs/experiences-devices/r9-sdk/docs/telemetry/metering/metrics-troubleshooting#:~:text=using%20Windows%20containers.-,Linux,-You%20can%20use).

Please continue **Next Step**: [Check Potential Risk](./PotentialRisks.md)