﻿// <copyright file="R9Host.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System;
using System.Diagnostics.CodeAnalysis;
using System.IO;

using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.R9.Extensions.Logging;
using Microsoft.R9.Extensions.Logging.Exporters;
using Microsoft.R9.Extensions.Metering;

// TODO(jiayiwang) Running this code requires extension libraries > 3.1.5. Try upgrade. If want to used for test, please AddJsonFile first.
namespace Microsoft.M365.Core.Telemetry.R9
{
    /// <summary>
    /// The internal config object to initialize DI.
    /// </summary>
    [ExcludeFromCodeCoverage]
    internal static class R9Host
    {
        private static IHost hostInstance = new HostBuilder()
            .ConfigureLogging(builder =>
            {
                _ = builder
                .AddOpenTelemetryLogging()
                .AddGenevaExporter(genevaOptions =>
                {
                    genevaOptions.ConnectionString = "EtwSession=o365PassiveMonitoringSession";
                });
            })
            .ConfigureAppConfiguration((context, config) =>
            {
                config.SetBasePath(Directory.GetCurrentDirectory());
                config.AddJsonFile("appsettings.json", optional: true, reloadOnChange: true);
                config.AddEnvironmentVariables();
            })
            .ConfigureServices((context, services) => InternalConfigureServices(services, context.Configuration))
            .Build();

        // TODO(jiayiwang): Route logs to tables and metrics to accounts&namespaces.
        private static void InternalConfigureServices(IServiceCollection services, IConfiguration configure)
        {
            services
            .AddGenevaMetering(options =>
            {
                options.Protocol = TransportProtocol.Etw;
                options.MonitoringAccount = "PassiveMonitoringTest";
                options.MonitoringNamespace = "O365PassiveTDS";
            })
            .InitTelemetry(configure);
        }

        /// <summary>
        /// ServiceProvider.
        /// </summary>
        /// <returns>ServiceProvider reference.</returns>
        public static IServiceProvider GetServices()
        {
            return hostInstance.Services;
        }

        /// <summary>
        /// Default IMeter accessor.
        /// </summary>
        /// <returns>Default R9 IMeter instance.</returns>
        public static IMeter GetMeter()
        {
            return hostInstance.Services.GetService<IMeter>();
        }

        /// <summary>
        /// Get IMeter<T> instances that sends to specific geneva monitoring account/namespace.
        /// </summary>
        /// <typeparam name="T">IMeter category class.</typeparam>
        /// <returns>The R9 IMeter</returns>
        public static IMeter<T> GetMeter<T>()
        {
            return hostInstance.Services.GetService<IMeter<T>>();
        }

        /// <summary>
        /// Get ILogger<T> instance that sends to specific geneva table.
        /// </summary>
        /// <typeparam name="T">ILogger category class.</typeparam>
        /// <returns>The logger</returns>
        public static ILogger<T> CreateLogger<T>()
        {
            return hostInstance.Services.GetRequiredService<ILogger<T>>();
        }
    }
}
