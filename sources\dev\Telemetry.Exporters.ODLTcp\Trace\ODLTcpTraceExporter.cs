﻿// <copyright file="ODLTcpTraceExporter.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System.Diagnostics;
using System.Diagnostics.CodeAnalysis;
using Microsoft.Extensions.ObjectPool;
using Microsoft.Extensions.Options;
using Microsoft.M365.ODL.NrtTcpClient;
using Microsoft.Office.BigData.DataLoader;
using Microsoft.R9.Extensions.Diagnostics;
using Newtonsoft.Json;
using OpenTelemetry;

namespace Microsoft.M365.Core.Telemetry.Exporters.ODLTCP.Trace
{
    /// <summary>
    /// ODL TCP trace exporter.
    /// </summary>
    public sealed class ODLTcpTraceExporter : BaseExporter<Activity>
    {
        private static long seq = 0;

        private IReadOnlyDictionary<string, string> logTypeMappings = new Dictionary<string, string>();

        private IReadOnlyDictionary<string, string> fields = new Dictionary<string, string>();

        //private readonly ServiceDependentLogger odlLogger;
        private bool matchAll;

        private string matchAllLogType = string.Empty;

        private ODLTcpTraceExporterOptions odlTcpTraceExporterOptions;

        private OdlNrtTcpClient tcpClient;

        private int recordNumPerRequest = 30;

        /// <summary>
        /// 9527 as the default tcp port in Substrate
        /// </summary>
        private int tcpPort = 9527;

        private List<KeyValuePair<string, string>> lines;

        private Dictionary<string, string> schemaToError = new Dictionary<string, string>();

        private IOdlLogger logger = OdlLogger.Instance;

        private Dictionary<string, List<string>> schemaToMessages = new Dictionary<string, List<string>>();

        private ObjectPool<ODLNRTRequest> nrtRequestPool;

        private ObjectPool<ODLNRTCommonMessage> nrtMessagePool;

        private TlsOptions tlsOptions;

        private bool disposed;

        /// <summary>
        /// Initializes a new instance of the <see cref="ODLTcpTraceExporter"/> class.
        /// </summary>
        /// <param name="odlTcpTraceExporterOptions"></param>
        public ODLTcpTraceExporter(IOptions<ODLTcpTraceExporterOptions> odlTcpTraceExporterOptions)
        {
            _ = Throws.IfNull(odlTcpTraceExporterOptions);
            this.odlTcpTraceExporterOptions = odlTcpTraceExporterOptions.Value;
            _ = Throws.IfNull(this.odlTcpTraceExporterOptions);

            this.recordNumPerRequest = this.odlTcpTraceExporterOptions.RecordCountPerRequest;
            OdlLogger.UseAdhocLogger = this.odlTcpTraceExporterOptions.UseAdhocLogger;

            this.lines = new List<KeyValuePair<string, string>>(this.recordNumPerRequest);
            this.nrtRequestPool = ODLNRTRequestPoolFactory.CreateODLNRTRequestPool(1);
            this.nrtMessagePool = ODLNRTRequestPoolFactory.CreateODLNRTMessagePool(recordNumPerRequest);

            this.HandleLogTypeMappings();

            this.SetupTcpPort();
            this.SetupTcpClient(odlTcpTraceExporterOptions.Value.EnableCleanUpArchivedFiles, odlTcpTraceExporterOptions.Value.RetentionInterval, odlTcpTraceExporterOptions.Value.RetentionCount);
        }

        private void SetupTcpClient(bool enableCleanUpArchivedFiles, TimeSpan retentionInterval, long retentionCount)
        {
            this.tlsOptions = GenerateTlsOptionsDic();
            this.tcpClient = new OdlNrtTcpClient(this.tcpPort, this.odlTcpTraceExporterOptions.SendTimeout, this.odlTcpTraceExporterOptions.ConnectTimeout, this.odlTcpTraceExporterOptions.ConnectionHealthCheckInterval, this.odlTcpTraceExporterOptions.ServerIP, tlsOptions);
            this.tcpClient.SetBufferSize(this.odlTcpTraceExporterOptions.BufferSize);
            this.tcpClient.SetBatchSizeForSendingMetric(this.odlTcpTraceExporterOptions.BatchSizeForSendingMetric);
            this.tcpClient.SetSendStatisticsInterval(this.odlTcpTraceExporterOptions.SendMetricInterval);
            this.tcpClient.SetEnableCleanUpArchivedFiles(enableCleanUpArchivedFiles);
            this.tcpClient.SetRetentionInterval(retentionInterval);
            this.tcpClient.SetRetentionCount(retentionCount);
        }

        private TlsOptions GenerateTlsOptionsDic()
        {
            return new TlsOptions
            {
                IsCosmic = this.odlTcpTraceExporterOptions.IsCosmic,
                IsNPE = this.odlTcpTraceExporterOptions.IsNPE,
                EnableTlsAuth = this.odlTcpTraceExporterOptions.EnableTls,
                TlsCertsFolderPath = this.odlTcpTraceExporterOptions.TlsCertsFolderPath,
                TlsPfxPassword = this.odlTcpTraceExporterOptions.TlsPfxPassword,
                TlsCertFilePath = this.odlTcpTraceExporterOptions.TlsCertFilePath,
                TlsCACertFilePath = this.odlTcpTraceExporterOptions.TlsCACertFilePath,
                TlsCertSubjectName = this.odlTcpTraceExporterOptions.TlsCertSubjectName,
                StoreLocation = this.odlTcpTraceExporterOptions.StoreLocation,
                StoreName = this.odlTcpTraceExporterOptions.StoreName,
                TlsCertWhiteList = this.odlTcpTraceExporterOptions.TlsCertWhiteList
            };
        }

        [ExcludeFromCodeCoverage]
        private void SetupTcpPort()
        {
            if (this.odlTcpTraceExporterOptions.IsCosmic)
            {
                this.tcpPort = this.odlTcpTraceExporterOptions.TCPPort;
                return;
            }

            try
            {
                int tmpPort = RegistryReader.GetTcpPort();
                if (tmpPort != RegistryReader.InvalidPort)
                {
                    this.tcpPort = tmpPort;
                }
            }
            catch (Exception ex)
            {
                logger.Log(LogLevel.Error, LogEventId.TcpExporterError, $"Fail to read Tcp port from registry, will try to connect through default port. Error is :{ex}");
            }
        }

        private void HandleLogTypeMappings()
        {
            // set fields with prepoluated fields
            this.fields = this.odlTcpTraceExporterOptions.PrepopulatedFields;
            this.logTypeMappings = this.odlTcpTraceExporterOptions.LogTypeMappings;

            //using default logtype while logtype mapping is empty or null. 
            if (logTypeMappings == null || logTypeMappings.Count == 0)
            {
                logTypeMappings = new Dictionary<string, string>
                {
                    ["*"] = "R9OdlTcpTraceExporter",
                };
            }
            foreach (var entry in logTypeMappings)
            {
                if (string.IsNullOrEmpty(entry.Key))
                {
                    Throws.ArgumentNullException("activity source name", "please provide valid activity source and logtype mapping");
                }
                if (string.IsNullOrEmpty(entry.Value))
                {
                    Throws.ArgumentNullException("ODL logtype", "please provide valid activity source and logtype mapping");
                }
                if (entry.Key == "*")
                {
                    this.matchAllLogType = entry.Value;
                    this.matchAll = true;
                }
            }
        }

        /// <summary>
        /// Batch Exporter for ODL TCP.
        /// </summary>
        /// <param name="batch"></param>
        /// <returns></returns>
        public override ExportResult Export(in Batch<Activity> batch)
        {
            try
            {
                this.DoBatchExport(batch);
                return ExportResult.Success;
            }
            catch (Exception ex)
            {
                logger.Log(LogLevel.Error, LogEventId.TcpExporterError, $"Fail to perform batch exporting for {batch.Count} records, error is :{ex}");
                return ExportResult.Failure;
            }
        }

        private void DoBatchExport(in Batch<Activity> batch)
        {
            int i = 0, schemaErrorCnt = 0;

            string logline;
            string sdkSource = string.Empty;

            foreach (var record in batch)
            {
                try
                {
                    if (!logTypeMappings.TryGetValue(record.Source.Name, out var sdkSourceName))
                    {
                        if (matchAll)
                        {
                            sdkSource = matchAllLogType;
                        }
                        else
                        {
                            continue;
                        }
                    }
                    else
                    {
                        sdkSource = sdkSourceName;
                    }
                    
                    logline = this.odlTcpTraceExporterOptions.TraceFormatter(record, this.fields);
                }
                catch (Exception ex)
                {
                    this.schemaToError[sdkSource] = ex.Message;
                    ++schemaErrorCnt;
                    continue;
                }

                this.lines.Add(new KeyValuePair<string, string>(sdkSource, logline));
                if ((++i) % recordNumPerRequest == 0)
                {
                    this.CreateRequestAndSend();
                }
            }

            if (this.lines.Any())
            {
                this.CreateRequestAndSend();
            }

            if (this.schemaToError.Any())
            {
                logger.Log(LogLevel.Error, LogEventId.TcpExporterError, $"There are {schemaErrorCnt} records ignored due to errors while serilizing Security Records:{JsonConvert.SerializeObject(schemaToError)} ");
                this.schemaToError.Clear();
            }
        }

        /// <summary>
        /// Encapuselate the batch of log lines into a NRTRquest and send it to local ODL Tcp Server in sync mode
        /// </summary>
        public void CreateRequestAndSend()
        {
            long now = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds();
            ODLNRTRequest oDLNRTRequest = this.nrtRequestPool.Get();
            Dictionary<string, long> countPerSdksource = new ();

            try
            {
                oDLNRTRequest.Head.Timestamp = now;
                oDLNRTRequest.Head.Sequence = Interlocked.Increment(ref seq);
                oDLNRTRequest.CommonMessageBatchReq.Head.Timestamp = now;

                foreach (var line in this.lines)
                {
                    var message = this.nrtMessagePool.Get();
                    oDLNRTRequest.CommonMessageBatchReq.Messages.Add(message);
                    message.Type = ODLNRTCommonMessageType.OdlnrtmessageTypeString;
                    message.ValueString = line.Value;
                    message.ID = Guid.NewGuid().ToString();
                    message.EventID = 1;
                    message.Source = line.Key;

                    if (!countPerSdksource.TryGetValue(line.Key, out var count))
                    {
                        count = 0;
                    }
                    countPerSdksource[line.Key] = count + 1;
                }

                this.tcpClient.Send(oDLNRTRequest, this.schemaToMessages, countPerSdksource);
            }
            finally
            {
                this.lines.Clear();
                foreach (var message in oDLNRTRequest.CommonMessageBatchReq.Messages)
                {
                    this.nrtMessagePool.Return(message);
                }

                this.nrtRequestPool.Return(oDLNRTRequest);
            }
        }

        /// <summary>
        /// Dispose the exporter.
        /// </summary>
        /// <param name="disposing">Indicates whether the method call comes from a Dispose method (true) or from a finalizer (false).</param>
        protected override void Dispose(bool disposing)
        {
            base.Dispose(disposing);
            if (!disposed)
            {
                if (disposing)
                {
                    this.tcpClient?.Dispose();
                    this.tcpClient = null;

                    this.schemaToMessages = null;
                    this.logTypeMappings = null;
                    this.fields = null;
                }

                disposed = true;
            }
        }
    }
}
