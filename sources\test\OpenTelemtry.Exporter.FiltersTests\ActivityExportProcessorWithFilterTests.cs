﻿// <copyright file="ActivityExportProcessorWithFilterTests.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Diagnostics.CodeAnalysis;
using System.Threading;
using OpenTelemetry;
using OpenTelemetry.Exporter.Filters;
using Xunit;

namespace Microsoft.M365.Core.Telemetry.OpenTelemetry.Exporter.Filters.Test
{
    [ExcludeFromCodeCoverage]
    public class ProcessorWithFilterTests
    {
        private ProcessorTestExporter mockBaseExporter;
        private ProcessorTestFilter mockBaseFilter;
        private Batch<Activity> mockBatch;
        private Activity mockActivity;

        public ProcessorWithFilterTests()
        {
            mockBaseExporter = new ProcessorTestExporter(new List<Activity>());
            mockBaseFilter = new ProcessorTestFilter();
            mockActivity = CreateActivity("testProcessorSuccess", isSuccess: true);
            mockBatch = new Batch<Activity>(new Activity[2] { mockActivity, CreateActivity("testProcessorFailed", isSuccess: false) }, 2);
        }

        private BatchActivityExportProcessorWithFilter CreateBatchActivityExportProcessorWithFilter()
        {
            return new BatchActivityExportProcessorWithFilter(
                mockBaseExporter,
                mockBaseFilter, maxExportBatchSize: 1, maxQueueSize: 1, exporterTimeoutMilliseconds: 0);
        }

        private SimpleActivityExportProcessorWithFilter CreateSimpleActivityExportProcessorWithFilter()
        {
            return new SimpleActivityExportProcessorWithFilter(
                mockBaseExporter,
                mockBaseFilter);
        }

        private ReentrantActivityExportProcessorWithFilter CreateReentrantActivityExportProcessorWithFilter()
        {
            return new ReentrantActivityExportProcessorWithFilter(
                mockBaseExporter,
                mockBaseFilter);
        }

        [Fact]
        public void CheckNullExporterAndFilter()
        {
            Assert.Throws<ArgumentNullException>(() => new BatchActivityExportProcessorWithFilter(null, null));
            Assert.Throws<ArgumentNullException>(() => new BatchActivityExportProcessorWithFilter(null, mockBaseFilter));
            Assert.Throws<ArgumentNullException>(() => new BatchActivityExportProcessorWithFilter(mockBaseExporter, null));
            Assert.Throws<ArgumentNullException>(() => new SimpleActivityExportProcessorWithFilter(null, null));
            Assert.Throws<ArgumentNullException>(() => new SimpleActivityExportProcessorWithFilter(null, mockBaseFilter));
            Assert.Throws<ArgumentNullException>(() => new SimpleActivityExportProcessorWithFilter(mockBaseExporter, null));
            Assert.Throws<ArgumentNullException>(() => new ReentrantActivityExportProcessorWithFilter(null, null));
            Assert.Throws<ArgumentNullException>(() => new ReentrantActivityExportProcessorWithFilter(null, mockBaseFilter));
            Assert.Throws<ArgumentNullException>(() => new ReentrantActivityExportProcessorWithFilter(mockBaseExporter, null));
        }

        [Fact]
        public void BatchProcessorWithFilter_OnEnd_DropTheFailActivityAndKeepSuccessOne()
        {
            // Arrange
            var batchActivityExportProcessorWithFilter = CreateBatchActivityExportProcessorWithFilter();

            // Act
            foreach (var item in mockBatch)
            {
                batchActivityExportProcessorWithFilter.OnEnd(
                   item);
            }

            // Assert
            Assert.Equal(1, batchActivityExportProcessorWithFilter.FilterDropSize);

            // Shutdown(0) will trigger flush and return immediately, so let's sleep for a while
            Thread.Sleep(1_000);

            Assert.Single(mockBaseExporter.Activities);

            //make sure will not impact by other tests 
            mockBaseExporter.ClearList();
        }

        [Fact]
        public void SimpleProcessorWithFilter_OnEnd_DropTheFailActivityAndKeepSuccessOne()
        {
            // Arrange
            var simpleActivityExportProcessorWithFilter = CreateSimpleActivityExportProcessorWithFilter();

            // Act
            foreach (var item in mockBatch)
            {
                simpleActivityExportProcessorWithFilter.OnEnd(
                   item);
            }

            Assert.Single(mockBaseExporter.Activities);

            //make sure will not impact by other tests 
            mockBaseExporter.ClearList();
        }

        [Fact]
        public void ReentrantProcessorWithFilter_OnEnd_DropTheFailActivityAndKeepSuccessOne()
        {
            // Arrange
            var reentrantActivityExportProcessorWithFilter = CreateReentrantActivityExportProcessorWithFilter();

            // Act
            foreach (var item in mockBatch)
            {
                reentrantActivityExportProcessorWithFilter.OnEnd(
                   item);
            }

            Assert.Single(mockBaseExporter.Activities);

            //make sure will not impact by other tests 
            mockBaseExporter.ClearList();
        }

        private Activity CreateActivity(string operationName, bool isSuccess)
        {
            Activity data = new Activity(operationName)
            {
                ActivityTraceFlags = ActivityTraceFlags.Recorded,
            };
            data.AddTag("expectedResult", isSuccess ? "success" : "fail");
            return data;
        }
    }

    [ExcludeFromCodeCoverage]
    internal class ProcessorTestExporter : BaseExporter<Activity>
    {
        internal List<Activity> Activities { get; set; }

        public ProcessorTestExporter(List<Activity> activities)
        {
            Activities = activities;
        }

        public override ExportResult Export(in Batch<Activity> batch)
        {
            foreach (var item in batch)
            {
                Activities.Add(item);
            }
            return ExportResult.Success;
        }

        public void ClearList()
        {
            Activities?.Clear();
        }
    }

    [ExcludeFromCodeCoverage]
    internal class ProcessorTestFilter : BaseFilter<Activity>
    {
        public override string GetDescription()
        {
            return "Filter for Test.";
        }

        public override bool ShouldFilter(Activity t)
        {
            if ("success".Equals(t.GetTagItem("expectedResult")))
            {
                return true;
            }
            return false;
        }
    }
}
