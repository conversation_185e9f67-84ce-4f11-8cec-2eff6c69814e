﻿// <copyright file="RuleValidator.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;

namespace Microsoft.M365.Core.Telemetry.R9.Logging.Substrate.Sampler
{
    /// <summary>
    /// Validator used to validate the constraints and strategy parameter of a rule.
    /// </summary>
    internal static class RuleValidator
    {
        // Used for Long, Double, and LogLevel
        private static readonly HashSet<string> NumericOperators = new ()
        {
            OperatorType.LessThan,
            OperatorType.LessThanOrEqual,
            OperatorType.GreaterThan,
            OperatorType.GreaterThanOrEqual,
            OperatorType.NumericEquals,
            OperatorType.NumericNotEquals,
        };

        private static readonly HashSet<string> StringOperators = new ()
        {
            OperatorType.StartsWith,
            OperatorType.NotStartsWith,
            OperatorType.EndsWith,
            OperatorType.NotEndsWith,
            OperatorType.StringEquals,
            OperatorType.StringNotEquals
        };

        private static readonly HashSet<string> EnumOperators = new ()
        {
            OperatorType.In,
            OperatorType.NotIn
        };

        private static readonly HashSet<string> EventIdOperators = new ()
        {
            OperatorType.NumericEquals,
            OperatorType.NumericNotEquals,
        };

        private static readonly HashSet<string> StrategyTypes = new ()
        {
            StrategyType.Random,
            StrategyType.HashBasedRandom
        };

        /// <summary>
        /// Validates the rule based on the provided constraints and strategy parameter.
        /// </summary>
        /// <param name="constraints">Constraints.</param>
        /// <param name="parameter">StrategyParameter.</param>
        /// <returns><see langword="true" /> if constaints and strategy paramter are both valid; otherwise, <see langword="false" />.</returns>
        public static bool IsValid(List<Constraint> constraints, StrategyParameter parameter)
        {
            if (!IsStrategyParameterValid(parameter))
            {
                return false;
            }

            // If there are no constraints, only validate the strategy parameter.
            if (constraints.Count == 0)
            {
                return true;
            }

            return AreConstraintsValid(constraints);
        }

        private static bool AreConstraintsValid(List<Constraint> constraints)
        {
            return constraints.All(constraint =>
            {
                if (constraint.Field == null || constraint.Type == null ||
                    constraint.RuleOperator == null || constraint.Value == null)
                {
                    return false; // All fields must be non-null.
                }

                return constraint.Type switch
                {
                    ConstraintType.Double => double.TryParse(constraint.Value, System.Globalization.NumberStyles.Any, System.Globalization.CultureInfo.InvariantCulture, out _) &&
                                           NumericOperators.Contains(constraint.RuleOperator),

                    ConstraintType.Long => long.TryParse(constraint.Value, System.Globalization.NumberStyles.Any, System.Globalization.CultureInfo.InvariantCulture, out _) &&
                                        NumericOperators.Contains(constraint.RuleOperator),

                    ConstraintType.LogLevel => Enum.TryParse<LogLevel>(constraint.Value, true, out _) &&
                                             NumericOperators.Contains(constraint.RuleOperator),

                    ConstraintType.String => StringOperators.Contains(constraint.RuleOperator),

                    ConstraintType.EventId => int.TryParse(constraint.Value, System.Globalization.NumberStyles.Any, System.Globalization.CultureInfo.InvariantCulture, out _) &&
                                                EventIdOperators.Contains(constraint.RuleOperator),

                    ConstraintType.Enum => EnumOperators.Contains(constraint.RuleOperator),

                    _ => false // Invalid constraint type.
                };
            });
        }

        private static bool IsStrategyParameterValid(StrategyParameter parameter)
        {
            if (parameter == null)
            {
                return false; // Strategy parameter must be non-null.
            }

            if (string.IsNullOrEmpty(parameter.Type))
            {
                return false; // Type cannot be empty.
            }

            if (!StrategyTypes.Contains(parameter.Type))
            {
                return false; // Invalid strategy type.
            }

            if (parameter.SampleRate < 0 || parameter.SampleRate > 1)
            {
                return false; // Sample rate must be in [0,1].
            }

            // For HashBasedRandom strategy, HashKey must be non-empty
            if (parameter.Type == StrategyType.HashBasedRandom && string.IsNullOrEmpty(parameter.HashKey))
            {
                return false; // HashKey cannot be empty for HashBasedRandom strategy.
            }

            return true;
        }
    }
}
