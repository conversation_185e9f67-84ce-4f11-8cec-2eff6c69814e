﻿// <copyright file="ReentrantExportProcessor.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

// <copyright file="ReentrantExportProcessor.cs" company="OpenTelemetry Authors">
// Copyright The OpenTelemetry Authors
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
// </copyright>
using System;
using System.Diagnostics.CodeAnalysis;
using System.Linq.Expressions;
using System.Reflection;

namespace OpenTelemetry.Exporter.Filters.Internal
{
    /// <summary>
    /// Reentrant export processor.
    /// </summary>
    /// <remarks>
    /// This is copied from ReentrantExportProcessor from GenevaExporter repo as the class
    /// is internal and not visible to this project. This will be removed from R9 library
    /// in one of the two conditions below. Both of these conditions are planned items.
    ///  - GenevaLogExporter will make it internalVisible to this project.
    ///  - This class will be added to OpenTelemetry project as public.
    /// </remarks>
    /// <typeparam name="T">Type of data to be exported.</typeparam>
    [ExcludeFromCodeCoverage]
    internal class ReentrantExportProcessor<T> : BaseExportProcessor<T>
        where T : class
    {
#pragma warning disable CA1810 // Initialize reference type static fields inline
        static ReentrantExportProcessor()
#pragma warning restore CA1810 // Initialize reference type static fields inline
        {
            var flags = BindingFlags.Instance | BindingFlags.NonPublic;
            var ctor = typeof(Batch<T>).GetConstructor(flags, null, new Type[] { typeof(T) }, null);
            var value = Expression.Parameter(typeof(T), null);
            var lambda = Expression.Lambda<Func<T, Batch<T>>>(Expression.New(ctor, value), value);
            CreateBatch = lambda.Compile();
        }

        public ReentrantExportProcessor(BaseExporter<T> exporter)
            : base(exporter)
        {
        }

        protected override void OnExport(T data)
        {
            this.exporter.Export(CreateBatch(data));
        }

        private static readonly Func<T, Batch<T>> CreateBatch;
    }
}
