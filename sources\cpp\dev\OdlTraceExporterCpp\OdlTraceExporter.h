#pragma once

#include "OdlTraceExporterRecordable.h"
#include "TcpClient.h"

#include <nlohmann/json.hpp>
#include <opentelemetry/common/attribute_value.h>
#include <opentelemetry/common/key_value_iterable.h>
#include <opentelemetry/common/timestamp.h>
#include <opentelemetry/logs/logger.h>
#include <opentelemetry/logs/logger_provider.h>
#include <opentelemetry/metrics/meter_provider.h>
#include <opentelemetry/metrics/meter.h>
#include <opentelemetry/nostd/string_view.h>
#include <opentelemetry/sdk/instrumentationscope/instrumentation_scope.h>
#include <opentelemetry/sdk/metrics/meter.h>
#include <opentelemetry/sdk/resource/resource.h>
#include <opentelemetry/sdk/trace/exporter.h>
#include <opentelemetry/sdk/trace/recordable.h>
#include <opentelemetry/trace/span_context.h>
#include <opentelemetry/trace/span_id.h>
#include <opentelemetry/trace/span_metadata.h>
#include <opentelemetry/trace/trace_flags.h>
#include <opentelemetry/version.h>

#include <atomic>
#include <chrono>
#include <memory>
#include <string>
#include <unordered_map>

namespace Microsoft::M365::Exporters
{
constexpr unsigned char kMagicByte = '\x01';

struct OdlTraceExporterOptions {
    std::string host;
    std::string port;
    std::string source;
    std::unordered_map<std::string, std::string> common_dimensions;
    int tcpclient_reconnect_interval_ms = 1000;
    int tcpclient_monitor_interval_s = 10;
};

// Theoretically this class can throw. Mark noexcept to explicity crash the process if an exception is thrown.
// TODO(jiayiwang): Catch and log the exception before going to SDF.
class OdlTraceExporter final : public opentelemetry::sdk::trace::SpanExporter
{
public:
    explicit OdlTraceExporter(OdlTraceExporterOptions options) noexcept;

    std::unique_ptr<opentelemetry::sdk::trace::Recordable> MakeRecordable() noexcept override;

    opentelemetry::sdk::common::ExportResult Export(
        const opentelemetry::nostd::span<std::unique_ptr<opentelemetry::sdk::trace::Recordable>> &spans) noexcept override;

    bool ForceFlush(std::chrono::microseconds timeout = (std::chrono::microseconds::max)()) noexcept override;
    bool Shutdown(std::chrono::microseconds timeout = (std::chrono::microseconds::max)()) noexcept override;

private:
    // Unit test.
    friend class OdlTraceExporterTest;

    static std::string serialize_spans(
        const opentelemetry::nostd::span<std::unique_ptr<opentelemetry::sdk::trace::Recordable>> &spans,
        const OdlTraceExporterOptions &options, std::shared_ptr<opentelemetry::logs::Logger> logger);
    static int64_t get_next_sequance();

    OdlTraceExporterOptions options_;
    std::shared_ptr<opentelemetry::logs::LoggerProvider> logger_provider_;
    std::shared_ptr<opentelemetry::logs::Logger> logger_;
    std::shared_ptr<opentelemetry::metrics::MeterProvider> meter_provider_;
    std::shared_ptr<opentelemetry::metrics::Meter> meter_;
    std::unique_ptr<opentelemetry::metrics::Counter<uint64_t>> span_number_counter_;
    std::unique_ptr<TcpClient> tcp_client_;
    std::atomic<bool> is_shutdown_{false};
};
} // namespace Microsoft::M365::Exporters