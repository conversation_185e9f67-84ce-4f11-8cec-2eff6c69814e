# Step 3 - Instrument Data

Use [ILogger](https://learn.microsoft.com/en-us/dotnet/api/microsoft.extensions.logging.ilogger?view=net-9.0-pp) and [Compile-time logging source generation API](https://learn.microsoft.com/en-us/dotnet/core/extensions/logger-message-generator) provided by [Microsoft.Extensions.Telemetry.Abstractions](https://www.nuget.org/packages/Microsoft.Extensions.Telemetry.Abstractions/). More specifically, use [Logging with Complex Object](https://eng.ms/docs/products/geneva/collect/instrument/opentelemetrydotnet/otel-logs-deep-dive#example-5---logging-with-complex-objects).


> [!Important]  
> Ensure that each event adheres to **a single, consistent schema**, with all records of this event conforming to this schema.

There are two steps: define Event(Log table) schema and logging API signatures.

## Define Log Event Scehma
Define a event class containing all dimensions you need. Each property with get accessors will be serialized into dimension. 

> [!Note]  
> All dimensions must be defined as properties with get accessors. Fields, or properties without get accessors, will not be serialized into dimensions

```csharp
internal sealed class TestEvent(string username, string status)
{
    // We annotate sensitive properties with corresponding attributes
    // so that they will be redacted when logging.
    [PrivateData]
    public string Username { get; } = username;

    public string Status { get; } = status;
}
```
## Define Log Signature

Create a logging method signature with [LoggerMessageAttribute](https://learn.microsoft.com/en-us/dotnet/api/microsoft.extensions.logging.loggermessageattribute?view=net-9.0-pp) and [LogPropertiesAttribute ](https://learn.microsoft.com/en-us/dotnet/api/microsoft.extensions.logging.logpropertiesattribute?view=net-9.0-pp).  

The `LoggerMessageAttribute` type, introduced in .NET 6, allows for compile-time logging source generation. You just need to define a signature then the implementation of logging methods will be auto-generated at compile time.The compile-time logging solution is typically considerably faster at run time than existing logging approaches. It achieves this by eliminating boxing, temporary allocations, and copies to the maximum extent possible. Examples could be found [.NET extensions complexObjectLogging example](https://github.com/dotnet/extensions-samples/tree/0d088b48e0d4114748ad4c13103202307527f946/src/Telemetry/Logging/ComplexObjectLogging) and [OpenTelemetry Log Deep Dive](https://eng.ms/docs/products/geneva/collect/instrument/opentelemetrydotnet/otel-logs-deep-dive#example-5---logging-with-complex-objects).


> [!Note]  
> Please ensures that the OmitReferenceName property is set to true and the SkipNullProperties property is set to false.    
>
>   - **OmitReferenceName**: When set to true, this property omits the reference name from the generated name of each tag being logged.
>   - **SkipNullProperties**: When set to false, this property ensures that null properties are not skipped and are included in the logs.
>
> By setting these properties, the event schema will remain consistent even when some fields have empty value.

```csharp
    internal static partial class LogMethods
    {
        /// <summary>
        /// The signature of writing your event
        /// </summary>
        /// <param name="logger">Logger</param>
        /// <param name="event">content of the event</param>
        [LoggerMessage(Level = LogLevel.Information)]
        public static partial void Log(ILogger logger, [LogProperties(OmitReferenceName = true)] TestEvent event);
    }
```

### Design Principle for Log Signatures

When using the [LoggerMessageAttribute](https://learn.microsoft.com/en-us/dotnet/api/microsoft.extensions.logging.loggermessageattribute?view=net-9.0-pp) on logging methods, some constraints must be followed:

- Logging methods must be partial and return void.
- Logging method names must not start with an underscore.
- Parameter names of logging methods must not start with an underscore.
- Logging methods may not be defined in a nested type.
- Logging methods cannot be generic.
- If a logging method is static, the ```ILogger``` instance is required as a parameter.

### Examples

#### Case 1
```csharp
    internal static partial class CustomizedR9Logging
    {
        /// <summary>
        /// The entrance of writing Customized event
        /// </summary>
        /// <param name="logger">Logger</param>
        /// <param name="event">content of the event</param>
        [LoggerMessage(Level = LogLevel.Information)]
        public static partial void Log(ILogger logger, [LogProperties(OmitReferenceName = true)]TestEvent event);
    }
```
This logging method can be called like
```csharp
CustomizedR9Logging.Log(logger, event);
```
Another option is ```this``` keyword to define the method as an extension method.

#### Case 2
```csharp
    internal static partial class CustomizedR9Logging
    {
        /// <summary>
        /// The entrance of writing Customized event
        /// </summary>
        /// <param name="logger">Logger</param>
        /// <param name="event">content of the event</param>
        [LoggerMessage(Level = LogLevel.Information)]
        public static partial void Log(this ILogger logger, [LogProperties(OmitReferenceName = true)]TestEvent event);
    }
```

In this case, you can call this method like:
```csharp
logger.Log(event);
```
#### Case 3
You may choose to use the attribute in a **non-static** context as well. Consider the following example where the logging method is declared as an instance method. In this context, the logging method gets the logger by accessing an ```ILogger``` field in the containing class.
```csharp
public partial class CustomizedR9Logging
{
    private readonly ILogger _logger;

    public CustomizedR9Logging(ILogger logger)
    {
        _logger = logger;
    
    /// <summary>
    /// The entrance of writing Customized event
    /// </summary>
    /// <param name="event">content of the event</param>
    [LoggerMessage(Level = LogLevel.Information)]
    public partial void Log([LogProperties(OmitReferenceName = true)]CustomizedEvent event);
}
```
#### Case 4
Starting with .NET 9, the logging method can additionally get the logger from an ILogger primary constructor parameter in the containing class.
```csharp
public partial class CustomizedR9Logging(ILogger logger)
{
    /// <summary>
    /// The entrance of writing Customized event
    /// </summary>
    /// <param name="event">content of the event</param>
    [LoggerMessage(Level = LogLevel.Information)]
    public partial void Log([LogProperties(OmitReferenceName = true)]TestEvent event);
}
```

#### Case 5
Sometimes, the log level needs to be dynamic rather than statically built into the code. You can do this by omitting the log level from the attribute and instead requiring it as a parameter to the logging method.

```csharp
public static partial class CustomizedR9Logging
{
    [LoggerMessage]
    public static partial void Log(
        ILogger logger,
        LogLevel level, /* Dynamic log level as parameter, rather than defined in attribute. */
        [LogProperties(OmitReferenceName = true)]TestEvent event);
}
```

More examples of logging methods could be found in [dotnet/extensions-samples](https://github.com/dotnet/extensions-samples/blob/0d088b48e0d4114748ad4c13103202307527f946/src/Telemetry/Logging/ComplexObjectLogging/Log.cs)


## Summary

> [!Note]  
> If you are migrating from existing Telemetry SDK, it is recommended to read this page alongside [Instrumentation for Migration Users](./MigrationInstrumentaion.md) together for a  comprehensive understanding

We have our code and configuration ready, the logs can be emitted and collected by Monitoring Agent.



**Next Step**: [Validate Result in Local](./LocalValid.md)