# About Metering Solution

*<div style="text-align: right; font-size:12px">Last Modified: @@LastModified</div>*

Welcome to the **Substrate Metering Solution**! 

The solution is recommended by the M365 Unified Telemetry (Logs/Metrics) Team. Overall, we recommend all Substrate service to follow the best practise of **.NET Metering** ([.NET Metrics](https://learn.microsoft.com/en-us/dotnet/core/diagnostics/metrics)). 

## Benefits
The adopt will brings in those benefits for logging:
- **Enabled Linux support** 
- **Enable cross model supporting ability**, single code serves Model A/B2/COSMIC. 
- **Performance improvement**, enable in process aggregation to reduce data volume.
- **Extensive built-in and third-party instrumentions**, built-in metrics from various component like Asp.Net, httpClient and rich third-party instrumentation libraries.
- **New features**, exemplar to correlate with <PERSON> and futher identify root cause across multiple components.


## Main work
The implementation consists of two main parts: instrumentation and collection.

![](../../.images/Metering.png)

### 1. Instrumentation
 Code in .NET libraries takes measurements and associates these measurements with a metric name. There are two steps to instrument your application/library to track important metrics. The first step is to create meters using the [System.Diagnostics.Metrics](https://learn.microsoft.com/en-us/dotnet/api/system.diagnostics.metrics) APIs. The second step is to use a [compile-time source generator (aka Fast Metering)]((https://learn.microsoft.com/en-us/dotnet/core/diagnostics/metrics-strongly-typed)) provided by [Microsoft.Extensions.Telemetry.Abstractions](https://www.nuget.org/packages/Microsoft.Extensions.Telemetry.Abstractions/) to define strongly-typed metric tags (TagNames) and metric recording types and methods. 

#### 1.1 Create a meter

We can leverage System.Diagnostics.Metrics APIs to create meters whether in DI or Non-DI environments. [Creating Metrics in .NET ](https://learn.microsoft.com/en-us/dotnet/core/diagnostics/metrics-instrumentation) shows how to leverage .NET meter to instrument your applications/library.

#### 1.2 Create a metric and record measurement
Recommend creating a metric using **Fast metering** which is more effective. By using strongly-typed tags, you eliminate repetitive boilerplate code and ensure that related metrics share the same set of tag names with compile-time safety. Examples could be found [.NET Extension Metrics.Generators Sample](https://github.com/dotnet/extensions-samples/tree/0d088b48e0d4114748ad4c13103202307527f946/src/Telemetry/Metering/Metrics.Generators)


The .NET runtime provides several types of instruments. The types of instruments currently available can be seen here: [Types of instruments](https://learn.microsoft.com/en-us/dotnet/core/diagnostics/metrics-instrumentation%23types-of-instruments). 


### 2. Init Collection
Instrumented code can record numeric measurements, but the measurements usually need to be aggregated, process, and export to external storage for monitoring. The process of aggregating and transmitting is called collection. 

Use [Substrate Metering Extension](../../SDKs/MeteringExtensions/About.md).  It is designed for Substrate services with common enrichers, exporters, and shared metrics built-in, allowing service teams to focus on their specific needs without worrying about fundamental details.

## Adopt the solution
We classify all Substrate Services into two types of projects: Library and Executable Service.
- **Libraries** provide common functionality or APIs for other services, such as Auth library, ADDriver, SSL, etc.
- **Executable Services** are applications that run on servers, such as POP3, SDS, PingB2, etc.

In the current state, Substrate libraries control their own telemetry configurations. We recommend keeping the library’s telemetry behavior. 

Both for libraries that control their own telemetry configuration as before and executable services, no matter you are creating new services or migrating existing service telemetry from legacy SDKs like IFx/PassiveMon, you can follow these steps to complete the adoption.

### Step-by-Step Guide
- [Prerequisites](GuideSteps/Prerequisites.md)
- [Set Up Configuration](GuideSteps/Configuration.md)
- [Initialize R9 Telemetry](GuideSteps/InitR9.md)
- [Instrument Data](GuideSteps/Instrumentation.md)
- [Validate Locally](GuideSteps/LocalValid.md)
- [(For Migration) Check Potential Risks](GuideSteps/PotentialRisks.md)
- [(For Migration) Switch Data Seamlessly](GuideSteps/MigrationSteps.md)

### Sample PRs
- [Pull Request 3874547: Add R9 metering for DsApi SDK](https://o365exchange.visualstudio.com/O365%20Core/_git/DsapiSdk/pullrequest/3874547)