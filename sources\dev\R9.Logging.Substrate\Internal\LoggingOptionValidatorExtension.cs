﻿// <copyright file="LoggingOptionValidatorExtension.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using Microsoft.Extensions.Configuration;
using Microsoft.R9.Extensions.Logging;
using Microsoft.R9.Extensions.Logging.Exporters;

namespace Microsoft.M365.Core.Telemetry.R9.Logging.Substrate
{
    /// <summary>
    /// constants for logging options
    /// </summary>
    internal static class ValidatorHelper
    {
        /// <summary>
        /// Mapping between option type and configuration section name
        /// </summary>
        private static readonly Dictionary<Type, string> configSectionName = new ()
        {
            [typeof(LoggingOptions)] = "SubstrateLogging:R9Logging",
            [typeof(GenevaLogExporterOptions)] = "SubstrateLogging:GenevaExporter"
        };

        /// <summary>
        /// Update options from configuration (general for all options)
        /// </summary>
        /// <typeparam name="T">option type</typeparam>
        /// <param name="options"></param>
        /// <param name="configuration"></param>
        /// <returns></returns>
        internal static void UpdateOption<T>(this T options, IConfiguration configuration)
        {
            if (configSectionName.TryGetValue(typeof(T), out var sectionName))
            {
                var section = configuration.GetSection(sectionName);
                section.Bind(options);
            }
            else
            {
                throw new ArgumentException($"Loading configurations, but Option Type [{typeof(T)}] is not registered.");
            }
        }
    }

    /// <summary>
    /// validate logging options for substrate
    /// </summary>
    public static class LoggingOptionValidatorExtension
    {
        /// <summary>
        /// load values from configuration section "Logging", update and validate logging options
        /// </summary>
        /// <param name="options"></param>
        /// <param name="configuration"></param>
        /// <returns></returns>
        public static LoggingOptions UpdateAndValidateOptions(this LoggingOptions options, IConfiguration configuration)
        {
            options.UpdateOption(configuration);

            return options;
        }
    }

    /// <summary>
    /// validate geneva log exporter options
    /// </summary>
    public static class GenevaLoggingOptionvalidatorExtension
    {
        /// <summary>
        /// load values from configuration "GenevaLoggingExporter", update and validate logging options
        /// </summary>
        /// <param name="options"></param>
        /// <param name="configuration"></param>
        /// <returns></returns>
        public static GenevaLogExporterOptions UpdateAndValidateOptions(this GenevaLogExporterOptions options, IConfiguration configuration)
        {
            options.UpdateOption(configuration);

            if (options.ConnectionString is null || options.ConnectionString.Length == 0)
            {
                throw new ArgumentException("*ConnectionString* is required for Geneva Event.");
            }

            if (options.TableNameMappings is null || options.TableNameMappings.Count == 0)
            {
                throw new ArgumentException("*TableNameMappings* is required for Geneva Event.");
            }

            return options;
        }
    }
}
