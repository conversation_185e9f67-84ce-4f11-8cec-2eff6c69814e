﻿using System;
using System.Collections.Generic;
using System.Diagnostics.CodeAnalysis;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.ObjectPool;
using Microsoft.Office.BigData.DataLoader;

namespace Microsoft.M365.Core.Telemetry.ODL.SecurityTelemetryTcpExporter
{
    /// <summary>
    /// ODLNRTRequestPooledObject Policy
    /// </summary>
    [ExcludeFromCodeCoverage]
    public class ODLNRTRequestPooledObjectPolicy : PooledObjectPolicy<ODLNRTRequest>
    {
        public override ODLNRTRequest Create()
        {
            ODLNRTRequest oDLNRTRequest = new ODLNRTRequest();
            oDLNRTRequest.Head = new ODLNRTCommonHead();
            oDLNRTRequest.Head.CommandType = ODLNRTCommandType.OdlnrtcmdSecurityMessageBatch;

            oDLNRTRequest.SecurityMessageBatchReq = new ODLNRTSecurityMessageBatchReq();
            oDLNRTRequest.SecurityMessageBatchReq.Head = new ODLNRTMessageHead();

            return oDLNRTRequest;
        }

        public override bool Return(ODLNRTRequest obj)
        {
            obj.SecurityMessageBatchReq.Messages.Clear();
            return true;
        }
    }
}
