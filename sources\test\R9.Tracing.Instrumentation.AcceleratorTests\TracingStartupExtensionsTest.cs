﻿// <copyright file="TracingStartupExtensionsTest.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

namespace Microsoft.M365.Core.Telemetry.R9.Tracing.Instrumentation.Accelerator.Tests
{
    using System;
    using System.Collections.Generic;
    using Microsoft.Extensions.Configuration;
    using Microsoft.Extensions.DependencyInjection;
    using Microsoft.M365.Core.Telemetry.ECSClient;
    using OpenTelemetry.Trace;
    using Xunit;

    /// <summary>
    /// TracingStartupExtensionsTest
    /// </summary>
    public class TracingStartupExtensionsTest
    {
        IConfigurationRoot jsonConfigRoot = new ConfigurationBuilder().AddJsonFile("appsettings.json").Build();

        public TracingStartupExtensionsTest()
        {
            TracerStartupExtensions.MockedR9TracingConfig = null;
        }

        /// <summary>
        /// AddDistributedTracingServiceSucceed
        /// </summary>
        [Fact]
        public void AddDistributedTracingServiceSucceed()
        {
            R9TracingConfig mockedR9TracingConfig = new R9TracingConfig(jsonConfigRoot);
            mockedR9TracingConfig.R9DTEnabled = true;
            mockedR9TracingConfig.SamplerType = Constants.DynamicSamplerType.AlwaysOn;
            mockedR9TracingConfig.TraceSampleRate = 1;
            TracerStartupExtensions.MockedR9TracingConfig = mockedR9TracingConfig;

            var services = new ServiceCollection();

            services.AddDistributedTracingService(jsonConfigRoot.GetSection("Microsoft_m365_core_telemetry:ServiceMetadata"));
            Assert.NotNull(services.BuildServiceProvider().GetService<TracerProvider>());
        }

        /// <summary>
        /// AddDistributedTracingServiceIsDisabled
        /// </summary>
        [Fact]
        public void AddDistributedTracingServiceIsDisabled()
        {
            var mockConfiguration = new Dictionary<string, string>
            {
                { "Microsoft_m365_core_telemetry:ServiceMetadata:ServiceName", "cc" },
                { "Microsoft_m365_core_telemetry:ServiceMetadata:RuntimeModel", "ModelA" }
            };

            var configuration = new ConfigurationBuilder()
                .AddInMemoryCollection(mockConfiguration)
                .Build();

            var sc = new ServiceCollection();
            TracerStartupExtensions.MockedR9TracingConfig = null;
            sc.AddDistributedTracingService(configuration.GetSection("Microsoft_m365_core_telemetry:ServiceMetadata"));
            Assert.Null(sc.BuildServiceProvider().GetService<TracerProvider>());
        }

        /// <summary>
        /// AddDistributedTracingServiceThrowsException
        /// </summary>
        [Fact]
        public void AddDistributedTracingServiceThrowsException()
        {
            Assert.Throws<ArgumentNullException>(() => TracerStartupExtensions.AddDistributedTracingService(null, jsonConfigRoot, null));
            Assert.Throws<ArgumentNullException>(() => TracerStartupExtensions.AddDistributedTracingService(new ServiceCollection(), null, jsonConfigRoot));
        }
    }
}
