<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <VCProjectVersion>16.0</VCProjectVersion>
    <Keyword>Win32Proj</Keyword>
    <ProjectGuid>{d7d0b355-fe24-4880-873b-dbc953bda102}</ProjectGuid>
    <RootNamespace>DemoApp</RootNamespace>
    <WindowsTargetPlatformVersion>10.0</WindowsTargetPlatformVersion>
    <ConfigurationType>Application</ConfigurationType>
    <PlatformToolset>v143</PlatformToolset>
    <CharacterSet>Unicode</CharacterSet>
    <ResolveNuGetPackages>false</ResolveNuGetPackages>
    <CopyLocalProjectReference>true</CopyLocalProjectReference>
    <CopyLocalDebugSymbols>true</CopyLocalDebugSymbols>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <UseDebugLibraries>true</UseDebugLibraries>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <UseDebugLibraries>false</UseDebugLibraries>
    <WholeProgramOptimization>true</WholeProgramOptimization>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="Shared">
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <SDLCheck>true</SDLCheck>
      <PreprocessorDefinitions>_ITERATOR_DEBUG_LEVEL=2;_DEBUG;_CONSOLE;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <ConformanceMode>true</ConformanceMode>
      <AdditionalIncludeDirectories>
        $(DistribRoot)\$(Configuration)\$(Platform)\ODLNRTMessageLib;
        $(EnlistmentRoot)\sources\cpp\dev\FileExporterCpp;
        $(EnlistmentRoot)\sources\cpp\dev\AsioUtils;
        $(EnlistmentRoot)\sources\cpp\test\TcpServer;
        $(EnlistmentRoot)\sources\cpp\dev\OdlTraceExporterCpp;
        %(AdditionalIncludeDirectories)
      </AdditionalIncludeDirectories>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
    </ClCompile>
    <Link>
      <SubSystem>Console</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <AdditionalLibraryDirectories>
        $(DistribRoot)\$(Configuration)\$(Platform)\ODLNRTMessageLib;
        $(DistribRoot)\$(Configuration)\$(Platform)\FileExporterCpp;
        $(DistribRoot)\$(Configuration)\$(Platform)\AsioUtils;
        $(DistribRoot)\$(Configuration)\$(Platform)\TcpServer;
        $(DistribRoot)\$(Configuration)\$(Platform)\OdlTraceExporterCpp;
        %(AdditionalLibraryDirectories)
      </AdditionalLibraryDirectories>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <SDLCheck>true</SDLCheck>
      <PreprocessorDefinitions>_ITERATOR_DEBUG_LEVEL=0;NDEBUG;_CONSOLE;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <ConformanceMode>true</ConformanceMode>
      <AdditionalIncludeDirectories>
        $(DistribRoot)\$(Configuration)\$(Platform)\ODLNRTMessageLib;
        $(EnlistmentRoot)\sources\cpp\dev\FileExporterCpp;
        $(EnlistmentRoot)\sources\cpp\dev\AsioUtils;
        $(EnlistmentRoot)\sources\cpp\test\TcpServer;
        $(EnlistmentRoot)\sources\cpp\dev\OdlTraceExporterCpp;
        %(AdditionalIncludeDirectories)
      </AdditionalIncludeDirectories>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
    </ClCompile>
    <Link>
      <SubSystem>Console</SubSystem>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <OptimizeReferences>true</OptimizeReferences>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <AdditionalLibraryDirectories>
        $(DistribRoot)\$(Configuration)\$(Platform)\ODLNRTMessageLib;
        $(DistribRoot)\$(Configuration)\$(Platform)\FileExporterCpp;
        $(DistribRoot)\$(Configuration)\$(Platform)\AsioUtils;
        $(DistribRoot)\$(Configuration)\$(Platform)\TcpServer;
        $(DistribRoot)\$(Configuration)\$(Platform)\OdlTraceExporterCpp;
        %(AdditionalLibraryDirectories)
      </AdditionalLibraryDirectories>
    </Link>
  </ItemDefinitionGroup>
  <ItemGroup>
    <None Include="packages.config" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="$(EnlistmentRoot)\sources\cpp\dev\OdlTraceExporterCpp\OdlTraceExporterCpp.vcxproj">
      <Project>{dfa75dff-f6ea-45e8-90ce-7858ef65f042}</Project>
    </ProjectReference>
    <ProjectReference Include="$(EnlistmentRoot)\sources\cpp\dev\ODLNRTMessageLib\ODLNRTMessageLib.vcxproj">
      <Project>{d7d0b355-fe24-4880-873b-dbc953bda103}</Project>
    </ProjectReference>
    <ProjectReference Include="$(EnlistmentRoot)\sources\cpp\dev\AsioUtils\AsioUtils.vcxproj">
      <Project>{7062318E-CE9D-4C93-B616-20D096520E1E}</Project>
    </ProjectReference>
    <ProjectReference Include="$(EnlistmentRoot)\sources\cpp\dev\FileExporterCpp\FileExporterCpp.vcxproj">
      <Project>{E9DDFD22-06B4-4BE0-A7FC-6F1E66C38DB5}</Project>
    </ProjectReference>
    <ProjectReference Include="$(EnlistmentRoot)\sources\cpp\test\TcpServer\TcpServer.vcxproj">
      <Project>{738F538A-C392-40B8-A97F-7B2BAF9D7992}</Project>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="DemoApp.cpp" />
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>