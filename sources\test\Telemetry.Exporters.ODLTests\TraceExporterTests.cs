﻿// <copyright file="TraceExporterTests.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.ServiceProcess;
using System.Threading.Tasks;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.R9.Extensions.Tracing.Exporters;
using OpenTelemetry;
using OpenTelemetry.Resources;
using OpenTelemetry.Trace;
using Xunit;
using Assert = Xunit.Assert;

namespace Microsoft.R9.Extensions.Telemetry.Exporters.ODL.Test
{
    public class TraceExporterTests
    {
        static string sourceName = "ODLTraceExporterTest";

        [Fact]
        public void AddODLExporter_GivenOptions_ExportToLogType()
        {
            var exception = Record.Exception(() =>
            {
                var host = new HostBuilder().ConfigureServices((context, service) =>
            {
                service.AddOpenTelemetry().WithTracing(builder =>
                {
                    builder.AddSource(sourceName)
                           .SetResourceBuilder(ResourceBuilder
                            .CreateDefault()
                            .AddService(serviceName: "ODLTestService", serviceVersion: "1.0.0"))
                           .AddODLExporter(options => options.EnableFallBack = false, false);
                });
            }).Build();
                RunHostAndActivityAsync(host).ConfigureAwait(false);
            });
            Assert.Null(exception);
        }

        [Fact]
        public void AddODLExporter_EnableBatch_UsingBatchProcessor()
        {
            var exception = Record.Exception(() =>
            {
                var host = new HostBuilder().ConfigureServices((context, service) =>
                {
                    service.AddOpenTelemetry().WithTracing(builder =>
                    {
                        builder.AddSource(sourceName)
                               .SetResourceBuilder(ResourceBuilder
                                .CreateDefault()
                                .AddService(serviceName: "ODLTestService", serviceVersion: "1.0.0"))
                               .AddODLExporter(options => options.EnableFallBack = false, true);
                    });
                }).Build();
                RunHostAndActivityAsync(host).ConfigureAwait(false);
                });
            Assert.Null(exception);
        }

        [Fact]
        public void AddODLExporter_PrepopulatedFields_AddToTrace()
        {
            var exception = Record.Exception(() =>
            {
                var host = new HostBuilder().ConfigureServices((context, service) =>
                {
                    service.AddOpenTelemetry().WithTracing(builder =>
                    {
                        builder.AddSource(sourceName)
                               .SetResourceBuilder(ResourceBuilder
                                .CreateDefault()
                                .AddService(serviceName: "ODLTestService", serviceVersion: "1.0.0"))
                               .AddODLExporter(
                               options =>
                               {
                                   options.EnableFallBack = false;
                                   options.PrepopulatedFields = new Dictionary<string, string>
                                   {
                                       ["TestField"] = "TestValue",
                                   };
                               }, false);
                    });
                }).Build();
                RunHostAndActivityAsync(host).ConfigureAwait(false);
            });
            Assert.Null(exception);
        }

        //[Fact]
        //public void AddODLExporter_GivenOptions_ExportToCustomerLogType()
        //{
        //    var exception = Record.Exception(() =>
        //    {
        //        var host = new HostBuilder().ConfigureServices((context, service) =>
        //    {
        //        service.AddOpenTelemetryTracing(builder =>
        //        builder.SetSampler(new AlwaysOnSampler())
        //               .AddSource(sourceName)
        //               .AddODLExporter(
        //                   options =>
        //                   {
        //                       options.EnableFallBack = false;

        //                       //options.LogTypeMappings = new Dictionary<string, string>
        //                       //{
        //                       //    ["*"] = "R9TraceExporterLocal",
        //                       //};
        //                   }, false));
        //    }).Build();
        //        RunHostAndActivityAsync(host).ConfigureAwait(false);
        //    });
        //    Assert.Null(exception);
        //}

        //[Fact]
        //public void AddODLExporter_InvalidMapping_SkipActivity()
        //{
        //    var exception = Record.Exception(() =>
        //    {
        //        var host = new HostBuilder().ConfigureServices((context, service) =>
        //        {
        //            service.AddOpenTelemetryTracing(builder =>
        //            builder.SetSampler(new AlwaysOnSampler())
        //                   .AddSource(sourceName)
        //                   .AddODLExporter(
        //                       options =>
        //                       {
        //                           options.EnableFallBack = false;
        //                           options.LogTypeMappings = new Dictionary<string, string>
        //                           {
        //                               ["Test2"] = "R9TraceExporterLocal",
        //                           };
        //                       }, false));
        //        }).Build();
        //        RunHostAndActivityAsync(host).ConfigureAwait(false);
        //    });
        //    Assert.Null(exception);
        //}

        //[Fact]
        //public void AddODLExporter_GivenInvalidMapping_throwException()
        //{
        //    var host = new HostBuilder().ConfigureServices((context, service) =>
        //    {
        //        service.AddOpenTelemetryTracing(builder =>
        //        builder.SetSampler(new AlwaysOnSampler())
        //               .AddSource(sourceName)
        //               .AddODLExporter(
        //                   options =>
        //                   {
        //                       options.EnableFallBack = false;
        //                       options.LogTypeMappings = new Dictionary<string, string>
        //                       {
        //                           [string.Empty] = "R9TraceExporterLocal",
        //                       };
        //                   }, false));
        //    }).Build();
        //    Assert.ThrowsAsync<ArgumentNullException>(() => host.StartAsync()).ConfigureAwait(false);

        //    host = new HostBuilder().ConfigureServices((context, service) =>
        //    {
        //        service.AddOpenTelemetryTracing(builder =>
        //        builder.SetSampler(new AlwaysOnSampler())
        //               .AddSource(sourceName)
        //               .AddODLExporter(
        //                   options =>
        //                   {
        //                       options.EnableFallBack = false;
        //                       options.LogTypeMappings = new Dictionary<string, string>
        //                       {
        //                           ["R9TraceExporterLocal"] = string.Empty,
        //                       };
        //                   }, false));
        //    }).Build();
        //    Assert.ThrowsAsync<ArgumentNullException>(() => host.StartAsync()).ConfigureAwait(false);
        //}

        private static async Task RunHostAndActivityAsync(IHost host, string activityName = "Test")
        {
            await host.StartAsync().ConfigureAwait(true);
            var source = new ActivitySource(sourceName);
            var link = new ActivityLink(new ActivityContext(ActivityTraceId.CreateRandom(), ActivitySpanId.CreateRandom(), ActivityTraceFlags.Recorded));
            using (var activity = source.StartActivity("Foo", ActivityKind.Internal, null, null, new ActivityLink[] { link }))
            {
            }

            using (var activity = source.StartActivity("Bar"))
            {
                activity.SetStatus(ActivityStatusCode.Error, "bar error");
                using (var child = source.StartActivity("Bar2", ActivityKind.Client))
                {
                    child?.SetTag("http.method", "GET");
                }
            }

            using (var activity = source.StartActivity("Baz"))
            {
                activity.SetStatus(ActivityStatusCode.Ok);
            }

            await host.StopAsync().ConfigureAwait(true);
        }
    }
}
