﻿// <copyright file="ODLFileExporter.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using Microsoft.Extensions.Options;
using Microsoft.R9.Extensions.SecurityTelemetry;

namespace Microsoft.M365.Core.Telemetry.FileExporter
{
    /// <summary>
    /// Exporter for file targets.
    /// </summary>
    internal sealed class ODLFileExporter : BatchExporter
    {
        private readonly SecurityRecordFileLogger securityRecordFileLogger;

        /// <summary>
        /// Initializes a new instance of the <see cref="ODLFileExporter"/> class.
        /// </summary>
        /// <param name="securityTelemetryOptions"></param>
        /// <param name="fileExporterOptions"></param>
        public ODLFileExporter(IOptions<SecurityTelemetryOptions> securityTelemetryOptions, IOptions<FileExporterOptions> fileExporterOptions) : base(fileExporterOptions.Value.MaxQueueSize, fileExporterOptions.Value.ScheduledDelayMilliseconds, fileExporterOptions.Value.ExporterTimeoutMilliseconds, fileExporterOptions.Value.MaxExportBatchSize)
        {
            var tmpFileExporterOptions = fileExporterOptions.Value;
            tmpFileExporterOptions.Validate();
            securityRecordFileLogger = new SecurityRecordFileLogger(securityTelemetryOptions.Value, tmpFileExporterOptions);
        }

        /// <summary>
        /// Use injected logger to write logs to file.
        /// </summary>
        /// <param name="batch">Batch of log records.</param>
        public override void ExportBatch(in Batch<SecurityRecord> batch)
        {
            securityRecordFileLogger.Log(batch);
        }

        /// <summary>
        /// Dispose
        /// </summary>
        /// <param name="disposing"></param>
        protected override void Dispose(bool disposing)
        {
            base.Dispose(disposing);

            if (disposing)
            {
                securityRecordFileLogger.Dispose();
            }
        }
    }
}
