﻿// <copyright file="ExporterWithFilterTraceExtensionTests.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Diagnostics.CodeAnalysis;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.R9.Extensions.Telemetry.Exporter.Filters;
using Microsoft.R9.Extensions.Tracing.Exporters;
using Newtonsoft.Json;
using OpenTelemetry;
using OpenTelemetry.Exporter.Filters;
using OpenTelemetry.Resources;
using OpenTelemetry.Trace;
using Xunit;

namespace Microsoft.M365.Core.Telemetry.R9.Exporter.Filters.Test
{
    [ExcludeFromCodeCoverage]
    public class ExporterWithFilterTraceExtensionTests
    {
        static string source = "TestSource";
        static ActivitySource activitySource = new ActivitySource(source);

        [Fact]
        public void AddODLExporter_GivenInvalidArguments_ThrowsException()
        {
            Assert.Throws<ArgumentNullException>(() =>
           ((TracerProviderBuilder)null!).AddODLExporter(option => { option.EnableFallBack = false; }, false, new ProcessorTestFilter()));

            Assert.Throws<ArgumentNullException>(() =>
            {
                var traceProvider = Sdk.CreateTracerProviderBuilder()
                .AddODLExporter(option => { option.EnableFallBack = false; }, false, filter: (BaseFilter<Activity>)null!).Build();
            });

            Assert.Throws<ArgumentNullException>(() =>
            {
                var traceProvider = Sdk.CreateTracerProviderBuilder()
                .AddODLExporter(option => { option.EnableFallBack = false; }, false, sampler: (Sampler)null!).Build();
            });
            Assert.Throws<ArgumentNullException>(() =>
            {
                var traceProvider = Sdk.CreateTracerProviderBuilder()
                .AddODLExporter(configure: null, false, filter: new ProcessorTestFilter()).Build();
            });
            Assert.Throws<ArgumentNullException>(() =>
            {
                var traceProvider = Sdk.CreateTracerProviderBuilder()
                .AddODLExporter(configure: null, false, sampler: new TestSampler()).Build();
            });
        }

        [Fact]
        public void AddGenevaExporter_GivenInvalidArguments_ThrowsException()
        {
            Assert.Throws<ArgumentNullException>(() =>
           ((TracerProviderBuilder)null!).AddGenevaExporter(filter: new ProcessorTestFilter()));

            Assert.Throws<ArgumentNullException>(() =>
            {
                var traceProvider = Sdk.CreateTracerProviderBuilder()
                .AddGenevaExporter(configure: option => { option.ConnectionString = "EtwSession=OpenTelemetry"; }, filter: (BaseFilter<Activity>)null!).Build();
            });

            Assert.Throws<ArgumentNullException>(() =>
            {
                var traceProvider = Sdk.CreateTracerProviderBuilder()
                .AddGenevaExporter(configure: option => { option.ConnectionString = "EtwSession=OpenTelemetry"; }, sampler: (Sampler)null!).Build();
            });
            Assert.Throws<ArgumentNullException>(() =>
            {
            var traceProvider = Sdk.CreateTracerProviderBuilder()
            .AddGenevaExporter(sampler: new TestSampler(), configure: null!).Build();
            });
            Assert.Throws<ArgumentNullException>(() =>
            {
                var traceProvider = Sdk.CreateTracerProviderBuilder()
                .AddGenevaExporter(filter: new ProcessorTestFilter(), configure: null!).Build();
            });
        }

        [Fact]
        public void AddGenevaExporter_AddFilter_FollowTheSampleResult()
        {
            var filter = new ProcessorTestFilter();
            var host = new HostBuilder().ConfigureServices((context, service) =>
            {
                service.AddOpenTelemetry().WithTracing(builder =>
                {
                    builder.AddSource(source)
                           .SetResourceBuilder(ResourceBuilder
                            .CreateDefault()
                            .AddService(serviceName: "ODLTestService", serviceVersion: "1.0.0"))
                           .AddGenevaExporter(configure: option => { option.ConnectionString = "EtwSession=OpenTelemetry"; }, filter);
                });
            }).Build();
            TestActivityOnHost(host, filter);
        }

        [Fact]
        public void AddGenevaExporter_AddSampler_FollowTheSampleResult()
        {
            var sampler = new TestSampler();
            var host = new HostBuilder().ConfigureServices((context, service) =>
            {
                service.AddOpenTelemetry().WithTracing(builder =>
                {
                    builder.AddSource(source)
                           .SetResourceBuilder(ResourceBuilder
                            .CreateDefault()
                            .AddService(serviceName: "ODLTestService", serviceVersion: "1.0.0"))
                           .AddGenevaExporter(configure: option => { option.ConnectionString = "EtwSession=OpenTelemetry"; }, sampler);
                });
            }).Build();
            TestActivityOnHost(host, sampler: sampler);
        }

        [Fact]
        public void AddGenevaExporter_AddSamplerWithConfigPreSet_FollowTheSampleResult()
        {
            var sampler = new TestSampler();
            var host = new HostBuilder().ConfigureServices((context, service) =>
            {
                Action<GenevaTraceExporterOptions> config = option => { option.ConnectionString = "EtwSession=OpenTelemetry"; };
                service.Configure(config);
                service.AddOpenTelemetry().WithTracing(builder =>
                {
                    builder.AddSource(source)
                           .SetResourceBuilder(ResourceBuilder
                            .CreateDefault()
                            .AddService(serviceName: "ODLTestService", serviceVersion: "1.0.0"))
                           .AddGenevaExporter(sampler);
                });
            }).Build();
            TestActivityOnHost(host, sampler: sampler);
        }

        [Fact]
        public void AddGenevaExporter_AddFilterWithConfigPreSet_FollowTheSampleResult()
        {
            var filter = new ProcessorTestFilter();
            var host = new HostBuilder().ConfigureServices((context, service) =>
            {
                Action<GenevaTraceExporterOptions> config = option => { option.ConnectionString = "EtwSession=OpenTelemetry"; };
                service.Configure(config);
                service.AddOpenTelemetry().WithTracing(builder =>
                {
                    builder.AddSource(source)
                           .SetResourceBuilder(ResourceBuilder
                            .CreateDefault()
                            .AddService(serviceName: "ODLTestService", serviceVersion: "1.0.0"))
                           .AddGenevaExporter(filter);
                });
            }).Build();
            TestActivityOnHost(host, filter);
        }

        [Fact]
        public void AddGenevaExporter_AddSamplerWithSection_FollowTheSampleResult()
        {
            var sampler = new TestSampler();
            IConfigurationRoot jsonConfigRoot = new ConfigurationBuilder().AddJsonFile("appsettings.json").Build();
            var config = jsonConfigRoot.GetSection("GenevaExporterWindows");

            var options = new ODLTraceExporterOptions();
            jsonConfigRoot.Bind("GenevaExporterWindows", options);
            config.Value = JsonConvert.SerializeObject(options);

            var host = new HostBuilder().ConfigureServices((context, service) =>
            {
                service.AddOpenTelemetry().WithTracing(builder =>
                {
                    builder.AddSource(source)
                           .SetResourceBuilder(ResourceBuilder
                            .CreateDefault()
                            .AddService(serviceName: "ODLTestService", serviceVersion: "1.0.0"))
                           .AddGenevaExporter(config, sampler);
                });
            }).Build();
            TestActivityOnHost(host, sampler: sampler);
        }

        [Fact]
        public void AddGenevaExporter_AddFilterWithSection_FollowTheSampleResult()
        {
            var filter = new ProcessorTestFilter();
            IConfigurationRoot jsonConfigRoot = new ConfigurationBuilder().AddJsonFile("appsettings.json").Build();
            var config = jsonConfigRoot.GetSection("GenevaExporterWindows");

            var options = new ODLTraceExporterOptions();
            jsonConfigRoot.Bind("GenevaExporterWindows", options);
            config.Value = JsonConvert.SerializeObject(options);

            var host = new HostBuilder().ConfigureServices((context, service) =>
            {
                service.AddOpenTelemetry().WithTracing(builder =>
                {
                    builder.AddSource(source)
                           .SetResourceBuilder(ResourceBuilder
                            .CreateDefault()
                            .AddService(serviceName: "ODLTestService", serviceVersion: "1.0.0"))
                           .AddGenevaExporter(config, filter);
                });
            }).Build();
            TestActivityOnHost(host, filter);
        }

        [Fact]
        public void AddODLExporter_AddSampler_FollowTheSampleResult()
        {
            var sampler = new TestSampler();
            var host = new HostBuilder().ConfigureServices((context, service) =>
            {
                service.AddOpenTelemetry().WithTracing(builder =>
                {
                    builder.AddSource(source)
                           .SetResourceBuilder(ResourceBuilder
                            .CreateDefault()
                            .AddService(serviceName: "ODLTestService", serviceVersion: "1.0.0"))
                           .AddODLExporter(option => { option.EnableFallBack = false; }, false, sampler);
                });
            }).Build();
            TestActivityOnHost(host, sampler: sampler);
        }

        [Fact]
        public void AddODLExporter_AddFilter_FollowTheSampleResult()
        {
            var filter = new ProcessorTestFilter();
            var host = new HostBuilder().ConfigureServices((context, service) =>
            {
                service.AddOpenTelemetry().WithTracing(builder =>
                {
                    builder.AddSource(source)
                           .SetResourceBuilder(ResourceBuilder
                            .CreateDefault()
                            .AddService(serviceName: "ODLTestService", serviceVersion: "1.0.0"))
                           .AddODLExporter(option => { option.EnableFallBack = false; }, false, filter);
                });
            }).Build();
            TestActivityOnHost(host, filter);
        }

        [Fact]
        public void AddODLExporter_AddSamplerWithSection_FollowTheSampleResult()
        {
            var sampler = new TestSampler();
            IConfigurationRoot jsonConfigRoot = new ConfigurationBuilder().AddJsonFile("appsettings.json").Build();
            var config = jsonConfigRoot.GetSection("ODLExporterWithParam");

            var options = new ODLTraceExporterOptions();
            jsonConfigRoot.Bind("ODLExporterWithParam", options);
            config.Value = JsonConvert.SerializeObject(options);
            
            var host = new HostBuilder().ConfigureServices((context, service) =>
            {
                service.AddOpenTelemetry().WithTracing(builder =>
                {
                    builder.AddSource(source)
                           .SetResourceBuilder(ResourceBuilder
                            .CreateDefault()
                            .AddService(serviceName: "ODLTestService", serviceVersion: "1.0.0"))
                           .AddODLExporter(config, false, sampler);
                });
            }).Build();
            TestActivityOnHost(host, sampler: sampler);
        }

        [Fact]
        public void AddODLExporter_AddFilterWithSection_FollowTheSampleResult()
        {
            var filter = new ProcessorTestFilter();
            IConfigurationRoot jsonConfigRoot = new ConfigurationBuilder().AddJsonFile("appsettings.json").Build();
            var config = jsonConfigRoot.GetSection("ODLExporterWithParam");

            var options = new ODLTraceExporterOptions();
            jsonConfigRoot.Bind("ODLExporterWithParam", options);
            config.Value = JsonConvert.SerializeObject(options);

            var host = new HostBuilder().ConfigureServices((context, service) =>
            {
                service.AddOpenTelemetry().WithTracing(builder =>
                {
                    builder.AddSource(source)
                           .SetResourceBuilder(ResourceBuilder
                            .CreateDefault()
                            .AddService(serviceName: "ODLTestService", serviceVersion: "1.0.0"))
                           .AddODLExporter(config, true, filter);
                });
            }).Build();
            TestActivityOnHost(host, filter);
        }

        [Fact]
        public void AddODLTcpExporter_AddSampler_FollowTheSampleResult()
        {
            var sampler = new TestSampler();
            var host = new HostBuilder().ConfigureServices((context, service) =>
            {
                service.AddOpenTelemetry().WithTracing(builder =>
                {
                    builder.AddSource(source)
                           .SetResourceBuilder(ResourceBuilder
                            .CreateDefault()
                            .AddService(serviceName: "ODLTestService", serviceVersion: "1.0.0"))
                           .AddODLTcpExporter(option => { option.IsCosmic = false; }, false, sampler);
                });
            }).Build();
            TestActivityOnHost(host, sampler: sampler);
        }

        [Fact]
        public void AddODLTcpExporter_AddFilter_FollowTheSampleResult()
        {
            var filter = new ProcessorTestFilter();
            var host = new HostBuilder().ConfigureServices((context, service) =>
            {
                service.AddOpenTelemetry().WithTracing(builder =>
                {
                    builder.AddSource(source)
                           .SetResourceBuilder(ResourceBuilder
                            .CreateDefault()
                            .AddService(serviceName: "ODLTestService", serviceVersion: "1.0.0"))
                           .AddODLTcpExporter(option => { option.IsCosmic = false; }, false, filter);
                });
            }).Build();
            TestActivityOnHost(host, filter);
        }

        [Fact]
        public void AddODLTcpExporter_AddSamplerWithSection_FollowTheSampleResult()
        {
            var sampler = new TestSampler();
            IConfigurationRoot jsonConfigRoot = new ConfigurationBuilder().AddJsonFile("appsettings.json").Build();
            var config = jsonConfigRoot.GetSection("ODLTcpExporterWithParam");

            var options = new ODLTraceExporterOptions();
            jsonConfigRoot.Bind("ODLTcpExporterWithParam", options);
            config.Value = JsonConvert.SerializeObject(options);
            
            var host = new HostBuilder().ConfigureServices((context, service) =>
            {
                service.AddOpenTelemetry().WithTracing(builder =>
                {
                    builder.AddSource(source)
                           .SetResourceBuilder(ResourceBuilder
                            .CreateDefault()
                            .AddService(serviceName: "ODLTestService", serviceVersion: "1.0.0"))
                           .AddODLTcpExporter(config, false, sampler);
                });
            }).Build();
            TestActivityOnHost(host, sampler: sampler);
        }

        [Fact]
        public void AddODLTcpExporter_AddFilterWithSection_FollowTheSampleResult()
        {
            var filter = new ProcessorTestFilter();
            IConfigurationRoot jsonConfigRoot = new ConfigurationBuilder().AddJsonFile("appsettings.json").Build();
            var config = jsonConfigRoot.GetSection("ODLTcpExporterWithParam");

            var options = new ODLTraceExporterOptions();
            jsonConfigRoot.Bind("ODLTcpExporterWithParam", options);
            config.Value = JsonConvert.SerializeObject(options);

            var host = new HostBuilder().ConfigureServices((context, service) =>
            {
                service.AddOpenTelemetry().WithTracing(builder =>
                {
                    builder.AddSource(source)
                           .SetResourceBuilder(ResourceBuilder
                            .CreateDefault()
                            .AddService(serviceName: "ODLTestService", serviceVersion: "1.0.0"))
                           .AddODLTcpExporter(config, true, filter);
                });
            }).Build();
            TestActivityOnHost(host, filter);
        }

        [Fact]
        public void AddGenevaExporter_linux_AddBatchProcessor()
        {
            var filter = new ProcessorTestFilter();
            var host = new HostBuilder().ConfigureServices((context, service) =>
            {
                service.AddOpenTelemetry().WithTracing(builder =>
                {
                    Action<GenevaTraceExporterOptions> config = option => 
                    { 
                        option.ConnectionString = "EtwSession=OpenTelemetry"; 
                        option.ExportInterval = TimeSpan.FromSeconds(1); 
                    };
                    service.Configure(config);
                    service.TryAddGenevaTraceExporter(false, filter);
                    builder.AddSource(source)
                           .SetResourceBuilder(ResourceBuilder
                            .CreateDefault()
                            .AddService(serviceName: "ODLTestService", serviceVersion: "1.0.0"));
                });
            }).Build();
            TestActivityOnHost(host, filter: filter);
        }

        private static async Task RunHostAndActivityAsync(IHost host, List<KeyValuePair<string, bool>> activities)
        {
            await host.StartAsync().ConfigureAwait(true);
            foreach (var kvp in activities)
            {
                CreateActivity(kvp.Key, kvp.Value);
            }
            await host.StopAsync().ConfigureAwait(true);
        }

        private static void CreateActivity(string operationName, bool isSuccess)
        {
            var link = new ActivityLink(new ActivityContext(ActivityTraceId.CreateRandom(), ActivitySpanId.CreateRandom(), ActivityTraceFlags.Recorded));
            using var activity = activitySource.StartActivity(operationName, ActivityKind.Internal, null, null, new ActivityLink[] { link });
            activity?.SetTag("foo", 1);
            activity?.SetTag("bar", "Hello, World!");
            activity?.SetTag("baz", new int[] { 1, 2, 3 });
            activity?.SetStatus(ActivityStatusCode.Ok);
            activity?.AddTag("expectedResult", isSuccess ? "success" : "fail");
        }

        private void TestActivityOnHost(IHost host, ProcessorTestFilter filter = null, TestSampler sampler = null)
        {
            Dictionary<string, bool> activities = new Dictionary<string, bool>
            {
                { "test1", true },
                { "test12", true },
                { "fail", false }
            };

            RunHostAndActivityAsync(host, activities.ToList()).ConfigureAwait(false);
            Thread.Sleep(1000);
            if (filter != null)
            {
                Assert.Equal(2, filter.SampleCount);
            }
            if (sampler != null)
            {
                Assert.Equal(2, sampler.SampleCount);
            }
        }

        internal class TestSampler : Sampler
        {
            private long sampleCount;

            internal long SampleCount => sampleCount;

            public override SamplingResult ShouldSample(in SamplingParameters samplingParameters)
            {
                if (samplingParameters.Name.Equals("fail"))
                {
                    return new SamplingResult(SamplingDecision.Drop);
                }
                else
                {
                    Interlocked.Increment(ref sampleCount);
                    return new SamplingResult(SamplingDecision.RecordAndSample);
                }
            }
        }

        internal class ProcessorTestFilter : BaseFilter<Activity>
        {
            private long sampleCount;

            internal long SampleCount => sampleCount;

            public override string GetDescription()
            {
                return "Filter for Test.";
            }

            public override bool ShouldFilter(Activity t)
            {
                if (!"fail".Equals(t.OperationName))
                {
                    Interlocked.Increment(ref sampleCount);
                    return true;
                }
                return false;
            }
        }
    }
}
