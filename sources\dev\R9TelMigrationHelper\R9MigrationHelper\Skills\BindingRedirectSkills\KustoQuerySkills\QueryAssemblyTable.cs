﻿// <copyright file="QueryAssemblyTable.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Kusto.Data;
using R9MigrationHelper.Model;
using R9MigrationHelper.Skills.KustoQuerySkills;

namespace R9MigrationHelper.Skills.BindingRedirectSkills.KustoQuerySkills
{
    /// <summary>
    /// QueryAssemblyTable.
    /// </summary>
    public class QueryAssemblyTable
    {
        private const string KustoCluster = "https://resourcemanagement.westus2.kusto.windows.net";
        private const string DatabaseName = "adhoc";
        private string connectionString = string.Empty;

        /// <summary>
        /// QueryAssemblyTable.
        /// </summary>
        public QueryAssemblyTable()
        {
            this.connectionString = new KustoConnectionStringBuilder(KustoCluster, DatabaseName)
            .WithAadUserPromptAuthentication("72f988bf-86f1-41af-91ab-2d7cd011db47")
            .ConnectionString;
        }

        /// <summary>
        /// GetAllAssemblyList.
        /// </summary>
        public async Task<List<AssemblyModel>> GetAllAssemblyList()
        {
            List<AssemblyModel> assemblyList = new List<AssemblyModel>();

            //            string query = @"cluster('resourcemanagement.westus2.kusto.windows.net').database('adhoc').BindingRedirectAssembly
            //| where not(SourcePath startswith ""lib\\netcoreapp"")
            //| join kind=rightouter PackageVersionConflict on $left.PackageName == $right.DependencyName
            //| join kind=leftouter AssemblyPublicKeyToken on AssemblyName
            //| join kind=leftouter 
            //(cluster('azscperf.westus.kusto.windows.net').database('dependency').PackageAssembly
            //| where LibraryDirectoryPath startswith ""lib"" and not(LibraryDirectoryPath endswith "".dll"")) on ($left.DependencyName == $right.Name and $left.MinimalVersion == $right.Version and $left.AssemblyName == $right.AssemblyName)
            //| distinct PackageName, AssemblyName, PublicKeyToken, MinimalVersion, NewVersion = AssemblyVersion, OldVersion = AssemblyVersion1";
            //string query = @"cluster('resourcemanagement.westus2.kusto.windows.net').database('adhoc').BindingRedirectAssembly
            //| where not(SourcePath startswith ""lib\\netcoreapp"")
            //| join kind=leftouter cluster('resourcemanagement.westus2.kusto.windows.net').database('adhoc').AssemblyPublicKeyToken on AssemblyName 
            //| distinct PackageName, AssemblyName, PublicKeyToken";
            string query = @"cluster('resourcemanagement.westus2.kusto.windows.net').database('adhoc').AssemblyPublicKeyToken";

            QueryKustoTableSkill queryKustoTableSkill = new QueryKustoTableSkill();

            using (IDataReader reader = await queryKustoTableSkill.GetKustoDataAsync(query))
            {
                while (reader.Read())
                {
                    //assemblyList.Add(new AssemblyModel { PackageName = reader.GetString(0), Name = reader.GetString(1), PublicKeyToken = reader.GetString(2), Version = reader.GetString(4), OldVersion = reader.GetString(5) });
                    assemblyList.Add(new AssemblyModel { Name = reader.GetString(0), PublicKeyToken = reader.GetString(1), Version = reader.GetString(2) });
                }
            }
            return assemblyList;
        }

        /// <summary>
        /// GetAssemblyList.
        /// </summary>
        /// <param name="packageList">package List.</param>
        public async Task<List<AssemblyModel>> GetAssemblyList(HashSet<string> packageList)
        {
            if (packageList == null || packageList.Count == 0)
            {
                return new List<AssemblyModel>();
            }
            List<AssemblyModel> assemblyList = await GetAllAssemblyList();
            List<AssemblyModel> updatedAssemblyList = new List<AssemblyModel>();
            foreach (AssemblyModel assembly in assemblyList)
            {
                if (packageList.Contains(assembly.PackageName))
                {
                    updatedAssemblyList.Add(assembly);
                }
            }
            return updatedAssemblyList;
        }
    }
}
