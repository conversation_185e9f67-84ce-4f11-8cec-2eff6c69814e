// <copyright file="CompositeLogRecordExporterExtension.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.M365.Core.Telemetry.Exporters.ODLTCP.Log;
using Microsoft.R9.Extensions.Logging.Exporters;
using OpenTelemetry;
using OpenTelemetry.Logs;

namespace Microsoft.M365.Core.Telemetry.R9.Logging.Substrate
{
    /// <summary>
    /// Provides extension methods for configuring Substrate log record Exporters.
    /// </summary>
    public static class CompositeLogRecordExporterExtension
    {
        /// <summary>
        /// Configures the Substrate log record Exporters using the specified logging builder and service configuration.
        /// </summary>
        /// <param name="loggingBuilder">The logging builder to configure.</param>
        /// <param name="serviceConfiguration">The service configuration to bind options from.</param>
        /// <returns>The configured logging builder.</returns>
        internal static ILoggingBuilder ConfigureCompositeExporter(this ILoggingBuilder loggingBuilder, IConfiguration serviceConfiguration)
        {
            loggingBuilder.Services
                .AddOptions<GenevaLogExporterOptions>()
                .Bind(serviceConfiguration.GetSection($"Geneva"));
            loggingBuilder.Services
                .AddOptions<ODLTcpLogExporterOptions>()
                .Bind(serviceConfiguration.GetSection("OdlTcp"));
            loggingBuilder.Services
                .AddOptions<CompositeLogRecordExporterOptions>()
                .Bind(serviceConfiguration);
            loggingBuilder.Services.AddOptionsWithValidateOnStart<CompositeLogRecordExporterOptions, CompositeLogRecordExporterOptionsValidator>();

            loggingBuilder.Services.AddSingleton<BaseProcessor<LogRecord>, CompositeLogRecordExportProcessor>();
            return loggingBuilder;
        }
    }
}
