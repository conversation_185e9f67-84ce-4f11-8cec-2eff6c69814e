﻿// <copyright file="ModifyXMLSkill.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System;
using System.Text;
using System.Text.Json;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using System.Xml;
using System.Xml.Linq;
using Kusto.Cloud.Platform.Utils;
using Microsoft.VisualStudio.Services.Commerce;
using R9MigrationHelper.Model;
using R9MigrationHelper.Skills.KustoQuerySkills;

namespace R9MigrationHelper.Skills.StringParseSkills
{
    /// <summary>
    /// ModifyXMLSkill.
    /// </summary>
    public class ModifyXMLSkill
    {
        /// <summary>
        /// ModifyXML method.
        /// </summary>
        /// <param name="xmlDropConfig">xmlDropConfig.</param>
        /// <param name="dllList">dllList.</param>
        /// <param name="destinationPath">destinationPath.</param>
        public string ModifyXML(string xmlDropConfig, string dllList, string destinationPath)
        {
            if (string.IsNullOrEmpty(xmlDropConfig) || string.IsNullOrEmpty(dllList))
            {
                return xmlDropConfig;
            }
            StringBuilder addedFileNupkg = new StringBuilder();
            string[] dlls = dllList.Split(';', StringSplitOptions.RemoveEmptyEntries);

            foreach (string dll in dlls)
            {
                string[] dllInfo = dll.Split(",");
                string dllName = dllInfo[0]; // ex. Microsoft.R9.Extensions.Metering.Geneva.dll
                string packageName = PackageNameConverter(dllInfo[1]); // ex. Microsoft.R9.Extensions.Metering.Geneva
                string sourcePath = dllInfo[2]; // ex. lib\net461
                addedFileNupkg.Append(System.Globalization.CultureInfo.InvariantCulture, $"  <FILENUPKG filename=\"{dllName}\" packagename=\"{packageName}\" ownername=\"SOTELS\" owneralias=\"sotelsdput-dev\">\r\n    <SOURCE>{sourcePath}</SOURCE>\r\n    <DELIVERABLES_SET>\r\n      <DATACENTER>\r\n        <DESTINATIONS>\r\n          <DEST>{destinationPath}</DEST>\r\n        </DESTINATIONS>\r\n      </DATACENTER>\r\n      <COMPONENTS>\r\n        <DESTINATIONS>\r\n          <DEST>{destinationPath}</DEST>\r\n        </DESTINATIONS>\r\n      </COMPONENTS>\r\n    </DELIVERABLES_SET>\r\n  </FILENUPKG>");
            }
            addedFileNupkg.ToString();
#pragma warning disable CA1307 // Dereference of a possibly null reference.
            string updatedXMLDropConfig = xmlDropConfig.Substring(0, xmlDropConfig.LastIndexOf("</ROOT>")) + addedFileNupkg.ToString() + "</ROOT>";
#pragma warning restore CA1307 // Dereference of a possibly null reference.
            return updatedXMLDropConfig;
        }

        /// <summary>
        /// ModifyXML method.
        /// </summary>
        /// <param name="xmlDropConfig">xmlDropConfig.</param>
        /// <param name="dllList">dllList.</param>
        public string ModifyXML(string xmlDropConfig, List<AssemblyModel> dllList)
        {
            if (string.IsNullOrEmpty(xmlDropConfig) || dllList.Count == 0)
            {
                Console.WriteLine("Empty xmlDropConfig or dllList!");
                return xmlDropConfig;
            }

            int total = dllList.Count;
            int count = 0;

            XmlDocument xmlDropDocument = new XmlDocument();
            xmlDropDocument.LoadXml(xmlDropConfig);
            XmlNode? root = xmlDropDocument.SelectSingleNode("ROOT");
            Dictionary<string, XmlNode> nodeDict = new Dictionary<string, XmlNode>();

            if (root == null)
            {
                Console.WriteLine("No Root Element in xmlDropConfig!");
                return xmlDropConfig;
            }

            foreach (XmlNode node in root.ChildNodes)
            {
                if (node.Name == "FILENUPKG")
                {
                    nodeDict.Add(node.Attributes!["filename"] !.Value, node);
                }
            }

            foreach (AssemblyModel dll in dllList)
            {
                string dllName = dll.Name; // ex. Microsoft.R9.Extensions.Metering.Geneva.dll
                string packageName = PackageNameConverter(dll.PackageName); // ex. Microsoft.R9.Extensions.Metering.Geneva
                string sourcePath = dll.SourcePath; // ex. lib\net461
                if (packageName.IsNullOrEmpty() || sourcePath.IsNullOrEmpty()) { continue; }
                
                foreach (XmlFullDestination fullDestination in dll.DropDestinations)
                {
                    XmlNode destNode = xmlDropDocument.CreateElement("DEST");
                    destNode.InnerText = fullDestination.Destination;

                    if (nodeDict.ContainsKey(dllName))
                    {
                        XmlNode nupkg = nodeDict[dllName];
                        bool foundRoot = false;
                        foreach (XmlNode drop in nupkg.SelectSingleNode("DELIVERABLES_SET")!.ChildNodes)
                        {
                            if (drop.Name == fullDestination.Root)
                            {
                                drop.SelectSingleNode("DESTINATIONS")!.AppendChild(destNode);
                                foundRoot = true;
                                break;
                            }
                        }
                        if (!foundRoot)
                        {
                            XmlNode drop = xmlDropDocument.CreateElement(fullDestination.Root);
                            XmlNode destinations = xmlDropDocument.CreateElement("DESTINATIONS");
                            destinations.AppendChild(destNode);
                            drop.AppendChild(destinations);
                            nupkg.SelectSingleNode("DELIVERABLES_SET")!.AppendChild(drop);
                        }
                    }
                    else
                    {
                        XmlNode fileNupkg = xmlDropDocument.CreateElement("FILENUPKG");
                        XmlAttribute filename = xmlDropDocument.CreateAttribute("filename");
                        filename.Value = dllName;
                        XmlAttribute packagename = xmlDropDocument.CreateAttribute("packagename");
                        packagename.Value = packageName;
                        XmlAttribute ownername = xmlDropDocument.CreateAttribute("ownername");
                        ownername.Value = "SOTELS";
                        XmlAttribute owneralias = xmlDropDocument.CreateAttribute("owneralias");
                        owneralias.Value = "sotelsdput-dev";
                        fileNupkg.Attributes!.Append(filename);
                        fileNupkg.Attributes!.Append(packagename);
                        fileNupkg.Attributes!.Append(ownername);
                        fileNupkg.Attributes!.Append(owneralias);

                        XmlNode source = xmlDropDocument.CreateElement("SOURCE");
                        source.InnerText = sourcePath;

                        XmlNode deliverablesSet = xmlDropDocument.CreateElement("DELIVERABLES_SET");
                        XmlNode rootElement = xmlDropDocument.CreateElement(fullDestination.Root);
                        XmlNode destinations = xmlDropDocument.CreateElement("DESTINATIONS");
                        destinations.AppendChild(destNode);
                        rootElement.AppendChild(destinations);
                        deliverablesSet.AppendChild(rootElement);

                        fileNupkg.AppendChild(source);
                        fileNupkg.AppendChild(deliverablesSet);

                        root.AppendChild(fileNupkg);
                        nodeDict.Add(dllName, fileNupkg);
                    }
                    
                    Console.WriteLine($"{count}/{total} Added {dllName} to {fullDestination.Root}/{fullDestination.Destination}");
                }
                count++;
            }

            XDeclaration? xDeclaration = XDocument.Parse(xmlDropConfig).Declaration;
            string declaration = string.Empty;
            if (xDeclaration != null)
            {
                declaration = xDeclaration!.ToString() + "\n";
            }

            return declaration + XElement.Parse(xmlDropDocument.OuterXml).ToString();
        }

        /// <summary>
        /// packageNameConverter.
        /// </summary>
        /// <param name="packageName">packageName.</param>
        public string PackageNameConverter(string packageName)
        {
            Regex macroEscape = new Regex("[^a-z0-9_]", RegexOptions.IgnoreCase);
            return "Pkg" + macroEscape.Replace(packageName, "_");
        }
    }
}
