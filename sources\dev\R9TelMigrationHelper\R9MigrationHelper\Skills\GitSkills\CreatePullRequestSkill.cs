﻿// <copyright file="CreatePullRequestSkill.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System;
using System.Diagnostics;
using System.Diagnostics.CodeAnalysis;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Web;
using Microsoft.Azure.Pipelines.WebApi;
using Microsoft.Identity.Client;
using Microsoft.TeamFoundation.SourceControl.WebApi;
using Microsoft.VisualStudio.Services.Client;
using Microsoft.VisualStudio.Services.Common;
using Microsoft.VisualStudio.Services.Organization.Client;
using Microsoft.VisualStudio.Services.WebApi;
using R9MigrationHelper.Model;

namespace R9MigrationHelper.Skills.GitSkills
{
    /// <summary>
    /// CreatePullRequestSkill.
    /// </summary>
    [ExcludeFromCodeCoverage]
    public class CreatePullRequestSkill
    {
        /// <summary>
        /// Create Pull Request and return the pull request url.
        /// </summary>
        /// <param name="organization">SK Context.</param>
        /// <param name="project">SK Context.</param>
        /// <param name="repository">SK Context.</param>
        /// <param name="changeList">SK Context.</param>
        /// <param name="branchName">SK Context.</param>
        /// <param name="title">SK Context.</param>
        /// <param name="description">SK Context.</param>
        /// <param name="comments">SK Context.</param>
        /// <param name="isDraft">Is pull request need to be draft</param>
        public async Task<string> CreatePullRequestAsync(string organization, string project, string repository, List<FileChange> changeList, string branchName, string title, string description, string comments, bool isDraft = false)
        {
            string organizationUrl = $"https://dev.azure.com/{HttpUtility.UrlEncode(organization)}";

            string[] scopes = new string[] { "499b84ac-1321-427f-aa17-267ca6975798/user_impersonation" }; //Constant value to target Azure DevOps. Do not change  

            // Initialize the MSAL library by building a public client application
            IPublicClientApplication application = PublicClientApplicationBuilder.Create("872cd9fa-d31f-45e0-9eab-6e460a02d1f1")
                                       .WithAuthority("https://login.microsoftonline.com/72f988bf-86f1-41af-91ab-2d7cd011db47/v2.0")
                                       .WithDefaultRedirectUri()
                                       .Build();
            AuthenticationResult authResult;

            try
            {
                var accounts = await application.GetAccountsAsync().ConfigureAwait(false);
                authResult = await application.AcquireTokenSilent(scopes, accounts.FirstOrDefault())
                        .ExecuteAsync().ConfigureAwait(false);
            }
            catch (MsalUiRequiredException)
            {
                authResult = await application.AcquireTokenByIntegratedWindowsAuth(scopes).ExecuteAsync().ConfigureAwait(false);
            }

            Console.WriteLine($"organization: {organization}, project: {project}, repository: {repository}, pat:******, organizationUrl: {organizationUrl}.");

            VssCredentials creds = new VssAadCredential(new VssAadToken("Bearer", authResult.AccessToken));
            VssConnection connection = new VssConnection(new Uri(organizationUrl), creds);
            GitHttpClient gitClient = connection.GetClient<GitHttpClient>();
            GitRepository repo = await gitClient.GetRepositoryAsync(project, repository).ConfigureAwait(false);
            Guid repoId = repo.Id;
            var allRefs = await gitClient.GetRefsAsync(repoId).ConfigureAwait(false);
            var masterRef = allRefs.First(x => x.Name == repo.DefaultBranch);
            Console.WriteLine($"masterRef.Name = {masterRef.Name}\n");
            string newBranchName = branchName;
            Console.WriteLine("Creating new git branch...");
            var updatedRefs = await gitClient.UpdateRefsAsync(
                 new[]
                 {
                    new GitRefUpdate
                    {
                        Name = newBranchName,
                        NewObjectId = masterRef.ObjectId,
                        OldObjectId = new string('0', 40)
                    }
                 }, repoId).ConfigureAwait(false);
            Console.WriteLine($"Successfully created new branch: {newBranchName}");
            var newBranch = updatedRefs[0];

            List<GitChange> changes = new List<GitChange>();
            List<FileChange> remainChanges = new List<FileChange>();
            int charCount = 0;
            foreach (FileChange change in changeList)
            {
                if ((charCount + change.UpdatedFileContent.Length) < 2621440) // Limit of pr max bytes: 26214400 
                {
                    changes.Add(new GitChange
                    {
                        ChangeType = VersionControlChangeType.Edit,
                        Item = new GitItem
                        {
                            Path = change.FilePath,
                        },
                        NewContent = new ItemContent
                        {
                            Content = change.UpdatedFileContent,
                            ContentType = ItemContentType.RawText
                        }
                    });
                    charCount += change.UpdatedFileContent.Length;
                }
                else
                {
                    remainChanges.Add(change);
                }
            }

            GitPush push = new GitPush
            {
                RefUpdates = new[]
                {
                    new GitRefUpdate { Name = newBranchName, OldObjectId = newBranch.NewObjectId }
                },
                Commits = new[]
                {
                    new GitCommitRef
                    {
                        Comment = comments,
                        Changes = changes
                    }
                }
            };
            Console.WriteLine("Pushing change...");
            await gitClient.CreatePushAsync(push, repoId).ConfigureAwait(false);
            Console.WriteLine("Done");

            GitPullRequest pullRequest = new GitPullRequest
            {
                Title = title,
                Description = description,
                SourceRefName = newBranchName,
                TargetRefName = repo.DefaultBranch,
                IsDraft = isDraft
            };
            Console.WriteLine("Creating pull request...");
            var result = await gitClient.CreatePullRequestAsync(pullRequest, repoId).ConfigureAwait(false);
            string pullRequestUrl = $"{result.Repository.WebUrl}/pullrequest/{result.PullRequestId}";
            Process.Start(new ProcessStartInfo(pullRequestUrl) { UseShellExecute = true });

            if (remainChanges.Count > 0)
            {
                PushToPRSkill createPullRequestToExistingSkill = new PushToPRSkill();
                await createPullRequestToExistingSkill.PushToPRAsync(organization, project, repository, remainChanges, comments, pullRequestUrl).ConfigureAwait(true);
            }

            return pullRequestUrl;
        }

        /// <summary>
        /// Create Pull Request and return the pull request url.
        /// </summary>
        /// <param name="organization">SK Context.</param>
        /// <param name="project">SK Context.</param>
        /// <param name="repository">SK Context.</param>
        /// <param name="updatedContent">SK Context.</param>
        /// <param name="updateFilePath">SK Context.</param>
        /// <param name="branchName">SK Context.</param>
        /// <param name="title">SK Context.</param>
        /// <param name="description">SK Context.</param>
        /// <param name="commments">SK Context.</param>
        /// <param name="isDraft">Is pull request need to be draft</param>
        public async Task<string> CreatePullRequestAsync(string organization, string project, string repository, string updatedContent, string updateFilePath, string branchName, string title, string description, string commments, bool isDraft = false)
        {
            if (string.IsNullOrEmpty(updatedContent))
            {
                throw new ArgumentException("updatedContent cannot be empty!");
            }

            FileChange change = new FileChange(updatedContent, updateFilePath);
            return await CreatePullRequestAsync(organization, project, repository, new List<FileChange> { change }, branchName, title, description, commments, isDraft).ConfigureAwait(true);
        }
    }
}
