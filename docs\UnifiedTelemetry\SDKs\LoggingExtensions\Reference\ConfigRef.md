
# Configuration

The package control the telemetry behavior with configurations.
It loads specific sections of an IConfiguration (which should be input when calling the method) and takes essential settings. A top-level seciton `SubstrateLogging` is introduced to store our settings.

Currently we support 2 configuration source: local file (`appsettings.json`) and ECS.

- Appsettings.json
A local config file. Can be used to store unchanged options.

- ECS
Some of our compoents support hot-reload, which means some behaviors may be changed with config update in runtime.
We choose ECS to store and manage such sections, with some fall-back policies in case it fails to load config from ECS.

## Connection to ECS
[!include[](../../../include/ECSConfig.md)]

## Set Exporter

### Use Geneva Exporter

In Extension, we set Geneva Exporter as default action to align with legacy PassiveMonitoring SDK for smooth migraiton.

We recommend to use Composite Exporter, which is able to switch exporter in runtime (currently support Geneva and OdlTcp, and will support more in the future).

[!include[](../../../include/GenevaExporterConfig.md)]

### (Recommended) Use Composite Exporter

[!include[](../../../include/CompositeExporterConfig.md)]


## Customize R9 Logging
[!include[Core Config](../../../include/R9LoggingConfig.md)]

## Full Example

[!code-json[](../../../include/full_config.json)]