﻿// <copyright file="HttpClientTracingOptionsInherited.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

namespace Microsoft.M365.Core.Telemetry.R9.Tracing.Instrumentation.Accelerator
{
    using Microsoft.R9.Extensions.HttpClient.Tracing;

    /// <summary>
    /// HttpClientTracingOptionsInherited
    /// </summary>
    public class HttpClientTracingOptionsInherited : HttpClientTracingOptions
    {
        /// <summary>
        /// IsEnabled
        /// </summary>
        public bool IsEnabled { get; set; } = true;

        /// <summary>
        /// RedactionStrategyType
        /// </summary>
        public RedactionStrategyType RedactionStrategyType { get; set; } = RedactionStrategyType.Default;
    }
}