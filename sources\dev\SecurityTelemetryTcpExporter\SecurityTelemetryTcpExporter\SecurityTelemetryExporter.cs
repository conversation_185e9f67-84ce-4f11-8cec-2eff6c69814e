﻿// <copyright file="SecurityTelemetryExporter.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Diagnostics.CodeAnalysis;
using System.Linq;
using System.Threading;
using Microsoft.Extensions.ObjectPool;
using Microsoft.Extensions.Options;
using Microsoft.M365.ODL.NrtTcpClient;
using Microsoft.Office.BigData.DataLoader;
using Microsoft.R9.Extensions.SecurityTelemetry;
using Newtonsoft.Json;
using static System.Net.Mime.MediaTypeNames;

namespace Microsoft.M365.Core.Telemetry.ODL.SecurityTelemetryTcpExporter
{
    /// <summary>
    /// The ODL TcpExporter for SecurityTelemetry
    /// </summary>
    [ExcludeFromCodeCoverage]
    public sealed class SecurityTelemetryExporter : BatchExporter
    {
        private static long seq = 0;

        private Dictionary<string, IReadOnlyDictionary<string, object>> schemaToStaticProperties = new Dictionary<string, IReadOnlyDictionary<string, object>>();

        private object mutex = new object();

        private readonly SecurityTelemetryOptions securityTelemetryOptions;

        private SecurityTelemetryTcpExporterOptions securityTelemetryTcpExporterOptions;

        private OdlNrtTcpClient tcpClient;

        private int recordNumPerRequest = 30;

        /// <summary>
        /// 9527 as the default tcp port in Substrate
        /// </summary>
        private int tcpPort = 9527;

        private List<KeyValuePair<string, string>> lines;

        private Dictionary<string, string> schemaToError = new Dictionary<string, string>();

        private IOdlLogger logger = OdlLogger.Instance;

        private Dictionary<string, List<string>> schemaToMessages = new Dictionary<string, List<string>>();

        private ObjectPool<ODLNRTRequest> nrtRequestPool;

        private ObjectPool<ODLNRTMessage> nrtMessagePool;

        private TlsOptions tlsOptions;

        private const string ApplicationName = "ApplicationName";

        private const string ApplicationId = "ApplicationId";

        private const string Ring = "Ring";

        private const string SubscriptionId = "SubscriptionId";

        private ConcurrentDictionary<string, SecurityTelemetryHeartbeat> heartbeatInfo = new ConcurrentDictionary<string, SecurityTelemetryHeartbeat>();

        // Heartbeat timer
        private Timer heartbeatTimer;

        /// <summary>
        /// The ctor
        /// </summary>
        /// <param name="securityTelemetryOptions">the options of SecurityTelemetry</param>
        /// <param name="securityTelemetryTcpExporterOptions">the options of SecurityTelemetryTcpExporter</param>
        public SecurityTelemetryExporter(IOptions<SecurityTelemetryOptions> securityTelemetryOptions, IOptions<SecurityTelemetryTcpExporterOptions> securityTelemetryTcpExporterOptions)
            : base(securityTelemetryTcpExporterOptions.Value.MaxQueueSize, securityTelemetryTcpExporterOptions.Value.ScheduledDelayMilliseconds, securityTelemetryTcpExporterOptions.Value.ExporterTimeoutMilliseconds, securityTelemetryTcpExporterOptions.Value.MaxExportBatchSize)
        {
            this.securityTelemetryOptions = securityTelemetryOptions.Value;
            this.securityTelemetryTcpExporterOptions = securityTelemetryTcpExporterOptions.Value;
            this.recordNumPerRequest = this.securityTelemetryTcpExporterOptions.RecordCountPerRequest;
            OdlLogger.UseAdhocLogger = this.securityTelemetryTcpExporterOptions.UseAdhocLogger;

            this.lines = new List<KeyValuePair<string, string>>(this.recordNumPerRequest);
            this.nrtRequestPool = ODLNRTRequestPoolFactory.CreateODLNRTRequestPool(1);
            this.nrtMessagePool = ODLNRTRequestPoolFactory.CreateODLNRTMessagePool(recordNumPerRequest);

            this.SetupTcpPort();
            this.SetupTcpClient(securityTelemetryTcpExporterOptions.Value.EnableCleanUpArchivedFiles, securityTelemetryTcpExporterOptions.Value.RetentionInterval, securityTelemetryTcpExporterOptions.Value.RetentionCount);

            // Initialize the heartbeat timer
            var heartbeatInterval = securityTelemetryTcpExporterOptions.Value.HeartbeatIntervalMilliseconds;
            this.heartbeatTimer = new Timer(this.SendHeartbeat, null, TimeSpan.Zero, heartbeatInterval);
        }

        private void SetupTcpClient(bool enableCleanUpArchivedFiles, TimeSpan retentionInterval, long retentionCount)
        {
            this.tlsOptions = GenerateTlsOptionsDic();
            this.tcpClient = new OdlNrtTcpClient(this.tcpPort, this.securityTelemetryTcpExporterOptions.SendTimeout, this.securityTelemetryTcpExporterOptions.ConnectTimeout, this.securityTelemetryTcpExporterOptions.ConnectionHealthCheckInterval, this.securityTelemetryTcpExporterOptions.ServerIP, tlsOptions);
            this.tcpClient.SetBufferSize(this.securityTelemetryTcpExporterOptions.BufferSize);
            this.tcpClient.SetBatchSizeForSendingMetric(this.securityTelemetryTcpExporterOptions.BatchSizeForSendingMetric);
            this.tcpClient.SetSendStatisticsInterval(this.securityTelemetryTcpExporterOptions.SendMetricInterval);
            this.tcpClient.SetEnableCleanUpArchivedFiles(enableCleanUpArchivedFiles);
            this.tcpClient.SetRetentionInterval(retentionInterval);
            this.tcpClient.SetRetentionCount(retentionCount);
        }

        private TlsOptions GenerateTlsOptionsDic()
        {
            return new TlsOptions
            {
                IsCosmic = this.securityTelemetryTcpExporterOptions.IsCosmic,
                EnableTlsAuth = this.securityTelemetryTcpExporterOptions.EnableTls,
                TlsCertsFolderPath = this.securityTelemetryTcpExporterOptions.TlsCertsFolderPath,
                TlsPfxPassword = this.securityTelemetryTcpExporterOptions.TlsPfxPassword,
                TlsCertFilePath = this.securityTelemetryTcpExporterOptions.TlsCertFilePath,
                TlsCACertFilePath = this.securityTelemetryTcpExporterOptions.TlsCACertFilePath,
                TlsCertSubjectName = this.securityTelemetryTcpExporterOptions.TlsCertSubjectName
            };
        }

        private void SetupTcpPort()
        {
            if (this.securityTelemetryTcpExporterOptions.IsCosmic)
            {
                this.tcpPort = this.securityTelemetryTcpExporterOptions.TCPPort;
                return;
            }

            try
            {
                int tmpPort = RegistryReader.GetTcpPort();
                if (tmpPort != RegistryReader.InvalidPort)
                {
                    this.tcpPort = tmpPort;
                }
            }
            catch (Exception ex)
            {
                logger.Log(LogLevel.Error, LogEventId.TcpExporterError, $"Fail to read Tcp port from registry, will try to connect through default port. Error is :{ex}");
            }
        }

        public override void ExportBatch(in Batch<SecurityRecord> batch)
        {
            try
            {
                this.DoBatchExport(batch);
            }
            catch (Exception ex)
            {
                logger.Log(LogLevel.Error, LogEventId.TcpExporterError, $"Fail to perform batch exporting for {batch.Count} records, error is :{ex}");
            }
        }

        private void DoBatchExport(in Batch<SecurityRecord> batch)
        {
            int i = 0, schemaErrorCnt = 0;

            string logline;

            foreach (var record in batch)
            {
                string applicationName = string.Empty;
                string applicationId = string.Empty;
                string ring = string.Empty;
                string subscriptionId = string.Empty;

                try
                {
                    var staticProperties = this.GetAndAddStaticProperties(record);
                    applicationName = staticProperties.TryGetValue(ApplicationName, out object? value) ? value.ToString() : applicationName;
                    applicationId = staticProperties.TryGetValue(ApplicationId, out object? appIDValue) ? appIDValue?.ToString() ?? string.Empty : string.Empty;
                    ring = staticProperties.TryGetValue(Ring, out object? ringValue) ? ringValue?.ToString() ?? string.Empty : string.Empty;
                    subscriptionId = staticProperties.TryGetValue(SubscriptionId, out object? subscriptionIdValue) ? subscriptionIdValue?.ToString() ?? string.Empty : string.Empty;

                    if (!string.IsNullOrEmpty(applicationName))
                    {
                        var heartbeatDetail = new SecurityTelemetryHeartbeat
                        {
                            ApplicationName = applicationName,
                            ApplicationId = applicationId,
                            Ring = ring,
                            SubscriptionId = subscriptionId
                        };

                        this.heartbeatInfo.AddOrUpdate(
                            applicationName,
                            heartbeatDetail,
                            (key, existingValue) =>
                            {
                                existingValue.ApplicationId = applicationId;
                                existingValue.Ring = ring;
                                existingValue.SubscriptionId = subscriptionId;
                                return existingValue;
                            });
                    }

                    logline = this.securityTelemetryTcpExporterOptions.SecurityRecordFormatter(record, staticProperties);
                }
                catch (Exception ex)
                {
                    this.schemaToError[string.IsNullOrEmpty(applicationName) ? record.SchemaName : applicationName] = ex.Message;
                    ++schemaErrorCnt;
                    continue;
                }

                this.lines.Add(new KeyValuePair<string, string>(string.IsNullOrEmpty(applicationName) ? record.SchemaName : applicationName, logline));
                if ((++i) % recordNumPerRequest == 0)
                {
                    this.CreateRequestAndSend();
                }
            }

            if (this.lines.Any())
            {
                this.CreateRequestAndSend();
            }

            if (this.schemaToError.Any())
            {
                logger.Log(LogLevel.Error, LogEventId.TcpExporterError, $"There are {schemaErrorCnt} records ignored due to errors while serilizing Security Records:{JsonConvert.SerializeObject(schemaToError)} ");
                this.schemaToError.Clear();
            }
        }

        /// <summary>
        /// Encapuselate the batch of log lines into a NRTRquest and send it to local ODL Tcp Server in sync mode
        /// </summary>
        public void CreateRequestAndSend()
        {
            long now = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds();
            ODLNRTRequest oDLNRTRequest = this.nrtRequestPool.Get();

            try
            {
                oDLNRTRequest.Head.Timestamp = now;
                oDLNRTRequest.Head.Sequence = Interlocked.Increment(ref seq);
                oDLNRTRequest.SecurityMessageBatchReq.Head.Timestamp = now;

                foreach (var line in this.lines)
                {
                    var message = this.nrtMessagePool.Get();
                    oDLNRTRequest.SecurityMessageBatchReq.Messages.Add(message);
                    message.Message = line.Value;
                    message.ID = Guid.NewGuid().ToString();
                    message.EventID = 1;
                    message.Source = line.Key;
                }

                this.tcpClient.Send(oDLNRTRequest, this.schemaToMessages);
            }
            finally
            {
                this.lines.Clear();
                foreach (var message in oDLNRTRequest.SecurityMessageBatchReq.Messages)
                {
                    this.nrtMessagePool.Return(message);
                }

                this.nrtRequestPool.Return(oDLNRTRequest);
            }
        }

        /// <summary>
        /// Get the static properties of the given SecurityRecrod
        /// </summary>
        /// <param name="securityRecord">the input SecurityRecord</param>
        /// <returns>the readonly static properties</returns>
        public IReadOnlyDictionary<string, object> GetAndAddStaticProperties(SecurityRecord securityRecord)
        {
            var schemaName = securityRecord.SchemaName;

            if (!schemaToStaticProperties.TryGetValue(schemaName, out IReadOnlyDictionary<string, object>? result))
            {
                lock (mutex)
                {
                    if (!schemaToStaticProperties.TryGetValue(schemaName, out result))
                    {
                        schemaToStaticProperties[schemaName] = result = this.GetStaticProperties(securityRecord);
                    }
                }
            }

            return result;
        }

        /// <summary>
        /// Create a string for static property pairs.
        /// </summary>
        /// <param name="securityRecord"></param>
        /// <returns></returns>
        public IReadOnlyDictionary<string, object> GetStaticProperties(SecurityRecord securityRecord)
        {
            Dictionary<string, object> result = new Dictionary<string, object>();
            foreach (var key in securityRecord.StaticProperties)
            {
                if (this.securityTelemetryOptions.PrepopulatedFields.TryGetValue(key, out object? value) && value != null)
                {
                    result[key] = value;
                }
            }

            return result;
        }

        /// <summary>
        /// Send application security telemetry heartbeat
        /// </summary>
        private void SendHeartbeat(object? state)
        {
            try
            {
                var currentHeartbeatInfo = Interlocked.Exchange(ref this.heartbeatInfo, new ConcurrentDictionary<string, SecurityTelemetryHeartbeat>());

                foreach (var heartbeat in currentHeartbeatInfo)
                {
                    var applicationName = heartbeat.Key; 
                    var heartbeatDetail = heartbeat.Value;

                    long now = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds();
                    ODLNRTRequest oDLNRTRequest = this.nrtRequestPool.Get();

                    try
                    {
                        oDLNRTRequest.Head.Timestamp = now;
                        oDLNRTRequest.Head.Sequence = Interlocked.Increment(ref seq);
                        oDLNRTRequest.SecurityMessageBatchReq.Head.Timestamp = now;

                        var message = this.nrtMessagePool.Get();
                        oDLNRTRequest.SecurityMessageBatchReq.Messages.Add(message);

                        string heartbeatMessage = $"Heartbeat - ApplicationName: {heartbeatDetail.ApplicationName}, ApplicationId: {heartbeatDetail.ApplicationId}, DeploymentRing: {heartbeatDetail.Ring}, SubscriptionId: {heartbeatDetail.SubscriptionId}";
                        message.Message = heartbeatMessage;
                        message.ID = Guid.NewGuid().ToString();
                        message.EventID = 1;
                        message.Source = applicationName;

                        this.tcpClient.Send(oDLNRTRequest, this.schemaToMessages);
                    }
                    finally
                    {
                        foreach (var message in oDLNRTRequest.SecurityMessageBatchReq.Messages)
                        {
                            this.nrtMessagePool.Return(message);
                        }
                        this.nrtRequestPool.Return(oDLNRTRequest);
                    }
                }
            }
            catch (Exception ex)
            {
                logger.Log(LogLevel.Error, LogEventId.TcpExporterError, $"Fail to send heartbeat: {ex}");
            }
        }

        /// <summary>
        /// Dispose the exporter
        /// </summary>
        public void Dispose()
        {
            base.Dispose();
            lock (mutex)
            {
                this.tcpClient?.Dispose();
                this.tcpClient = null;

                this.schemaToStaticProperties = null;
                this.schemaToMessages = null;

                this.heartbeatInfo.Clear();
                this.heartbeatInfo = null;
            }
        }
    }
}
