﻿// <copyright file="ModifyBinPlaceSkill.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using System.Xml.Linq;

namespace R9MigrationHelper.Skills.StringParseSkills
{
    /// <summary>
    /// ModifyBinPlaceSkill.
    /// </summary>
    public class ModifyBinPlaceSkill
    {
        /// <summary>
        /// ModifyBinPlace method for SK.
        /// </summary>
        /// <param name="dllList">dllList.</param>
        /// <param name="repoBasePath">repoBasePath.</param>
        public string ModifyBinPlace(string dllList, string repoBasePath = "C:\\src\\Substrate")
        {
            string binPlaceForPackages = LoadBinPlace(repoBasePath);

            if (string.IsNullOrEmpty(binPlaceForPackages) || string.IsNullOrEmpty(dllList))
            {
                return binPlaceForPackages;
            }

            XElement xelementBinPlaceForPackages = XElement.Parse(binPlaceForPackages);
            StringBuilder addedBinPlaceNetFramework = new StringBuilder();
            StringBuilder addedBinPlaceNetCore = new StringBuilder();
            HashSet<string> addedPackages = new HashSet<string>();
            string[] dlls = dllList.Split(';', StringSplitOptions.RemoveEmptyEntries);
            foreach (string dll in dlls)
            {
                string[] dllInfo = dll.Split(",");
                string dLLName = dllInfo[0]; // ex. Microsoft.R9.Extensions.Metering.Geneva.dll
                string normalPackageName = dllInfo[1]; // ex. Microsoft.R9.Extensions.Metering.Geneva
                string sourcePath = dllInfo[2]; // ex. lib\net461
                string framework = 
                    sourcePath.Contains("net461", StringComparison.OrdinalIgnoreCase) 
                    || sourcePath.Contains("net462", StringComparison.OrdinalIgnoreCase) 
                    || sourcePath.Contains("net471", StringComparison.OrdinalIgnoreCase) 
                    || sourcePath.Contains("net472", StringComparison.OrdinalIgnoreCase) ? "NetFramework" : "NetCore";

                //string framework = dllInfo[3]; // ex. NetFramework
                //string packageName = PackageNameConverter(normalPackageName); // ex. PkgMicrosoft_R9_Extensions_Metering_Geneva
                string fullPath = dllInfo[3]; // ex. $(PkgMicrosoft_R9_Extensions_Logging)\lib\net462\Microsoft.R9.Extensions.Logging.dll
                if (framework == "NetCore")
                {
                    XElement? duplicate = FindXElementWithAttribute(xelementBinPlaceForPackages, "BinPlaceNetFramework", "Include", fullPath);
                    if (duplicate != null)
                    {
                        continue; // Keep the .NET Framework version
                    }
                    addedBinPlaceNetCore.Append(System.Globalization.CultureInfo.InvariantCulture, $"    <BinPlaceNetCore Include=\"{fullPath}\" />\r\n");
                }
                else
                {
                    XElement? duplicate = FindXElementWithAttribute(xelementBinPlaceForPackages, "BinPlaceNetCore", "Include", fullPath);
                    if (duplicate != null)
                    {
                        duplicate.Remove(); // Replace the .NET Framework version with .NET Core one
                        addedBinPlaceNetFramework.Append(System.Globalization.CultureInfo.InvariantCulture, $"    <BinPlaceNetFramework Include=\"{fullPath}\" />\r\n");
                        continue;
                    }
                    addedBinPlaceNetFramework.Append(System.Globalization.CultureInfo.InvariantCulture, $"    <BinPlaceNetFramework Include=\"{fullPath}\" />\r\n");
                }
                addedPackages.Add(normalPackageName);
            }
            StringBuilder addedPackageReference = new StringBuilder();
            foreach (string packageName in addedPackages)
            {
                addedPackageReference.Append(System.Globalization.CultureInfo.InvariantCulture, $"    <PackageReference Include=\"{packageName}\" GeneratePathProperty=\"true\" />\r\n");
            }

            string addedConfig = string.Empty;
            if (addedPackageReference.Length > 0)
            {
                addedConfig += "  <ItemGroup>\r\n" + addedPackageReference.ToString() + "  </ItemGroup>\r\n";
            }
            if (addedBinPlaceNetCore.Length > 0)
            {
                addedConfig += "  <ItemGroup>\r\n" + addedBinPlaceNetCore.ToString() + "  </ItemGroup>\r\n";
            }
            if (addedBinPlaceNetFramework.Length > 0)
            {
                addedConfig += "  <ItemGroup>\r\n" + addedBinPlaceNetFramework.ToString() + "  </ItemGroup>\r\n";
            }
#pragma warning disable CA1307 // Dereference of a possibly null reference.
            string updatedBinPlaceForPackages = binPlaceForPackages.Substring(0, length: binPlaceForPackages.LastIndexOf("</Project>")) + addedConfig + "</Project>";
#pragma warning restore CA1307 // Dereference of a possibly null reference.
            return updatedBinPlaceForPackages;
        }

        private XElement? FindXElementWithAttribute(XElement root, string elementName, string attributeName, string attributeValue)
        {
            if (root == null)
            {
                return null;
            }
            foreach (XElement el in root.Descendants(elementName))
            {
#pragma warning disable CS8602 // Dereference of a possibly null reference.
                if (el.Attribute(attributeName) != null && el.Attribute(attributeName).Value == attributeValue)
#pragma warning restore CS8602 // Dereference of a possibly null reference.
                {
                    return el;
                }
            }
            return null;
        }

        /// <summary>
        /// PackageNameConverter.
        /// </summary>
        /// <param name="packageName">packageName.</param>
        public string PackageNameConverter(string packageName)
        {
            Regex macroEscape = new Regex("[^a-z0-9_]", RegexOptions.IgnoreCase);
            return "Pkg" + macroEscape.Replace(packageName, "_");
        }

        /// <summary>
        /// LoadBinPlace.
        /// </summary>
        /// <param name="repoBasePath"></param>
        /// <returns></returns>
        private string LoadBinPlace(string repoBasePath)
        {
            string relativePath = "sources\\dev\\common\\src\\BinPlaceForPackages\\BinPlaceForPackages.csproj";
            string fullPath = Path.Combine(repoBasePath, relativePath);
            return File.ReadAllText(fullPath);
        }
    }
}
