// <copyright file="EnumConstraintTest.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.M365.Core.Telemetry.R9.Logging.Substrate.Sampler;
using OpenTelemetry.Logs;
using Xunit;

namespace Microsoft.M365.Core.Telemetry.R9.Logging.Substrate.Test.SamplingTest
{
    public class EnumConstraintTest
    {
        private const string LoggerName = "EnumConstraintTest";

        private const string EnumConstraintsConfig = @"
{
    ""SubstrateLogging"": {
        ""RuleBasedSampler"": {
            ""EnumConstraintTest"": [
                {
                    ""Constraints"": [
                        {
                            ""Field"": ""FieldPlaceholder"",
                            ""Type"": ""TypePlaceholder"",
                            ""Operator"": ""OperatorPlaceholder"",
                            ""Value"": ""ValuePlaceholder""
                        }
                    ],
                    ""Strategy"": {
                        ""Type"": ""Random"",
                        ""SampleRate"": 1.0
                    }
                },
                {
                    ""Constraints"": [],
                    ""Strategy"": {
                        ""Type"": ""Random"",
                        ""SampleRate"": 0.0
                    }
                }
            ]
        }
    }
}";

        private static string ReplaceConfigPlaceholders(string field, string type, string op, string value)
        {
            return EnumConstraintsConfig
                .Replace("FieldPlaceholder", field)
                .Replace("TypePlaceholder", type)
                .Replace("OperatorPlaceholder", op)
                .Replace("ValuePlaceholder", value);
        }

        private static void GenerateLog(ILogger logger, string fieldName, string fieldValue)
        {
            logger.LogInformation($"Test message {{{fieldName}}}", fieldValue);
        }

        [Fact]
        public void In_ShouldMatchAndSample()
        {
            var config = ReplaceConfigPlaceholders("TestField", ConstraintType.Enum, OperatorType.In, "Value1,Value2,Value3");
            var exportedItems = new List<LogRecord>();
            var logger = LoggerTestUtils.CreateLoggerWithConfig(config, exportedItems, LoggerName);

            GenerateLog(logger, "TestField", "Value2");

            Assert.Single(exportedItems);
        }

        [Fact]
        public void NotIn_ShouldMatchAndSample()
        {
            var config = ReplaceConfigPlaceholders("TestField", ConstraintType.Enum, OperatorType.NotIn, "Value1,Value2,Value3");
            var exportedItems = new List<LogRecord>();
            var logger = LoggerTestUtils.CreateLoggerWithConfig(config, exportedItems, LoggerName);

            GenerateLog(logger, "TestField", "Value4");

            Assert.Single(exportedItems);
        }

        [Fact]
        public void In_WithWhitespace_ShouldMatchAndSample()
        {
            var config = ReplaceConfigPlaceholders("TestField", ConstraintType.Enum, OperatorType.In, "Value1, Value2, Value3");
            var exportedItems = new List<LogRecord>();
            var logger = LoggerTestUtils.CreateLoggerWithConfig(config, exportedItems, LoggerName);

            GenerateLog(logger, "TestField", "Value2");

            Assert.Single(exportedItems);
        }

        [Fact]
        public void NotIn_WithWhitespace_ShouldMatchAndSample()
        {
            var config = ReplaceConfigPlaceholders("TestField", ConstraintType.Enum, OperatorType.NotIn, "Value1, Value2, Value3");
            var exportedItems = new List<LogRecord>();
            var logger = LoggerTestUtils.CreateLoggerWithConfig(config, exportedItems, LoggerName);

            GenerateLog(logger, "TestField", "Value4");

            Assert.Single(exportedItems);
        }

        [Fact]
        public void In_WithEmptyList_ShouldNotMatch()
        {
            var config = ReplaceConfigPlaceholders("TestField", ConstraintType.Enum, OperatorType.In, String.Empty);
            var exportedItems = new List<LogRecord>();
            var logger = LoggerTestUtils.CreateLoggerWithConfig(config, exportedItems, LoggerName);

            GenerateLog(logger, "TestField", "Value1");

            Assert.Empty(exportedItems);
        }

        [Fact]
        public void NotIn_WithEmptyList_ShouldMatch()
        {
            var config = ReplaceConfigPlaceholders("TestField", ConstraintType.Enum, OperatorType.NotIn, String.Empty);
            var exportedItems = new List<LogRecord>();
            var logger = LoggerTestUtils.CreateLoggerWithConfig(config, exportedItems, LoggerName);

            GenerateLog(logger, "TestField", "Value1");

            Assert.Single(exportedItems);
        }

        [Fact]
        public void InvalidType_ShouldNotMatch()
        {
            var config = ReplaceConfigPlaceholders("TestField", "InvalidEnum", OperatorType.In, "Value1,Value2");
            var exportedItems = new List<LogRecord>();
            var logger = LoggerTestUtils.CreateLoggerWithConfig(config, exportedItems, LoggerName);

            GenerateLog(logger, "TestField", "Value1");

            Assert.Empty(exportedItems);
        }

        [Fact]
        public void InvalidOperator_ShouldNotMatch()
        {
            var config = ReplaceConfigPlaceholders("TestField", ConstraintType.Enum, "InvalidOperator", "Value1,Value2");
            var exportedItems = new List<LogRecord>();
            var logger = LoggerTestUtils.CreateLoggerWithConfig(config, exportedItems, LoggerName);

            GenerateLog(logger, "TestField", "Value1");

            Assert.Empty(exportedItems);
        }

        [Fact]
        public void OperatorWithWhitespace_ShouldMatchAndSample()
        {
            var config = ReplaceConfigPlaceholders("TestField", ConstraintType.Enum, "  " + OperatorType.In + "  ", "Value1,Value2");
            var exportedItems = new List<LogRecord>();
            var logger = LoggerTestUtils.CreateLoggerWithConfig(config, exportedItems, LoggerName);

            GenerateLog(logger, "TestField", "Value1");

            Assert.Single(exportedItems);
        }

        [Fact]
        public void TypeWithWhitespace_ShouldMatchAndSample()
        {
            var config = ReplaceConfigPlaceholders("TestField", "  " + ConstraintType.Enum + "  ", OperatorType.In, "Value1,Value2");
            var exportedItems = new List<LogRecord>();
            var logger = LoggerTestUtils.CreateLoggerWithConfig(config, exportedItems, LoggerName);

            GenerateLog(logger, "TestField", "Value1");

            Assert.Single(exportedItems);
        }

        [Fact]
        public void CaseSensitive_ShouldMatchAndSample()
        {
            var config = ReplaceConfigPlaceholders("TestField", ConstraintType.Enum, OperatorType.In, "Value1,Value2");
            var exportedItems = new List<LogRecord>();
            var logger = LoggerTestUtils.CreateLoggerWithConfig(config, exportedItems, LoggerName);

            GenerateLog(logger, "TestField", "VALUE1");

            Assert.Empty(exportedItems);
        }

        [Fact]
        public void In_WithEmptyValue_ShouldNotMatch()
        {
            var config = ReplaceConfigPlaceholders("TestField", ConstraintType.Enum, OperatorType.In, "Value1,,Value2");
            var exportedItems = new List<LogRecord>();
            var logger = LoggerTestUtils.CreateLoggerWithConfig(config, exportedItems, LoggerName);

            GenerateLog(logger, "TestField", string.Empty);

            Assert.Single(exportedItems);
        }

        [Fact]
        public void In_WithNullValue_ShouldNotMatch()
        {
            var config = ReplaceConfigPlaceholders("TestField", ConstraintType.Enum, OperatorType.In, "Value1,Value2");
            var exportedItems = new List<LogRecord>();
            var logger = LoggerTestUtils.CreateLoggerWithConfig(config, exportedItems, LoggerName);

            GenerateLog(logger, "TestField", null);

            Assert.Empty(exportedItems);
        }

        [Fact]
        public void In_WithNumericValue_ShouldMatchAndSample()
        {
            var config = ReplaceConfigPlaceholders("TestField", ConstraintType.Enum, OperatorType.In, "1,2,3");
            var exportedItems = new List<LogRecord>();
            var logger = LoggerTestUtils.CreateLoggerWithConfig(config, exportedItems, LoggerName);

            GenerateLog(logger, "TestField", "2");

            Assert.Single(exportedItems);
        }

        [Fact]
        public void In_WithSpecialCharacters_ShouldMatchAndSample()
        {
            var config = ReplaceConfigPlaceholders("TestField", ConstraintType.Enum, OperatorType.In, "Value@#$%,Value2");
            var exportedItems = new List<LogRecord>();
            var logger = LoggerTestUtils.CreateLoggerWithConfig(config, exportedItems, LoggerName);

            GenerateLog(logger, "TestField", "Value@#$%");

            Assert.Single(exportedItems);
        }

        [Fact]
        public void In_WithLongList_ShouldMatchAndSample()
        {
            var longList = string.Join(",", Enumerable.Range(1, 1000).Select(i => $"Value{i}"));
            var config = ReplaceConfigPlaceholders("TestField", ConstraintType.Enum, OperatorType.In, longList);
            var exportedItems = new List<LogRecord>();
            var logger = LoggerTestUtils.CreateLoggerWithConfig(config, exportedItems, LoggerName);

            GenerateLog(logger, "TestField", "Value500");

            Assert.Single(exportedItems);
        }
    }
} 