// <copyright file="EventIdConstraintTest.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>
using System;
using System.Collections.Generic;
using System.IO;
using System.Text;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.M365.Core.Telemetry.R9.Logging.Substrate.Sampler;
using OpenTelemetry.Logs;
using Xunit;

namespace Microsoft.M365.Core.Telemetry.R9.Logging.Substrate.Test.SamplingTest
{
    public class EventIdConstraintTest
    {
        private const string LoggerName = "EventIdConstraintTest";

        private const string EventIdConstraintsConfig = @"
{
    ""SubstrateLogging"": {
        ""RuleBasedSampler"": {
            ""EventIdConstraintTest"": [
                {
                    ""Constraints"": [
                        {
                            ""Field"": ""FieldPlaceholder"",
                            ""Type"": ""TypePlaceholder"",
                            ""Operator"": ""OperatorPlaceholder"",
                            ""Value"": ""ValuePlaceholder""
                        }
                    ],
                    ""Strategy"": {
                        ""Type"": ""Random"",
                        ""SampleRate"": 1.0
                    }
                },
                {
                    ""Constraints"": [],
                    ""Strategy"": {
                        ""Type"": ""Random"",
                        ""SampleRate"": 0.0
                    }
                }
            ]
        }
    }
}";

        private static string ReplaceConfigPlaceholders(string field, string type, string op, string value)
        {
            return EventIdConstraintsConfig
                .Replace("FieldPlaceholder", field)
                .Replace("TypePlaceholder", type)
                .Replace("OperatorPlaceholder", op)
                .Replace("ValuePlaceholder", value);
        }

        private static void GenerateLog(ILogger logger, int eventId)
        {
            logger.LogInformation(new EventId(eventId), "Test message");
        }

        [Fact]
        public void NumericEquals_ShouldMatchAndSample()
        {
            var config = ReplaceConfigPlaceholders("TestField", ConstraintType.EventId, OperatorType.NumericEquals, "42");
            var exportedItems = new List<LogRecord>();
            var logger = LoggerTestUtils.CreateLoggerWithConfig(config, exportedItems, LoggerName);

            GenerateLog(logger, 42);

            Assert.Single(exportedItems);
        }

        [Fact]
        public void NumericNotEquals_ShouldMatchAndSample()
        {
            var config = ReplaceConfigPlaceholders("TestField", ConstraintType.EventId, OperatorType.NumericNotEquals, "42");
            var exportedItems = new List<LogRecord>();
            var logger = LoggerTestUtils.CreateLoggerWithConfig(config, exportedItems, LoggerName);

            GenerateLog(logger, 43);

            Assert.Single(exportedItems);
        }

        [Fact]
        public void InvalidType_ShouldNotMatch()
        {
            var config = ReplaceConfigPlaceholders("TestField", "InvalidEventId", OperatorType.NumericEquals, "42");
            var exportedItems = new List<LogRecord>();
            var logger = LoggerTestUtils.CreateLoggerWithConfig(config, exportedItems, LoggerName);

            GenerateLog(logger, 42);

            Assert.Empty(exportedItems);
        }

        [Fact]
        public void InvalidOperator_ShouldNotMatch()
        {
            var config = ReplaceConfigPlaceholders("TestField", ConstraintType.EventId, "InvalidOperator", "42");
            var exportedItems = new List<LogRecord>();
            var logger = LoggerTestUtils.CreateLoggerWithConfig(config, exportedItems, LoggerName);

            GenerateLog(logger, 42);

            Assert.Empty(exportedItems);
        }

        [Fact]
        public void OperatorWithWhitespace_ShouldMatchAndSample()
        {
            var config = ReplaceConfigPlaceholders("TestField", ConstraintType.EventId, "  " + OperatorType.NumericEquals + "  ", "42");
            var exportedItems = new List<LogRecord>();
            var logger = LoggerTestUtils.CreateLoggerWithConfig(config, exportedItems, LoggerName);

            GenerateLog(logger, 42);

            Assert.Single(exportedItems);
        }

        [Fact]
        public void TypeWithWhitespace_ShouldMatchAndSample()
        {
            var config = ReplaceConfigPlaceholders("TestField", "  " + ConstraintType.EventId + "  ", OperatorType.NumericEquals, "42");
            var exportedItems = new List<LogRecord>();
            var logger = LoggerTestUtils.CreateLoggerWithConfig(config, exportedItems, LoggerName);

            GenerateLog(logger, 42);

            Assert.Single(exportedItems);
        }

        [Fact]
        public void StringValue_ShouldMatchAndSample()
        {
            var config = ReplaceConfigPlaceholders("TestField", ConstraintType.EventId, OperatorType.NumericEquals, "42");
            var exportedItems = new List<LogRecord>();
            var logger = LoggerTestUtils.CreateLoggerWithConfig(config, exportedItems, LoggerName);

            logger.LogInformation(new EventId(42), "Test message");

            Assert.Single(exportedItems);
        }

        [Fact]
        public void NegativeEventId_ShouldMatchAndSample()
        {
            var config = ReplaceConfigPlaceholders("TestField", ConstraintType.EventId, OperatorType.NumericEquals, "-42");
            var exportedItems = new List<LogRecord>();
            var logger = LoggerTestUtils.CreateLoggerWithConfig(config, exportedItems, LoggerName);

            GenerateLog(logger, -42);

            Assert.Single(exportedItems);
        }

        [Fact]
        public void ZeroEventId_ShouldMatchAndSample()
        {
            var config = ReplaceConfigPlaceholders("TestField", ConstraintType.EventId, OperatorType.NumericEquals, "0");
            var exportedItems = new List<LogRecord>();
            var logger = LoggerTestUtils.CreateLoggerWithConfig(config, exportedItems, LoggerName);

            GenerateLog(logger, 0);

            Assert.Single(exportedItems);
        }

        [Fact]
        public void NumericEquals_WithMaxValue_ShouldMatchAndSample()
        {
            var config = ReplaceConfigPlaceholders("EventId", ConstraintType.EventId, OperatorType.NumericEquals, int.MaxValue.ToString());
            var exportedItems = new List<LogRecord>();
            var logger = LoggerTestUtils.CreateLoggerWithConfig(config, exportedItems, LoggerName);

            GenerateLog(logger, int.MaxValue);

            Assert.Single(exportedItems);
        }

        [Fact]
        public void NumericEquals_WithMinValue_ShouldMatchAndSample()
        {
            var config = ReplaceConfigPlaceholders("EventId", ConstraintType.EventId, OperatorType.NumericEquals, int.MinValue.ToString());
            var exportedItems = new List<LogRecord>();
            var logger = LoggerTestUtils.CreateLoggerWithConfig(config, exportedItems, LoggerName);

            GenerateLog(logger, int.MinValue);

            Assert.Single(exportedItems);
        }

        [Fact]
        public void NumericEquals_WithInvalidFormat_ShouldNotMatch()
        {
            var config = ReplaceConfigPlaceholders("EventId", ConstraintType.EventId, OperatorType.NumericEquals, "invalid");
            var exportedItems = new List<LogRecord>();
            var logger = LoggerTestUtils.CreateLoggerWithConfig(config, exportedItems, LoggerName);

            GenerateLog(logger, 42);

            Assert.Empty(exportedItems);
        }

        [Fact]
        public void NumericEquals_WithNamedEventId_ShouldMatchAndSample()
        {
            var config = ReplaceConfigPlaceholders("EventId", ConstraintType.EventId, OperatorType.NumericEquals, "42");
            var exportedItems = new List<LogRecord>();
            var logger = LoggerTestUtils.CreateLoggerWithConfig(config, exportedItems, LoggerName);

            logger.LogInformation(new EventId(42, "TestEvent"), "Test message");

            Assert.Single(exportedItems);
        }

        [Fact]
        public void NullState_ShouldStillMatchEventId()
        {
            var config = ReplaceConfigPlaceholders("EventId", ConstraintType.EventId, OperatorType.NumericEquals, "42");
            var exportedItems = new List<LogRecord>();
            var logger = LoggerTestUtils.CreateLoggerWithConfig(config, exportedItems, LoggerName);

            // Using null as state
            logger.Log<object>(LogLevel.Information, new EventId(42), null, null,
                (state, ex) => "Test message with null state");

            // Should still match because EventId constraint doesn't depend on state
            Assert.Single(exportedItems);
        }

        [Fact]
        public void CustomStateObject_ShouldStillMatchEventId()
        {
            var config = ReplaceConfigPlaceholders("EventId", ConstraintType.EventId, OperatorType.NumericEquals, "42");
            var exportedItems = new List<LogRecord>();
            var logger = LoggerTestUtils.CreateLoggerWithConfig(config, exportedItems, LoggerName);

            // Using a custom object as state that doesn't implement IReadOnlyList<KeyValuePair<string, object?>>
            var customState = new { UserId = "user_123", Value = 42 };

            logger.Log(LogLevel.Information, new EventId(42), customState, null,
                (state, ex) => $"Test message with custom state object: {state}");

            // Should still match because EventId constraint doesn't depend on state
            Assert.Single(exportedItems);
        }

        [Fact]
        public void PrimitiveStateType_ShouldStillMatchEventId()
        {
            var config = ReplaceConfigPlaceholders("EventId", ConstraintType.EventId, OperatorType.NumericEquals, "42");
            var exportedItems = new List<LogRecord>();
            var logger = LoggerTestUtils.CreateLoggerWithConfig(config, exportedItems, LoggerName);

            // Using an integer as state
            logger.Log(LogLevel.Information, new EventId(42), 123, null,
                (state, ex) => $"Test message with int state: {state}");

            // Should still match because EventId constraint doesn't depend on state
            Assert.Single(exportedItems);
        }
    }
}