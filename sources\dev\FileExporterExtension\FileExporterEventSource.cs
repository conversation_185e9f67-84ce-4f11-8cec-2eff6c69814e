// <copyright file="FileExporterEventSource.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System.Diagnostics.CodeAnalysis;
using System.Diagnostics.Tracing;

namespace Microsoft.M365.Core.Telemetry.FileExporter
{
    /// <summary>
    /// Initializes a new instance of the <see cref="FileExporterEventSource"/> class.
    /// </summary>
    [EventSource(Name = "NRTFileExporter")]
    [ExcludeFromCodeCoverage]
    internal sealed class FileExporterEventSource : EventSource
    {
        /// <param name="Instance">The type</param>
        public static readonly FileExporterEventSource Instance = new ();

        private const int EventIdCleanUpArchivedLogFiles = 1;

        private const int EventIdFileIOError = 10;

        /// <summary>
        /// CleanUpArchivedLogFiles
        /// </summary>
        /// <param name="reason"><see cref="string"/>.</param>
        /// <param name="logFileName"><see cref="string"/>.</param>
        [Event(EventIdCleanUpArchivedLogFiles, Level = EventLevel.Informational)]
        public void CleanUpArchivedLogFiles(string reason, string logFileName)
        {
            WriteEvent(EventIdCleanUpArchivedLogFiles, "Clean up archived log files", reason, logFileName);
        }

        /// <summary>
        /// FileIOFailed
        /// </summary>
        /// <param name="reason"><see cref="string"/>.</param>
        /// <param name="logFileName"><see cref="string"/>.</param>
        [Event(EventIdFileIOError, Level = EventLevel.Error)]
        public void FileIOFailed(string reason, string logFileName)
        {
            WriteEvent(EventIdFileIOError, "Failed on file IO action", reason, logFileName);
        }
    }
}
