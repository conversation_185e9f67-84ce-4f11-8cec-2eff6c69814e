// <copyright file="DynamicRatioSampler.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System;
using Microsoft.M365.Core.Telemetry.ECSClient;
using OpenTelemetry.Trace;

namespace Microsoft.M365.Core.Telemetry.ECS.DynamicComponent.Samplers
{
    /// <summary>
    /// DynamicRatioSampler
    /// </summary>
    internal class DynamicRatioSampler : Sampler
    {
        private readonly R9TracingConfig config;

        /// <summary>
        /// DynamicRatioSampler
        /// </summary>
        /// <param name="config"></param>
        public DynamicRatioSampler(R9TracingConfig config)
        {
            this.config = config;
        }

        /// <summary>
        /// ShouldSample
        /// </summary>
        /// <param name="samplingParameters"></param>
        /// <returns></returns>
        public override SamplingResult ShouldSample(in SamplingParameters samplingParameters)
        {
            //System.Console.WriteLine(System.Environment.StackTrace);
            if (config.TraceSampleRate <= 0.0)
            {
                return new SamplingResult(SamplingDecision.Drop);
            }
            if (config.TraceSampleRate >= 1.0)
            {
                return new SamplingResult(SamplingDecision.RecordAndSample);
            }
            Span<byte> traceIdBytes = stackalloc byte[16];
            samplingParameters.TraceId.CopyTo(traceIdBytes);
            return new SamplingResult(Math.Abs(GetLowerLong(traceIdBytes)) < (long)(config.TraceSampleRate * long.MaxValue));
        }

        //copied from Open telemetry TraceIdRatioBasedSampler.cs
        private static long GetLowerLong(ReadOnlySpan<byte> bytes)
        {
            long result = 0;
            for (var i = 0; i < 8; i++)
            {
                result <<= 8;
#pragma warning disable CS0675 // Bitwise-or operator used on a sign-extended operand
                result |= bytes[i] & 0xff;
#pragma warning restore CS0675 // Bitwise-or operator used on a sign-extended operand
            }

            return result;
        }
    }
}
