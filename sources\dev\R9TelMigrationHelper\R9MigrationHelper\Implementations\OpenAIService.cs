﻿// <copyright file="OpenAIService.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System.Diagnostics.CodeAnalysis;
using Azure;
using Azure.AI.OpenAI;
using Microsoft.Extensions.Logging;

namespace R9MigrationHelper.Implementations
{
    /// <summary>
    /// OpenAI Service
    /// </summary>
    [ExcludeFromCodeCoverage]
    public class OpenAIService : IOpenAIService
    {
        //private ILogger logger;
        //private string embeddingModelName;
        private OpenAIClient openAIClient;
        private string gptModelName;

        /// <summary>
        /// The constructor
        /// </summary>
        /// <param name="configurationManager"></param>
        public OpenAIService(IConfigurationManager configurationManager)
        {
            var endPoint = configurationManager.GetConfiguration("OpenAIEndpoint");
            var apiKey = configurationManager.GetConfiguration("OpenAIKey");
            this.openAIClient = new OpenAIClient(
            new Uri(endPoint),
            new AzureKeyCredential(apiKey));

            //this.logger = logger;
            //this.embeddingModelName = configurationManager.GetConfiguration("EmbeddingModelName");
            this.gptModelName = configurationManager.GetConfiguration("OpenAIGPTModelName");
        }

        /// <summary>
        /// The constructor
        /// </summary>
        /// <param name="endPoint"></param>
        /// <param name="apiKey"></param>
        /// <param name="openAIGPTModelName"></param>
        public OpenAIService(string endPoint, string apiKey, string openAIGPTModelName)
        {
            //var endPoint = configurationManager.GetConfiguration("OpenAIEndpoint");
            //var apiKey = configurationManager.GetConfiguration("OpenAIKey");
            this.openAIClient = new OpenAIClient(
            new Uri(endPoint),
            new AzureKeyCredential(apiKey));

            //this.logger = logger;
            //this.embeddingModelName = embeddingModelName;
            this.gptModelName = openAIGPTModelName;
        }

        /// <summary>
        /// Azure GPT conversation API
        /// </summary>
        /// <param name="instruction">instruction</param>
        /// <param name="question">question</param>
        /// <param name="retryMS">retry time interval</param>
        /// <returns></returns>
        public async Task<string> AzureGPTConversation(string instruction, string question, int retryMS = 3000)
        {
            string answer = string.Empty;
            var option = new ChatCompletionsOptions()
            {
                Messages = { new ChatMessage(ChatRole.System, instruction) },
                Temperature = 0F,
                MaxTokens = 2048,
                NucleusSamplingFactor = 0.95F,
                FrequencyPenalty = 0,
                PresencePenalty = 0,
            };

            option.Messages.Add(new ChatMessage(ChatRole.User, question));

            try //TODO Using Polly to retry
            {
                Response<ChatCompletions> responseWithoutStream = await this.openAIClient.GetChatCompletionsAsync(gptModelName, option);
                if (responseWithoutStream.GetRawResponse().Status != 200)
                {
                    //logger.LogWarning($"Failed to get response from GPT4, status code: {responseWithoutStream.GetRawResponse().Status}, retry in 3s...");
                    Thread.Sleep(3000);
                    return await AzureGPTConversation(instruction, question);
                }
                ChatCompletions completions = responseWithoutStream.Value;

                foreach (ChatChoice choice in completions.Choices)
                {
                    answer = choice.Message.Content;
                    if (!string.IsNullOrEmpty(answer))
                    {
                        break;
                    }
                }

                return answer;
            }
            catch (Exception)
            {
                //logger.LogError($"Failed to get response from GPT4, exception: {ex.Message}, retry in 3s...");
                Thread.Sleep(retryMS);
                return await AzureGPTConversation(instruction, question);
            }
        }
    }
}
