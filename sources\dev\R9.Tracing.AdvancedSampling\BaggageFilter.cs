﻿// <copyright file="BaggageFilter.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.Diagnostics;
using OpenTelemetry;
using OpenTelemetry.Exporter.Filters;

namespace Microsoft.M365.Core.Telemetry.R9.Tracing.AdvancedSampling
{
    /// <summary>
    /// Filter for activities with specific BaggageItem
    /// </summary>
    public class BaggageFilter : BaseFilter<Activity>
    {
        private static readonly IReadOnlyDictionary<string, string> DefaultFilterItems = new Dictionary<string, string> 
        {
            { Constants.PostTraceBaggageName, Constants.SampleBaggage },
            { Constants.SampleBaggageName, Constants.SampleBaggage }
        };

        private const string Description = "A Filter for activities with specific BaggageItem";

        private readonly IReadOnlyDictionary<string, string> filterItems;

        /// <summary>
        /// Config specific baggage items for filtering activities
        /// </summary>
        /// <param name="filterItems"></param>
        public BaggageFilter()
        {
            this.filterItems = DefaultFilterItems;
        }

        /// <summary>
        /// Config specific activityBaggage items for filtering activities
        /// </summary>
        /// <param name="filterItems"></param>
        public BaggageFilter(Dictionary<string, string> filterItems = null)
        {
            if (filterItems == null || filterItems.Count == 0)
            {
                this.filterItems = DefaultFilterItems;
            }

            this.filterItems = filterItems;
        }

        /// <inheritdoc/>
        public override string GetDescription()
        {
            return Description;
        }

        /// <inheritdoc/>
        public override bool ShouldFilter(Activity activity)
        {
            if (activity == null)
            {
                return false;
            }

            foreach (var kvp in this.filterItems)
            {
                var activityBaggage = activity.GetBaggageItem(kvp.Key);
                var baggage = Baggage.GetBaggage(kvp.Key);

                if ((activityBaggage != null && activityBaggage.Equals(kvp.Value, StringComparison.OrdinalIgnoreCase)) 
                    || (baggage != null && baggage.Equals(kvp.Value, StringComparison.OrdinalIgnoreCase)))
                {
                    return true;
                }
            }

            return false;
        }
    }
}
