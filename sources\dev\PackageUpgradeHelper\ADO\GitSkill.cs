﻿// <copyright file="GitSkill.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http.Headers;
using System.Security.Policy;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;

namespace PackageUpgradeHelper.ADO
{
    /// <summary>
    /// Git skill
    /// </summary>
    public class GitSkill
    {
        private readonly string org = Globals.ORGANIZATION;
        private readonly string proj = Globals.PROJECT;
        private readonly string repo = Globals.REPOSITORY;
        private readonly string apiVersion = Globals.ADO_API_VERSION;
        private readonly string pat = Globals.PAT;

        /// <summary>
        /// Get lateset commit id of target branch
        /// </summary>
        /// <param name="branch"></param>
        /// <returns></returns>
        public async Task<string> GetLatestCommitId(string branch = "master")
        {
            using HttpClient client = new HttpClient();
            client.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));
            client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Basic", Convert.ToBase64String(Encoding.ASCII.GetBytes(string.Format("{0}:{1}", string.Empty, pat))));
            var url = $"https://dev.azure.com/{org}/{proj}/_apis/git/repositories/{repo}/refs?filter=heads/{branch}&$top=10&api-version={apiVersion}";

            var response = await client.GetAsync(url).ConfigureAwait(false);
            response.EnsureSuccessStatusCode();
            using var content = await JsonDocument.ParseAsync(await response.Content.ReadAsStreamAsync().ConfigureAwait(false)).ConfigureAwait(false);
            return content.RootElement.GetProperty("value")[0].GetProperty("objectId").GetString() ?? String.Empty;
        }

        /// <summary>
        /// Create new branch based on targetCommitId
        /// </summary>
        /// <param name="targetCommitId"></param>
        /// <param name="newBranchName"></param>
        /// <returns></returns>
        public async Task CreateBranch(string targetCommitId, string newBranchName)
        {
            using HttpClient client = new HttpClient();
            client.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));
            client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Basic", Convert.ToBase64String(Encoding.ASCII.GetBytes(string.Format("{0}:{1}", string.Empty, pat))));
            var requestBody = 
                new[]
                {
                    new
                    {
                        name = $"refs/heads/{newBranchName}",
                        oldObjectId = "0000000000000000000000000000000000000000",
                        newObjectId = targetCommitId
                    }
                }
            ;
            var requestBodyJson = new StringContent(JsonSerializer.Serialize(requestBody), Encoding.UTF8, "application/json");
            var url = $"https://dev.azure.com/{org}/{proj}/_apis/git/repositories/{repo}/refs?api-version={apiVersion}";
            var response = await client.PostAsync(url, requestBodyJson).ConfigureAwait(false);
            response.EnsureSuccessStatusCode();
        }

        /// <summary>
        /// Push changes to remote branch
        /// </summary>
        /// <param name="branchName"></param>
        /// <param name="latestCommitId"></param>
        /// <param name="fileChanges"></param>
        /// <returns></returns>
        public async Task<string> PushToRemoteBranch(string branchName, string latestCommitId, List<FileChange> fileChanges, string comment)
        {
            using HttpClient client = new HttpClient();
            client.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));
            client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Basic", Convert.ToBase64String(Encoding.ASCII.GetBytes(string.Format("{0}:{1}", string.Empty, pat))));
            var requestBody = new
            {
                refUpdates = new[]
                {
                    new
                    {
                        name = $"refs/heads/{branchName}",
                        oldObjectId = latestCommitId
                    }
                },
                commits = new[]
                {
                    new
                    {
                        comment = comment,
                        changes = fileChanges.Select(fileChange => new
                        {
                            changeType = fileChange.changeType,
                            item = new
                            {
                                path = fileChange.filePath
                            },
                            newContent = new
                            {
                                content = fileChange.content,
                                contentType = "rawtext"
                            }
                        }).ToArray()
                    }
                }
            };
            var requestBodyJson = new StringContent(JsonSerializer.Serialize(requestBody), Encoding.UTF8, "application/json");
            var url = $"https://dev.azure.com/{org}/{proj}/_apis/git/repositories/{repo}/pushes?api-version={apiVersion}";
            var response = await client.PostAsync(url, requestBodyJson).ConfigureAwait(false);
            response.EnsureSuccessStatusCode();
            using var content = await JsonDocument.ParseAsync(await response.Content.ReadAsStreamAsync().ConfigureAwait(false)).ConfigureAwait(false);
            return content.RootElement.GetProperty("commits")[0].GetProperty("commitId").GetString() ?? String.Empty;
        }
    }

    /// <summary>
    /// File change
    /// </summary>
    public record FileChange(
        string filePath,
        string changeType,
        string content
    );
}
