# About LoggerFactory, Lo<PERSON> and Events

## Can I send different events with one ILogger?

No.

The destination event is decided by [`TableNameMappings`](../Glossary.md#tablenamemappings) (for Geneva Exporter. There will be similar tables when using other Exporter), which decides the event table according to the category.
Logs from the same ILogger have the same Category (or the logger name).
They will be routed to the same event table.

## Can I have multiple Logger sending a same event?

Yes.

We can do this by setting multiple rules in [`TableNameMappings`](../Glossary.md#tablenamemappings) to map all logger name to the same events.

Alternatively, we can use the pass through rule, set key to `*`, value to the desired event.
This will map **all** logs to the same event.

## Can I have multiple LoggerFactory with different settings?

Yes, but not recommended.

We can use different loggers to route logs to different events.

`LoggerFactory` is configured with the configuration used to create it.
When using `LoggerFactory.Create()`, each LoggerFactory instance use the options on the `ILoggerFactoryBuilder`.
When the `LoggerFactory` is injected, it's configured by `IServiceCollection.AddLogging(ILoggingBulider)`.
Each `ServiceCollection` will have at most one `LoggerFactory`.
