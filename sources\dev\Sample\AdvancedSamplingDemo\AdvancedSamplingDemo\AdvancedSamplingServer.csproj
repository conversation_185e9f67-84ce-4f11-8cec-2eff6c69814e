﻿<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <RootNamespace>Microsoft.M365.Core.Tracing.AdvancedSamplingServer</RootNamespace>
     <AssemblyName>Microsoft.M365.Core.Tracing.AdvancedSamplingServer</AssemblyName>
    <TargetFramework>net6.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>

	<ItemGroup>
		<PackageReference Include="Newtonsoft.Json" />
		<PackageReference Include="Microsoft.Extensions.Hosting" />
		<PackageReference Include="OpenTelemetry.Exporter.Geneva" />
	</ItemGroup>

	<ItemGroup>
	  <ProjectReference Include="..\..\..\R9.Tracing.AdvancedSampling\R9.Tracing.AdvancedSampling.csproj" />
		<ProjectReference Include="..\..\..\R9.Tracing.Instrumentation.Accelerator\R9.Tracing.Instrumentation.Accelerator.csproj" />
	</ItemGroup>

  <ItemGroup>
	<None Update="AppSettings.json">
	<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
	</None>
  </ItemGroup>

</Project>
