{"ECS": {"c72ea287-ed77-4fa6-a480-3712406c367e": "aka.ms/EcsCanary"}, "UnifiedTelemetry": {"SubstrateLogging": {"R9Logging": {}, "Exporters": {"Geneva": {"ConnectionString": "EtwSession=test"}, "OdlTcp": {"ConnectionString": "tcp://localhost:1234"}, "VirtualTableMappings": {"Test.TestEvent": "TestEventTable", "Test.TestEvent3": "TestEventTable", "TestMetric.MyService": "MyServiceTable"}, "VirtualTableExports": {"TestEventTable": [{"ExportPlatform": "Odl", "ExportTableName": "TestLogType"}], "MyServiceTable": [{"ExportPlatform": "Geneva", "ExportTableName": "<PERSON><PERSON><PERSON><PERSON>"}, {"ExportPlatform": "Odl", "ExportTableName": "TestOdl"}]}}}}}