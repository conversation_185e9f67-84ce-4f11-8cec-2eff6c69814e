// <copyright file="FileExporterTests.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.IO;
using System.Threading;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.R9.Extensions.SecurityTelemetry;
using Xunit;

namespace Microsoft.M365.Core.Telemetry.FileExporter.Tests
{
#pragma warning disable R9EXPDEV
#pragma warning disable R9EXP0008

    /// <summary>
    /// FileExporterTests
    /// </summary>
    public class FileExporterTests
    {
        private readonly object sequentialTestLock = new ();

        /// <summary>
        /// GetFormattedFileName
        /// </summary>
        /// <param name="directoryPath"></param>
        /// <param name="searchPattern"></param>
        /// <returns></returns>
        private string GetFormattedFileName(string directoryPath, string searchPattern)
        {
            string[] matchingFiles = Directory.GetFiles(directoryPath, searchPattern);
            if (matchingFiles.Length > 0)
            {
                return matchingFiles[0];
            }
            return string.Empty;
        }

        /// <summary>
        /// Test CreateSingleSecurityRecordList
        /// </summary>
        /// <param name="schemaName"></param>
        /// <param name="value"></param>
        /// <returns></returns>
        internal SecurityRecord[] CreateSingleSecurityRecordList(string schemaName, string value)
        {
            SecurityRecord securityRecord = new SecurityRecord();

            securityRecord.Initialize(schemaName, new List<string>
            {
                "ComputerName",
                "ClusterId"
            });
            securityRecord.AddProperty("testKey", value);

            SecurityRecord[] recordList = new SecurityRecord[] { securityRecord };

            return recordList;
        }

        /// <summary>
        /// Test ValidateOptions_Pass
        /// </summary>
        [Fact]
        public void ValidateOptionsPass()
        {
            IHostBuilder hostbuilder = new HostBuilder()
            .ConfigureServices((context, services) =>
            {
                _ = services.AddSecurityTelemetry(builder =>
                {
                    _ = services.Configure<SecurityTelemetryOptions>(option =>
                    {
                        option.PrepopulatedFields = new Dictionary<string, object>
                                {
                                    { "ComputerName", "TestComputerName" },
                                    { "ClusterId", "TestClusterId" }
                                };
                    });
                    _ = services.Configure<FileExporterOptions>(option =>
                    {
                        option.FileName = "app";
                        option.FileExtension = ".log";
                        option.FileDateTimePattern = "yyyy-MM-dd";
                    });
                    _ = builder.AddODLFileExporter();
                });
            });
            var iHost = hostbuilder.Build();

            Assert.NotNull(iHost.Services.GetRequiredService<ISecurityTelemetryExporter>());
        }

        /// <summary>
        /// Test ValidateOptionsFail
        /// </summary>
        [Fact]
        public void ValidateOptionsFail()
        {
            IHostBuilder hostbuilder = new HostBuilder()
            .ConfigureServices((context, services) =>
            {           
                _ = services.AddSecurityTelemetry(builder =>
                {
                    _ = services.Configure<SecurityTelemetryOptions>(option =>
                    {
                        option.PrepopulatedFields = new Dictionary<string, object>
                            {
                                { "ComputerName", "TestComputerName" },
                                { "ClusterId", "TestClusterId" }
                            };
                    });
                    _ = services.Configure<FileExporterOptions>(option =>
                    {
                        option.FileName = "app";
                        option.FileExtension = ".log";
                        option.FileDateTimePattern = "yyyy/MM/dd";
                    });
                    _ = builder.AddODLFileExporter();
                });
            });
            
            var iHost = hostbuilder.Build();

            var exception = Assert.Throws<ArgumentException>(() =>
            {
                _ = iHost.Services.GetRequiredService<ISecurityTelemetryExporter>();
            });
            Assert.Contains("Invalid FileDateTimePattern specified, should not contain any of /\\|*?<>", exception.Message, StringComparison.Ordinal);
        }

        /// <summary>
        /// Test WriteToFileWithSecurityTelemetryOption
        /// </summary>
        [Fact]
        public void WriteToFileWithSecurityTelemetryOption()
        {
            lock (sequentialTestLock)
            {
                IHostBuilder hostbuilder = new HostBuilder()
                    .ConfigureServices((context, services) =>
                    {
                        _ = services.AddSecurityTelemetry(builder =>
                        {
                            _ = services.Configure<SecurityTelemetryOptions>(option =>
                            {
                                option.PrepopulatedFields = new Dictionary<string, object>
                                {
                                    { "ComputerName", "TestComputerName" },
                                    { "ClusterId", "TestClusterId" }
                                };
                            });
                            _ = services.Configure<FileExporterOptions>(option =>
                            {
                                option.FileName = "appWriteToFile";
                                option.Directory = "logs";
                                option.FileDateTimePattern = "yyyy-MM-dd";
                                option.MaxQueueSize = 1024;
                                option.ScheduledDelayMilliseconds = 2500;
                                option.ExporterTimeoutMilliseconds = 20000;
                                option.MaxExportBatchSize = 256;
                                option.EnableCleanUpArchivedFiles = true;
                            });
                            _ = builder.AddODLFileExporter();
                        });
                    });

                var iHost = hostbuilder.Build();

                var exporter = iHost.Services.GetRequiredService<ISecurityTelemetryExporter>();

                var batch = new Batch<SecurityRecord>(CreateSingleSecurityRecordList("TestSchemaName", "testValue"), 1);
                var batch2 = new Batch<SecurityRecord>(CreateSingleSecurityRecordList("TestSchemaName2", "testValue2"), 1);

                ODLFileExporter fileExporter = (ODLFileExporter)exporter;

                fileExporter.ExportBatch(batch);
                fileExporter.ExportBatch(batch2);
                Thread.Sleep(5000);

                var fullFileName = GetFormattedFileName("logs", "*appWriteToFile*");
                var writtenText = File.ReadAllText(fullFileName);

                Assert.Contains("TestComputer", writtenText, StringComparison.Ordinal);
                Assert.Contains("TestSchemaName", writtenText, StringComparison.Ordinal);
                Assert.Contains("testValue", writtenText, StringComparison.Ordinal);

                fileExporter.Dispose();
            }
        }
    }
}
