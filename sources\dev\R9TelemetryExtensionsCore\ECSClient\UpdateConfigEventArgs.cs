﻿// <copyright file="UpdateConfigEventArgs.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;

namespace Microsoft.M365.Core.Telemetry.ECSClient
{
    /// <summary>
    /// UpdateConfigEventArgs
    /// </summary>
    internal class UpdateConfigEventArgs : EventArgs
    {
        /// <summary>
        /// Agents that has been changed
        /// </summary>
        public HashSet<string> ChangedAgents { get; set; }

        /// <summary>
        /// Wait handler counter
        /// </summary>
        public WaitHandleCounter WaitHandleCounter { get; set; }
    }
}
