﻿// <copyright file="HashBasedRandomStrategy.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.IO.Hashing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging.Abstractions;

namespace Microsoft.M365.Core.Telemetry.R9.Logging.Substrate.Sampler
{
    /// <summary>
    /// Sampling strategy that samples log entries based on a hash of a specified key.
    /// </summary>
    internal class HashBasedRandomStrategy : SamplingStrategy
    {
        /// <summary>
        /// Expected sample rate between [0,1].
        /// For example, 0.3 means 30% of logs are expected to be sampled.
        /// If sampled, keep the log. If not, discard the log.
        /// </summary>
        public double SampleRate { get; set; }

        /// <summary>
        /// Hash key used to get the value to hash.
        /// </summary>
        public string HashKey { get; set; }

        /// <summary>
        /// Initializes a new instance of the <see cref="HashBasedRandomStrategy"/> class.
        /// </summary>
        /// <param name="sampleRate">The expected sample rate.</param>
        /// <param name="hashKey">The hash key used to get the value to hash.</param>
        public HashBasedRandomStrategy(double sampleRate, string hashKey)
        {
            SampleRate = sampleRate;
            HashKey = hashKey;
        }

        /// <summary>
        /// Makes a sampling decision for the provided <paramref name="logEntry"/>.
        /// If sampled, keep the log. If not, discard the log.
        /// </summary>
        /// <param name="logEntry">The log entry used to make the sampling decision for.</param>
        /// <typeparam name="TState">The type of the log entry state.</typeparam>
        /// <returns><see langword="true" /> if the log record should be sampled; otherwise, <see langword="false" />.</returns>
        public override bool ShouldSample<TState>(in LogEntry<TState> logEntry)
        {
            if (logEntry.State == null)
            {
                return true; // We need to sample all the logs if the state is null
            }
            
            if (logEntry.State is not IReadOnlyList<KeyValuePair<string, object?>> state)
            {
                return true; // We need to sample all the logs if the state is not of expected type
            }

            var matchingKVP = state.FirstOrDefault(kv => kv.Key == HashKey);
            if (matchingKVP.Key == null)
            {
                return true; // We need to sample all the logs if the hash key is not found
            }
            if (matchingKVP.Value == null)
            {
                return true; // We need to sample all the logs if the hash value is null
            }

            string valueToHash = matchingKVP.Value is string str ? str : matchingKVP.Value.ToString() ?? string.Empty;
            ReadOnlySpan<byte> bytes = Encoding.UTF8.GetBytes(valueToHash);
            ulong hash = XxHash64.HashToUInt64(bytes);
            double normalizedHash = (double)(hash >> 11) / (1UL << 53); // A high performance way to normalize the hash value to [0, 1) range
            return normalizedHash < SampleRate;
        }
    }
}
