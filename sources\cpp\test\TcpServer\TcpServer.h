#pragma once

#include <boost/asio.hpp>
#include <opentelemetry/logs/provider.h>
#include <opentelemetry/logs/logger.h>

#include <memory>
#include <string>
#include <unordered_set>

namespace Microsoft {
namespace M365 {
namespace Exporters {

class TcpServer
{
public:
    TcpServer(short port);
    std::string get_last_message() const { return last_message_; };
    void ShutDown();
    void Start();

    ~TcpServer();

private:
    // Must called under mutex_.
    void start_accept();
    void start_stats_timer();
    void handle_accept(const boost::system::error_code& error, std::shared_ptr<boost::asio::ip::tcp::socket> socket);
    // Must called under mutex_.
    void read(std::shared_ptr<boost::asio::ip::tcp::socket> socket);
    void handle_read(const boost::system::error_code& error, std::size_t bytes_transferred, std::shared_ptr<boost::asio::ip::tcp::socket> socket);
    void handle_stats_timer(const boost::system::error_code& error);
    void print_stats();
    // Must called under mutex_.
    void close_socket(std::shared_ptr<boost::asio::ip::tcp::socket> socket);
    // Must called under mutex_.
    void remove_active_socket(std::shared_ptr<boost::asio::ip::tcp::socket> socket);    

    boost::asio::io_context& io_context_;
    short port_;
    std::unique_ptr<boost::asio::ip::tcp::acceptor> acceptor_;
    std::unordered_set<std::shared_ptr<boost::asio::ip::tcp::socket>> active_sockets_;
    bool is_live_;
    opentelemetry::nostd::shared_ptr<opentelemetry::logs::Logger> logger_;
    boost::asio::steady_timer stats_timer_;
    boost::asio::steady_timer read_timer_;
    char buffer_[128 * 1024];
    int count_;
    size_t size_;
    std::string last_message_;
    std::mutex mutex_;  // guard active_sockets_, all its sockets, acceptor_.
};

} // namespace Exporters
} // namespace M365
} // namespace Microsoft