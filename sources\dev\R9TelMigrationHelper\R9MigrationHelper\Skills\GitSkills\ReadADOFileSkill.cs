﻿// <copyright file="ReadADOFileSkill.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.Diagnostics.CodeAnalysis;
using System.IO;
using System.Linq;
using System.Net.Http.Formatting;
using System.Text;
using System.Threading.Tasks;
using System.Web;
using System.Xml;
using System.Xml.Linq;
using Kusto.Cloud.Platform.Utils;
using Microsoft.Azure.Pipelines.WebApi;
using Microsoft.Identity.Client;
using Microsoft.TeamFoundation.Build.WebApi;
using Microsoft.TeamFoundation.SourceControl.WebApi;
using Microsoft.VisualStudio.Services.Client;
using Microsoft.VisualStudio.Services.Common;
using Microsoft.VisualStudio.Services.Identity;
using Microsoft.VisualStudio.Services.Organization.Client;
using Microsoft.VisualStudio.Services.WebApi;
using NuGet.Versioning;
using static Microsoft.VisualStudio.Services.Graph.GraphResourceIds;

namespace R9MigrationHelper.Skills.GitSkills
{
    /// <summary>
    /// ReadADOFileSkill.
    /// </summary>
    [ExcludeFromCodeCoverage]
    public class ReadADOFileSkill
    {
        private AuthenticationResult? authResult = null;

        private GitHttpClient? gitClient = null;

        private async Task RefreshAuthResult()
        {
            if (this.authResult == null || DateTime.UtcNow.AddMinutes(5) >= this.authResult.ExpiresOn)
            {
                string[] scopes = new string[] { "499b84ac-1321-427f-aa17-267ca6975798/user_impersonation" }; //Constant value to target Azure DevOps. Do not change

                // Initialize the MSAL library by building a public client application
                IPublicClientApplication application = PublicClientApplicationBuilder.Create("872cd9fa-d31f-45e0-9eab-6e460a02d1f1")
                                           .WithAuthority("https://login.microsoftonline.com/72f988bf-86f1-41af-91ab-2d7cd011db47/v2.0")
                                           .WithDefaultRedirectUri()
                                           .Build();

                try
                {
                    var accounts = await application.GetAccountsAsync().ConfigureAwait(true);
                    this.authResult = await application.AcquireTokenSilent(scopes, accounts.FirstOrDefault())
                            .ExecuteAsync().ConfigureAwait(true);
                }
                catch (MsalUiRequiredException)
                {
                    this.authResult = await application.AcquireTokenByIntegratedWindowsAuth(scopes).ExecuteAsync().ConfigureAwait(true);
                }
            }
        }

        /// <summary>
        /// GetGitClient
        /// </summary>
        /// <param name="organization"></param>
        /// <returns>A <see cref="Task{TResult}"/> representing the result of the asynchronous operation.</returns>
        public async Task<GitHttpClient> GetGitClient(string organization)
        {
            if (this.authResult == null || DateTime.UtcNow.AddMinutes(5) >= this.authResult.ExpiresOn)
            {
                if (this.gitClient != null)
                {
                    this.gitClient.Dispose();
                }
                await RefreshAuthResult().ConfigureAwait(true);
                var organizationUrl = $"https://dev.azure.com/{HttpUtility.UrlEncode(organization)}";
                VssCredentials creds = new VssAadCredential(new VssAadToken("Bearer", this.authResult?.AccessToken));
                VssConnection connection = new VssConnection(new Uri(organizationUrl), creds);
                this.gitClient = connection.GetClient<GitHttpClient>();
            }

#pragma warning disable CS8603 // Possible null reference return.
            return this.gitClient;
#pragma warning restore CS8603 // Possible null reference return.
        }

        /// <summary>
        /// Read service config file
        /// </summary>
        /// <param name="organization">Organization, ex. o365exchange</param>
        /// <param name="project">Project, ex. O365 Core</param>
        /// <param name="repository">Repository, ex. Substrate</param>
        /// <param name="configPath">Config path, ex. /sources/dev/services/src/Shadow/Service/Web.config</param>
        public async Task<string> ReadADOFile(string organization, string project, string repository, string configPath)
        {
            Console.WriteLine($"Reading {configPath} of {organization}, {project}, {repository}");

            GitHttpClient gitClient = await GetGitClient(organization);
            GitRepository repo = await gitClient.GetRepositoryAsync(project, repository);
            Guid repoId = repo.Id;

            var organizationUrl = $"https://dev.azure.com/{HttpUtility.UrlEncode(organization)}";

            //Console.WriteLine($"organization: {organization}, project: {project}, repository: {repository}, pat:******, organizationUrl: {organizationUrl}.");
            Stream fileStream = await gitClient.GetItemContentAsync(repoId, configPath, download: true, versionDescriptor: new GitVersionDescriptor { VersionType = GitVersionType.Branch, Version = repo.DefaultBranch.Replace("refs/heads/", string.Empty, StringComparison.Ordinal) });

            // Read the content of the file as a string
            using (StreamReader reader = new StreamReader(fileStream))
            {
                return await reader.ReadToEndAsync();
            }
        }

        /// <summary>
        /// ListXmlDropFiles
        /// </summary>
        /// <param name="organization"></param>
        /// <param name="project"></param>
        /// <param name="repository"></param>
        /// <returns>A <see cref="Task{TResult}"/> representing the result of the asynchronous operation.</returns>
        public async Task<List<string>> ListXmlDropFiles(string organization, string project, string repository)
        {
            Console.WriteLine($"Listing XmlDrop files of {organization}, {project}, {repository}");

            GitHttpClient gitClient = await GetGitClient(organization).ConfigureAwait(true);
            GitRepository repo = await gitClient.GetRepositoryAsync(project, repository).ConfigureAwait(true);
            List<string> xmlDropFiles = new List<string>();

            var items = await gitClient.GetItemsAsync(repo.Id, recursionLevel: VersionControlRecursionType.Full).ConfigureAwait(true);
            foreach (var item in items)
            {
                string path = item.Path;
                if (!item.IsFolder && path.EndsWith("XmlDrop.xml", StringComparison.OrdinalIgnoreCase))
                {
                    xmlDropFiles.Add(path);
                    Console.WriteLine($"Found XmlDrop file: {path}");
                }
            }
            Console.WriteLine($"Found {xmlDropFiles.Count} XmlDrop files");
            return xmlDropFiles;
        }

        /// <summary>
        ///
        /// </summary>
        /// <param name="organization"></param>
        /// <param name="project"></param>
        /// <param name="repository"></param>
        /// <returns>A <see cref="Task{TResult}"/> representing the result of the asynchronous operation.</returns>
        public async Task GetFullPathDllList(string organization, string project, string repository)
        {
            List<string> xmlDropFiles = await ListXmlDropFiles(organization, project, repository).ConfigureAwait(true);

            var csv = new StringBuilder();
            int count = 0;
            foreach (var file in xmlDropFiles)
            {
                string xmlDropContent = await ReadADOFile(organization, project, repository, file).ConfigureAwait(true);
                XmlDocument doc = new XmlDocument();
                doc.LoadXml(xmlDropContent);
                var nodes = doc.SelectNodes("ROOT/FILENUPKG/DELIVERABLES_SET");
                if (nodes == null || nodes.Count == 0)
                {
                    Console.WriteLine($"No FILENUPKG node found in {file}");
                    count++;
                    Console.WriteLine($"Processed {count}/{xmlDropFiles.Count} XmlDrop files");
                    continue;
                }

                foreach (XmlNode node in nodes)
                {
#pragma warning disable CS8602 // Dereference of a possibly null reference.
                    string filename = node.ParentNode.Attributes["filename"].Value;
#pragma warning restore CS8602 // Dereference of a possibly null reference.

                    foreach (XmlNode xmlNode in node.ChildNodes)
                    {
                        string root = xmlNode.Name;
                        var destNodes = xmlNode.SelectNodes("DESTINATIONS/DEST");
                        if (destNodes == null || destNodes.Count == 0)
                        {
                            Console.WriteLine($"No DESTINATIONS/DEST node found in {file}, {filename}, {root}");
                            continue;
                        }
                        foreach (XmlNode destNode in destNodes)
                        {
                            string dest = destNode.InnerText;
                            string key = $"{root} + {dest}";
                            string newLine = $"{filename},{root},{dest}";
                            csv.AppendLine(newLine);
                        }
                    }
                }
                count++;
                Console.WriteLine($"Processed {count}/{xmlDropFiles.Count} XmlDrop files");
            }
            File.WriteAllText("xmldrop.csv", csv.ToString());
        }

        /// <summary>
        /// ValidateXml
        /// </summary>
        public void ValidateXml()
        {
            XmlDocument manualXml = new XmlDocument();
            XmlDocument autoXml = new XmlDocument();
            Dictionary<string, string> dict = new Dictionary<string, string>();

            manualXml.Load("D:\\repos\\SOTELS\\SOTELS\\sources\\dev\\PackageUpgradeCopilot\\XMLDropCopilot\\XmlDropManual.xml");
            autoXml.Load("D:\\repos\\SOTELS\\SOTELS\\sources\\dev\\PackageUpgradeCopilot\\XMLDropCopilot\\XmlDropAuto.xml");

            var autoNodes = autoXml.SelectNodes("ROOT/FILENUPKG");

#pragma warning disable CS8602 // Dereference of a possibly null reference.
            foreach (XmlNode autoNode in autoNodes)
            {
                string filename = autoNode.Attributes["filename"].Value;
                string packagename = autoNode.Attributes["packagename"].Value;
                if (!dict.ContainsKey(filename))
                {
                    dict.Add(filename, packagename);
                }
                else
                {
                    Console.WriteLine($"duplicate in autoXml: {filename}");
                }
            }

            var manualNodes = manualXml.SelectNodes("ROOT/FILENUPKG");
            foreach (XmlNode manNode in manualNodes)
            {
                string filename = manNode.Attributes["filename"].Value;
                string packagename = manNode.Attributes["packagename"].Value;
                if (!dict.ContainsKey(filename))
                {
                    Console.WriteLine($"miss in autoXml: {filename}");
                }
                else
                {
                    if (dict[filename] != packagename)
                    {
                        Console.WriteLine($"packagename mismatch in autoXml: {filename},   {packagename}:{dict[filename]}");
                    }
                }
            }
#pragma warning restore CS8602 // Dereference of a possibly null reference.
        }
    }
}
