{"Microsoft_m365_core_telemetry": {"ServiceMetadata": {"ServiceName": "PassiveValidation2", "RuntimeModel": "ModelA"}, "Tracing": {"ScehmaVersion": "V1", "ActivitySources": ["TA"], "HttpTracing": {"IsEnabled": true, "RedactionStrategyType": "<PERSON><PERSON><PERSON>"}, "HttpClientTracing": {"IsEnabled": true, "RedactionStrategyType": "<PERSON><PERSON><PERSON>"}, "ConsoleTracingExporter": {"IsEnabled": true}, "GenevaTraceExporter": {"ConnectionString": "EtwSession=O365OpenTelemetrySession", "TableNameMappings": {"Span": "SampleSpan"}}, "TracingSamplerAndEnabled": [{"DeployRing": "Unknown", "R9DTEnabled": true, "SamplerType": "AlwaysOn"}, {"DeployRing": "SDFV2,MSIT", "R9DTEnabled": false, "SamplerType": "ParentBased", "ParentRootSamplerType": "RatioBased", "TraceSampleRate": 0.1}]}}, "R9": {"Logging": {"GenevaLogging": {"ConnectionString": "EtwSession=o365PassiveMonitoringSessionR9", "TableNameMappings": {"Microsoft.M365.Core.PassiveValidation.SampleEvent": "SampleEventR9", "*": "PassiveR9"}}}, "Metering": {"GenevaMetering": {"Protocol": "Etw", "MonitoringAccount": "PassveValidation", "MonitoringNamespace": "PassveValidationR9"}}}}