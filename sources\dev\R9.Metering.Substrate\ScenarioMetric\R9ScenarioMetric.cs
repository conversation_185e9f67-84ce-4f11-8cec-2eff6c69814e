﻿// <copyright file="R9ScenarioMetric.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System.Diagnostics.Metrics;
using Microsoft.Extensions.Diagnostics.Metrics;

namespace Microsoft.M365.Core.Telemetry.R9.Metering.Substrate
{
    /// <summary>
    /// Provides methods to create and manage scenario metrics.
    /// </summary>
    public static partial class R9ScenarioMetric
    {
        /// <summary>
        /// Creates a latency metric for a given meter.
        /// </summary>
        /// <param name="meter">The meter to create the latency metric for.</param>
        /// <returns>A latency metric instance.</returns>
        [Histogram(typeof(ScenarioDimension))]
        public static partial LatencyMetric CreateLatencyMetric(this Meter meter);

        /// <summary>
        /// Creates an availability metric for a given meter.
        /// </summary>
        /// <param name="meter">The meter to create the availability metric for.</param>
        /// <returns>An availability metric instance.</returns>
        [Histogram(typeof(ScenarioDimension))]
        public static partial AvailabilityMetric CreateAvailabilityMetric(this Meter meter);

        /// <summary>
        /// Creates an error metric for a given meter.
        /// </summary>
        /// <param name="meter">The meter to create the error metric for.</param>
        /// <returns>An error metric instance.</returns>
        [Counter(typeof(ErrorScenario))]
        public static partial ErrorMetric CreateErrorMetric(this Meter meter);
    }
}
