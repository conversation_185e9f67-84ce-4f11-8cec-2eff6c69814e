﻿// <copyright file="DyeBasedController.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System.Diagnostics;
using Microsoft.AspNetCore.Mvc;

namespace Microsoft.M365.Core.Tracing.AdvancedSamplingClient
{
    /// <summary>
    /// dye based controller
    /// </summary>
    [ApiController]
    [Route("api/")]
    public class DyeBasedController : ControllerBase
    {
        ActivitySource source = new ActivitySource("DyeBasedActivitySource");

        /// <summary>
        /// client
        /// </summary>
        /// <returns></returns>
        [HttpGet("dyeclientFirst")]
        public async Task<IActionResult> Client()
        {
            {
                var url = "https://localhost:5001/api/dyeclientSecond";

                var client = new HttpClient();
                await client.GetAsync(url).ConfigureAwait(true);
            }
                
            return Ok("DyeBasedController::First");
        }

        /// <summary>
        /// Baggage
        /// </summary>
        /// <returns></returns>
        [HttpGet("dyeclientSecond")]
        public async Task<IActionResult> DyeclientSecond()
        {
            var tags = new List<KeyValuePair<string, object?>>
            {
                new KeyValuePair<string, object?>("SampleKey", "SampleVal")
            };

            var id = Activity.Current?.Id;

            using (var activity = source.StartActivity("dyeclientSecond", ActivityKind.Internal, id == null ? "test" : id, tags))
            {
                var url = "https://localhost:5001/api/dyeclientThird";

                var client = new HttpClient();
                await client.GetAsync(url).ConfigureAwait(true);
            }

            return Ok("DyeBasedController::Second");
        }

        /// <summary>
        /// internal
        /// </summary>
        /// <returns></returns>
        [HttpGet("dyeclientThird")]
        public async Task<IActionResult> InternalAsync()
        {
            var url = "https://localhost:5002/api/dyeserverFirst";

            var client = new HttpClient();
            await client.GetAsync(url).ConfigureAwait(true);
            return Ok("value");
        }
    }
}
