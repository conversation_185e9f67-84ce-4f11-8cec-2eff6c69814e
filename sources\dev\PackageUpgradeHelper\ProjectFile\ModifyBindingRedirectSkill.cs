﻿// <copyright file="ModifyBindingRedirectSkill.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml;
using System.Xml.Linq;
using PackageUpgradeHelper.ADO;

namespace PackageUpgradeHelper.ProjectFile
{
    /// <summary>
    /// Modify binding redirect
    /// </summary>
    class ModifyBindingRedirectSkill
    {
        /// <summary>
        /// Update assembly version
        /// This api can be covered by SubstrateCLI tool:
        /// substrate-cli bindingredirects update by-package --package-name <package-name> --target-framework <target-framework>
        /// </summary>
        /// <param name="fileInfo"></param>
        /// <param name="assemblyToNewVersion"></param>
        /// <param name="branch"></param>
        public string UpdateAssemblyVersion(ADOFileInfo fileInfo, Dictionary<string, string> assemblyToNewVersion, string branch)
        {
            var getFileContentSkill = new GetFileContentSkill();
            var content = getFileContentSkill.GetFileContent(fileInfo.Path, branch).Result;
            var xml = new XmlDocument();
            xml.LoadXml(content);
            var root = xml.SelectSingleNode("configuration");
            if (root != null)
            {
                var runtime = root.SelectSingleNode("runtime");
                if (runtime != null)
                {
                    var nsManager = new XmlNamespaceManager(xml.NameTable);
                    nsManager.AddNamespace("ns", "urn:schemas-microsoft-com:asm.v1");
                    var assemblyBinding = runtime.SelectSingleNode("ns:assemblyBinding", nsManager);
                    if (assemblyBinding != null)
                    {
                        foreach ((var assemblyName, var newVersion) in assemblyToNewVersion)
                        {
                            foreach (XmlNode node in assemblyBinding.ChildNodes)
                            {
                                if (node.Name.Equals("dependentAssembly"))
                                {
                                    var assemblyIdentity = node.SelectSingleNode("ns:assemblyIdentity", nsManager);
                                    if (assemblyIdentity != null && assemblyIdentity.Attributes!["name"]!.Value == assemblyName)
                                    {
                                        var bindingRedirect = node.SelectSingleNode("ns:bindingRedirect", nsManager);
                                        var oldVersion = bindingRedirect!.Attributes!["oldVersion"]!.Value;
                                        var str = oldVersion.Substring(0, oldVersion.IndexOf('-'));
                                        bindingRedirect.Attributes["oldVersion"]!.Value = $"{oldVersion.Substring(0, oldVersion.IndexOf('-') + 1)}{newVersion}";
                                        bindingRedirect.Attributes["newVersion"]!.Value = newVersion;
                                    }
                                }
                            }
                        }
                    }
                }
            }
            return XDocument.Parse(xml.OuterXml).ToString();
        }

        /// <summary>
        /// Add assembly binding redirect
        /// </summary>
        /// <param name="fileInfo"></param>
        /// <param name="bindingRedirectXMLList"></param>
        /// <param name="branch"></param>
        /// <returns></returns>
        public string AddAssemblyBindingRedirect(ADOFileInfo fileInfo, List<string> bindingRedirectXMLList, string branch)
        {
            var getFileContentSkill = new GetFileContentSkill();
            var content = getFileContentSkill.GetFileContent(fileInfo.Path, branch).Result;
            var xml = new XmlDocument();
            xml.LoadXml(content);
            var root = xml.SelectSingleNode("configuration");
            if (root != null)
            {
                var runtime = root.SelectSingleNode("runtime");
                if (runtime != null)
                {
                    var nsManager = new XmlNamespaceManager(xml.NameTable);
                    nsManager.AddNamespace("ns", "urn:schemas-microsoft-com:asm.v1");
                    var assemblyBinding = runtime.SelectSingleNode("ns:assemblyBinding", nsManager);
                    if (assemblyBinding != null)
                    {
                        foreach (var bindingRedirectXML in bindingRedirectXMLList)
                        {
                            var newBindingRedirect = new XmlDocument();
                            newBindingRedirect.LoadXml(bindingRedirectXML);
                            var newNode = xml.ImportNode(newBindingRedirect.DocumentElement!, true);
                            assemblyBinding.AppendChild(newNode);
                        }
                    }
                }
            }
            return XDocument.Parse(xml.OuterXml).ToString();
        }
    }
}
