﻿// <copyright file="ODLNRTRequestPoolFactory.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System.Diagnostics.CodeAnalysis;
using Microsoft.Extensions.ObjectPool;
using Microsoft.Office.BigData.DataLoader;

namespace Microsoft.M365.Core.Telemetry.Exporters.ODLTCP
{
    /// <summary>
    /// A factory of object pools for ODLNRTRequest related
    /// </summary>
    /// <remarks>
    /// This class makes it easy to create efficient object pools used to improve performance by reducing
    /// strain on the garbage collector.
    /// </remarks>
    [ExcludeFromCodeCoverage]
    public static class ODLNRTRequestPoolFactory
    {
        private static PooledObjectPolicy<ODLNRTCommonMessage> defaultPooledPolicyForODLNRTCommonMessage = new DefaultPooledObjectPolicy<ODLNRTCommonMessage>();

        private static PooledObjectPolicy<ODLNRTRequest> defaultPooledPolicyForODLNRTRequest = new ODLNRTRequestPooledObjectPolicy();

        /// <summary>
        /// Creates a pool of <see cref="ODLNRTRequest"/> instances.
        /// </summary>
        /// <param name="maxRetained">the max number of retained object</param>
        /// <returns>The pool.</returns>
        public static ObjectPool<ODLNRTRequest> CreateODLNRTRequestPool(int maxRetained)
        {
            return MakePool(defaultPooledPolicyForODLNRTRequest, maxRetained);
        }

        /// <summary>
        /// Creates a pool of <see cref="ODLNRTMessage"/> instances.
        /// </summary>
        /// <param name="maxRetained">the max number of retained object</param>
        /// <returns>The pool.</returns>
        public static ObjectPool<ODLNRTCommonMessage> CreateODLNRTMessagePool(int maxRetained)
        {
            return MakePool(defaultPooledPolicyForODLNRTCommonMessage, maxRetained);
        }

        private static DefaultObjectPool<T> MakePool<T>(IPooledObjectPolicy<T> policy, int maxRetained)
        where T : class
        => new DefaultObjectPool<T>(policy, maxRetained);
    }
}
