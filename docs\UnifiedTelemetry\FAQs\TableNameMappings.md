# How to set TableNameMappings for Geneva Log Exporter

## What is TableNameMappings doing

In simple words, it sends the logs to specific Geneva Events.

Each log record has a [Category](https://learn.microsoft.com/en-us/dotnet/core/extensions/logging?tabs=command-line#log-category), it comes from the ILogger who sends it.
Category is specified when the ILogger is created.
Geneva Exporter will check the Category, and decides where the logs should go according to `TableNameMappings`.

See this sample:
```json
{
    "TableNameMappings": {
        "MyLogger": "MyEvent"
    }
}
```

It means that the logs with Category `MyLogger` are routed to the Geneva Event `MyEvent`, while other logs are sent to `DefaultEvent`.
If a log's category does not match any key in this mapping, then it will be **dropped**.

It's impossible to send 2 different Events from a single logger, because all logs from one logger will only match one routing rule (defined in `TableNameMappings`), which designate them a single Geneva Event.

By contrast, you can configure 2 loggers sending logs to the same event. But make sure that logs from different loggers share the same schema and are intended be managed together.
A simple example:

```json
{
    "TableNameMappings": {
        "MyLogger": "MyEvent",
        "AnotherLogger": "MyEvent"
    }
}
```

It supports configuraing a pass-through rule (`*`), which allow all un-matched rules to be routed to a default event.
It looks like:

```json
{
    "TableNameMappings": {
        "*": "DefaultEvent"
    }
}
```

> [!Tip]
> If you want to know more detail, please check [the document from Open Telemetry](https://github.com/open-telemetry/opentelemetry-dotnet-contrib/blob/main/src/OpenTelemetry.Exporter.Geneva/README.md#log-table-name-mappings)

## What is the Category of my Logger (key of TableNameMappings)

Generally, we have 2 ways to create ILogger:
1. An injected `ILogger<T>`.
Its category will be the full name of `T`.
In this sample, category is `Sample.Foo.MyService`.

```csharp
namespace Sample.Foo
{
    // Option 1: inject ILogger.
    public class MyService {
        public MyService(ILogger<MyService> logger)
        {
            _logger = logger;
        }
    }
```

2. Created with `ILoggerFactory.CreateLogger()`.
In this case, category can be specified by passing a string.
In this sample, category is `MyServiceLogger`.

```csharp
namespace Sample.Foo
{
    // Option 2: Create from ILoggerFactory.
    public class MyService {
        public MyService(ILoggerFactory loggerFactory)
        {
            _logger = loggerFactory.CreateLogger("MyServiceLogger");
        }
    }
}
```

## How should I set the destination Event (value of TableNameMappings)

This event name should align with the Event name you set in MDS config XML (managed by [M365 Passive Monitoring Team](mailto:<EMAIL>)).

You should have a section like:
```xml
<OneDSProviders>
    <OneDSProvider name="o365PassiveMonitoringSessionR9" storeType="CentralBond">
    <Event eventName="PassiveR9Event" />
    </OneDSProvider>
</OneDSProviders>
```

It specifies an `eventName` with value `PassiveR9Event`, which should align with the destinaion of `TableNameMappings` (or the value of this dictionary) to make sure this event can be collected by Geneva Agent.

A corresponding `TableNameMappings` looks like:
```json
{
    "GenevaExporter":
    {
        "TableNameMappings":{
            "*": "PassiveR9Event"
        }
    }
}
```

> [!Tip]
> You can find some sample and tutorial on [Set up Geneva on a TDS or dev machine | M365 Passive Monitoring](https://eng.ms/docs/experiences-devices/m365-core/o365-substrate-fabric-extended-suzhou/o365-telemetry/m365-passive-monitoring/m365-passive-monitoring/geneva/tds-setup#test-mds-event-warm-path-only).
