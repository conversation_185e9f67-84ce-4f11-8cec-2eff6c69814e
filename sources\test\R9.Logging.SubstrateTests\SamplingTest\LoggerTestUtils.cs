// <copyright file="LoggerTestUtils.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.IO;
using System.Text;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.M365.Core.Telemetry.R9.Logging.Substrate.Sampler;
using OpenTelemetry.Logs;

namespace Microsoft.M365.Core.Telemetry.R9.Logging.Substrate.Test.SamplingTest
{
    public static class LoggerTestUtils
    {
        public static ILogger CreateLoggerWithConfig(string configJson, List<LogRecord> exportedItems, string loggerName)
        {
            IConfiguration configuration = new ConfigurationBuilder()
                .AddJsonStream(new MemoryStream(Encoding.UTF8.GetBytes(configJson)))
                .Build();

            var factory = LoggerFactory.Create(builder =>
            {
                builder
                    .SetMinimumLevel(LogLevel.Trace)
                    .AddRuleBasedSampler(configuration.GetSection("SubstrateLogging"))
                    .AddOpenTelemetry(options =>
                    {
                        options.AddInMemoryExporter(exportedItems);
                    });
            });

            return factory.CreateLogger(loggerName);
        }
    }
} 