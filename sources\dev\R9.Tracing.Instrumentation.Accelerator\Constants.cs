﻿// <copyright file="Constants.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

namespace Microsoft.M365.Core.Telemetry.R9.Tracing.Instrumentation.Accelerator
{
    /// <summary>
    /// Constants variables
    /// </summary>
    internal static class Constants
    {
        /// <summary>
        /// Namespace that ECS Dynamic package is using
        /// For compatible with the ECS Dynamic package in the short term
        /// </summary>
        private const string NameSapce = "Microsoft_m365_core_telemetry";

        /// <summary>
        /// Section key that ECS Dynamic package is using
        /// For compatible with the ECS Dynamic package in the short term
        /// </summary>
        private const string ServiceMetaDataConfiguration = NameSapce + ":ServiceMetaData";

        /// <summary>
        /// Cosmic running model value
        /// </summary>
        internal const string Cosmic = "Cosmic";

        /// <summary>
        /// Used for activity tag value when the value is missing
        /// </summary>
        internal const string Missing = "Missing";

        /// <summary>
        /// Section key that ECS Dynamic package is using
        /// For compatible with the ECS Dynamic package in the short term
        /// </summary>
        internal const string ServiceName = ServiceMetaDataConfiguration + ":ServiceName";

        /// <summary>
        /// Section key that ECS Dynamic package is using
        /// For compatible with the ECS Dynamic package in the short term
        /// </summary>
        internal const string RuntimeModel = ServiceMetaDataConfiguration + ":RuntimeModel";

        /// <summary>
        /// Cloud Role key
        /// </summary>
        internal const string CloudRole = "env_cloud_role";

        /// <summary>
        /// Cloud Role Instance key
        /// </summary>
        internal const string CloudRoleInstance = "env_cloud_roleInstance";

        /// <summary>
        /// Http url key
        /// </summary>
        internal const string HttpUrl = "http.url";

        /// <summary>
        /// Http url backup key
        /// </summary>
        internal const string HttpUrlBackup = "http.url.backup";

        /// <summary>
        /// Http route key
        /// </summary>
        internal const string HttpRoute = "http.route";

        /// <summary>
        /// Http target key
        /// </summary>
        internal const string HttpTarget = "http.target";

        /// <summary>
        /// On start activity event
        /// </summary>
        internal const string OnStartActivity = "OnStartActivity";

        /// <summary>
        /// Redacted placeholder
        /// </summary>
        internal const string RedactedPlacholder = "Redacted";

        /// <summary>
        /// Default Geneva Trace Exporter Connection String
        /// </summary>
        internal const string DefaultGenevaTraceExporterConnectionString = "EtwSession=O365OpenTelemetrySession";

        /// <summary>
        /// Build version
        /// </summary>
        internal const string BuildVersion = "BuildVersion";

        /// <summary>
        /// Default table name
        /// </summary>
        internal const string DefaultTableName = "Span";

        /// <summary>
        /// HttpPath
        /// </summary>
        internal const string HttpPath = "http.path";

        /// <summary>
        /// GenevaTraceExporterTraceIdBasedSampleRatio
        /// </summary>
        internal const double GenevaTraceExporterTraceIdBasedSampleRatio = 0.1;

        /// <summary>
        /// R9Enabled
        /// </summary>
        internal const string R9DTEnabled = "R9DTEnabled";

        /// <summary>
        /// TracingSamplerAndEnableOptions
        /// </summary>
        internal const string TracingSamplerAndEnableOptions = "TracingSamplerAndEnableOptions";
    }
}
