﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net6.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <NoWarn>CA2227,CA1822</NoWarn>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="System.IdentityModel.Tokens.Jwt" VersionOverride="8.1.2" />
    <PackageReference Include="Microsoft.IdentityModel.JsonWebTokens" VersionOverride="8.1.2" />
    <PackageReference Include="Azure.AI.OpenAI" VersionOverride="1.0.0-beta.8" />
    <PackageReference Include="Azure.Identity" VersionOverride="1.12.0" />
    <PackageReference Include="Azure.Security.KeyVault.Secrets" VersionOverride="4.5.0" />
    <PackageReference Include="Microsoft.Azure.Kusto.Data" VersionOverride="13.0.1"/>
    <PackageReference Include="Microsoft.Extensions.Logging" VersionOverride="7.0.0" />
    <PackageReference Include="Microsoft.Identity.Client" VersionOverride="4.68.0"/>
    <PackageReference Include="Microsoft.TeamFoundationServer.Client" VersionOverride="19.220.0-preview" />
    <PackageReference Include="Microsoft.VisualStudio.Services.Client" VersionOverride="19.220.0-preview" />
    <PackageReference Include="Microsoft.VisualStudio.Services.InteractiveClient" VersionOverride="19.220.0-preview" />
    <PackageReference Include="NuGet.Packaging" VersionOverride="6.12.1" />
    <PackageReference Include="System.Data.SqlClient" VersionOverride="4.9.0"/>
    <PackageReference Include="System.Text.Json" VersionOverride="8.0.5" />
  </ItemGroup>

  <ItemGroup>
    <Content Include="manifest.json">
	    <CopyToOutputDirectory>Always</CopyToOutputDirectory>	
	  </Content>
    <Content Include="SelectedProjConfigFiles.txt">
	    <CopyToOutputDirectory>Always</CopyToOutputDirectory>	
	  </Content>		
  </ItemGroup>

</Project>