// <copyright file="NrtTcpClientThread.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System.Collections.Concurrent;
using System.Diagnostics.CodeAnalysis;
using System.Net.Security;
using System.Net.Sockets;
using System.Security.Cryptography.X509Certificates;
using Microsoft.Office.BigData.DataLoader;

namespace Microsoft.M365.ODL.NrtTcpClient
{
    /// <summary>
    /// The thread of nrt tcp client for sending data
    /// </summary>
    [ExcludeFromCodeCoverage]
    public sealed class NrtTcpClientThread : IDisposable
    {
        private static IOdlLogger logger = OdlLogger.Instance;

        /// <summary>
        /// if current env is NPE
        /// </summary>
        private bool ignoreAMEIssuerCheck = false;

        /// <summary>
        /// swtich for tls auth
        /// </summary>
        private bool enableTlsAuth = false;

        /// <summary>
        /// tls cert collection
        /// </summary>
        private X509Certificate2Collection x509Certificate2s;

        /// <summary>
        /// tls server ca cert
        /// </summary>
        private X509Certificate2 caCertificate;

        /// <summary>
        /// The white list for tls cert name to ignore RemoteNameMissmatch error
        /// </summary>
        private HashSet<string> tlsCertWhiteList = new HashSet<string>();

        /// <summary>
        /// default timeout milliseconds for stopping thread
        /// </summary>
        public const int DefaultStopTimeoutMilliSec = 1500;

        /// <summary>
        /// Default size of read/write buffer,128KB
        /// </summary>
        public const int DefaultBufferSize = 32 * 4 * 1024;

        /// <summary>
        /// max size of read/write buffer,4MB
        /// </summary>
        public const int MaxBufferSize = 4 * 1024 * 1024;

        /// <summary>
        /// mutex for updating critical status
        /// </summary>
        private readonly object mutex = new object();

        /// <summary>
        /// The Tcp client with connection to the TCP-NRT server
        /// </summary>
        private volatile TcpClient tcpClient;

        /// <summary>
        /// TCP stream
        /// </summary>
        private volatile Stream currentStream;

        /// <summary>
        /// Reusable memory stream for Network I/O
        /// </summary>
        private MemoryStream memoryStream = new MemoryStream();

        /// <summary>
        /// Internal thread
        /// </summary>
        private Thread thread;

        /// <summary>
        /// The size of the read/write buffer
        /// </summary>
        private int bufferSize = DefaultBufferSize;

        /// <summary>
        /// The buffer for read/write
        /// </summary>
        private byte[] buffer;

        /// <summary>
        /// Message queue for asynchronous data sending
        /// The second Kvp stores message count for each sdksource for telemetry when fallback
        /// </summary>
        private BlockingCollection<KeyValuePair<ODLNRTRequest, Dictionary<string, long>>> messageQueue;

        /// <summary>
        /// whether thread is interrupted to be stopped
        /// </summary>
        private volatile bool interrupted = false;

        /// <summary>
        /// timeout milliseconds for data sending
        /// </summary>
        private int sendTimeoutMilliSeconds;

        /// <summary>
        /// timeout milliseconds for stop thread
        /// </summary>
        private int stopTimeoutMilliSec = DefaultStopTimeoutMilliSec;

        /// <summary>
        /// the data send processors pipeline
        /// </summary>
        private ConcurrentQueue<ISendDataProcessor> sendDataProcessors;

        /// <summary>
        /// whether current TCP connection is healthy
        /// </summary>
        private volatile bool alive = false;

        /// <summary>
        /// Whether current thread obj is being disposed
        /// </summary>
        private volatile bool isDisposing = false;

        private Dictionary<string, List<string>> schemaToMessages = new Dictionary<string, List<string>>();

        /// <summary>
        /// The ctor
        /// </summary>
        /// <param name="ip">TCP-NRT server ip</param>
        /// <param name="port">TCP-NRT server port</param>
        /// <param name="messageQueue">the async message queue</param>
        /// <param name="timeoutMilliSeconds">the sending timeout in milli seconds</param>
        /// <param name="bufferSize">the read/write buffer size</param>
        /// <param name="enableTlsAuth">switch of tls auth enable</param>
        /// <param name="x509Certificate2s">the tls cert collection</param>
        /// <param name="caCertificate">the tls server ca cert</param>
        /// <param name="isNPE">if current env is NPE</param>
        /// <param name="tlsCertWhiteList">the tls cert whitelist to ignore RemoteCertNameMismatch error</param>
        public NrtTcpClientThread(string ip, int port, BlockingCollection<KeyValuePair<ODLNRTRequest, Dictionary<string, long>>> messageQueue = null,
            int timeoutMilliSeconds = TcpUtils.NeverTimeout,
            int bufferSize = DefaultBufferSize, bool enableTlsAuth = false, X509Certificate2Collection x509Certificate2s = null, X509Certificate2 caCertificate = null, bool isNPE = false, string tlsCertWhiteList = null)
        {
            ServerIP = ip;
            Port = port;

            this.messageQueue = messageQueue;
            this.sendTimeoutMilliSeconds = timeoutMilliSeconds;
            this.BufferSize = bufferSize;
            this.enableTlsAuth = enableTlsAuth;
            this.x509Certificate2s = x509Certificate2s;
            this.caCertificate = caCertificate;
            this.ignoreAMEIssuerCheck = isNPE;
            if (!string.IsNullOrEmpty(tlsCertWhiteList))
            {
                this.tlsCertWhiteList = new HashSet<string>(tlsCertWhiteList.Split(new char[] { ',' }, StringSplitOptions.RemoveEmptyEntries).Select(name => "CN=" + name));
            }
            thread = new Thread(Run);
        }

        /// <summary>
        /// Validate ClientCertificate for m-TLS auth.
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="certificate"></param>
        /// <param name="chain"></param>
        /// <param name="sslPolicyErrors"></param>
        /// <returns></returns>
        public bool ValidateServerCertificate(object sender, X509Certificate certificate,
            X509Chain chain,
            SslPolicyErrors sslPolicyErrors)
        {
            // need to check if the certificate is AME cert, CA should be start with "CN=AME Infra CA"
            // in Cosmic NPE env, AME issuer check could be ignored
            if (!(certificate.Issuer.StartsWith("CN=AME Infra CA", StringComparison.OrdinalIgnoreCase) || ignoreAMEIssuerCheck))
            {
                logger.Log(LogLevel.Error, LogEventId.TCPTlsAuthenticationError, $"Error: Remote Tls and CA certs must be AME cert, except in Cosmic NPE environment");
                return false;
            }

            if ((sslPolicyErrors & SslPolicyErrors.RemoteCertificateNotAvailable) != 0)
            {
                logger.Log(LogLevel.Error, LogEventId.TCPTlsAuthenticationError, $"Error: RemoteCertificateNotAvailable, please check server cert.");
                return false;
            }

            // Allow certs in white list to ignore RemoteCertificateNameMismatch error
            if ((sslPolicyErrors & SslPolicyErrors.RemoteCertificateNameMismatch) != 0)
            {
                if (tlsCertWhiteList.Contains(certificate.Subject))
                {
                    logger.Log(LogLevel.Warning, LogEventId.TCPTlsAuthenticationWarning, $"Warning: RemoteCertificateNameMismatch errors will be ignored.");
                    sslPolicyErrors &= ~SslPolicyErrors.RemoteCertificateNameMismatch;
                }
                else
                {
                    logger.Log(LogLevel.Error, LogEventId.TCPTlsAuthenticationError, $"Error: RemoteCertificateNameMismatch, DNS name doesn't match any SAN.");
                    return false;
                }
            }

            if (sslPolicyErrors == SslPolicyErrors.None)
            {
                return true;
            }

            if (caCertificate == null)
            {
                logger.Log(LogLevel.Error, LogEventId.TCPTlsAuthenticationError, $"Error: Cannot find remote CA cert, tls auth failed.");
                return false;
            }

            // Hanlde RemoteCertificateChain recheck for Self-signed cert
            if (certificate.Issuer != caCertificate.Subject)
            {
                logger.Log(LogLevel.Error, LogEventId.TCPTlsAuthenticationError, $"Error: Remote Tls and CA certs mismatch under certification path.");
                return false;
            }

            chain.ChainPolicy.ExtraStore.Add(caCertificate);
            chain.ChainPolicy.VerificationFlags = X509VerificationFlags.AllowUnknownCertificateAuthority;
            chain.ChainPolicy.VerificationTime = DateTime.Now;
#pragma warning disable IA5352 // Do Not Misuse Cryptographic APIs 
            chain.ChainPolicy.RevocationMode = X509RevocationMode.NoCheck;
#pragma warning restore IA5352 // Do Not Misuse Cryptographic APIs 
            chain.ChainPolicy.RevocationFlag = X509RevocationFlag.EntireChain;

            if (!chain.Build((X509Certificate2)certificate))
            {
                logger.Log(LogLevel.Error, LogEventId.TCPTlsAuthenticationError, $"Error: Remote Tls and CA certs are unable to build cert chain.");
                return false;
            }

            foreach (X509ChainStatus chainStatus in chain.ChainStatus)
            {
                if (chainStatus.Status != X509ChainStatusFlags.UntrustedRoot)
                {
                    logger.Log(LogLevel.Error, LogEventId.TCPTlsAuthenticationError, $"Error: found unaccepted remote cert chain status {chainStatus.Status}");
                    return false;
                }
            }

            return true;
        }

        /// <summary>
        /// HandleAuthenticationNegotiation if need M-TLS auth.
        /// </summary>
        public void HandleAuthenticationNegotiation()
        {
            if (!enableTlsAuth || ServerIP == OdlNrtTcpClient.Localhost)
            {
                return;
            }

            SslStream sslStream = new SslStream(tcpClient.GetStream(), false, new RemoteCertificateValidationCallback(ValidateServerCertificate));

            try
            {                
                sslStream.AuthenticateAsClient(ServerIP, x509Certificate2s, false);
                currentStream = sslStream;
            }
            catch (Exception e)
            {
                logger.Log(LogLevel.Error, LogEventId.TcpClientThreadError, $"Error: failed for m-TLS authentication: {e}");
                sslStream.Close();
                this.ReleaseTcpClient();
            }
        }

        /// <summary>
        /// try to connect to the remote with timeout, this should be invoked by Monitor only in most cases
        /// </summary>
        /// <param name="timeoutMilliSeconds">connect timeout millis sec</param>
        /// <returns>true if connect success</returns>
        public bool TryConnect(int timeoutMilliSeconds)
        {
            if (isDisposing) { return false; }

            lock (mutex)
            {
                try
                {
                    this.ReleaseTcpClient();

                    tcpClient = new TcpClient();
                    var connectAsync = tcpClient.ConnectAsync(ServerIP, Port);

                    if (!connectAsync.Wait(timeoutMilliSeconds) || connectAsync.IsFaulted || connectAsync.IsCanceled || !tcpClient.Connected)
                    {
                        this.ReleaseTcpClient();
                        return IsAlive = false;
                    }

                    this.currentStream = tcpClient.GetStream();
                    HandleAuthenticationNegotiation();

                    if (!this.IsCurrentStreamHealthy())
                    {
                        logger.Log(LogLevel.Error, LogEventId.TcpClientThreadError, $"Warning: fail to check currentStream,CanRead:{currentStream?.CanRead},CanWrite:{currentStream?.CanWrite},CanTimeout:{currentStream?.CanTimeout}");
                        this.ReleaseTcpClient();
                        return IsAlive = false;
                    }

                    // In some scenarios, post processing may be needed after connection established
                    bool afterConnect = true;

                    if (this.sendDataProcessors != null)
                    {
                        foreach (var processor in this.sendDataProcessors)
                        {
                            if (processor != null)
                            {
                                afterConnect = processor.AfterReconnect(this);
                                if (!afterConnect) { break; }
                            }
                        }
                    }

                    if (!afterConnect)
                    {
                        logger.Log(LogLevel.Error, LogEventId.TcpClientThreadError, "Warning: fail to process sendDataProcessor after connection success");
                        this.ReleaseTcpClient();
                    }
                    else
                    {
                        IsAlive = true;
                    }
                }
                catch (Exception e)
                {
                    logger.Log(LogLevel.Error, LogEventId.TcpClientThreadError, $"Error when trying to connect to NRT server:{e}");
                    this.ReleaseTcpClient();
                }
            }

            return IsAlive;
        }

        /// <summary>
        /// The read/write buffer size
        /// </summary>
        public int BufferSize
        {
            get => bufferSize;
            set
            {
                if (value < 0)
                {
                    value = DefaultBufferSize;
                }

                lock (mutex)
                {
                    if (value != bufferSize)
                    {
                        this.buffer = null;
                        value = Math.Min(value, MaxBufferSize);
                        bufferSize = value;
                    }
                }
            }
        }

        /// <summary>
        /// Server IP
        /// </summary>
        public string ServerIP
        {
            get;
            private set;
        }
        = OdlNrtTcpClient.Localhost;

        /// <summary>
        /// Whether current obj is being disposed
        /// </summary>
        public bool IsDisposing
        {
            get => isDisposing;
            private set
            {
                isDisposing = value;
            }
        }

        /// <summary>
        /// latest time that send statistical data
        /// </summary>
        public DateTime LastSendStatisticTime
        {
            get;
            set;
        }
        = DateTime.UtcNow;

        /// <summary>
        /// Maxmumim number of records for each send batch of statistics
        /// </summary>
        public int MaxRecordCountForStatic
        {
            get;
            set;
        }
        = 500;

        /// <summary>
        /// total count of record send in a time period
        /// </summary>
        public SumDataAggregator TotalRecordCount
        {
            get;
            private set;
        }
        = new SumDataAggregator();

        /// <summary>
        /// total count of record fallback to disk in a time period
        /// </summary>
        public SumDataAggregator FallbackRecordCount
        {
            get;
            private set;
        }
        = new SumDataAggregator();

        /// <summary>
        /// The max interval to send statistical data
        /// </summary>
        public TimeSpan SendStatisticsInterval
        {
            get;
            set;
        }
        = TimeSpan.FromMinutes(5);

        /// <summary>
        /// Gets or sets a value indicating whether to enable archived files clean up or not.
        /// </summary>
        /// <remarks>Default to true.</remarks>
        public bool EnableCleanUpArchivedFiles { get; set; } = true;

        /// <summary>
        /// Gets or sets the max retention <see cref="TimeSpan"/>.
        /// </summary>
        /// <remarks>Default to 1 day.</remarks>
        public TimeSpan RetentionInterval { get; set; } = TimeSpan.FromDays(1);

        /// <summary>
        /// Gets or sets the max retention count.
        /// </summary>
        /// <remarks>Default to 99 files.</remarks>
        public long RetentionCount { get; set; } = 99;

        /// <summary>
        /// whether current client is ever inited
        /// </summary>
        /// <returns>true if it has been inited</returns>
        public bool IsInit()
        {
            return IsCurrentStreamHealthy() && this.tcpClient != null && this.tcpClient.Connected;
        }

        /// <summary>
        /// IsCurrentStreamHealthy
        /// </summary>
        /// <returns></returns>
        public bool IsCurrentStreamHealthy()
        {
            return this.currentStream != null && this.currentStream.CanRead && this.currentStream.CanWrite && this.currentStream.CanTimeout;
        }

        /// <summary>
        /// Set SendDataProcessors
        /// </summary>
        /// <param name="sendDataProcessors"></param>
        public void SetSendDataProcessors(ConcurrentQueue<ISendDataProcessor> sendDataProcessors)
        {
            this.sendDataProcessors = sendDataProcessors;
        }

        /// <summary>
        /// wait timeout for stopping working thread
        /// </summary>
        public int TimeoutStopThread
        {
            get => stopTimeoutMilliSec;
            set
            {
                if (value < 0)
                {
                    stopTimeoutMilliSec = DefaultStopTimeoutMilliSec;
                }
                else
                {
                    stopTimeoutMilliSec = value;
                }
            }
        }

        /// <summary>
        /// Whether current TCP connect is avaliable
        /// </summary>
        public bool IsAlive
        {
            get => alive;
            set
            {
                alive = value;
            }
        }

        /// <summary>
        /// The TCP port
        /// </summary>
        public int Port
        {
            get;
            set;
        }

        /// <summary>
        /// start thread for data sending
        /// </summary>
        public void StartThread()
        {
            thread.Start();
        }

        /// <summary>
        /// Thread for data sending in async mode
        /// </summary>
        private void Run()
        {
            ODLNRTRequest message = null;
            Dictionary<string, long> messageCountPerSdksource = null;

            while (true)
            {
                if (interrupted || IsDisposing)
                {
                    logger.Log(LogLevel.Information, LogEventId.TcpClientCommonInfo, "Stop thread since it is being interupted or disposed");
                    this.CleanUpQueue();

                    return;
                }

                try
                {
                    if (null == this.messageQueue)
                    {
                        return;
                    }

                    message = null;
                    messageCountPerSdksource = null;

                    KeyValuePair<ODLNRTRequest, Dictionary<string, long>> kvp = this.messageQueue.Take();
                    message = kvp.Key;
                    messageCountPerSdksource = kvp.Value;
                }
                catch (Exception ex)
                {
                    if (TcpUtils.IsThreadStopException(ex))
                    {
                        logger.Log(LogLevel.Information, LogEventId.TcpClientCommonInfo, "Stop thread since it is being interupted or disposed");
                    }
                    else
                    {
                        logger.Log(LogLevel.Error, LogEventId.TcpClientThreadError, $"Unexpected exception while performing async sending in the thread:{ex}");
                    }

                    if (message != null)
                    {
                        FallbackToDisk(message, this.FallbackRecordCount, this.EnableCleanUpArchivedFiles, this.RetentionInterval, this.RetentionCount, this.schemaToMessages, messageCountPerSdksource);
                    }

                    continue;
                }

                try
                {
                    if (!IsAlive || !SendData(message))
                    {
                        FallbackToDisk(message, this.FallbackRecordCount, this.EnableCleanUpArchivedFiles, this.RetentionInterval, this.RetentionCount, this.schemaToMessages, messageCountPerSdksource);
                    }
                }
                catch
                {
                    FallbackToDisk(message, this.FallbackRecordCount, this.EnableCleanUpArchivedFiles, this.RetentionInterval, this.RetentionCount, this.schemaToMessages, messageCountPerSdksource);
                }
            }
        }

        private void CleanUpQueue()
        {
            if (this.IsDisposing && this.messageQueue != null)
            {
                while (this.messageQueue.Any())
                {
                    try
                    {
                        if (this.messageQueue.TryTake(out var messageBundle))
                        {
                            FallbackToDisk(messageBundle.Key, this.FallbackRecordCount, this.EnableCleanUpArchivedFiles, this.RetentionInterval, this.RetentionCount, this.schemaToMessages, messageBundle.Value);
                        }
                    }
                    catch { }
                }
            }
        }

        /// <summary>
        /// count and aggregate metric
        /// </summary>
        /// <param name="data"></param>
        /// <param name="aggregator"></param>
        /// <param name="nrtMessageCount"></param>
        public static void CountRecordAndAggregate(ODLNRTRequest data, SumDataAggregator aggregator, Dictionary<string, long> nrtMessageCount)
        {
            if (null == data || null == aggregator) { return; }

            try
            {
                if (data.Head?.CommandType == ODLNRTCommandType.OdlnrtcmdSecurityMessageBatch)
                {
                    var cnt = data.SecurityMessageBatchReq?.Messages?.Count ?? 0;

                    aggregator.Aggregate(cnt);
                    aggregator.AggregateKvp(nrtMessageCount);
                }

                if (data.Head?.CommandType == ODLNRTCommandType.OdlnrtcmdCommonMessageBatch)
                {
                    var cnt = data.CommonMessageBatchReq?.Messages?.Count ?? 0;
                    aggregator.Aggregate(cnt);
                    aggregator.AggregateKvp(nrtMessageCount);
                }
            }
            catch { }
        }

        /// <summary>
        /// Fall back the message to disk synchronously in batch
        /// </summary>
        /// <param name="message">the message</param>
        /// <param name="aggregator">the sum aggregator for fallback</param>
        /// <param name="enableCleanUpArchivedFiles"></param>
        /// <param name="retentionInterval"></param>
        /// <param name="retentionCount"></param>
        /// <param name="schemaToMessages">the dictionary buffer for mapping schema to messages</param>
        /// <param name="countPerSdksource">the fallback cout per sdksource</param>
        public static void FallbackToDisk(ODLNRTRequest message, SumDataAggregator aggregator, bool enableCleanUpArchivedFiles,
            TimeSpan retentionInterval, long retentionCount, Dictionary<string, List<string>> schemaToMessages = null, Dictionary<string, long> countPerSdksource = null)
        {
            try
            {
                switch (message.BodyCase)
                {
                    case ODLNRTRequest.BodyOneofCase.SecurityMessageBatchReq:
                        FallbackToDiskForSecurityTelemetry(message, aggregator, ref schemaToMessages, countPerSdksource);
                        break;
                    case ODLNRTRequest.BodyOneofCase.CommonMessageBatchReq:
                        FallbackToDiskForCommon(message, aggregator, ref schemaToMessages, countPerSdksource);
                        break;
                    default:
                        throw new ArgumentException($"Invalid body type of message {message}");
                }

                foreach (var schemaAndLines in schemaToMessages)
                {
                    var fileExporter = FileLoggerManager.GetNRTFileExporter(schemaAndLines.Key, enableCleanUpArchivedFiles, retentionInterval, retentionCount);
                    fileExporter.BatchExport(schemaAndLines.Value);
                }
            }
            catch (Exception ex)
            {
                logger.Log(LogLevel.Error, LogEventId.TcpClientThreadError, $"Exception while performing fallback to disk:{ex}");
            }
        }

        private static void FallbackToDiskForCommon(ODLNRTRequest message, SumDataAggregator aggregator, ref Dictionary<string, List<string>> schemaToMessages, Dictionary<string, long> countPerSdksource)
        {
            var dataLines = message.CommonMessageBatchReq.Messages;
            if (dataLines != null)
            {
                aggregator.Aggregate(dataLines.Count);
                aggregator.AggregateKvp(countPerSdksource);

                if (null == schemaToMessages)
                {
                    schemaToMessages = new Dictionary<string, List<string>>();
                }
                else
                {
                    schemaToMessages.Clear();
                }

                List<string>? lines;

                foreach (var line in dataLines)
                {
                    if (!schemaToMessages.TryGetValue(line.Source, out lines))
                    {
                        schemaToMessages.Add(line.Source, lines = new List<string>());
                    }

                    switch (line.ValueCase)
                    {
                        case ODLNRTCommonMessage.ValueOneofCase.ValueString:
                            lines.Add(line.ValueString);
                            break;
                        default:
                            logger.Log(LogLevel.Error, LogEventId.TcpClientThreadError, $"Unsupported Value type of ODLNRTCommonMessage " +
                                $"from {line},skip this line");
                            break;
                    }
                }
            }
        }

        private static void FallbackToDiskForSecurityTelemetry(ODLNRTRequest message, SumDataAggregator aggregator, ref Dictionary<string, List<string>> schemaToMessages, Dictionary<string, long> countPerSdksource)
        {
            var dataLines = message.SecurityMessageBatchReq?.Messages;
            if (dataLines != null)
            {
                aggregator.Aggregate(dataLines.Count);
                aggregator.AggregateKvp(countPerSdksource);

                if (null == schemaToMessages)
                {
                    schemaToMessages = new Dictionary<string, List<string>>();
                }
                else
                {
                    schemaToMessages.Clear();
                }

                List<string>? lines;

                foreach (var line in dataLines)
                {
                    if (!schemaToMessages.TryGetValue(line.Source, out lines))
                    {
                        schemaToMessages.Add(line.Source, lines = new List<string>());
                    }

                    lines.Add(line.Message);
                }
            }
        }

        /// <summary>
        /// Send data synchronously, this method could be called from both inside and outside, 
        /// so need to ensure thread-safe
        /// </summary>
        /// <param name="data">the data</param>
        /// <returns>false if timeout or exception</returns>
        public bool SendData(ODLNRTRequest data)
        {
            lock (mutex)
            {
                if (null == this.buffer)
                {
                    this.buffer = new byte[this.BufferSize];
                }

                // Try to serlize using the existing buffer
                int bytesLen = NRTMessageSerilizer.Serilize(data, this.buffer, out byte[] bytes);

                // Replace with a larger buffer
                if (bytes != null && bytes.Length > this.buffer.Length && bytes.Length <= MaxBufferSize)
                {
                    this.buffer = bytes;
                }

                this.memoryStream.SetLength(0);
                bool sendSucc = TcpUtils.Send(data.Head.CommandType, currentStream, bytes, bytesLen, data.Head.Sequence, this.memoryStream, this.sendTimeoutMilliSeconds);
                if (!sendSucc)
                {
                    this.ReleaseTcpClient();
                }

                return sendSucc;
            }
        }

        /// <summary>
        /// Stop current thread
        /// </summary>
        public bool StopThread()
        {
            interrupted = true;

            try
            {
                if (thread.IsAlive)
                {
                    thread.Interrupt();
                    return thread.Join(TimeoutStopThread);
                }

                return true;
            }
            catch (Exception ex)
            {
                logger.Log(LogLevel.Error, LogEventId.TcpClientThreadError, $"Exception while stopping thread:{ex}");
                return false;
            }
        }

        /// <summary>
        /// Release resources and dispose
        /// </summary>
        public void Dispose()
        {
            try
            {
                this.IsDisposing = true;

                StopThread();
                ReleaseTcpClient();
            }
            catch (Exception ex)
            {
                logger.Log(LogLevel.Error, LogEventId.TcpClientThreadError, $"Exception while disposing thread obj:{ex}");
            }
            finally
            {
                this.memoryStream?.Dispose();
            }
        }

        /// <summary>
        /// Release tcp resources
        /// </summary>
        public void ReleaseTcpClient()
        {
            lock (mutex)
            {
                IsAlive = false;

                try
                {
                    currentStream?.Dispose();
                }
                catch (Exception e)
                {
                    logger.Log(LogLevel.Error, LogEventId.TcpClientThreadError, $"Error when releasing TCP network stream: {e}");
                }
                finally
                {
                    currentStream = null;
                }

                try
                {
                    tcpClient?.Dispose();
                }
                catch (Exception e)
                {
                    logger.Log(LogLevel.Error, LogEventId.TcpClientThreadError, $"Error when releasing TcpClient: {e}");
                }
                finally
                {
                    tcpClient = null;
                }
            }
        }
    }
}
