﻿// <copyright file="TelemetryEmitConfigTest.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.IO;
using System.Reflection;
using System.Text;
using Microsoft.Extensions.Configuration;
using Microsoft.M365.Core.Telemetry.ECSClient;
using Microsoft.M365.Core.Telemetry.Enrichment;
using Microsoft.Skype.ECS.Client;
using Newtonsoft.Json.Linq;
using NSubstitute;
using Xunit;

namespace Microsoft.M365.Core.Telemetry.ECSClientTest
{
    /// <summary>
    /// Tests to validate PassiveR9Config code.
    /// </summary>
    [Collection("Non-Parallel Collection")]
    public class TelemetryEmitConfigTest : IDisposable
    {
        private TelemetryEmitConfig config;
        private Dictionary<string, string> ecsContext;
        private UnifiedTelemetryECSClient ecsClient;

        /// <summary>
        /// Setup.
        /// </summary>
        public TelemetryEmitConfigTest()
        {
            IConfiguration configuration = Substitute.For<IConfiguration>();
            config = new TelemetryEmitConfig(configuration);
            ecsContext = TestUtils.GetPrivateField<Dictionary<string, string>>(config, "ecsContext");
            ecsClient = TestUtils.GetPrivateField<UnifiedTelemetryECSClient>(config, "ecsClient");
        }

        /// <summary>
        /// Cleanup.
        /// </summary>
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        /// <summary>
        /// Basic Dispose Pattern.
        /// </summary>
        /// <param name="disposing">disposing boolean.</param>
        protected virtual void Dispose(bool disposing)
        {
            if (disposing)
            {
                TestUtils.SetPrivateStaticField(typeof(UnifiedTelemetryECSClient), "ecsInstance", null);
            }
        }

        // Set up mock ecs.
        private void SetupMockECS(string appsettings_str)
        {
            JObject configRoot = JObject.Parse(appsettings_str);

            IECSConfigSettings mockConfigSettings = Substitute.For<IECSConfigSettings>();
            object unusedObjectArg = default(object);
            mockConfigSettings.TryGetRootValue("PassiveMon", out unusedObjectArg).Returns(r =>
            {
                r[1] = configRoot;
                return true;
            });

            IECSConfigurationRequester mockEcsRequester = Substitute.For<IECSConfigurationRequester>();
            SettingsETag settingsETag = new SettingsETag(mockConfigSettings, "FakeETag");
            mockEcsRequester.GetSettings(ecsContext).Returns(settingsETag);
            ecsClient.EcsRequester = mockEcsRequester;

            TestUtils.InvokePrivateMethod<object>(config, "UpdateConfigValue", new object[] { null, new HashSet<string>() { "PassiveMon" } });
        }

        /// <summary>
        /// TestECSContext
        /// </summary>
        [Fact]
        public void TestECSContext()
        {
            Assert.True(ecsContext.Count == 3);
            Assert.True(ecsContext.TryGetValue("DeployRing", out string deployRing));
            Assert.NotNull(deployRing);
        }

        /// <summary>
        /// Test UpdateConfigValue with wrong agent.
        /// </summary>
        [Fact]
        public void TestUpdateConfigValueWithWrongAgent()
        {
            TestUtils.InvokePrivateMethod<object>(config, "UpdateConfigValue", new object[] { null, new HashSet<string>() { "testAgent" } });
        }

        /// <summary>
        /// TestUpdateConfigValue
        /// </summary>
        [Fact]
        public void TestUpdateConfigValue()
        {
            string appsettings_str = @"
            {
                ""IfxDisabled"": true,
                ""R9Enabled"": false,
                ""TableNameMapsTo"": ""ResultCheckerEventOtel""
                
            }";
            SetupMockECS(appsettings_str);
        }

        /// <summary>
        /// TestQueryEventConfig
        /// </summary>
        [Fact]
        public void TestQueryEventConfigWithValidParams()
        {
            string appsettings_str = @"
            {
                ""Event_TestEvent"": {
                    ""IfxDisabled"": false,
                    ""R9Enabled"": false,
                    ""TableNameMapsTo"": ""ResultCheckerEventOtel""
                }
            }";
            SetupMockECS(appsettings_str);

            bool ifxDisabled = config.QueryEventConfig<bool>("TestEvent", "IfxDisabled");
            Assert.False(ifxDisabled);

            bool r9Enabled = config.QueryEventConfig<bool>("TestEvent", "R9Enabled");
            Assert.False(r9Enabled);
        }

        /// <summary>
        /// TestQueryEventConfig
        /// </summary>
        [Fact]
        public void TestQueryEventConfigWithInvalidParams()
        {
            string appsettings_str = @"
            {
                ""Event_TestEvent"": {
                    ""IfxDisabled"": false,
                    ""R9Enabled"": true,
                    ""TableNameMapsTo"": ""ResultCheckerEventOtel""
                }
            }";
            SetupMockECS(appsettings_str);

            bool r9Enabled = config.QueryEventConfig<bool>("NoExistingEvent", "R9Enabled");
            Assert.False(r9Enabled);
        }

        /// <summary>
        /// TestQueryMetricConfig
        /// </summary>
        [Fact]
        public void TestQueryMetricConfigWithValidParams()
        {
            string appsettings_str = @"
            {
                ""Metric_TestMetric"": {
                    ""IfxDisabled"": false,
                    ""R9Enabled"": true,
                    ""MonitoringAccount"": ""account"",
                    ""MonitoringNamespace"": ""space""
                }
            }";
            SetupMockECS(appsettings_str);

            bool ifxDisabled = config.QueryMetricConfig<bool>("TestMetric", "IfxDisabled");
            Assert.False(ifxDisabled);

            bool r9Enabled = config.QueryMetricConfig<bool>("TestMetric", "R9Enabled");
            Assert.True(r9Enabled);

            string monitoringAccount = config.QueryMetricConfig<string>("TestMetric", "MonitoringAccount");
            Assert.Equal("account", monitoringAccount);

            string monitoringNamespace = config.QueryMetricConfig<string>("TestMetric", "MonitoringNamespace");
            Assert.Equal("space", monitoringNamespace);
        }

        /// <summary>
        /// TestQueryMetricConfig
        /// </summary>
        [Fact]
        public void TestQueryMetricConfigWithInValidParams()
        {
            string appsettings_str = @"
            {
                ""Metric_TestMetric"": {
                    ""IfxDisabled"": false,
                    ""R9Enabled"": true,
                    ""MonitoringAccount"": ""account"",
                    ""MonitoringNamespace"": ""space""
                }
            }";
            SetupMockECS(appsettings_str);

            bool r9Enabled = config.QueryMetricConfig<bool>("NoExistingMetric", "R9Enabled");
            Assert.False(r9Enabled);
        }

        /// <summary>
        /// TestUpdateConfigValueInterval
        /// </summary>
        [Fact]
        public void TestUpdateConfigValueInterval()
        {
            UpdateConfigEventArgs updateConfigEventArgs = new UpdateConfigEventArgs();
            updateConfigEventArgs.ChangedAgents = new HashSet<string>() { "PassiveMon" };
            updateConfigEventArgs.WaitHandleCounter = new WaitHandleCounter(1);
            ecsClient.RegularUpdateEventWrapper.Notify(updateConfigEventArgs);
        }
    }
}
