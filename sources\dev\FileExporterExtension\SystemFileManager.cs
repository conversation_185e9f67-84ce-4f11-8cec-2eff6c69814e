// <copyright file="SystemFileManager.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System.Collections.Generic;
using System.IO;

namespace Microsoft.M365.Core.Telemetry.FileExporter
{
    /// <summary>
    /// Delegate class to convert static APIs of <see cref="File" /> to dynamic.
    /// </summary>
    internal sealed class SystemFileManager : IFileManager
    {
        /// <summary>
        /// Singleton instance.
        /// </summary>
        public static readonly SystemFileManager Instance = new ();
        
        /// <summary>
        /// Constructor.
        /// </summary>
        private SystemFileManager()
        {
        }

        /// <summary>
        /// Create directory.
        /// </summary>
        /// <param name="directoryFullPath">Full directory path.</param>
        public void CreateDirectory(string directoryFullPath)
        {
            _ = Directory.CreateDirectory(directoryFullPath);
        }

        /// <summary>
        /// Get all files under the given directory.
        /// </summary>
        /// <param name="directoryFullPath">Full directory path.</param>
        /// <returns>Array of file names.</returns>
        public string[] GetFiles(string directoryFullPath)
        {
            return Directory.GetFiles(directoryFullPath);
        }

        /// <summary>
        /// Delete the given file.
        /// </summary>
        /// <param name="fullFileName">Full file name.</param>
        public void DeleteFile(string fullFileName)
        {
            File.Delete(fullFileName);
        }

        /// <summary>
        /// Create file with given name.
        /// </summary>
        /// <param name="fullFileName">Full file name.</param>
        /// <returns>Resulted <see cref="FileStream"/>.</returns>
        public FileStream CreateFile(string fullFileName)
        {
            return File.Create(fullFileName);
        }

        /// <summary>
        /// Check whether directory exists.
        /// </summary>
        /// <param name="directoryPath">Full directory path.</param>
        /// <returns>Boolean indicates whether directory exists.</returns>
        public bool DirectoryExists(string directoryPath)
        {
            return Directory.Exists(directoryPath);
        }

        /// <summary>
        /// Check whether file exists.
        /// </summary>
        /// <param name="fullFileName">Full file name.</param>
        /// <returns>Boolean indicates whether file exists.</returns>
        public bool FileExists(string fullFileName)
        {
            return File.Exists(fullFileName);
        }

        /// <summary>
        /// Rename the given file to the given new name.
        /// </summary>
        /// <param name="originalFileName">Original full file name.</param>
        /// <param name="newFileName">New full file name.</param>
        public void RenameFile(string originalFileName, string newFileName)
        {
            File.Move(originalFileName, newFileName);
        }

        /// <summary>
        /// Write all lines to the given file.
        /// </summary>
        /// <param name="fullFileName">Full file name.</param>
        /// <param name="lines">String lines to write.</param>
        public void WriteAllLines(string fullFileName, IEnumerable<string> lines)
        {
            File.AppendAllLines(fullFileName, lines);
        }

        /// <summary>
        /// Create <see cref="LogFile"/> for given path.
        /// </summary>
        /// <param name="fullFileName">Full file name.</param>
        /// <returns><see cref="LogFile"/>.</returns>
        public LogFile CreateLogFile(string fullFileName)
        {
            return new LogFile(new FileInfo(fullFileName));
        }
    }
}
