﻿// <copyright file="BatchActivityExportProcessorWithFilter.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>
using System.Diagnostics;
using System.Threading;
using OpenTelemetry.Exporter.Filters.Internal;
using OpenTelemetry.Trace;

namespace OpenTelemetry.Exporter.Filters
{
    /// <summary>
    /// BatchActivityExportProcessor with a filter internal and do filtering before export
    /// </summary>
    /// <remarks>
    /// the default values are copied from OpenTelemetry and need to keep same with 
    /// these of BatchActivityExportProcessor,as these consts are internal and not 
    /// visible to this project.They will be removed in one of the two conditions below.
    /// - these consts will be public in Open Telemery.
    /// - this class will be added to Open Telmetry and can touch these consts
    /// </remarks>
    public class BatchActivityExportProcessorWithFilter : BatchActivityExportProcessor
    {
        /// <summary>
        /// default max value
        /// </summary>
        internal const int DefaultMaxQueueSize = 2048;
        
        /// <summary>
        /// default scheduled delay milliseconds
        /// </summary>
        internal const int DefaultScheduledDelayMilliseconds = 5000;

        /// <summary>
        /// default Exporter timeout milliseconds
        /// </summary>
        internal const int DefaultExporterTimeoutMilliseconds = 30000;

        /// <summary>
        /// default max Export batch size
        /// </summary>
        internal const int DefaultMaxExportBatchSize = 512;
        
        /// <summary>
        /// internal filter
        /// </summary>
        internal readonly BaseFilter<Activity> Filter;

        private long filterDropSize;

        /// <summary>
        /// initialize the instance with filter and exporter
        /// </summary>
        /// <param name="exporter"></param>
        /// <param name="filter"></param>
        /// <param name="maxQueueSize"></param>
        /// <param name="scheduledDelayMilliseconds"></param>
        /// <param name="exporterTimeoutMilliseconds"></param>
        /// <param name="maxExportBatchSize"></param>
        public BatchActivityExportProcessorWithFilter(
            BaseExporter<Activity> exporter, 
            BaseFilter<Activity> filter,
            int maxQueueSize = DefaultMaxQueueSize,
            int scheduledDelayMilliseconds = DefaultScheduledDelayMilliseconds,
            int exporterTimeoutMilliseconds = DefaultExporterTimeoutMilliseconds,
            int maxExportBatchSize = DefaultMaxExportBatchSize)
            : base(
                exporter,
                maxQueueSize,
                scheduledDelayMilliseconds,
                exporterTimeoutMilliseconds,
                maxExportBatchSize)
        {
            Guard.ThrowIfNull(filter, nameof(filter));
            this.Filter = filter;
        }

        /// <summary>
        /// Gets the number of telemetry objects dropped by the filter in processor.
        /// </summary>
        internal long FilterDropSize => this.filterDropSize;

        /// <summary>
        /// filter the data before they are actually exported.
        /// </summary>
        /// <param name="data">completed activity</param>
        public override void OnEnd(Activity data)
        {
            if (this.Filter.ShouldFilter(data))
            {
                base.OnEnd(data);
                return;
            }

            Interlocked.Increment(ref filterDropSize);
        }
    }
}
