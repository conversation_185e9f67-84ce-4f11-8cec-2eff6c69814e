<Project Sdk="Microsoft.NET.Sdk">
    <PropertyGroup>
        <TargetFrameworks>net472;net6.0;net8.0</TargetFrameworks>
        <DocumentationFile>bin\$(Configuration)\$(TargetFramework)\$(AssemblyName).xml</DocumentationFile>
        <Authors>SOTELS</Authors>
        <RootNamespace>Microsoft.M365.Core.Telemetry.R9Test</RootNamespace>
        <AssemblyName>Microsoft.M365.Core.Telemetry.R9Test</AssemblyName>
        <IsCodedUITest>False</IsCodedUITest>
        <TestProjectType>UnitTest</TestProjectType>
        <PlatformTarget>anycpu</PlatformTarget>
        <LangVersion>9</LangVersion>
        <SuppressTfmSupportBuildWarnings>true</SuppressTfmSupportBuildWarnings>
    </PropertyGroup>
    <ItemGroup>
        <ProjectReference Include="..\..\..\dev\R9TelemetryExtensionsCore\R9\R9.csproj" />
        <ProjectReference Include="..\..\..\dev\TestCommon\TestCommon.csproj" />
    </ItemGroup>
    <ItemGroup>
        <PackageReference Include="Bond.Core.Csharp" />
        <PackageReference Include="FluentAssertions" />
        <PackageReference Include="Microsoft.NET.Test.Sdk" />
        <PackageReference Include="Microsoft.R9.Extensions.Metering.Fakes" />
        <PackageReference Include="NSubstitute" />
        <PackageReference Include="xunit.runner.visualstudio" />
        <PackageReference Include="xunit" />
    </ItemGroup>
</Project>