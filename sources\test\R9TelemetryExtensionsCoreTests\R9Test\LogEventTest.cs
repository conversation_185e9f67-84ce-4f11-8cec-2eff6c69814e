﻿// <copyright file="LogEventTest.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.Reflection;
using Microsoft.Extensions.Configuration;
using Microsoft.M365.Core.Telemetry.ECSClient;
using Microsoft.M365.Core.Telemetry.R9;
using Microsoft.M365.Core.Telemetry.TestCommon;
using NSubstitute;
using Xunit;

namespace Microsoft.M365.Core.Telemetry.R9Test
{
    /// <summary>
    /// LogEventTest
    /// </summary>
    [Collection("Do not parallel")]
    public class LogEventTest
    {
        private List<string> logRecords;

        /// <summary>
        /// LogEventTest
        /// </summary>
        public LogEventTest()
        {
            logRecords = new List<string>();
        }

        private List<MemberInfo> GetBondFields(System.Type eventType)
        {
            List<MemberInfo> bondFields = new List<MemberInfo>();
            foreach (var field in eventType.GetMembers(BindingFlags.Public | BindingFlags.FlattenHierarchy | BindingFlags.Instance))
            {
                if (field.MemberType == MemberTypes.Field || field.MemberType == MemberTypes.Property)
                {
                    bondFields.Add(field);
                }
            }
            return bondFields;
        }

        /// <summary>
        /// TestLogR9Event
        /// </summary>
        [Fact]
        public void TestLogR9Event()
        {
            AddFakeExtensions.EnableR9(logRecords, null);
            var obj = new R9TestEvent();
            var r9Event = new LogEvent<R9TestEvent>();
            r9Event.Log(obj, GetBondFields(typeof(R9TestEvent)));

            Assert.Single(logRecords);
            Assert.Contains("customVal1", logRecords[0], StringComparison.CurrentCulture);
            Assert.Contains("SDK error! Contact <EMAIL>", logRecords[0], StringComparison.CurrentCulture);
        }

        /// <summary>
        /// TestLogR9EventAlwaysEnableR9
        /// </summary>
        [Fact]
        public void TestLogR9EventAlwaysEnableR9()
        {
            var passiveConfig = Substitute.For<IPassiveR9Config>();
            passiveConfig.R9EventEnabled.Returns(false);
            var sc = AddFakeExtensions.ConfigureServicesForR9Test(null, logRecords, null, passiveConfig);
            R9Services.InitR9Services(sc, true);
            var obj = new R9TestEvent();
            var r9Event = new LogEvent<R9TestEvent>();
            r9Event.Log(obj, GetBondFields(typeof(R9TestEvent)));

            Assert.Single(logRecords);
            Assert.Contains("customVal1", logRecords[0], StringComparison.CurrentCulture);
        }

        /// <summary>
        /// TestLogR9EventSelectivelyEnableR9
        /// </summary>
        [Fact]
        public void TestLogR9EventSelectivelyEnableR9()
        {
            var passiveConfig = Substitute.For<IPassiveR9Config>();
            passiveConfig.EventEnabledForR9("Microsoft.M365.Core.Telemetry.R9Test.EventEnabled").Returns(true);
            passiveConfig.EventEnabledForR9("Microsoft.M365.Core.Telemetry.R9Test.EventDisabled").Returns(false);
            var sc = AddFakeExtensions.ConfigureServicesForR9Test(null, logRecords, null, passiveConfig);
            R9Services.InitR9Services(sc);

            var eventEnabled = new LogEvent<EventEnabled>();
            eventEnabled.Log(new EventEnabled(), GetBondFields(typeof(EventEnabled)));
            var eventDisabled = new LogEvent<EventDisabled>();
            eventDisabled.Log(new EventDisabled(), GetBondFields(typeof(EventDisabled)));

            Assert.Contains("Enabled", logRecords[0], StringComparison.CurrentCulture);

            Assert.Equal("Disabled", new EventDisabled().CustomProperty);
        }

        /// <summary>
        /// TestLogR9EventforLibrary
        /// </summary>
        [Fact]
        public void TestLogR9EventforLibrary()
        {
            AddFakeExtensions.EnableR9(logRecords, null);
            var obj = new R9SDKTestEvent();
            var r9Event = new LogEvent<R9SDKTestEvent>();
            r9Event.Log(obj, GetBondFields(typeof(R9SDKTestEvent)));

            Assert.Single(logRecords);
            Assert.Contains("customVal1", logRecords[0], StringComparison.CurrentCulture);
            Assert.Contains("SDK error! Contact <EMAIL>", logRecords[0], StringComparison.CurrentCulture);
        }

        /// <summary>
        /// TestLogR9SDKTestEventNoSDKError
        /// </summary>
        [Fact]
        public void TestLogR9SDKTestEventNoSDKError()
        {
            AddFakeExtensions.EnableR9(logRecords, null);
            var obj = new R9SDKTestEvent();
            obj.CustomProperty3 = "customVal3";
            var r9Event = new LogEvent<R9SDKTestEvent>();
            r9Event.Log(obj, GetBondFields(typeof(R9SDKTestEvent)));
            Assert.DoesNotContain("SDK error! Contact <EMAIL>", logRecords[0], StringComparison.CurrentCulture);
        }

        /// <summary>
        /// TestLogR9TestEventNoSDKError
        /// </summary>
        [Fact]
        public void TestLogR9TestEventNoSDKError()
        {
            AddFakeExtensions.EnableR9(logRecords, null);
            var obj = new R9TestEvent();
            obj.CustomProperty3 = "customVal3";
            var r9Event = new LogEvent<R9TestEvent>();
            r9Event.Log(obj, GetBondFields(typeof(R9TestEvent)));
            Assert.DoesNotContain("SDK error! Contact <EMAIL>", logRecords[0], StringComparison.CurrentCulture);
        }

        /// <summary>
        /// CreateLoggerV2Test
        /// </summary>
        [Fact]
        public void CreateLoggerV2Test()
        {
            AddFakeExtensions.EnableR9(logRecords, null);
            var mockConfiguration = new Dictionary<string, string>
            {
                ["GenevaLogging:ConnectionString"] = "EtwSession=o365PassiveMonitoringSessionR9",
                ["GenevaMetering:Protocol"] = "Etw"
            };
            var configuration = new ConfigurationBuilder()
                .AddInMemoryCollection(mockConfiguration)
                .Build();
            AddFakeExtensions.InjectV2ServiceProvider(configuration);

            var obj = new R9TestEvent();
            obj.CustomProperty3 = "customVal3";
            var r9Event = new LogEvent<R9TestEvent>();
            r9Event.Log(obj, GetBondFields(typeof(R9TestEvent)));
        }
    }

    /// <summary>
    /// R9TestEvent
    /// </summary>
    public class R9TestEvent
    {
        /// <summary>
        /// CustomProperty1
        /// </summary>
        public string CustomProperty1 { get; set; }

        /// <summary>
        /// CustomProperty2
        /// </summary>
        public string CustomProperty2 { get; set; }

        /// <summary>
        /// CustomProperty3
        /// </summary>
        public string CustomProperty3 { get; set; }

        /// <summary>
        /// R9TestEvent
        /// </summary>
        internal R9TestEvent()
        {
            CustomProperty1 = "customVal1";
            CustomProperty2 = "customVal2";
        }
    }

    /// <summary>
    /// R9SDKTestEvent
    /// </summary>
    public class R9SDKTestEvent
    {
        /// <summary>
        /// CustomProperty1
        /// </summary>
        public string CustomProperty1 { get; set; }

        /// <summary>
        /// CustomProperty2
        /// </summary>
        public string CustomProperty2 { get; set; }

        /// <summary>
        /// CustomProperty2
        /// </summary>
        public string CustomProperty3 { get; set; }

        /// <summary>
        /// R9SDKTestEvent
        /// </summary>
        internal R9SDKTestEvent()
        {
            CustomProperty1 = "customVal1";
            CustomProperty2 = "customVal2";
        }
    }

    /// <summary>
    /// EventEnabled
    /// </summary>
    public class EventEnabled
    {
        /// <summary>
        /// CustomProperty
        /// </summary>
#pragma warning disable CA1822 // Mark members as static
        public string CustomProperty
#pragma warning restore CA1822 // Mark members as static
        {
            get => "Enabled";
        }
    }

    /// <summary>
    /// EventDisabled
    /// </summary>
    public class EventDisabled
    {
        /// <summary>
        /// CustomProperty
        /// </summary>
#pragma warning disable CA1822 // Mark members as static
        public string CustomProperty
#pragma warning restore CA1822 // Mark members as static
        {
            get => "Disabled";
        }
    }
}
