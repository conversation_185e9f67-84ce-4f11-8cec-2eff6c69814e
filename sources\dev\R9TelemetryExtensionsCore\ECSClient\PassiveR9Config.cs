﻿// <copyright file="PassiveR9Config.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Runtime.InteropServices;
using System.Text;

using Microsoft.Extensions.Configuration;
using Microsoft.M365.Core.Telemetry.Enrichment;
using Microsoft.M365.Core.Telemetry.SDKLogger;

using Newtonsoft.Json.Linq;

namespace Microsoft.M365.Core.Telemetry.ECSClient
{
    /// <summary>
    /// PassiveR9Config
    /// </summary>
    internal class PassiveR9Config : IPassiveR9Config
    {
        /// <summary>
        /// The default assembly name.
        /// </summary>
        private const string DefaultAssemblyName = "Unmanaged";

        /// <summary>
        /// The ECS context filter.
        /// </summary>
        private Dictionary<string, string> ecsContext;

        /// <summary>
        /// R9 library configuration
        /// </summary>
        private string libraryR9Config;

        /// <summary>
        /// R9 service configuration
        /// </summary>
        private string serviceLogR9Config;

        /// <summary>
        /// R9 service configuration
        /// </summary>
        private string serviceMetricR9Config;

        /// <summary>
        /// If R9 Event is enabled.
        /// </summary>
        public bool R9EventEnabled { get; set; } = OperatingSystemHelper.IsLinux;

        /// <summary>
        /// If R9 Metric is enabled.
        /// </summary>
        public bool R9MetricEnabled { get; set; }

        /// <summary>
        /// If Ifx Event is enabled.
        /// </summary>
        public bool IfxEventEnabled { get; set; } = !OperatingSystemHelper.IsLinux;

        /// <summary>
        /// If Ifx Metric is enabled.
        /// </summary>
        public bool IfxMetricEnabled { get; set; } = !OperatingSystemHelper.IsLinux;

        /// <summary>
        /// [For debug] If debug info collection is enabled.
        /// </summary>
        public bool IsDebugInfoCollectionEnabled { get; set; }

        /// <summary>
        /// [For debug] If mds trace is enabled.
        /// </summary>
        public bool MdsTraceEnabled { get; set; }

        /// <summary>
        /// [For debug] EventId list that included in TraceLog.
        /// </summary>
        public string IncludedTraceLog { get; set; }

        /// <summary>
        /// ecsClient instance.
        /// </summary>
        private readonly UnifiedTelemetryECSClient ecsClient;

        private HashSet<string> disabledIfxEvents = new HashSet<string>();
        private HashSet<string> disabledIfxMetrics = new HashSet<string>();
        private HashSet<string> enabledR9Events = new HashSet<string>();
        private HashSet<string> enabledR9Metrics = new HashSet<string>();

        /// <summary>
        /// Returns if an event is DISABLED for Ifx.
        /// </summary>
        /// <param name="category"></param>
        /// <returns></returns>
        public bool EventDisabledForIfx(string category)
        {
            return disabledIfxEvents.Contains(category);
        }

        /// <summary>
        /// Returns if a metric is DISABLED for Ifx.
        /// </summary>
        /// <param name="category"></param>
        /// <returns></returns>
        public bool MetricDisabledForIfx(string category)
        {
            return disabledIfxMetrics.Contains(category);
        }

        /// <summary>
        /// Returns if an event is enabled for R9.
        /// </summary>
        /// <param name="category"></param>
        /// <returns></returns>
        public bool EventEnabledForR9(string category)
        {
            return enabledR9Events.Contains(category);
        }

        /// <summary>
        /// Returns if a metric is enabled for R9.
        /// </summary>
        /// <param name="category"></param>
        /// <returns></returns>
        public bool MetricEnabledForR9(string category)
        {
            return enabledR9Metrics.Contains(category);
        }

        /// <summary>
        /// Constructor of the <see cref="PassiveR9Config" /> class.
        /// </summary>
        /// <param name="configuration"> The application configuration properties. </param>
        public PassiveR9Config(IConfiguration configuration)
        {
            try
            {
                SDKLog.Info("Initializing PassiveR9Config");
                this.ecsClient = UnifiedTelemetryECSClient.EcsClientInstance(configuration);
                this.InitializeECSContext();
                this.ecsClient.RegularUpdateEventWrapper.UpdateConfig += (sender, args) =>
                {
                    args.WaitHandleCounter.AddCount();
                    UpdateConfigValue(sender, args.ChangedAgents);
                    args.WaitHandleCounter.Signal();
                };

                // One time update config
                var changedAgents = new HashSet<string>(StringComparer.InvariantCultureIgnoreCase);
                changedAgents.Add("PassiveMon");
                UpdateConfigValue(default(Object), changedAgents);
            }
            catch (Exception e)
            {
                SDKLog.Error($"PassiveR9Config init exception: {e}");
            }
        }

        /// <summary>
        /// Initialize the ECS context.
        /// </summary>
        private void InitializeECSContext()
        {
            string assemblyName = DefaultAssemblyName;

            try
            {
                assemblyName = Assembly.GetEntryAssembly().GetName().Name;
            }
            catch (Exception e)
            {
                SDKLog.Error($"PassiveR9Config Error getting assembly name: {e}");
            }

            ecsContext = new Dictionary<string, string>()
            {
                { "AssemblyName", assemblyName },
                { "BuildVersion", DimensionValues.BuildVersion },
                { "DeployRing", DimensionValues.DeployRing },
                { "Forest", DimensionValues.Forest },
                { "IsEOP", ECSClientUtilities.IsEOP() },
                { "Machine", DimensionValues.Machine },
                { "Role", DimensionValues.Role },
                { "Service", DimensionValues.Service },
                { "ServiceName", this.ecsClient.ServiceName },
                { "Model",  this.ecsClient.RuntimeModel }
            };

            string ecsContestMsg = string.Join(";", ecsContext.Select(x => x.Key + "=" + x.Value).ToArray());
            SDKLog.Info($"PassiveR9Config ECSContext. {ecsContestMsg}");
        }

        private void UpdateEnableFlags(JObject configRoot)
        {
            JToken res;

            // R9 default value is false
            bool r9EventEnabledTmp = (res = ECSClientUtilities.ParseConfig(configRoot, Constants.R9EventEnabled)) != null && res.ToObject<bool>();
            R9EventEnabled = RuntimeInformation.IsOSPlatform(OSPlatform.Linux) ? true : r9EventEnabledTmp;
            R9MetricEnabled = (res = ECSClientUtilities.ParseConfig(configRoot, Constants.R9MetricEnabled)) != null && res.ToObject<bool>();

            // Ifx default value is true
            IfxEventEnabled = (res = ECSClientUtilities.ParseConfig(configRoot, Constants.IfxEventEnabled)) == null || res.ToObject<bool>();
            IfxMetricEnabled = (res = ECSClientUtilities.ParseConfig(configRoot, Constants.IfxMetricEnabled)) == null || res.ToObject<bool>();
        }

        private void UpdateSelectedTelemetry(JObject configRoot)
        {
            JToken res;

            // Read disabledIfxMetrics from the DisabledIfxMetrics ecs config.
            res = ECSClientUtilities.ParseConfig(configRoot, Constants.DisabledIfxMetrics);
            if (res != null)
            {
                disabledIfxMetrics = new HashSet<string>(res.ToObject<string[]>(), StringComparer.InvariantCultureIgnoreCase);
            }

            // Read disabledIfxEvents from the DisabledIfxEvents ecs config.
            res = ECSClientUtilities.ParseConfig(configRoot, Constants.DisabledIfxEvents);
            if (res != null)
            {
                disabledIfxEvents = new HashSet<string>(res.ToObject<string[]>(), StringComparer.InvariantCultureIgnoreCase);
            }

            // Read enabledR9Metrics from the EnabledR9Metrics ecs config.
            res = ECSClientUtilities.ParseConfig(configRoot, Constants.EnabledR9Metrics);
            if (res != null)
            {
                enabledR9Metrics = new HashSet<string>(res.ToObject<string[]>(), StringComparer.InvariantCultureIgnoreCase);
            }

            // Read enabledR9Events from the EnabledR9Events ecs config.
            res = ECSClientUtilities.ParseConfig(configRoot, Constants.EnabledR9Events);
            if (res != null)
            {
                enabledR9Events = new HashSet<string>(res.ToObject<string[]>(), StringComparer.InvariantCultureIgnoreCase);
            }
        }

        private void UpdateDebugFlags(JObject configRoot)
        {
            JToken res;

            // For debug purpose
            MdsTraceEnabled = (res = ECSClientUtilities.ParseConfig(configRoot, Constants.Debug + "_" + Constants.MdsTraceEnabled)) != null && res.ToObject<bool>();
            IsDebugInfoCollectionEnabled = (res = ECSClientUtilities.ParseConfig(configRoot, Constants.Debug + "_" + Constants.IsDebugInfoCollectionEnabled)) != null && res.ToObject<bool>();
            IncludedTraceLog = ECSClientUtilities.ParseConfig(configRoot, Constants.Debug + "_" + Constants.IncludedTraceLog)?.ToString() ?? string.Empty;
        }

        private void UpdateLibraryConfig(JObject configRoot)
        {
            JToken res;

            // Generate Library service collection
            string libraryR9Config = (res = ECSClientUtilities.ParseConfig(configRoot, Constants.LibraryR9Config)) != null ? res.ToString() : string.Empty;
            if (!string.IsNullOrEmpty(libraryR9Config) && !string.Equals(libraryR9Config, this.libraryR9Config, StringComparison.Ordinal))
            {
                using (MemoryStream libStream = new MemoryStream(Encoding.ASCII.GetBytes(libraryR9Config)))
                {
                    IConfigurationRoot sdkOption = new ConfigurationBuilder().AddJsonStream(libStream).Build();
                    LibraryManagedServiceCollection.Instance.UpdateInstance(sdkOption);
                    this.libraryR9Config = libraryR9Config;
                }
                SDKLog.Info($"ECS LibraryR9Config: {this.libraryR9Config}");
            }
        }

        private void UpdateServiceConfig(JObject configRoot)
        {
            JToken res;

            // Generate ECS managed service collection
            string logR9Config = (res = ECSClientUtilities.ParseConfig(configRoot, Constants.ServiceLogR9Config)) != null ? res.ToString() : string.Empty;
            string metricR9Config = (res = ECSClientUtilities.ParseConfig(configRoot, Constants.ServiceMetricR9Config)) != null ? res.ToString() : string.Empty;
            if (!string.IsNullOrEmpty(logR9Config) && !string.IsNullOrEmpty(metricR9Config) &&
                (!string.Equals(logR9Config, this.serviceLogR9Config, StringComparison.Ordinal) || !string.Equals(metricR9Config, this.serviceMetricR9Config, StringComparison.Ordinal)))
            {
                JObject tmpstr = JObject.Parse(logR9Config);
                tmpstr.Merge(JObject.Parse(metricR9Config));
                using (MemoryStream serviceStream = new MemoryStream(Encoding.ASCII.GetBytes(tmpstr.ToString())))
                {
                    IConfigurationRoot serviceOption = new ConfigurationBuilder().AddJsonStream(serviceStream).Build();
                    ECSManagedServiceCollection.Instance.UpdateInstance(serviceOption);
                    this.serviceLogR9Config = logR9Config;
                    this.serviceMetricR9Config = metricR9Config;
                }
                SDKLog.Info($"ECS ServiceLogR9Config: {serviceLogR9Config} ### ServiceMetricR9Config: {serviceMetricR9Config}");
            }
        }

        /// <summary>
        /// Update PassiveR9Config value.
        /// </summary>
        /// <param name="sender"> The sender. </param>
        /// <param name="changedAgents"> Agents that has been changed. </param>
#pragma warning disable CA1801 // Remove unused parameter
        private void UpdateConfigValue(object sender, HashSet<string> changedAgents)
#pragma warning restore CA1801 // Remove unused parameter
        {
            try
            {
                if (changedAgents == null || !changedAgents.Contains("PassiveMon"))
                {
                    return;
                }

                JObject configRoot = ECSClientUtilities.GetUnifiedTelemetryConfig("PassiveMon", ecsContext, ecsClient.EcsRequester);
                if (configRoot == null)
                {
                    return;
                }

                UpdateEnableFlags(configRoot);
                UpdateSelectedTelemetry(configRoot);
                UpdateDebugFlags(configRoot);
                UpdateLibraryConfig(configRoot);
                UpdateServiceConfig(configRoot);

                SDKLog.Info(
                    $"PassiveR9Config refresh: R9EventEnabled={R9EventEnabled};R9MetricEnabled={R9MetricEnabled};IfxEventEnabled={IfxEventEnabled};IfxMetricEnabled={IfxMetricEnabled}.\n" +
                    $"MdsTraceEnabled={MdsTraceEnabled};IsDebugInfoCollectionEnabled={IsDebugInfoCollectionEnabled};IncludedTraceLog={IncludedTraceLog}\n" +
                    $"disabledIfxMetrics={string.Join(",", disabledIfxMetrics)}; disabledIfxEvents={string.Join(",", disabledIfxEvents)}; enabledR9Metrics={string.Join(",", enabledR9Metrics)}; enabledR9Events={string.Join(",", enabledR9Events)}");
            }
            catch (Exception e)
            {
                SDKLog.Error($"UpdateConfigValue for PassiveMon failed : {e}");
            }
        }
    }
}
