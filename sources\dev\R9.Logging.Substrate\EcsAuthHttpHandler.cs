﻿// <copyright file="EcsAuthHttpHandler.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System;
using System.Diagnostics.CodeAnalysis;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Identity.Client;

namespace Microsoft.M365.Core.Telemetry.R9.Logging.Substrate.Configuration
{
    /// <summary>
    /// The handler to add ECS token to the request header.
    /// </summary>
    // Exclude the class from code coverage because the required cert doesn't exist in the test environment.
    // TODO: Refactor the cert as service dependency and test it.
    [ExcludeFromCodeCoverage]
    public sealed class EcsAuthHttpHandler : DelegatingHandler
    {
        private readonly IConfidentialClientApplication app;
        private readonly string[] scopes = new[] { "https://ecs.skype.ame.gbl/.default" };

        /// <summary>
        /// Constructor of handler.
        /// </summary>
        /// <param name="app">The app to acquire token.</param>
        public EcsAuthHttpHandler(IConfidentialClientApplication app)
        {
            this.app = app;
        }

        /// <summary>
        /// Send request to acquire token.
        /// </summary>
        /// <param name="request">The request to send.</param>
        /// <param name="cancellationToken">The cancellationToken.</param>
        /// <returns></returns>
        protected override async Task<HttpResponseMessage> SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
        {
            AuthenticationResult? token = null;
            try
            {
                token = await app.AcquireTokenForClient(scopes).ExecuteAsync(cancellationToken).ConfigureAwait(false);
            }
            catch (Exception)
            {
            }

            if (token != null)
            {
                request.Headers.Add("X-ECS-ClientAppToken", token.CreateAuthorizationHeader());
            }
            return await base.SendAsync(request, cancellationToken).ConfigureAwait(false);
        }
    }
}
