﻿// <copyright file="HttpClientEmailRedaction.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>
using System;
using System.Diagnostics;

#if !NETFRAMEWORK
using System.Net.Http;

#else
using System.Net;
#endif
using System.Text.RegularExpressions;
using Microsoft.Extensions.Http.Diagnostics;
using Microsoft.R9.Extensions.HttpClient.Tracing;
using Microsoft.R9.Extensions.Telemetry;
using RequestMetadata = Microsoft.R9.Extensions.Telemetry.RequestMetadata;

namespace Microsoft.M365.Core.Telemetry.R9.Tracing.Instrumentation.Accelerator.EUIIRedaction
{
    /// <summary>
    /// Email Redaction
    /// </summary>
    public sealed class HttpClientEmailRedaction : IHttpClientTraceEnricher
    {
        /// <summary>
        /// Do redaction by <PERSON>rich
        /// </summary>
        /// <param name="activity"></param>
        /// <param name="request"></param>
        /// <param name="response"></param>
#if !NETFRAMEWORK
#pragma warning disable CS8632 // The annotation for nullable reference types should only be used in code within a '#nullable' annotations context.
        public void Enrich(Activity activity, HttpRequestMessage? request, HttpResponseMessage? response)
#pragma warning restore CS8632 // The annotation for nullable reference types should only be used in code within a '#nullable' annotations context.
        {
            var requestUri = request?.RequestUri?.AbsolutePath;
            if (requestUri == null || activity == null)
            {
                return;
            }
            var redactedValue = RedactEmail(requestUri);
            if (request.GetRequestMetadata() == null)
            {
                request.SetRequestMetadata(new RequestMetadata
                {
                    RequestRoute = redactedValue,
                    RequestName = $"{request.Method} {redactedValue}",
                });
            }
        }
#else
#pragma warning disable CS8632 // The annotation for nullable reference types should only be used in code within a '#nullable' annotations context.
        public void Enrich(Activity activity, HttpWebRequest? request, HttpWebResponse? response)
#pragma warning restore CS8632 // The annotation for nullable reference types should only be used in code within a '#nullable' annotations context.
        {
            var requestUri = request?.RequestUri?.AbsolutePath;
            if (requestUri == null || activity == null)
            {
                return;
            }
            var redactedValue = RedactEmail(requestUri);
#pragma warning disable R9EXP0008 // Type is for evaluation purposes only and is subject to change or removal in future updates. Suppress this diagnostic to proceed.
            if (request.GetRequestMetadata() == null)
            {
                request.SetRequestMetadata(new RequestMetadata
                {
                    RequestRoute = redactedValue,
                    RequestName = $"{request.Method} {redactedValue}",
                });
            }
#pragma warning restore R9EXP0008 // Type is for evaluation purposes only and is subject to change or removal in future updates. Suppress this diagnostic to proceed.
        }
#endif

        private string RedactEmail(string inputString)
        {
            string pattern = @"[a-zA-Z0-9.!#$%&’*+/=?^_`{|}~-]+@[a-zA-Z0-9-]+(?:\.[a-zA-Z0-9-]+)*";
            string replacement = "{email}";
            Regex regex = new Regex(pattern);
            string[] parts = inputString.Split(new char[] { '/' }, StringSplitOptions.RemoveEmptyEntries);
            string[] newParts = new string[parts.Length];
            for (int i = 0; i < parts.Length; i++)
            {
                newParts[i] = regex.Replace(parts[i], replacement);
            }
            return string.Join("/", newParts);
        }
    }
}