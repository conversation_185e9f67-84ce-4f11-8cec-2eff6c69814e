﻿using System;
using System.Collections.Generic;
using System.Diagnostics.CodeAnalysis;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Microsoft.Extensions.ObjectPool;
using Microsoft.Office.BigData.DataLoader;

namespace Microsoft.M365.Core.Telemetry.ODL.SecurityTelemetryTcpExporter
{
    /// <summary>
    /// A factory of object pools for ODLNRTRequest related
    /// </summary>
    /// <remarks>
    /// This class makes it easy to create efficient object pools used to improve performance by reducing
    /// strain on the garbage collector.
    /// </remarks>
    [ExcludeFromCodeCoverage]
    public static class ODLNRTRequestPoolFactory
    {
        private static PooledObjectPolicy<ODLNRTMessage> defaultPooledPolicyForODLNRTMessage = new DefaultPooledObjectPolicy<ODLNRTMessage>();

        private static PooledObjectPolicy<ODLNRTRequest> defaultPooledPolicyForODLNRTRequest = new ODLNRTRequestPooledObjectPolicy();

        /// <summary>
        /// Creates a pool of <see cref="ODLNRTRequest"/> instances.
        /// </summary>
        /// <param name="maxRetained">the max number of retained object</param>
        /// <returns>The pool.</returns>
        public static ObjectPool<ODLNRTRequest> CreateODLNRTRequestPool(int maxRetained)
        {
            return MakePool(defaultPooledPolicyForODLNRTRequest, maxRetained);
        }

        /// <summary>
        /// Creates a pool of <see cref="ODLNRTMessage"/> instances.
        /// </summary>
        /// <param name="maxRetained">the max number of retained object</param>
        /// <returns>The pool.</returns>
        public static ObjectPool<ODLNRTMessage> CreateODLNRTMessagePool(int maxRetained)
        {
            return MakePool(defaultPooledPolicyForODLNRTMessage, maxRetained);
        }

        private static DefaultObjectPool<T> MakePool<T>(IPooledObjectPolicy<T> policy, int maxRetained)
        where T : class
        => new DefaultObjectPool<T>(policy, maxRetained);
    }
}
