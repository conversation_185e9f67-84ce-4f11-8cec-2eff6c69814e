﻿// <copyright file="IRegistryKey.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Microsoft.M365.Core.Telemetry.R9.Tracing.Instrumentation.Accelerator.Utilities
{
    /// <summary>
    /// The interface of registry key
    /// </summary>
    public interface IRegistryKey
    {
        /// <summary>
        /// Set the value
        /// </summary>
        /// <param name="name"> The value name </param>
        /// <param name="defaultValue"> The default value </param>
        /// <returns> The value </returns>
        object GetValue(string name, object defaultValue = null);

        /// <summary>
        /// Open a sub key for read
        /// </summary>
        /// <param name="subkey"> The sub key path </param>
        /// <returns> The sub key </returns>
        IRegistryKey OpenSubKey(string subkey);
    }
}
