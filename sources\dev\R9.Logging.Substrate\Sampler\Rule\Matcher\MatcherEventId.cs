// <copyright file="MatcherEventId.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Microsoft.M365.Core.Telemetry.R9.Logging.Substrate.Sampler
{
    /// <summary>
    /// EventId related match methods for matcher.
    /// <see cref="TryMatch{TState}(in Extensions.Logging.Abstractions.LogEntry{TState})"/> for matching logic.
    /// </summary>
    internal partial class Matcher
    {
        private static bool CompareEventId(int logValue, string fieldValue, Func<int, int, bool> compareFunc)
        {
            if (!int.TryParse(fieldValue, System.Globalization.NumberStyles.Any, System.Globalization.CultureInfo.InvariantCulture, out int constraintValue))
            {
                return false;
            }
            return compareFunc(logValue, constraintValue);
        }

        private static MatchFunc GenerateEventIdMatchFunc(Constraint constraint)
        {
            var fieldValue = constraint.Value;
            var ruleOperator = constraint.RuleOperator;

            return ruleOperator switch
            {
                OperatorType.NumericEquals => (_, eventId, _) =>
                    CompareEventId(eventId.Id, fieldValue, (e, v) => e == v),

                OperatorType.NumericNotEquals => (_, eventId, _) =>
                    CompareEventId(eventId.Id, fieldValue, (e, v) => e != v),
                    
                _ => throw new NotImplementedException($"Unsupported operator type: {ruleOperator}") // This should never happen, we should have validated the constraints before.
            };
        }
    }
}
