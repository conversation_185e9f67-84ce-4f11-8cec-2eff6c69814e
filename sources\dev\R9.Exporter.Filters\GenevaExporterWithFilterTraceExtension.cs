﻿// <copyright file="GenevaExporterWithFilterTraceExtension.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System;
using System.Diagnostics;
using System.Linq;
using System.Runtime.InteropServices;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Options;
using Microsoft.R9.Extensions.Diagnostics;
using Microsoft.R9.Extensions.Telemetry.Exporter.Filters;
using OpenTelemetry.Exporter.Filters;
using OpenTelemetry.Trace;

namespace Microsoft.R9.Extensions.Tracing.Exporters;

/// <summary>
/// Geneva tracing extensions for R9 Tracer.
/// </summary>
public static class GenevaTracingExtensions
{
    /// <summary>
    /// Adds Geneva exporter.
    /// </summary>
    /// <param name="builder">TracerProvider builder where the exporter will be added.</param>
    /// <param name="filter">Filter to be applied to the exporter.</param>
    /// <returns>The instance of <see cref="TracerProviderBuilder"/>.</returns>
    /// <exception cref="ArgumentNullException">When <paramref name="builder"/> is <see langword="null" />.</exception>
    public static TracerProviderBuilder AddGenevaExporter(this TracerProviderBuilder builder, BaseFilter<Activity> filter)
    {
        _ = Throws.IfNull(builder);
        return builder.ConfigureServices(services => services
            .TryAddGenevaTraceExporter(filter));
    }

    /// <summary>
    /// Adds Geneva exporter.
    /// </summary>
    /// <param name="builder">TracerProvider builder where the exporter will be added.</param>
    /// <param name="sampler">Sampler to be applied to the exporter.</param>
    /// <returns>The instance of <see cref="TracerProviderBuilder"/>.</returns>
    /// <exception cref="ArgumentNullException">When <paramref name="builder"/> is <see langword="null" />.</exception>
    public static TracerProviderBuilder AddGenevaExporter(this TracerProviderBuilder builder, Sampler sampler)
    {
        _ = Throws.IfNull(builder);
        return builder.ConfigureServices(services => services
            .TryAddGenevaTraceExporter(new SamplerFilter(sampler)));
    }

    /// <summary>
    /// Adds Geneva exporter.
    /// </summary>
    /// <param name="builder">TracerProvider builder where the exporter will be added.</param>
    /// <param name="configure">Geneva exporter extended options to be configured.</param>
    /// <param name="filter">Filter to be applied to the exporter.</param>
    /// <returns>The instance of <see cref="TracerProviderBuilder"/>.</returns>
    /// <exception cref="ArgumentNullException">When <paramref name="builder"/> or <paramref name="configure"/> is <see langword="null"/>.</exception>
    public static TracerProviderBuilder AddGenevaExporter(
        this TracerProviderBuilder builder,
        Action<GenevaTraceExporterOptions> configure, BaseFilter<Activity> filter)
    {
        _ = Throws.IfNull(builder);
        _ = Throws.IfNull(configure);

        return builder.ConfigureServices(services => services
            .Configure(configure)
            .TryAddGenevaTraceExporter(filter));
    }

    /// <summary>
    /// Adds Geneva exporter.
    /// </summary>
    /// <param name="builder">TracerProvider builder where the exporter will be added.</param>
    /// <param name="configure">Geneva exporter extended options to be configured.</param>
    /// <param name="sampler">Sampler to be applied to the exporter.</param>
    /// <returns>The instance of <see cref="TracerProviderBuilder"/>.</returns>
    /// <exception cref="ArgumentNullException">When <paramref name="builder"/> or <paramref name="configure"/> is <see langword="null"/>.</exception>
    public static TracerProviderBuilder AddGenevaExporter(
        this TracerProviderBuilder builder,
        Action<GenevaTraceExporterOptions> configure, Sampler sampler)
    {
        _ = Throws.IfNull(builder);
        _ = Throws.IfNull(configure);

        return builder.ConfigureServices(services => services
            .Configure(configure)
            .TryAddGenevaTraceExporter(new SamplerFilter(sampler)));
    }

    /// <summary>
    /// Adds Geneva exporter.
    /// </summary>
    /// <param name="builder">TracerProvider builder where the exporter will be added.</param>
    /// <param name="section">Configuration section that contains <see cref="GenevaTraceExporterOptions"/>.</param>
    /// <param name="filter">Filter to be applied to the exporter.</param>
    /// <returns>The instance of <see cref="TracerProviderBuilder"/>.</returns>
    /// <exception cref="ArgumentNullException">When <paramref name="builder"/> or <paramref name="section"/> is <see langword="null" />.</exception>
    public static TracerProviderBuilder AddGenevaExporter(
        this TracerProviderBuilder builder,
        IConfigurationSection section, BaseFilter<Activity> filter)
    {
        _ = Throws.IfNull(builder);
        _ = Throws.IfNull(section);

        return builder.ConfigureServices(services => services
            .Configure<GenevaTraceExporterOptions>(section)
            .TryAddGenevaTraceExporter(filter));
    }

    /// <summary>
    /// Adds Geneva exporter.
    /// </summary>
    /// <param name="builder">TracerProvider builder where the exporter will be added.</param>
    /// <param name="section">Configuration section that contains <see cref="GenevaTraceExporterOptions"/>.</param>
    /// <param name="sampler">Sampler to be applied to the exporter.</param>
    /// <returns>The instance of <see cref="TracerProviderBuilder"/>.</returns>
    /// <exception cref="ArgumentNullException">When <paramref name="builder"/> or <paramref name="section"/> is <see langword="null" />.</exception>
    public static TracerProviderBuilder AddGenevaExporter(
        this TracerProviderBuilder builder,
        IConfigurationSection section, Sampler sampler)
    {
        _ = Throws.IfNull(builder);
        _ = Throws.IfNull(section);

        return builder.ConfigureServices(services => services
            .Configure<GenevaTraceExporterOptions>(section)
            .TryAddGenevaTraceExporter(new SamplerFilter(sampler)));
    }

    internal static IServiceCollection TryAddGenevaTraceExporter(this IServiceCollection services, bool isWindows, BaseFilter<Activity> filter)
    {
        if (services.Any(x => !x.IsKeyedService
                              && (x.ImplementationType == typeof(GenevaReentrantActivityExportProcessor)
                                || x.ImplementationType == typeof(GenevaBatchActivityExportProcessor))))
        {
            return services;
        }

        _ = services.AddOptionsWithValidateOnStart<GenevaTraceExporterOptions, GenevaTraceExporterOptionsValidator>();
        _ = services.AddOptionsWithValidateOnStart<GenevaTraceExporterOptions, CustomGenevaTraceExporterOptionsValidator>();

        if (isWindows)
        {
            return services.ConfigureOpenTelemetryTracerProvider((sp, builder) =>
            {
                var exporterOptions = sp.GetRequiredService<IOptions<GenevaTraceExporterOptions>>();
                _ = builder.AddProcessor(new GenevaReentrantActivityExportProcessorWithFilter(exporterOptions, filter));
            });
        }
        return services.ConfigureOpenTelemetryTracerProvider((sp, builder) =>
        {
            var exporterOptions = sp.GetRequiredService<IOptions<GenevaTraceExporterOptions>>();
            _ = builder.AddProcessor(new GenevaBatchActivityExportProcessorWithFilter(exporterOptions, filter));
        });
    }

    private static IServiceCollection TryAddGenevaTraceExporter(this IServiceCollection services, BaseFilter<Activity> filter) =>
        services.TryAddGenevaTraceExporter(RuntimeInformation.IsOSPlatform(OSPlatform.Windows), filter);
}
