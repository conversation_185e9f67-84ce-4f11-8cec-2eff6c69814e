#pragma once

#include "FileManager.h"
#include <opentelemetry/sdk/common/exporter_utils.h>
#include <opentelemetry/sdk/logs/exporter.h>

#include <memory>

namespace Microsoft {
namespace M365 {
namespace Exporters {

struct LogFileExporterOptions {
    std::string file_path;
    std::chrono::seconds reset_duration;
};

// Theoretically this class can throw. Mark noexcept to explicity crash the process if an exception is thrown.
// TODO(jiayiwang): Catch and log the exception before going to SDF.
class LogFileExporter : public opentelemetry::sdk::logs::LogRecordExporter {
public:
    LogFileExporter(LogFileExporterOptions options) noexcept;
    std::unique_ptr<opentelemetry::sdk::logs::Recordable> MakeRecordable() noexcept override;
    opentelemetry::sdk::common::ExportResult Export(
        const opentelemetry::nostd::span<std::unique_ptr<opentelemetry::sdk::logs::Recordable>> &records) noexcept override;
    bool Shutdown(std::chrono::microseconds timeout = std::chrono::microseconds(0)) noexcept override;
    bool ForceFlush(std::chrono::microseconds timeout = std::chrono::microseconds(0)) noexcept override;

private:
    std::unique_ptr<FileManager> file_manager_;
};

} // namespace Exporters
} // namespace M365
} // namespace Microsoft