<?xml version="1.0" encoding="utf-8"?>
<Project xmlns="http://schemas.microsoft.com/developer/msbuild/2003">

  <Import Project="$([MSBuild]::GetDirectoryNameOfFileAbove('$(MSBuildThisFileDirectory)..', 'Directory.Build.targets'))\Directory.Build.targets" />

  <!--  Explicitly import for native C++ project files. -->
  <Import Project="$(PkgMicrosoft_Internal_CodeSign_CloudBuild)\CodeSign.targets" Condition="Exists('$(PkgMicrosoft_Internal_CodeSign_CloudBuild)\CodeSign.targets ') And '$(Project_IsSigned)' == 'true' And '$(TargetFramework)' == ''" />
  <!-- Since -5 is for strong name, which is not required for native binaries, excluding it -->
  <PropertyGroup Condition = "'$(ENABLE_CLOUDSIGN)' == '1' And '$(Project_IsSigned)' == 'true' And '$(TargetFramework)' == '' And '$(TargetFrameworks)' == ''">
    <AssemblyCertificates>-6</AssemblyCertificates>
  </PropertyGroup>

  <!-- Mitigation of MSB3491 issue for building vcxprojs since MSBuild 16.8 according to https://stackoverflow.microsoft.com/questions/238673 -->
  <PropertyGroup>
    <GetProjectInfoForReferenceDependsOn>
      GetProjectInfoForReferenceWorkaround;
      $(GetProjectInfoForReferenceDependsOn);
    </GetProjectInfoForReferenceDependsOn>
  </PropertyGroup>

  <Target Name="GetProjectInfoForReferenceWorkaround">
    <PropertyGroup>
      <ComputeCompileInputsTargets />
    </PropertyGroup>
  </Target>

</Project>