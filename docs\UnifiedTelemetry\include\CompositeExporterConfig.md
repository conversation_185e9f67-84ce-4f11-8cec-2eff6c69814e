**Section Key**: `SubstrateLogging:CompositeExporter`

This is an internal Component, designed to route Logs among different exporters in runtime.
Now it supports Geneva Exporter and OdlTcp Exporter and is able to export logs to either or both expoters.

Before setup options for Composite Exporter, add `SubstrateLogging:UseCompositeExporter=true` to enable it.

There **must** be:
  - A `VirtualTableMappings` to map logs to virtual tables
  - A `Geneva:ConnectionString` to setup ETW session with Geneva Agent for data export.
  - A `OdlTcp:ConnectionString` to setup TCP session with Odl Agent for data export.
  - A `VirtualTableExports` to define export destination of each virtual table.

The config of a dedicate exporter is configured with `SubstrateLogging:CompositeExporter:{Exporter}`.
Please check docs for detail options of each exporter:
- [Geneva Exporter](https://eng.ms/docs/experiences-devices/r9-sdk/docs/telemetry/logging/geneva-log-export#logger-configuration)
- [OdlTcp Exporter](https://o365exchange.visualstudio.com/O365%20Core/_git/TelemetryCore?path=/sources/dev/Telemetry.Exporters.ODLTcp/Log/README.md&_a=preview)

> [!Note]
> No matter which exporter is defined, we require both `ConnectionString` to be set to initialize all exporters for swithing in runtime.
> For example, no export (empty `VirtualTableExports`) is set for debug purpose. We still need to have both `ConnectionString` to make it run successfully.

<!-- Check [Composite Exporter](CompositeExporter.md) page for option detail. -->

**Example**

```json
{
    "SubstrateLogging": {
        "UseCompositeExporter": true,
        "CompositeExporter": {
            "VirtualTableMappings": {
                "Test.MyService": "MyServiceTable"
            },
            "Geneva": {
                "ConnectionString": "EtwSession=test"
            },
            "OdlTcp": {
                "ConnectionString": "tcp://localhost:1234"
            },
            "VirtualTableExports": {
                "MyServiceTable": [
                    {
                        "ExporterType": "Geneva",
                        "ExportTable": "ServiceEvent"
                    },
                    {
                        "ExporterType": "OdlTcp",
                        "ExportTable": "TestLogType"
                    }
                ]
            }
        }
    }
}
```