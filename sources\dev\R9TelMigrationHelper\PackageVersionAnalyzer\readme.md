## why we need this tool?
When we upgrade packages in Substrate, we need to update multiple indirect-dependencies in the same time. It will be a very time-consuming work if we test them by running cloudbuild repeatedly. 
So I created this tool during FHL to get the full list of packages that we need to upgrade in Substrate.
## how this tool works?
It is a Depth first tree: each node represents a package, the leaves are it's dependencies.
The dependency data is queried from https://o365exchange.pkgs.visualstudio.com/DefaultCollection/_packaging/Common/nuget/v3/index.json.
### what is the input?
1. the list of packages you want to upgrade in ```upgradingPackages.xml```.
2. your PAT for querying data from VSO.
### what is the output?
All extra packages that you need to update in ```packages.props```.
