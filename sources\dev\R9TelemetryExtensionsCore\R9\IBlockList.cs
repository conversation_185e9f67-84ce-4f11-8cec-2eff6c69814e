﻿// <copyright file="IBlockList.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

namespace Microsoft.M365.Core.Telemetry.R9
{
    /// <summary>
    /// IBlockList
    /// </summary>
    public interface IBlockList
    {
        /// <summary>
        /// Block a metric if it has been logged with another category before.
        /// Technically using another category with the same account&namespace is ok. We block them anyway.
        /// </summary>
        /// <param name="name">Metric name.</param>
        /// <param name="category">Metric category.</param>
        public bool ShouldBlockMetric(string name, string category);
    }
}
