<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="Sdk.props" Sdk="Microsoft.Build.NoTargets" />

  <PropertyGroup>
    <GitExe Condition="'$(GitExe)' == ''">$(PkgPortableGit)\bin\git.exe</GitExe>
    <GitRepoPath Condition="'$(GitRepoPath)' == ''">$(EnlistmentRoot)</GitRepoPath>
    <TargetFramework>net472</TargetFramework>
  </PropertyGroup>
  <ItemGroup>
    <PackageReference Include="PortableGit" GeneratePathProperty="true" />
  </ItemGroup>

  <Import Project="Sdk.targets" Sdk="Microsoft.Build.NoTargets" />

  <Target Name="BeforeBuild">
    <Exec Command='$(GitExe) describe --tags --abbrev=0 --match v*'
        WorkingDirectory="$(GitRepoPath)"
        StandardErrorImportance="low"
        StandardOutputImportance="low"
        ConsoleToMSBuild="true"
        ContinueOnError="true">
      <Output TaskParameter="ConsoleOutput" PropertyName="GitConsoleOutput"/>
      <Output TaskParameter="ExitCode" PropertyName="MSBuildLastExitCode" />
    </Exec>

    <Message Text="git describe completed with exit code $(MSBuildLastExitCode) and console output: '$(GitConsoleOutput)'. Executed from working directory: '$(EnlistmentRoot)'" Importance="high" />

    <PropertyGroup Condition="'$(MSBuildLastExitCode)' == '0'">
      <InformationalVersion>$(GitConsoleOutput.Substring(1))</InformationalVersion>
      <FileVersion>$(InformationalVersion.Replace('-pre','.').Replace('..','.'))</FileVersion>
      <AssemblyVersion>$(FileVersion.Substring(0, $(FileVersion.IndexOf('.')))).0.0.0</AssemblyVersion>
    </PropertyGroup>

    <Message Text="AssemblyVersion='$(AssemblyVersion)', FileVersion='$(FileVersion)', InformationalVersion='$(InformationalVersion)'" Importance="high" Condition="'$(AssemblyVersion)' != ''" />
    <Error Text="Failed to parse git version tags! Git command exit code: $(MSBuildLastExitCode), console output: '$(GitConsoleOutput)'. Executed from working directory: '$(EnlistmentRoot)'" Condition="'$(AssemblyVersion)' == ''" />

  </Target>
  <Import Project="$(EnlistmentRoot)\build\Extensions\versiongen.targets" />

</Project>