﻿// <copyright file="IOpenAIService.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System.Runtime.CompilerServices;

namespace R9MigrationHelper.Implementations
{
    /// <summary>
    /// interface for OpenAI service
    /// </summary>
    public interface IOpenAIService
    {
        /// <summary>
        /// GPT covnersation
        /// </summary>
        /// <param name="instruction"></param>
        /// <param name="question"></param>
        /// <param name="retryMS"></param>
        /// <returns></returns>
        Task<string> AzureGPTConversation(string instruction, string question, int retryMS = 3000);
    }
}
