﻿// <copyright file="ServiceMetaDataOptions.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

namespace Microsoft.M365.Core.Telemetry.R9.Tracing.Instrumentation.Accelerator
{
    using System.ComponentModel.DataAnnotations;

    /// <summary>
    /// ServiceMetaDataOptions
    /// </summary>
    public class ServiceMetaDataOptions
    {
        /// <summary>
        /// SchemaVersion
        /// </summary>
        public ConfigSchemaSupportedVersions SchemaVersion { get; set; } = ConfigSchemaSupportedVersions.V1;

        /// <summary>
        /// ServiceVersion
        /// </summary>
        public string ServiceVersion { get; set; } = "1.0";

        /// <summary>
        /// ServiceName
        /// </summary>
        [Required]
        public string ServiceName { get; set; }

        /// <summary>
        /// RuntimeModel
        /// </summary>
        [Required]
        public string RuntimeModel { get; set; }
    }
}
