{"options": [{"enabled": false, "definition": {"id": "5d58cc01-7c75-450c-be18-a388ddb129ec"}, "inputs": {"branchFilters": "[\"+refs/heads/*\"]", "additionalFields": "{}"}}, {"enabled": false, "definition": {"id": "a9db38f9-9fdc-478c-b0f9-464221e58316"}, "inputs": {"workItemType": "358048", "assignToRequestor": "true", "additionalFields": "{}"}}], "variables": {"system.debug": {"value": "false", "allowOverride": true}}, "retentionRules": [{"branches": ["+refs/heads/*"], "artifacts": [], "artifactTypesToDelete": ["FilePath", "SymbolStore"], "daysToKeep": 10, "minimumToKeep": 1, "deleteBuildRecord": true, "deleteTestResults": true}], "properties": {}, "tags": [], "jobAuthorizationScope": 1, "jobTimeoutInMinutes": 60, "jobCancelTimeoutInMinutes": 5, "process": {"phases": [{"steps": [{"environment": {}, "enabled": true, "continueOnError": true, "alwaysRun": true, "displayName": "Task group: Automatic Reviewers (Verify) $(Build.Repository.Id)", "timeoutInMinutes": 0, "condition": "succeededOrFailed()", "task": {"id": "ce354158-326a-4e56-99a5-d855d31b2ae7", "versionSpec": "1.*", "definitionType": "metaTask"}, "inputs": {"Build.Repository.Id": "$(Build.Repository.Id)"}}], "name": "Agent job 1", "refName": "Phase_1", "condition": "succeeded()", "target": {"executionOptions": {"type": 0}, "allowScriptsAuthAccessOption": false, "type": 1}, "jobAuthorizationScope": 1, "jobCancelTimeoutInMinutes": 1}], "type": 1}, "repository": {"properties": {"cleanOptions": "0", "labelSources": "0", "labelSourcesFormat": "$(build.buildNumber)", "reportBuildStatus": "true", "gitLfsSupport": "false", "skipSyncSource": "true", "checkoutNestedSubmodules": "false", "fetchDepth": "0"}, "id": "{{RepositoryId}}", "type": "TfsGit", "name": "{{RepositoryName}}", "url": "{{RepositoryUrl}}", "defaultBranch": "refs/heads/master", "clean": "false", "checkoutSubmodules": false}, "processParameters": {}, "quality": 1, "drafts": [], "queue": {"id": "{{AgentPoolId}}", "name": "Official", "url": "https://o365exchange.visualstudio.com/_apis/build/Queues/{{AgentPoolId}}", "pool": {"id": 11, "name": "Official"}}, "id": 1659, "name": "{{RepositoryName}} VerifyAutomaticReviewers", "path": "\\{{RepositoryName}}", "type": 2, "queueStatus": 0}