﻿```
appsettings.json
{
  "Microsoft_m365_core_telemetry": {
    "ServiceMetadata": {

      // required, your service identifier
      "ServiceName": "Auth",

      // required, your service model: ModelA, ModelB, ModelB2, ModelD, Cosmic
      "RuntimeModel": "Cosmic"
    },
    "Tracing": {

      // optional Default: V1
      "ScehmaVersion": "V1",

      // optional Default: []
      "ActivitySources": [ "Source1", "Source2" ],

      // Configurations for trace auto collector for incoming http requests
      // Extra configuration items please reference https://eng.ms/docs/experiences-devices/r9-sdk/docs/telemetry/distributed-tracing/http-tracing for more info.
      "HttpTracing": {

        // optional Default: false
        "IsEnabled": true,

        // optional Default: "Default", currently we just support Default
        "RedactionStrategyType": "Default"
      },
      // Configurations for trace auto collector for outgoing http requests
      // Extra configuration items please reference https://eng.ms/docs/experiences-devices/r9-sdk/docs/telemetry/distributed-tracing/http-client-tracing for more info.
      "HttpClientTracing": {

        // optional Default: true
        "IsEnabled": true, 

        // optional Default: "Default", currently we just support Default
        "RedactionStrategyType": "Default"
      },
      "ConsoleTracingExporter": {

        // optional Default: false
        "IsEnabled": true
      },
      "ODLTraceExporter": {
        "IsEnabled": false //optional default: true
        "BatchExport": true //optional default: true
      },
      "BatchExport": false // ODL BatchExport, default: true
      // Check out https://eng.ms/docs/experiences-devices/r9-sdk/docs/telemetry/distributed-tracing/geneva-trace-export for more info.
      "GenevaTraceExporter": {
        // optional Default: EtwSession=PassiveR9TestSession
        "ConnectionString": "EtwSession=PassiveR9TestSession",
        // optional default: 0.1
        "TraceIdBasedSampleRatio": 0.1 
      }
    }
  }
}
```
```csharp

namespace Microsoft.M365.Core.Telemetry.R9.Tracing.Instrumentation.Accelerator
{
    public enum ConfigSchemaSupportedVersions
    {
         V1 = 0,
    }

    public class ServiceMetaDataOptions
    {
        public ConfigSchemaSupportedVersions SchemaVersion { get; set; } = ConfigSchemaSupportedVersions.V1;

        public string ServiceVersion { get; set; } = "1.0";

        [Required]
        public string ServiceName { get; set; }

        [Required]
        public string RuntimeModel { get; set; }
    }

    public class RoleInfoEnricher : Microsoft.R9.Extensions.Enrichment.ITraceEnricher
    {
        public RoleInfoEnricher(Microsoft.Extensions.Options.IOptions<ServiceMetaDataOptions> options);
        public void Enrich(System.Diagnostics.Activity activity);
    }

    public class ConsoleTracingExporterOptionsInherited : OpenTelemetry.Exporter.ConsoleExporterOptions
    {
        public bool IsEnabled { get; set; }
    }

    public class GenevaTraceExporterOptionsInherited : GenevaTraceExporterOptions
    {
        public bool IsEnabled { get; } = true;
        public double TraceIdBasedSampleRatio { get; set; } = 1.0;
    }

    public class ODLTraceExporterOptionsInherited : ODLTraceExporterOptions
    {

        public bool IsEnabled { get; set; } = true;
        public new bool EnableFallBack
        {
            get { return false;  }
        }
        public bool BatchExport { get; set; } = true;
    }

    public enum RedactionStrategyType
    {
        Default
    }

    public class HttpClientTracingOptionsInherited : Microsoft.R9.Extensions.HttpClient.Tracing.HttpClientTracingOptions
    {
        public bool IsEnabled { get; set; } = true;

        public RedactionStrategyType RedactionStrategyType { get; set; } = RedactionStrategyType.Default;
    }

    public class HttpTracingOptionsInherited : Microsoft.R9.Extensions.Tracing.Http.HttpTracingOptions
    {
        public bool IsEnabled { get; set; }

        public RedactionStrategyType RedactionStrategyType { get; set; } = RedactionStrategyType.Default;
    }

    public class TracingAcceleratorOptions 
    {
        public ConfigSchemaSupportedVersions SchemaVersion { get; set; } = ConfigSchemaSupportedVersions.V1;

        public string[] ActivitySources { get; set; } = Array.Empty<string>();

        public HttpClientTracingOptionsInherited HttpClientTracing { get; set; } = new HttpClientTracingOptionsInherited();

        public HttpTracingOptionsInherited HttpTracing { get; set; } = new HttpTracingOptionsInherited();

        public GenevaTraceExporterOptionsInherited GenevaTraceExporter { get; set; } = new GenevaTraceExporterOptionsInherited()
        { 
            ConnectionString = Constants.DefaultGenevaTraceExporterConnectionString,
            TraceIdBasedSampleRatio = Constants.GenevaTraceExporterTraceIdBasedSampleRatio
        };

        public ConsoleTracingExporterOptionsInherited ConsoleTracingExporter { get; set; } = new ConsoleTracingExporterOptionsInherited();

        public ODLTraceExporterOptionsInherited ODLTraceExporter { get; set; } = new ODLTraceExporterOptionsInherited();
    }

    public static class TracerSdk
    {
        public static ServiceProvider InitDistributedTracingService(Microsoft.Extensions.Configuration.IConfiguration serviceMetaDataConfig, Microsoft.Extensions.Configuration.IConfiguration tracingAcceleratorConfig = default, Action<OpenTelemetry.Trace.TracerProviderBuilder> extraTraceProviderBuilderConfigure = default);
    }

    public static class TracerStartupExtensions
    {
         public static R9TracingConfig MockedR9TracingConfig { get; set; } 
         public static IServiceCollection AddDistributedTracingService(this Microsoft.Extensions.DependencyInjection.IServiceCollection services, Microsoft.Extensions.Configuration.IConfiguration serviceMetaDataConfig, Microsoft.Extensions.Configuration.IConfiguration tracingAcceleratorConfig = default, Action<OpenTelemetry.Trace.TracerProviderBuilder> extraTraceProviderBuilderConfigure = default);
#if NETFRAMEWORK
        public static ServiceProvider StartServices(this Microsoft.Extensions.DependencyInjection.IServiceCollection services);
        public static void StopServices(this Microsoft.Extensions.DependencyInjection.ServiceProvider serviceProvider);
#endif
    }
}
```
