// <copyright file="ServiceInfo.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System;
using System.Diagnostics.CodeAnalysis;

namespace Microsoft.M365.Core.Telemetry.ECS.DynamicComponent
{
    /// <summary>
    /// Class to store service running information
    /// </summary>
    [ExcludeFromCodeCoverage]
    [Obsolete("ServiceName and RunningModel should be put in appsettings.json")]
    public class ServiceInfo
    {
        /// <summary>
        /// service name
        /// </summary>
        public string ServiceName { get; set; } = "*";

        /// <summary>
        /// service running model, like ModelA
        /// </summary>
        public ServiceRunningModel RunningModel { get; set; } = ServiceRunningModel.Default;

        /// <summary>
        /// default constructor
        /// </summary>
        public ServiceInfo() 
        {
        }

        /// <summary>
        /// initialize with service name
        /// </summary>
        /// <param name="serviceName"></param>
        public ServiceInfo(string serviceName)
        {
            this.ServiceName = serviceName;
        }

        /// <summary>
        /// initialize with service name and running model
        /// </summary>
        /// <param name="serviceName">service name</param>
        /// <param name="runningModel">running model</param>
        public ServiceInfo (string serviceName, ServiceRunningModel runningModel) 
        { 
            this.ServiceName = serviceName;
            this.RunningModel = runningModel;
        }
    }

    /// <summary>
    /// service running model
    /// </summary>
    public enum ServiceRunningModel
    {
        Default,
        ModelA,
        ModelB,
        ModelB2,
        ModelD,
        ModelD2,
        Cosmic
    }
}
