﻿// <copyright file="ModifyServiceConfigSkill.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml;
using System.Xml.Linq;
using Kusto.Cloud.Platform.Utils;
using R9MigrationHelper.Model;

namespace R9MigrationHelper.Skills.BindingRedirectSkills.StringParseSkills
{
    /// <summary>
    /// ModifyServiceConfigSkill.
    /// </summary>
    public class ModifyServiceConfigSkill
    {
        /// <summary>
        /// ModifyServiceConfig.
        /// </summary>
        /// <param name="serviceConfigFileContent">serviceConfigFileContent.</param>
        /// <param name="assemblyList">assemblyList.</param>
        public string ModifyServiceConfig(string serviceConfigFileContent, List<AssemblyModel> assemblyList)
        {
            if (assemblyList == null || assemblyList.Count == 0)
            {
                return serviceConfigFileContent;
            }

            XDeclaration? xDeclaration = XDocument.Parse(serviceConfigFileContent).Declaration;
            string declaration = string.Empty;
            if (xDeclaration != null)
            {
                declaration = xDeclaration!.ToString() + "\n";
            }

            XmlDocument xelementServiceConfigFile = new XmlDocument();
            xelementServiceConfigFile.LoadXml(serviceConfigFileContent);

            string xmlns = "urn:schemas-microsoft-com:asm.v1";
            XmlNamespaceManager mgr = new XmlNamespaceManager(xelementServiceConfigFile.NameTable);
            mgr.AddNamespace("ns", xmlns);
            XmlNode? assemblyBinding = xelementServiceConfigFile.SelectSingleNode("//ns:assemblyBinding", mgr);
            if (assemblyBinding == null)
            {
                assemblyBinding = xelementServiceConfigFile.CreateNode(XmlNodeType.Element, "assemblyBinding", xmlns);

//                var attr = xelementServiceConfigFile.CreateAttribute("xmlns");
//                attr.Value = "urn:schemas-microsoft-com:asm.v1";
//#pragma warning disable CS8602 // Dereference of a possibly null reference.
//                assemblyBinding.Attributes.Append(attr);
//#pragma warning restore CS8602 // Dereference of a possibly null reference.
#pragma warning disable CS8602 // Dereference of a possibly null reference.
                xelementServiceConfigFile.SelectSingleNode("configuration/runtime").AppendChild(assemblyBinding);
#pragma warning restore CS8602 // Dereference of a possibly null reference.
            }

            Dictionary<string, XmlNode> existingBindingRedirect = new Dictionary<string, XmlNode>();
            foreach (XmlNode assembly in assemblyBinding.ChildNodes)
            {
                foreach (XmlNode node in assembly.ChildNodes)
                {
                    if (node.Name != "assemblyIdentity")
                    {
                        continue;
                    }
                    if (existingBindingRedirect.ContainsKey(node!.Attributes!["name"] !.Value))
                    {
                        existingBindingRedirect.Remove(node!.Attributes!["name"] !.Value);
                    }
                    existingBindingRedirect.Add(node!.Attributes["name"] !.Value, assembly);
                }
            }

            HashSet<string> editted = new HashSet<string>();
            foreach (AssemblyModel assembly in assemblyList)
            {
                if (assembly.PublicKeyToken.IsNullOrEmpty() || editted.Contains(assembly.Name)) { continue; }

                string dllName = assembly.Name.Substring(0, assembly.Name.LastIndexOf(".dll", StringComparison.InvariantCulture));
                if (existingBindingRedirect.ContainsKey(dllName))
                {
                    assemblyBinding.RemoveChild(existingBindingRedirect[dllName]);
                }

                XmlNode dependentAssembly = xelementServiceConfigFile.CreateNode(XmlNodeType.Element, "dependentAssembly", xmlns);

                XmlNode assemblyIdentity = xelementServiceConfigFile.CreateNode(XmlNodeType.Element, "assemblyIdentity", xmlns);
                XmlAttribute name = xelementServiceConfigFile.CreateAttribute("name");
                name.Value = dllName;
                XmlAttribute publicKeyToken = xelementServiceConfigFile.CreateAttribute("publicKeyToken");
                publicKeyToken.Value = assembly.PublicKeyToken;
                XmlAttribute culture = xelementServiceConfigFile.CreateAttribute("culture");
                culture.Value = "neutral";
#pragma warning disable CS8602 // Dereference of a possibly null reference.
                assemblyIdentity.Attributes.Append(name);
#pragma warning restore CS8602 // Dereference of a possibly null reference.
                assemblyIdentity.Attributes.Append(publicKeyToken);
                assemblyIdentity.Attributes.Append(culture);

                XmlNode bindingRedirect = xelementServiceConfigFile.CreateNode(XmlNodeType.Element, "bindingRedirect", xmlns);
                XmlAttribute oldVersion = xelementServiceConfigFile.CreateAttribute("oldVersion");
                oldVersion.Value = $"0.0.0.0-{MaxAssemblyVersion(assembly.Version, assembly.OldVersion)}";
                XmlAttribute newVersion = xelementServiceConfigFile.CreateAttribute("newVersion");
                newVersion.Value = assembly.Version;
#pragma warning disable CS8602 // Dereference of a possibly null reference.
                bindingRedirect.Attributes.Append(oldVersion);
#pragma warning restore CS8602 // Dereference of a possibly null reference.
                bindingRedirect.Attributes.Append(newVersion);

                dependentAssembly.AppendChild(assemblyIdentity);
                dependentAssembly.AppendChild(bindingRedirect);
                assemblyBinding.AppendChild(dependentAssembly);
                editted.Add(assembly.Name);
            }

            return declaration + XElement.Parse(xelementServiceConfigFile.OuterXml).ToString();
        }

        private XElement? FindXElementWithAttribute(XElement root, string elementName, string attributeName, string attributeValue)
        {
            if (root == null)
            {
                return null;
            }
            XElement? newRoot = root.Descendants("runtime").FirstOrDefault();
            if (newRoot == null)
            {
                return null;
            }
            foreach (XElement el in newRoot.Descendants(elementName))
            {
                if (el.Attribute(attributeName)?.Value == attributeValue)
                {
                    return el;
                }
            }
            return null;
        }

        private string MaxAssemblyVersion(string version1, string version2)
        {
            if (version1.IsNullOrEmpty())
            {
                return version2;
            }
            if (version2.IsNullOrEmpty())
            {
                return version1;
            }

            string[] version1Array = version1.Split('.', StringSplitOptions.RemoveEmptyEntries);
            string[] version2Array = version2.Split('.', StringSplitOptions.RemoveEmptyEntries);
            for (int i = 0; i < version1Array.Length; i++)
            {
                if (i >= version2Array.Length)
                {
                    return version1;
                }
                if (int.Parse(version1Array[i], CultureInfo.InvariantCulture) > int.Parse(version2Array[i], CultureInfo.InvariantCulture))
                {
                    return version1;
                }
                else if (int.Parse(version1Array[i], CultureInfo.InvariantCulture) < int.Parse(version2Array[i], CultureInfo.InvariantCulture))
                {
                    return version2;
                }
            }
            return version2;
        }
    }
}
