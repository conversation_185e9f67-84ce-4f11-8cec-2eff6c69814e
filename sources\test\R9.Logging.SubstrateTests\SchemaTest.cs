﻿// <copyright file="SchemaTest.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>
namespace Microsoft.M365.Core.Telemetry.R9.Logging.Substrate.Test
{
    using System;
    using System.Diagnostics.Tracing;
    using System.IO;
    using System.Text;
    using FluentAssertions;
    using Microsoft.Extensions.Configuration;
    using Microsoft.Extensions.Logging;
    using Microsoft.R9.Extensions.Logging.Exporters;
    using Xunit;

    public class SchemaTest
    {
        private const string BasicConfig = @"
{
        ""SubstrateLogging"": {
        ""R9Logging"": {
            ""MaxStackTraceLength"": 3000
        },
        ""GenevaExporter"": {
            ""ConnectionString"": ""EtwSession=TestSession1"",
            ""TableNameMappings"": {
                ""Microsoft.Namespace.EventName"": ""EventName""
            }
        }
    }
}";

        /// <summary>
        /// Test ilooger for SchemaTest
        /// </summary>
        private ILogger? schemalogger;

        /// <summary>
        /// call to initialize normally
        /// </summary>
        internal void InitializeR9SubstrateLogging()
        {
            IConfiguration configuration = new ConfigurationBuilder()
                .AddJsonStream(new MemoryStream(Encoding.UTF8.GetBytes(BasicConfig)))
                .Build();
            ILoggerFactory loggerFactory = LoggerFactory.Create(builder =>
            {
                builder.AddConsoleExporter().ConfigureSubstrateLogging(configuration);
            });

            this.schemalogger = loggerFactory.CreateLogger("SchemaLogger");
        }

        [Fact]
        public void SchemaEventGenerationTest()
        {
            SampleEvent sampleEvent = new SampleEvent(0.0, "SampleService");
            SampleEvent sampleEvent2 = new SampleEvent(0.0, "SampleService", "test message", EventLevel.Warning);

            var fields = sampleEvent.GetType()
           .GetProperties(System.Reflection.BindingFlags.Public | System.Reflection.BindingFlags.Instance);
            var fields2 = sampleEvent2.GetType()
            .GetProperties(System.Reflection.BindingFlags.Public | System.Reflection.BindingFlags.Instance);

            Assert.NotNull(sampleEvent.MachineProvisioningState);
            Assert.Equal("test message", sampleEvent2.Message);
            Assert.Equal((uint)EventLevel.Warning, sampleEvent2.EventLevel);
            Assert.Equal(12, fields.Length);
            Assert.Equal(12, fields2.Length);
        }

        [Fact]
        public void SchemaEventLoggingTest()
        {
            this.InitializeR9SubstrateLogging();
            SampleEvent sampleEvent = new SampleEvent(0.0, "SampleService");
            Assert.NotNull(this.schemalogger);
            var writer = new StringWriter();
            Console.SetOut(writer);
            if (this.schemalogger != null)
            {
                R9TestLogging.Log(this.schemalogger, sampleEvent);
            }
            Assert.NotNull(sampleEvent.MachineProvisioningState);

            // Assert.
            string output = writer.GetStringBuilder().ToString();
            output.Should().Contain("SchemaLogger");
        }
    }
}
