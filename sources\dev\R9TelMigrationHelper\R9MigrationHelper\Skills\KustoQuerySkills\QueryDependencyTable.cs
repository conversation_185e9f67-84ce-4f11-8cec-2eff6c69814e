﻿// <copyright file="QueryDependencyTable.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System.Collections.Generic;
using System.Data;
using System.Diagnostics.CodeAnalysis;
using System.Globalization;
using System.Reflection;
using System.Text;
using System.Threading.Tasks;
using Kusto.Cloud.Platform.Utils;
using Kusto.Data;
using Kusto.Data.Common;
using Kusto.Data.Net.Client;
using R9MigrationHelper.Model;

namespace R9MigrationHelper.Skills.KustoQuerySkills
{
    /// <summary>
    /// QueryDependencyTable.
    /// </summary>
    [ExcludeFromCodeCoverage]
    internal class QueryDependencyTable
    {
        private readonly string kustoCluster = "https://azscperf.westus.kusto.windows.net";
        private readonly string databaseName = "dependency";
        private string connectionString = string.Empty;

        /// <summary>
        /// QueryDependencyTable.
        /// </summary>
        public QueryDependencyTable()
        {
            this.connectionString = new KustoConnectionStringBuilder(kustoCluster, databaseName)
            .WithAadUserPromptAuthentication("72f988bf-86f1-41af-91ab-2d7cd011db47")
            .ConnectionString;
        }

        /// <summary>
        /// QueryDependencyTable.
        /// </summary>
        /// <param name="connectionString">connection String.</param>
        /// <param name="databaseName">database Name.</param>
        public QueryDependencyTable(string connectionString, string databaseName)
        {
            this.connectionString = connectionString;
            this.databaseName = databaseName;
            _ = new QueryDependencyTable();
        }

        /// <summary>
        /// GetAssemblyDataAsync.
        /// </summary>
        /// <param name="assemblyName">assembly Name.</param>
        /// <param name="assemblyVersion">assembly Version.</param>
        /// <param name="serviceFramework">service Framework.</param>
        public async Task<AssemblyModel> GetAssemblyDataAsync(string assemblyName, string assemblyVersion, string serviceFramework)
        {
            if (!assemblyName.EndsWith(".dll", StringComparison.OrdinalIgnoreCase))
            {
                assemblyName += ".dll";
            }

            string query = @"declare query_parameters (assemblyName:string, assemblyVersion:string);
cluster('azscperf.westus.kusto.windows.net').database('dependency').PackageAssembly
| where AssemblyName =~ assemblyName and AssemblyVersion == assemblyVersion
| where LibraryDirectoryPath startswith ""lib"" and not(LibraryDirectoryPath endswith "".dll"")
| distinct Name, Version, LibraryDirectoryPath"; // TBD

            Dictionary<string, string> queryParameters = new Dictionary<string, string>()
            {
                { "assemblyName", assemblyName },
                { "assemblyVersion", assemblyVersion }
            };

            AssemblyModel assemblyModel = new AssemblyModel()
            {
                Name = assemblyName,
                Version = assemblyVersion
            };

            Dictionary<string, int> versionOrder;
#pragma warning disable CA1304 // Specify CultureInfo
            if (serviceFramework.ToLower().Contains("netcore", StringComparison.OrdinalIgnoreCase))
            {
                versionOrder = new Dictionary<string, int>()
                {
                    { @"lib\netcoreapp3.1", 0 },
                    { @"lib\netstandard2.0", 1 }
                };
            }
            else // .NET Framework
            {
                versionOrder = new Dictionary<string, int>()
                {
                    { @"lib\net462", 0 },
                    { @"lib\net461", 1 },
                    { @"lib\net46", 2 },
                    { @"lib\net45", 3 },
                    { @"lib\netstandard2.0", 4 }
                };
            }
#pragma warning restore CA1304 // Specify CultureInfo

            QueryKustoTableSkill queryKustoTableSkill = new QueryKustoTableSkill(connectionString, databaseName);

            AssemblyModel unconventional = new AssemblyModel()
            {
                Name = assemblyName,
                Version = assemblyVersion
            };

            using (IDataReader reader = await queryKustoTableSkill.GetKustoDataAsync(query, queryParameters))
            {
                int currentOrder = 100;
                while (reader.Read())
                {
                    string name = reader.GetString(0);
                    string version = reader.GetString(1);
                    string libraryDirectoryPath = reader.GetString(2);
                    int packageNameWeight = 0;

                    if (name != assemblyName.Substring(0, assemblyName.LastIndexOf(".dll", StringComparison.OrdinalIgnoreCase)))
                    {
                        packageNameWeight = 20;
                    }

                    if (versionOrder.ContainsKey(libraryDirectoryPath) ) 
                    {
                        if (versionOrder[libraryDirectoryPath] + packageNameWeight < currentOrder)
                        {
                            assemblyModel.PackageName = name;
                            assemblyModel.PackageVersion = version;
                            assemblyModel.SourcePath = libraryDirectoryPath;
                            currentOrder = versionOrder[libraryDirectoryPath] + packageNameWeight;
                        }
                        else if (unconventional.SourcePath.IsNullOrEmpty() || version == assemblyVersion)
                        {
                            unconventional.PackageName = name;
                            unconventional.SourcePath = libraryDirectoryPath;
                        }
                    }
                }
            }
            if (assemblyModel.SourcePath.IsNullOrEmpty())
            {
                return unconventional;
            }
            return assemblyModel;
        }

        /// <summary>
        /// GetAssemblyDataAsync.
        /// </summary>
        /// <param name="targetAssembly">old Assembly.</param>
        /// <param name="serviceFramework">service Framework.</param>
        public async Task<AssemblyModel> GetAssemblyDataAsync(AssemblyModel targetAssembly, string serviceFramework)
        {
            string query = @"declare query_parameters (assemblyName:string, assemblyVersion:string);
cluster('resourcemanagement.westus2.kusto.windows.net').database('adhoc').BindingRedirectAssembly
| where AssemblyName =~ assemblyName
| where SourcePath startswith ""lib"" and not(SourcePath endswith "".dll"")
| distinct PackageName, PackageVersion, SourcePath"; // TBD Need to update kusto table

            Dictionary<string, string> queryParameters = new Dictionary<string, string>()
            {
                { "assemblyName", targetAssembly.Name },
                { "assemblyVersion", targetAssembly.Version }
            };

            Dictionary<string, int> versionOrder;
            if (serviceFramework.ToLower(CultureInfo.CurrentCulture).Contains("netcore", StringComparison.InvariantCulture))
            {
                versionOrder = new Dictionary<string, int>()
                {
                    { @"lib\netcoreapp3.1", 0 },
                    { @"lib\netstandard2.0", 1 }
                };
            }
            else // .NET Framework
            {
                versionOrder = new Dictionary<string, int>()
                {
                    { @"lib\net462", 0 },
                    { @"lib\net461", 1 },
                    { @"lib\net46", 2 },
                    { @"lib\net45", 3 },
                    { @"lib\netstandard2.0", 4 }
                };
            }

            QueryKustoTableSkill queryKustoTableSkill = new QueryKustoTableSkill(connectionString, databaseName);

            AssemblyModel unconventional = new AssemblyModel() { Name = targetAssembly.Name, Version = targetAssembly.Version };

            using (IDataReader reader = await queryKustoTableSkill.GetKustoDataAsync(query, queryParameters))
            {
                int currentOrder = 100;
                int unconventionalOrder = 100;
                while (reader.Read())
                {
                    string packageName = reader.GetString(0);
                    string packageVersion = reader.GetString(1);
                    string sourcePath = reader.GetString(2);
                    int packageNameWeight = 0;

                    if (packageName != targetAssembly.Name.Substring(0, targetAssembly.Name.LastIndexOf(".dll", StringComparison.OrdinalIgnoreCase)))
                    {
                        packageNameWeight = 200;
                    }

                    if (versionOrder.ContainsKey(sourcePath))
                    {
                        if (versionOrder[sourcePath] + packageNameWeight < currentOrder)
                        {
                            targetAssembly.PackageName = packageName;
                            targetAssembly.PackageVersion = packageVersion;
                            targetAssembly.SourcePath = sourcePath;
                            currentOrder = versionOrder[sourcePath] + packageNameWeight;
                        }
                        else if (packageNameWeight == 200) // still no same name package found
                        {
                            int unconventionalScore = 0;
                            unconventionalScore += versionOrder[sourcePath];
                            
                            if (packageVersion != targetAssembly.Version) { unconventionalScore += 20; }
                            if (unconventionalScore < unconventionalOrder)
                            {
                                unconventional.PackageName = packageName;
                                unconventional.PackageVersion = packageVersion;
                                unconventional.SourcePath = sourcePath;
                                unconventionalOrder = unconventionalScore;
                            }
                        }
                    }
                }
            }

            if (targetAssembly.SourcePath.IsNotNullOrEmpty())
            {
                return targetAssembly;
            }
            return unconventional;
        }

        /// <summary>
        /// GetAssemblyDataAsync.
        /// </summary>
        /// <param name="targetAssembly">target Assembly.</param>
        public async Task<AssemblyModel> GetAssemblyDataAsync(AssemblyModel targetAssembly)
        {
            string query = @"declare query_parameters (assemblyName:string, assemblyVersion:string);
cluster('resourcemanagement.westus2.kusto.windows.net').database('adhoc').BindingRedirectAssembly
| where AssemblyName =~ assemblyName
| where SourcePath startswith ""lib"" and not(SourcePath endswith "".dll"")
| distinct PackageName, PackageVersion, SourcePath"; // TBD

            Dictionary<string, string> queryParameters = new Dictionary<string, string>()
            {
                { "assemblyName", targetAssembly.Name },
                { "assemblyVersion", targetAssembly.Version }
            };

            QueryKustoTableSkill queryKustoTableSkill = new QueryKustoTableSkill(connectionString, databaseName);

            AssemblyModel unconventional = new AssemblyModel() { Name = targetAssembly.Name, Version = targetAssembly.Version };

            using (IDataReader reader = await queryKustoTableSkill.GetKustoDataAsync(query, queryParameters))
            {
                while (reader.Read())
                {
                    string name = reader.GetString(0);
                    string version = reader.GetString(1);
                    string libraryDirectoryPath = reader.GetString(2);

                    if (name == targetAssembly.Name.Substring(0, targetAssembly.Name.LastIndexOf(".dll", StringComparison.OrdinalIgnoreCase)))
                    {
                        if (string.IsNullOrEmpty(targetAssembly.PackageVersion) || PackageVersionCompare(version, targetAssembly.PackageVersion) > 0)
                        {
                            targetAssembly.PackageName = name;
                            targetAssembly.PackageVersion = version;
                            targetAssembly.SourcePathCandidates = new HashSet<string>() { libraryDirectoryPath };
                        }
                        else if (PackageVersionCompare(version, targetAssembly.PackageVersion) == 0)
                        {
                            targetAssembly.SourcePathCandidates.Add(libraryDirectoryPath);
                        }
                    }
                    else
                    {
                        if (string.IsNullOrEmpty(unconventional.PackageName) || (unconventional.PackageName == name && PackageVersionCompare(version, unconventional.PackageVersion) > 0))
                        {
                            unconventional.PackageName = name;
                            unconventional.PackageVersion = version;
                            unconventional.SourcePathCandidates = new HashSet<string>() { libraryDirectoryPath };
                        }
                        else if (unconventional.PackageName == name && PackageVersionCompare(version, unconventional.PackageVersion) == 0)
                        {
                            unconventional.SourcePathCandidates.Add(libraryDirectoryPath);
                        }
                    }
                }
            }

            if (targetAssembly.SourcePathCandidates.Count > 0)
            {
                return targetAssembly;
            }
            return unconventional;
        }

        private int PackageVersionCompare(string verisonA, string versionB)
        {
            if (verisonA == versionB)
            {
                return 0;
            }
            else
            {
                string[] versionAList = verisonA.Split('.');
                string[] versionBList = versionB.Split('.');
                for (int i = 0; i < versionAList.Length; i++)
                {
                    if (int.Parse(versionAList[i], CultureInfo.InvariantCulture) > int.Parse(versionBList[i], CultureInfo.InvariantCulture))
                    {
                        return 1;
                    }
                }
                return -1;
            }
        }
    }
}
