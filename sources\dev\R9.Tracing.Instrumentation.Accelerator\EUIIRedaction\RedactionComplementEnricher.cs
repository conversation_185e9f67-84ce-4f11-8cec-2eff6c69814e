﻿// <copyright file="RedactionComplementEnricher.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

namespace Microsoft.M365.Core.Telemetry.R9.Tracing.Instrumentation.Accelerator
{
    using System.Diagnostics;
    using Microsoft.R9.Extensions.Enrichment;

    /// <summary>
    /// RedactionComplementEnricher
    /// </summary>
    // This is interim remedy for HttpUrl attributed overrided by R9 internal redaction processors
    internal class RedactionComplementEnricher : ITraceEnricher
    {
        /// <summary>
        /// Enrich
        /// </summary>
        /// <param name="activity"></param>
        public void Enrich(Activity activity)
        {
#if !NETFRAMEWORK
            var value = activity?.GetTagItem(Constants.HttpUrlBackup);
            if ( value != null)
            {
                activity?.SetTag(Constants.HttpUrl, value);
                activity?.SetTag(Constants.HttpUrlBackup, null);
            }
#endif
        }
    }
}
