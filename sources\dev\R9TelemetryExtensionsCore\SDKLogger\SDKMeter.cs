﻿// <copyright file="SDKMeter.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Diagnostics.CodeAnalysis;
using System.Reflection;
using System.Threading;

namespace Microsoft.M365.Core.Telemetry.SDKLogger
{
    /// <summary>
    /// SDKMeter
    /// </summary>
    [ExcludeFromCodeCoverage]
    internal static class SDKMeter
    {
        /// <summary>
        /// Process name, as shown in dependency database.
        /// </summary>
        private static readonly Lazy<string> processName = new Lazy<string>(
            () =>
            {
                string result = "UnknownDueToError";
                try
                {
                    result = String.Join(
                        "#", new string[]
                        {
                            Process.GetCurrentProcess().ProcessName,
                            Environment.GetEnvironmentVariable("APP_POOL_ID", EnvironmentVariableTarget.Process)
                        });
                }
                catch (Exception)
                {
                    // best effort
                }
                return result;
            });

        private static SDKCounter sdkCounter = InternalSDKMetric.CreateSDKCounter(
            InternalSDKR9Services.GetMeter<SDKMeterCategory>());

        private static SDKDebugPerCounter sdkDebugPerfCounter = InternalSDKMetric.CreateSDKDebugPerfCounter(
            InternalSDKR9Services.GetMeter<SDKMeterCategory>());

        /// <summary>
        /// RecordSDKCounter
        /// </summary>
        /// <param name="countEvent"></param>
        /// <param name="countType"></param>
        [SuppressMessage("Microsoft.Performance", "CA1801", Justification = "No action")]
        public static void RecordSDKCounter(string countEvent, string countType)
        {
            if (InternalSDKR9Services.BypassSDKLogger)
            {
                return;
            }
            sdkCounter.Record(1, countEvent, countType, processName.Value);
        }

        /// <summary>
        /// RecordSDKDebugPerfCounter
        /// </summary>
        /// <param name="ns"></param>
        [SuppressMessage("Microsoft.Performance", "CA1801", Justification = "No action")]
        public static void RecordSDKDebugPerfCounter(long ns)
        {
            if (InternalSDKR9Services.BypassSDKLogger)
            {
                return;
            }
            sdkDebugPerfCounter.Record(ns);
        }
    }

    /// <summary>
    /// SDKMeterCategory
    /// </summary>
    internal class SDKMeterCategory
    {
    }
}
